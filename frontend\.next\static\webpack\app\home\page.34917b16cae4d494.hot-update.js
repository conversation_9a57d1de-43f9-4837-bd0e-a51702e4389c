"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./common/lang.ts":
/*!************************!*\
  !*** ./common/lang.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   lang: function() { return /* binding */ lang; }\n/* harmony export */ });\nconst header = {\n    connectButton: {\n        connectWallet: \"Login\",\n        wrongNetwork: \"Wrong Network\",\n        myIdeas: \"My Projects\",\n        logout: \"Logout\",\n        copyAddress: \"Copy Address\",\n        fundAccount: \"Add Funds\",\n        connectedTo: \"Connected to\"\n    },\n    myDreams: \"My Projects\",\n    generate: \"Launch ISO\",\n    searchDreams: \"Search Projects\",\n    bigger: \"Build\",\n    dream: \"Launch\",\n    faster: \"& Scale\",\n    dreamsSubHeading: \"Discover subscription projects\",\n    pilotSubheading: \"Build your community\",\n    dreamathonSubheading: \"Join builder events\",\n    searchIdeas: {\n        placeholder: \"Search for Projects\",\n        noIdeasFound: \"No Projects found\"\n    }\n};\nconst notFound = {\n    heading: \"Something went wrong\",\n    subHeading1: \"Brace yourself till we get the error fixed\",\n    subHeading2: \"You may also refresh the page or try again later\",\n    buttonTitle: \"Return Home\"\n};\nconst footer = {\n    terms: \"Terms\",\n    aboutUs: \"About Us\",\n    privacyPolicy: \"Privacy\",\n    contactUs: \"Contact us\",\n    contactUsModal: {\n        heading: \"Contact Us\",\n        submitButton: \"Send\",\n        youCanContact: \"You can contact us at\"\n    }\n};\nconst createIdea = {\n    categories: {\n        noTagsFound: \"No Categories found\"\n    },\n    addCategoryError: \"Unable to add new category\",\n    accountWrongError: \"Please connect with your account to edit this dream\",\n    generateError: \"Something went wrong. Please try again later.\",\n    transactionError: \"Transaction reverted. Please try again.\",\n    aiRegenerateError: \"Failed to regenerate content. Please try again.\",\n    insufficientBalance: \"You have insufficient balance to create a dream\",\n    whatToImprove: \"What do you want to improve?\",\n    reservedDomain: \"This domain is reserved. Please choose another ticker\",\n    heading: \"Create Token\",\n    subHeading: \"Launch Your Dream\",\n    subHeadingCreated: \"Design Your Dream\",\n    subHeadingFundingReached: \"Funding Reached\",\n    subHeading2Part1: \"Share your dream in detail to connect with potential believers. We will create an\",\n    subHeading2Part2: \"that you can trade on\",\n    subHeading2FundingReached: \"We would suggest not to update the dream details as the funding target has been met and keep the original idea intact.\",\n    tokenCreationFeeLabel: \"One Time Fee\",\n    maxSupplyLabel: \"Max Supply\",\n    initialMintLabel: \"Initial Supply\",\n    tokensSuffix: \" Tokens\",\n    targetInfoPart1: \"After funding target of \",\n    targetInfoPart2: \"is met, a liquidity pool will be created on Uniswap.\",\n    ideaNotFound: \"Dream not found!\",\n    ideaCreatedMessage: \"Dream successfully created!\",\n    ideaDetailsUpdated: \"Dream details updated!\",\n    errorOccurred: \"Something went wrong. Please try again!\",\n    cantEditError: \"You can't update as you are not the owner of this dream!\",\n    validationErrors: {\n        nameRequired: \"Please enter a name for your dream\",\n        nameMinError: \"Please enter more than 2 characters\",\n        nameMaxError: \"Please enter less than 20 characters\",\n        tickerRequired: \"Please enter a name for your ticker\",\n        tickerMinError: \"Please enter more than 2 characters\",\n        tickerMaxError: \"Please enter less than 20 characters\",\n        logoRequired: \"Please upload logo or image depicting your dream\",\n        descriptionRequired: \"Please provide a description for your dream\",\n        descriptionMinError: \"Please enter more than 10 characters\",\n        descriptionMaxError: \"Please enter less than 1000 characters\",\n        categoriesRequired: \"Please select a category\",\n        websiteRequired: \"Please provide a valid link\",\n        twitterInvalid: \"Please provide a valid X link\",\n        telegramInvalid: \"Please provide a valid telegram link\"\n    },\n    uploadDifferent: \"Upload a different image\",\n    imageUpload: {\n        title: \"Dream Logo\",\n        postImage: \"Post Image\",\n        imageSizeError: \"Please upload image less than 5 mb\",\n        imageType: \"This is a wrong file format. Only image files are allowed.\",\n        uploadError: \"Could not add file. Please try again.\",\n        uploading: \"Uploading...\",\n        uploadLabel: \"Upload here\"\n    },\n    form: {\n        name: \"Project Title\",\n        address: \"Address\",\n        ticker: \"Subscription Token\",\n        description: \"Project Description\",\n        category: \"Select Category\",\n        website: \"Website\",\n        websitePreview: \"Project Preview\",\n        twitter: \"X(Twitter)\",\n        telegram: \"Telegram\",\n        submitLabel: \"Submit\",\n        connectWallet: \"Connect Wallet\",\n        brandingDetails: \"Project Identity\",\n        dreamDetails: \"Describe your Project\",\n        generateButtonLabel: \"Restart\",\n        optional: \"Recommended\",\n        communityChannels: \"Community Channels\",\n        createIdea: \"Create Project\",\n        confirm: \"Confirm\",\n        subdomainExisting: \"Please note you will be assigned a alternate subdomain as this subdomain is already taken\",\n        viewDream: \"View Project\",\n        socialAssistants: \"Community Tools\",\n        note: \"Note: You can always edit some parts of the project later\"\n    }\n};\nconst generateIdea = {\n    promptPlaceholders: [\n        \"Create a subscription-based fitness coaching platform\",\n        \"Launch a premium newsletter for tech professionals\",\n        \"Build a membership site for creative professionals\",\n        \"Create an exclusive learning community for developers\",\n        \"Launch a subscription box service for artisan products\"\n    ],\n    promptLoadingStates: [\n        {\n            text: \"Analyzing Market Opportunity\"\n        },\n        {\n            text: \"Designing Subscription Model\"\n        },\n        {\n            text: \"Creating Brand Identity\"\n        },\n        {\n            text: \"Building Member Experience\"\n        },\n        {\n            text: \"Setting Up Payment Systems\"\n        },\n        {\n            text: \"Preparing ISO Launch\"\n        }\n    ],\n    greeting: {\n        morning: \"Good morning, tell me about your project idea\",\n        afternoon: \"Good afternoon, tell me about your project idea\",\n        evening: \"Good evening, tell me about your project idea\"\n    },\n    whatsYourDream: \"Tell me about your project idea\",\n    generateError: \"Something went wrong. Please try again later.\",\n    poweredBy: \"powered by\",\n    readyTo: \"You are one step away from launching your subscription business\",\n    continue: \"Continue to Launch ISO\",\n    proceed: \"Proceed\",\n    orEnhance: \"Or keep refining the project\",\n    h1: \"Generate your Subscription Business with AI\"\n};\nconst ideas = {\n    ideaCard: {\n        raised: \"Revenue\",\n        holders: \"Subscribers\",\n        trade: \"Subscribe\",\n        marketcap: \"Total Value\",\n        uniswapLabel: \"SUB\",\n        openUniswap: \"View Subscription\",\n        dexLabel: \"ISO\",\n        openX: \"Open X\",\n        openWebsite: \"View Website\",\n        openDex: \"View ISO Details\",\n        openTelegram: \"Open Telegram\"\n    },\n    currentIdeas: \"Project Gallery\",\n    currentIdeasSubHeading: \"Discover successful subscription projects ready for community support\",\n    noIdeasHeading: \"No projects yet in this category\",\n    noIdeasSubHeading: \"Be the first to contribute! Share your innovative subscription projects and help grow this category.\",\n    registerIdea: \"Register Existing Project\",\n    launchNew: \"Launch New ISO\",\n    detailedView: \"Detailed View\",\n    compactView: \"Compact View\",\n    refreshData: \"Refresh Data\"\n};\nconst homePage = {\n    timeline: {\n        heading: \"How to Launch Your Project?\",\n        subHeading: \"Transform your innovative ideas into sustainable subscription-based projects with our comprehensive ISO Hub\"\n    },\n    subHeading: \"The ultimate platform for builders to launch, grow, and monetize their projects through Initial Subscription Offerings\",\n    h1: \"Build. Launch. Scale.\",\n    subHeading1: \"Every great project starts with a vision.\",\n    subHeading2: \"What if you could turn your next big idea into a thriving subscription business?\",\n    subHeading3: \"From concept to community-driven success. Launch your Initial Subscription Offering and build sustainable revenue streams with engaged subscribers.\",\n    heroWords: [\n        {\n            text: \"Build.\"\n        },\n        {\n            text: \"Launch.\"\n        },\n        {\n            text: \"Scale.\",\n            className: \"gradientText\"\n        }\n    ],\n    answeredByHuman: \"Supported by Expert Advisors\",\n    haveDream: \"Have an existing project?\",\n    register: \"Register Project\",\n    answeredByAi: \"Powered by AI Tools\",\n    fundButtonLabel: \"Register Existing Project\",\n    generateButtonLabel: \"Launch New ISO\",\n    trendingIdeas: \"Featured Projects\",\n    trendingIdeasSubHeading: \"Discover successful subscription-based projects built by our community\",\n    readyToTurn: \"Ready to Launch Your Subscription Business?\",\n    readyToTurnSubheading: \"Join DreamStartr ISO Hub today and transform your ideas into sustainable revenue streams. Launch your Initial Subscription Offering in minutes.\",\n    dreamGallery: \"Explore Projects\",\n    stats: {\n        heading: \"Platform Overview\",\n        description: \"See how builders are creating sustainable subscription businesses on our platform\",\n        dreamsLaunched: \"Projects launched on platform\",\n        agentsRunning: \"AI agents actively running\",\n        aiPowered: \"AI-powered development support\",\n        fundsRaised: \"Total funds raised for dreams\",\n        activeAgents: \"Running\"\n    },\n    developmentProcess: {\n        heading: \"Process\",\n        stepOneTitle: \"Dream Validation\",\n        stepOneInfo: \"From raw concept to refined vision\",\n        stepOnePoints: \"AI-enhanced ideation \\uD83D\\uDCA1 with expert community feedback \\uD83D\\uDCAC and direct market validation from target users \\uD83C\\uDFAF\",\n        stepTwoTitle: \"AI-Powered Development\",\n        stepTwoInfo: \"Where vision meets execution\",\n        stepTwoPoints: \"Rapid development with our AI-powered IDE \\uD83D\\uDCBB and real-time community-driven validation \\uD83D\\uDCAC\",\n        stepThreeTitle: \"Sustainable Launch\",\n        stepThreeInfo: \"Built for lasting impact\",\n        stepThreePoints: \"Customized token economics \\uD83D\\uDCB0, engaged early users \\uD83D\\uDC65 and proven growth strategies \\uD83D\\uDCC8\",\n        stepFourTitle: \"Accelerated Growth\",\n        stepFourInfo: \"Scaling with purpose\",\n        stepFourPoints: \"Access strategic partnerships \\uD83E\\uDD1D, expand market reach \\uD83C\\uDF10 and track measurable impact \\uD83D\\uDCCA\"\n    }\n};\nconst ideaPage = {\n    createdBy: \"Created by\",\n    believers: \"Believers\",\n    tokenAddress: \"Token address\",\n    fundingRaised: \"Raised\",\n    visitWebsite: \"View website\",\n    attachImage: \"Attach image\",\n    categories: \"Categories\",\n    connectWallet: \"Connect Wallet\",\n    tokenDetails: \"Token Details\",\n    transfers: \"Transfers\",\n    trade: \"Trade\",\n    uniswapLabel: \"UNI\",\n    conversations: \"Comments\",\n    post: \"Post\",\n    noComments: \"No comments yet. Be the first to start the conversation!\",\n    preview: \"Preview\",\n    buyFeed: \"Buy Feed\",\n    notActive: \"This idea is no longer active. Redirecting to homepage.\",\n    sellFeed: \"Sell Feed\",\n    owners: \"Believers\",\n    chart: \"Buy Trend\",\n    stakeholders: \"Believers\",\n    stakeholdersDesc: \"Our visionary believers making this dream possible\",\n    checkTransHeading: \"Check out ongoing trades on\",\n    transactionsTable: {\n        columnOne: \"Account\",\n        columnTwo: \"Value\",\n        columnThree: \"Time\",\n        columnFour: \"Transaction\"\n    },\n    ownersTable: {\n        columnOne: \"Account\",\n        columnTwo: \"Percentage\"\n    },\n    limitedTokensError: \"Limited tokens. There is only %amount% tokens left.\",\n    purchaseSuccess: \"Congratulations! You have purchased\",\n    purchaseError: \"Purchase could not be completed. Please try again!\",\n    postError: \"Unable to add new post\",\n    bondingCurveProgress: \"Progress\",\n    buyTokensFor: \"Buy tokens for\",\n    availableTokens: \"Available tokens\",\n    purchaseLabel: \"Get Quote\",\n    fetchQuoteError: \"Unable to fetch quote\",\n    approveTokenError: \"Unable to approve token\",\n    swapTokensSuccess: \"You have successfully swapped your tokens\",\n    transactionSwapError: \"Transaction failed. Please check your balance and try again\",\n    swapGeneralError: \"An error occurred while processing the swap\",\n    youWillReceive: \"You will receive\",\n    maxButton: \"Max\",\n    deleteComment: \"Delete comment\",\n    likeComment: \"Like comment\",\n    areYouSureToDelete: \"Are you sure you want to delete this comment?\",\n    editIdea: \"Edit Dream\",\n    codeAssist: \"Code Assist\",\n    boostIdea: \"Social Agents\",\n    bondingCurve: \"Bonding Curve Progress\",\n    buyPriceFetchError: \"Unable to fetch buying price\",\n    calculateTokensError: \"Unable to calculate tokens\",\n    priceQuoteError: \"Unable to get price quote\",\n    confirm: \"Confirm\",\n    for: \"for\",\n    confirmPurchase: \"Confirm purchase\",\n    buyTokens: \"Support Dream\",\n    buy: \"Buy\",\n    remaining: \" Remaining\",\n    swapTokens: \"Swap Tokens\",\n    swapTokensDesc: \"You can buy/sell tokens from the liquidity pool\",\n    openUniswap: \"Open Uniswap\",\n    dexLabel: \"DEX\",\n    openDex: \"Open DEX Screener\",\n    openTelegram: \"Open Telegram\",\n    openX: \"Open X\",\n    swapping: \"Swapping...\",\n    swap: \"Swap\",\n    buyTokensDesc: \"Join our community and shape the future - get tokens now \\uD83D\\uDE80\",\n    ensure: \"Ensure you have enough funds in your account\",\n    likelyFail: \"Note: This transaction will likely fail as you do not have enough funds\",\n    buyNow: \"Confirm\",\n    bondingCurveInfo: \"When the market cap reaches %goal% %currency%, all the liquidity from the bonding curve will be deposited into Uniswap, and the LP tokens will be burned. Progression increases as the price goes up.\"\n};\nconst profile = {\n    heading: \"Manage Dreams\",\n    startAllAgents: \"Start All Agents\",\n    subheading: \"Track your dream progress and manage your launch strategy\",\n    noIdeasHeading: \"You have not created any Dream\",\n    noIdeasSubHeading: \"Create your first Dream\",\n    registerIdea: \"Register Existing Dream\",\n    launchNew: \"Generate Dream\",\n    table: {\n        ideaName: \"Dream name\",\n        status: \"Status\",\n        ideaAddress: \"Address\",\n        ticker: \"Ticker\",\n        actions: \"Actions\",\n        view: \"View\",\n        dev: \"Code Assist\",\n        review: \"Social Agents\",\n        edit: \"Edit Dream\"\n    }\n};\nconst dreamathon = {\n    title: \"Dreamathon:\",\n    animatedText: \"Innovation at Scale\",\n    description: \"A large-scale hackathon initiative across 50 Indian cities, bringing together innovators, developers, and dreamers to build the future of Web3.\",\n    ongoingEvents: \"Ongoing Events\",\n    cities: \"50 Indian Cities\",\n    prizesTitle: \"Prizes & Recognition\",\n    prizesDescription: \"Win awards and get your projects featured\",\n    communityTitle: \"Community Support\",\n    communityDescription: \"Connect with mentors and fellow builders\",\n    viewMoreDetails: \"View More Details\"\n};\nconst enhancedDevelopmentProcess = {\n    sectionTitle: \"How ISO Hub Works\",\n    sectionDescription: \"Everything you need to launch and scale your subscription-based project\",\n    cards: {\n        blockchain: {\n            title: \"Subscription Management\",\n            description: \"Launch Initial Subscription Offerings with smart contract automation. Manage recurring payments, subscriber tiers, and revenue distribution seamlessly.\"\n        },\n        mediaPilot: {\n            title: \"Community Building\",\n            description: \"Build engaged subscriber communities with integrated social tools, exclusive content delivery, and member engagement analytics.\"\n        },\n        dreamathons: {\n            title: \"Builder Network\",\n            description: \"Connect with fellow builders, share resources, and collaborate on projects. Access mentorship and partnership opportunities within our ecosystem.\"\n        },\n        devAgent: {\n            title: \"Development Tools\",\n            description: \"Comprehensive development environment with AI-assisted coding, project templates, and deployment tools specifically designed for subscription businesses.\",\n            terminal: {\n                command: \"> create subscription service for fitness coaching\",\n                step1: \"✔ Setting up subscription tiers and pricing.\",\n                step2: \"✔ Generating member portal and content delivery.\",\n                step3: \"✔ Configuring payment processing and analytics.\",\n                success: \"Success! Your ISO is ready to launch.\"\n            }\n        }\n    }\n};\nconst manageIdea = {\n    heading: \" Social Assistants\",\n    subHeading: \"Meet your personal team of AI experts, each uniquely trained to help transform different aspects of your dream into reality.\",\n    twitterDescription: \"This agent can create a personalized persona based on your dream which will schedule tweets, handle replies, and more. Additionally, you can attach AI generated images for your posts.\",\n    tweets: \"Tweets Generated\",\n    posts: \"Posts Generated\",\n    postsSingular: \"Post Generated\",\n    tweetsSingular: \"Tweet Generated\",\n    engagement: \"Engagement Metrics\",\n    createSandboxError: \"Failed to create sandbox. Please try again.\",\n    linkedInDescription: \"This agent can create a personalized persona based on your dream which will schedule posts, handle replies, and more. Additionally, you can attach AI generated images for your posts.\",\n    manageAgent: \"Manage Persona\",\n    modifyCharacter: \"Target X Users\",\n    agentSettings: \"Engagement Settings\",\n    upcomingPosts: \"Upcoming Tweets\",\n    upcomingPostsLinkedin: \"Upcoming Posts\",\n    devAgent: \"Dev Agent\",\n    openAgent: \"Open Agent\",\n    createSandbox: \"Create Sandbox\",\n    openSandbox: \"Open Sandbox\",\n    addNewDev: \"Add New\",\n    active: \"Active\",\n    yourDreams: \"Your Dreams\",\n    partneredDreams: \"Partnered Dreams\",\n    owned: \"Owned\",\n    drafts: \"Drafts\",\n    draft: \"Draft\",\n    back: \"Back to Dream\",\n    devAgentDesc: \"The Dev Agent is a powerful tool that helps you build your dream. It provides you with a sandbox environment to develop your code and deploy your dream.\",\n    enableFollowing: \"Enable Following\",\n    enableFollowDescription: \"Allow agent to follow relevant accounts\",\n    enableActions: \"Enable Interactions\",\n    enableActionsDescription: \"Check and engage with tweets\",\n    enablePosts: \"Enable Tweeting\",\n    postInterval: \"Tweet Interval\",\n    postIntervalDesc: \"Duration between each tweet\",\n    interactionInterval: \"Interaction Interval\",\n    interactionIntervalDesc: \"Frequency of interactions(likes, replies)\",\n    followInterval: \"Follow Interval\",\n    followIntervalDesc: \"Frequency of following new accounts\",\n    enablePostsDescription: \"Allow agent to post personalized tweets\",\n    replies: \"Replies to Tweets\",\n    repliesSingular: \"Reply to Tweets\",\n    likes: \"Tweets Liked\",\n    likesSingular: \"Tweet Liked\",\n    retweets: \"Tweets Retweeted\",\n    retweetsSingular: \"Tweet Retweeted\",\n    followers: \"Accounts Followed\",\n    followersSingular: \"Account Followed\",\n    prev: \"Prev\",\n    next: \"Next\",\n    skip: \"Skip\",\n    agentRunning: \"Running since\",\n    twitterForm: {\n        username: \"Username\",\n        password: \"Password\",\n        email: \"Email\",\n        submit: \"Start Agent\",\n        connect: \"Connect Wallet\",\n        stopAgent: \"Pause Agent\",\n        viewCharacter: \"View Settings\",\n        updateCharacter: \"Update Character\",\n        hideCharacter: \"Hide Settings\"\n    },\n    character: {\n        exampleOne: \"Example 1\",\n        exampleTwo: \"Example 2\",\n        exampleUserLabel: \"User\",\n        exampleAgentLabel: \"Agent\",\n        styleAll: \"All\",\n        styleChat: \"Chat\",\n        stylePost: \"Post\",\n        professional: \"Professional\"\n    },\n    promptLoadingStates: [\n        {\n            text: \"Crafting Your Digital Identity\"\n        },\n        {\n            text: \"Preparing Your Agent\"\n        },\n        {\n            text: \"Processing Platform Data\"\n        },\n        {\n            text: \"Designing Engagement Plan\"\n        },\n        {\n            text: \"Optimizing Post Schedule\"\n        }\n    ],\n    devLoadingStates: [\n        {\n            text: \"Creating Sandbox\"\n        },\n        {\n            text: \"Deploying Agent\"\n        },\n        {\n            text: \"Building Dream\"\n        },\n        {\n            text: \"Testing Dream\"\n        },\n        {\n            text: \"Launching Dream\"\n        }\n    ]\n};\nconst lang = {\n    header,\n    profile,\n    createIdea,\n    manageIdea,\n    homePage,\n    ideas,\n    generateIdea,\n    footer,\n    notFound,\n    ideaPage,\n    dreamathon,\n    enhancedDevelopmentProcess\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (lang);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./common/lang.ts\n"));

/***/ })

});