import type { SVGProps } from "react";

export const PolygonIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={16}
    height={14}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <path
        fill="#6C00F6"
        d="m6.004 4.757-1.5-.844L0 6.443v5.044l4.503 2.522 4.504-2.522V3.643l2.499-1.4 2.498 1.4v2.8l-2.498 1.4L10.004 7v2.243l1.502.844 4.503-2.522V2.522L11.506 0 7.002 2.522v7.843l-2.499 1.4-2.499-1.4V7.557l2.5-1.4 1.5.843V4.757Z"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h16v14H0z" />
      </clipPath>
    </defs>
  </svg>
)
