declare module '@doogly/react' {
  import { FC } from 'react';

  export interface TransactionCallback {
    transactionId: string;
    requestId: string;
    fromChainId: string;
    toChainId: string;
    status: string;
  }

  export interface PostSwapHook {
    target: `0x${string}`;
    callData: `0x${string}`;
    callType?: number;
    tokenAddress?: `0x${string}`;
    inputPos?: number;
  }

  // Define the web3 configuration type
  export interface Web3Config {
    destinationChain: string;
    destinationAddress: string;
    destinationOutputTokenAddress: string;
    initialAmount?: string;
    initialChainId?: string;
    initialToken?: string;
  }

  export interface ModalStyles {
    backgroundColor?: string;
    buttonColor?: string;
    textColor?: string;
    headingColor?: string;
  }

  export interface DooglyProps {
    buttonText?: string;
    buttonClassName?: string;
    modalTitle?: string;
    apiUrl: string;
    config: Web3Config;
    modalStyles?: ModalStyles;
    webhookUrl?: string;
    webHookData?: string;
    postSwapHook?: PostSwapHook[];
    callback?: (transactionCallback: TransactionCallback) => void;
    privyAppId?: string;
  }

  export const DooglyButton: FC<DooglyProps>;

  export function createCustomProvider(url: string, chainId: number): any;
  export function createBrowserProvider(ethereum: any): any;
}
