// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "./IdeaFactoryTypes.sol";

contract IdeaFactoryStorage is IdeaFactoryTypes {
    uint24 internal constant POOL_FEE = 3000;
    uint256 internal constant IDEACOIN_FUNDING_GOAL = 42001829178114400000 wei;
    uint256 public constant DECIMALS = 1e18;
    uint256 public constant MAX_SUPPLY = ********** * DECIMALS;
    uint256 public constant INIT_SUPPLY = MAX_SUPPLY / 5;
    uint256 public constant MIGRATION_DELAY = 24 hours;
    uint256 internal IDEATOKEN_CREATION_FEE = 0.01 ether;

    // Uniswap addresses - polygon mainnet
    address internal immutable UNISWAP_V3_FACTORY_ADDRESS = ******************************************;
    address internal immutable UNISWAP_V3_POSITION_MANAGER = ******************************************;
    address internal immutable WETH9 = ******************************************;

    // Sepolia addresses - mainnet
    // address internal immutable UNISWAP_V3_FACTORY_ADDRESS = ******************************************;
    // address internal immutable UNISWAP_V3_POSITION_MANAGER = ******************************************;
    // address internal immutable WETH9 = ******************************************;

    address[] public ideaTokenAddresses;
    mapping(address => IdeaTokenBase) public tokenBase;
    mapping(address => IdeaTokenData) public tokenData;
    mapping(address => address) public tokenToPool;
    mapping(address => MigrationState) public migrationStates;
    mapping(address => GovernanceInfo) public tokenGovernance;
}