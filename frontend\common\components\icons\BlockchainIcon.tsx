import type { SVGProps } from "react";

export const BlockchainIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <path
        fill="#78EB7B"
        d="M4.167 5A.833.833 0 0 1 5 4.167h10a.834.834 0 0 1 .834.833v10a.834.834 0 0 1-.834.833H5A.834.834 0 0 1 4.167 15V5Z"
      />
      <path
        fill="#C9F7CA"
        d="M15 4.167H5A.833.833 0 0 0 4.167 5v10c0 .214.084.419.232.572L15.573 4.4A.826.826 0 0 0 15 4.167Z"
      />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M4.167 5A.833.833 0 0 1 5 4.167h10a.834.834 0 0 1 .834.833v10a.834.834 0 0 1-.834.833H5A.834.834 0 0 1 4.167 15V5ZM8.958 6.25V7.5M10.626 6.25V7.5M8.958 12.5v1.25M10.626 12.5v1.25"
      />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M12.289 8.75a1.25 1.25 0 0 1-1.25 1.25H8.122V7.5h2.917a1.25 1.25 0 0 1 1.25 1.25ZM12.289 11.25a1.25 1.25 0 0 1-1.25 1.25H8.122V10h2.917a1.25 1.25 0 0 1 1.25 1.25ZM10 .833v3.334M6.667.833v3.334M13.333.833v3.334M.833 10h3.334M.833 13.333h3.334M.833 6.667h3.334M10 19.167v-3.334M13.333 19.167v-3.334M6.667 19.167v-3.334M19.167 10h-3.334M19.167 6.667h-3.334M19.167 13.333h-3.334"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h20v20H0z" />
      </clipPath>
    </defs>
  </svg>
)
