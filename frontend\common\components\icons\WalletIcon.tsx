import type { SVGProps } from "react";

export const WalletIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <path
      fill="#6673FF"
      d="M23 4.156v10.52a2.88 2.88 0 0 1-2.869 2.87h-2.87V8.938a3.879 3.879 0 0 0-2.783-3.568L1.776 2.195a2.852 2.852 0 0 1 2.095-.909h16.26a2.87 2.87 0 0 1 2.87 2.87Z"
    />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M9.608 4.156h9.565"
    />
    <path
      fill="#C4C2FF"
      d="M12.956 15.633a1.913 1.913 0 1 0 0-3.826 1.913 1.913 0 0 0 0 3.826Z"
    />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M17.26 8.938h1.914"
    />
    <path
      fill="#C4C2FF"
      d="M12.956 13.348a1.913 1.913 0 0 1 1.75 1.141 1.912 1.912 0 1 0-3.5 0 1.913 1.913 0 0 1 1.75-1.14Z"
    />
    <path
      fill="#C4C2FF"
      d="M17.26 8.938v11.478a2.181 2.181 0 0 1-2.793 2.21L3.793 20.11A3.81 3.81 0 0 1 1 16.59V4.156c0-.726.277-1.424.775-1.951v-.01L14.477 5.37a3.879 3.879 0 0 1 2.783 3.568Z"
    />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M12.956 15.633a1.913 1.913 0 1 0 0-3.826 1.913 1.913 0 0 0 0 3.826Z"
    />
  </svg>
)
