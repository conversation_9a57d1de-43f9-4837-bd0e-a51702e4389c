{"v": "5.12.1", "fr": 60, "ip": 0, "op": 156, "w": 430, "h": 430, "nm": "wired-flat-2233-elf", "ddd": 0, "assets": [{"id": "comp_1", "nm": "hover-pinch", "fr": 60, "layers": [{"ddd": 0, "ind": 4, "ty": 4, "nm": "Cap 2", "parent": 12, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [262.417, 139.745, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [262.417, 139.745, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [-4.865, -4.945]], "o": [[6.137, 3.346], [0.517, 0.525]], "v": [[-18.327, -28.303], [-1.729, -15.78]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [-3.261, -3.992]], "o": [[6.141, 3.347], [0.345, 0.423]], "v": [[-12.176, -24.64], [-1.032, -15.362]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[0, 0], [-6.729, -6.258]], "o": [[11.831, -0.111], [0.713, 0.663]], "v": [[-29.163, -27.211], [0.81, -14.624]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 91, "s": [{"i": [[0, 0], [-3.261, -3.992]], "o": [[6.141, 3.347], [0.345, 0.423]], "v": [[-12.176, -24.64], [-1.032, -15.362]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 113, "s": [{"i": [[0, 0], [-5.694, -5.53]], "o": [[8.67, 1.81], [0.603, 0.586]], "v": [[-23.143, -27.817], [-0.596, -15.262]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [{"i": [[0, 0], [-4.466, -4.709]], "o": [[6.141, 3.347], [0.473, 0.499]], "v": [[-16.789, -27.387], [-1.549, -15.67]], "c": false}]}, {"t": 156, "s": [{"i": [[0, 0], [-4.867, -4.949]], "o": [[6.141, 3.347], [0.516, 0.524]], "v": [[-18.327, -28.303], [-1.721, -15.772]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.173, 0.647, 0.553, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [283.805, 155.789], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Cap fill 2", "parent": 12, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [262.417, 139.745, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [262.417, 139.745, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[-0.066, -9.05], [9.05, -0.066], [0.066, 9.05], [-9.05, 0.066]], "o": [[0.066, 9.05], [-9.05, 0.066], [-0.066, -9.05], [9.05, -0.066]], "v": [[15.673, -0.354], [-0.593, 16.151], [-17.099, -0.115], [-0.832, -16.621]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[-3.237, -7.962], [7.962, -3.237], [3.237, 7.962], [-7.962, 3.237]], "o": [[3.237, 7.962], [-7.962, 3.237], [-3.237, -7.962], [7.962, -3.237]], "v": [[-5.788, -10.752], [-14.343, 9.524], [-34.62, 0.968], [-26.064, -19.308]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[-5.504, -7.184], [7.184, -5.504], [5.504, 7.184], [-7.184, 5.504]], "o": [[5.504, 7.184], [-7.184, 5.504], [-5.504, -7.184], [7.184, -5.504]], "v": [[-18.461, -30.304], [-21.502, -7.33], [-44.475, -10.371], [-41.435, -33.344]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [{"i": [[1.282, -9.431], [9.431, 1.282], [-1.282, 9.431], [-9.431, -1.282]], "o": [[-1.282, 9.431], [-9.431, -1.282], [1.282, -9.431], [9.431, 1.282]], "v": [[15.747, 18.497], [-3.652, 33.252], [-18.407, 13.854], [0.992, -0.901]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[6.636, -6.154], [6.154, 6.636], [-6.636, 6.154], [-6.154, -6.636]], "o": [[-6.636, 6.154], [-6.154, -6.636], [6.636, -6.154], [6.154, 6.636]], "v": [[26.823, 60.862], [3.665, 59.989], [4.538, 36.831], [27.696, 37.704]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [{"i": [[1.444, -8.284], [8.284, 1.444], [-1.444, 8.284], [-8.284, -1.444]], "o": [[-1.444, 8.284], [-8.284, -1.444], [1.444, -8.284], [8.284, 1.444]], "v": [[13.506, 24.078], [-4.107, 36.463], [-16.492, 18.85], [1.121, 6.465]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 91, "s": [{"i": [[-5.504, -7.184], [7.184, -5.504], [5.504, 7.184], [-7.184, 5.504]], "o": [[5.504, 7.184], [-7.184, 5.504], [-5.504, -7.184], [7.184, -5.504]], "v": [[-18.461, -30.304], [-21.502, -7.33], [-44.475, -10.371], [-41.435, -33.344]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [{"i": [[-2.479, -8.347], [8.347, -2.479], [2.479, 8.347], [-8.347, 2.479]], "o": [[2.479, 8.347], [-8.347, 2.479], [-2.479, -8.347], [8.347, -2.479]], "v": [[4.542, -5.895], [-6.083, 13.707], [-25.686, 3.082], [-15.061, -16.52]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 113, "s": [{"i": [[2.913, -7.763], [7.763, 2.913], [-2.913, 7.763], [-7.763, -2.913]], "o": [[-2.913, 7.763], [-7.763, -2.913], [2.913, -7.763], [7.763, 2.913]], "v": [[20.629, 26.853], [1.299, 35.635], [-7.482, 16.305], [11.847, 7.523]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [{"i": [[-1.426, -8.583], [8.583, -1.426], [1.426, 8.583], [-8.583, 1.426]], "o": [[1.426, 8.583], [-8.583, 1.426], [-1.426, -8.583], [8.583, -1.426]], "v": [[7.14, -7.842], [-5.82, 10.281], [-23.943, -2.679], [-10.983, -20.802]], "c": true}]}, {"t": 156, "s": [{"i": [[-0.066, -9.05], [9.05, -0.066], [0.066, 9.05], [-9.05, 0.066]], "o": [[0.066, 9.05], [-9.05, 0.066], [-0.066, -9.05], [9.05, -0.066]], "v": [[15.673, -0.354], [-0.593, 16.151], [-17.099, -0.115], [-0.832, -16.621]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.922, 0.902, 0.937, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [316.267, 79.707], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "mask 4", "parent": 12, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [280.417, 141.745, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [262.417, 139.745, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[66.914, -55.889], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-29.646, -39.385], [-10.335, -55.888], [8.975, -39.385], [28.286, -55.888], [47.6, -39.385]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[65.317, -56.278], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-31.244, -39.774], [-11.933, -56.277], [7.377, -39.774], [26.689, -56.277], [46.003, -39.774]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[62.697, -55.67], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-33.863, -39.166], [-14.553, -55.669], [4.758, -39.166], [24.069, -55.669], [43.383, -39.166]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 91, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[67.089, -65.51], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-29.471, -49.006], [-10.16, -65.509], [9.15, -49.006], [28.461, -65.509], [47.775, -49.006]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 113, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[68.137, -56.335], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-28.423, -39.832], [-9.112, -56.335], [10.198, -39.832], [29.509, -56.335], [48.823, -39.832]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[67.435, -59.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-29.125, -43.107], [-9.814, -59.61], [9.496, -43.107], [28.807, -59.61], [48.121, -43.107]], "c": true}]}, {"t": 156, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[71.435, -72.111], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-25.125, -55.607], [-5.814, -72.11], [13.496, -55.607], [32.807, -72.11], [52.121, -55.607]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.92156869173, 0.901960849762, 0.937254965305, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.92156869173, 0.901960849762, 0.937254965305, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [249.989, 197.684], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "shadow 4", "parent": 12, "tt": 2, "tp": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [262.417, 139.745, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [262.417, 139.745, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.935, -18.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-38.625, -2.107], [-19.314, -18.61], [-0.004, -2.107], [19.307, -18.61], [38.621, -2.107]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.935, -18.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-38.625, -2.107], [-19.314, -18.61], [-0.004, -2.107], [19.307, -18.61], [38.621, -2.107]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.935, -18.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-38.625, -2.107], [-19.314, -18.61], [-0.004, -2.107], [19.307, -18.61], [38.621, -2.107]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 91, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.935, -18.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-38.625, -2.107], [-19.314, -18.61], [-0.004, -2.107], [19.307, -18.61], [38.621, -2.107]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 113, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.935, -18.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-38.625, -2.107], [-19.314, -18.61], [-0.004, -2.107], [19.307, -18.61], [38.621, -2.107]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.935, -18.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-38.625, -2.107], [-19.314, -18.61], [-0.004, -2.107], [19.307, -18.61], [38.621, -2.107]], "c": true}]}, {"t": 156, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.935, -18.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-38.625, -2.107], [-19.314, -18.61], [-0.004, -2.107], [19.307, -18.61], [38.621, -2.107]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.922, 0.902, 0.937, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [0.922, 0.902, 0.937, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [249.989, 197.684], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Cap fill 3", "parent": 12, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [262.417, 139.745, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [262.417, 139.745, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.935, -18.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-38.625, -2.107], [-19.314, -18.61], [-0.004, -2.107], [19.307, -18.61], [38.621, -2.107]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.935, -18.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-38.625, -2.107], [-19.314, -18.61], [-0.004, -2.107], [19.307, -18.61], [38.621, -2.107]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.935, -18.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-38.625, -2.107], [-19.314, -18.61], [-0.004, -2.107], [19.307, -18.61], [38.621, -2.107]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 91, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.935, -18.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-38.625, -2.107], [-19.314, -18.61], [-0.004, -2.107], [19.307, -18.61], [38.621, -2.107]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 113, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.935, -18.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-38.625, -2.107], [-19.314, -18.61], [-0.004, -2.107], [19.307, -18.61], [38.621, -2.107]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.935, -18.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-38.625, -2.107], [-19.314, -18.61], [-0.004, -2.107], [19.307, -18.61], [38.621, -2.107]], "c": true}]}, {"t": 156, "s": [{"i": [[0, 0], [0, 0], [0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.002, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.935, -18.611], [57.935, 18.607], [57.931, 18.611], [-57.931, 18.611], [-57.935, 18.609], [-57.935, -18.611], [-38.625, -2.107], [-19.314, -18.61], [-0.004, -2.107], [19.307, -18.61], [38.621, -2.107]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.922, 0.902, 0.937, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [0.922, 0.902, 0.937, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [249.989, 197.684], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "mask 3", "parent": 12, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [276.917, 139.745, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [262.417, 139.745, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0, 7.23], [-12.64, 7.17], [0.144, 2.073], [2.7, 0.43], [0.552, 0], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [0, -15.57], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-34.7, 0], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.157, -21.201], [51.318, -57.521], [54.19, -62.721], [49.298, -67.841], [47.997, -67.851], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [{"i": [[0, 0], [0.595, 7.203], [-4.284, 14.484], [0.144, 2.073], [2.7, 0.43], [0.468, -0.28], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-1.495, -17.885], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-24.231, 14.148], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.41, -21], [32.286, -72.542], [33.419, -76.153], [27.059, -78.322], [25.942, -77.343], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 22, "s": [{"i": [[0, 0], [0.648, 7.201], [-3.533, 15.141], [0.144, 2.073], [2.7, 0.43], [0.46, -0.305], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-1.629, -18.093], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-23.29, 15.419], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.432, -20.982], [29.206, -73.987], [31.553, -77.36], [25.061, -79.264], [23.761, -79.274], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [{"i": [[0, 0], [0.201, 7.449], [-2.761, 13.167], [0.144, 2.073], [2.7, 0.43], [0.452, -0.252], [19.888, -27.603], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-1.041, -17.956], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-22.987, 12.569], [-11.709, 16.467], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.361, -1.328], [31.073, -21.29], [30.424, -70.736], [34.825, -73.576], [28.631, -75.476], [26.274, -77.254], [-35.857, -23.653], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.435, 7.265], [35.361, -1.25], [34.906, -1.843], [15.023, -17.972]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [{"i": [[0, 0], [-0.357, 7.759], [-1.797, 10.704], [0.144, 2.073], [2.7, 0.43], [0.44, -0.187], [21.658, -29.055], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-0.307, -17.786], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-22.61, 9.013], [-11.857, 16.351], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.528, -1.436], [30.782, -23.708], [31.896, -66.878], [38.906, -68.853], [33.086, -70.749], [29.542, -73.748], [-35.888, -23.082], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.494, 7.236], [35.528, -1.262], [34.505, -2.582], [15.229, -18.149]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [{"i": [[0, 0], [-0.337, 9.949], [0.13, 5.779], [0.144, 2.073], [2.7, 0.43], [0.418, -0.056], [25.197, -31.957], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [1.664, -29.646], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-21.854, 1.901], [-12.154, 16.119], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.862, -1.654], [32.353, -24.442], [38.588, -57.337], [47.07, -59.409], [41.995, -61.295], [38.433, -64.836], [-35.949, -21.94], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.611, 7.176], [35.861, -1.286], [33.704, -4.061], [15.642, -18.503]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [{"i": [[0, 0], [-2.87, 9.154], [2.54, -0.379], [0.144, 2.073], [2.7, 0.43], [0.39, 0.108], [29.621, -35.586], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [2.996, -17.02], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-20.909, -6.99], [-12.524, 15.829], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.279, -1.925], [35.468, -23.405], [48.392, -42.968], [57.275, -47.604], [53.132, -49.478], [50.597, -53.001], [-36.025, -20.513], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.759, 7.101], [36.278, -1.316], [32.702, -5.909], [16.157, -18.945]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [{"i": [[0, 0], [-3.788, 7.799], [0.208, 0.166], [0.144, 2.073], [2.7, 0.43], [0.379, 0.174], [29.189, -34.773], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [7.733, -17.044], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-23.146, -9.715], [-12.674, 15.712], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.448, -2.035], [37.458, -24.857], [53.289, -38.272], [61.399, -42.833], [57.632, -44.703], [55.094, -47.862], [-36.056, -19.936], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.818, 7.071], [36.447, -1.328], [32.297, -6.656], [16.366, -19.124]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [{"i": [[0, 0], [-4.707, 6.443], [-2.124, 0.71], [0.144, 2.073], [2.7, 0.43], [0.368, 0.24], [28.756, -33.961], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [12.469, -17.068], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-20.146, -14.174], [-12.824, 15.595], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.617, -2.145], [38.974, -23.557], [60.742, -31.848], [65.523, -38.062], [62.133, -39.928], [59.59, -42.722], [-36.087, -19.359], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.878, 7.041], [36.615, -1.34], [31.892, -7.403], [16.574, -19.302]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [{"i": [[0, 0], [-5.788, 4.449], [-6.161, 1.652], [0.144, 2.073], [2.7, 0.43], [0.348, 0.354], [28.007, -32.554], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [15.104, -11.682], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-19.485, -20.394], [-13.083, 15.392], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.909, -2.335], [43.345, -22.526], [71.433, -22.223], [72.662, -29.803], [69.924, -31.661], [67.048, -34.833], [-36.14, -18.36], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.981, 6.989], [36.907, -1.361], [31.191, -8.695], [16.935, -19.612]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[0, 0], [-6.601, 2.95], [-9.194, 2.361], [0.144, 2.073], [2.7, 0.43], [0.333, 0.44], [27.444, -31.497], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [17.085, -7.636], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-18.989, -25.067], [-13.278, 15.239], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[37.128, -2.478], [46.629, -21.751], [79.466, -14.99], [78.026, -23.597], [75.778, -25.448], [71.917, -29.938], [-36.18, -17.61], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [42.058, 6.95], [37.126, -1.376], [30.664, -9.667], [17.206, -19.844]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[0, 0], [-6.065, 3.215], [-8.75, 1.915], [0.144, 2.073], [2.7, 0.43], [0.343, 0.386], [26.786, -31.126], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [15.565, -9.156], [2.159, -0.88], [-0.174, -2.518], [-0.294, -0.004], [-19.744, -22.651], [-13.154, 15.336], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.989, -2.387], [45.107, -22.216], [75.762, -18.478], [75.603, -25.986], [73.116, -28.29], [68.993, -32.383], [-36.155, -18.087], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [42.009, 6.975], [36.987, -1.367], [30.999, -9.05], [17.034, -19.696]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [{"i": [[0, 0], [-4.528, 3.975], [-6.94, 4.096], [0.144, 2.073], [2.7, 0.43], [0.37, 0.229], [24.896, -30.061], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [11.684, -10.395], [2.929, -0.243], [-0.174, -2.518], [-0.294, -0.004], [-21.913, -15.714], [-12.799, 15.614], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.589, -2.126], [40.738, -23.55], [65.209, -30.293], [68.646, -32.843], [65.471, -36.45], [61.358, -39.522], [-36.082, -19.456], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.868, 7.046], [36.587, -1.338], [31.96, -7.277], [16.539, -19.272]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [{"i": [[0, 0], [-3.21, 4.627], [-5.389, 5.966], [0.144, 2.073], [2.7, 0.43], [0.392, 0.094], [23.276, -29.148], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [8.356, -11.458], [3.588, 0.304], [-0.174, -2.518], [-0.294, -0.004], [-23.772, -9.767], [-12.494, 15.853], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.245, -1.903], [36.992, -24.693], [56.073, -38.47], [62.682, -38.721], [58.918, -43.444], [53.564, -45.224], [-36.019, -20.63], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.747, 7.108], [36.244, -1.313], [32.784, -5.757], [16.115, -18.909]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [{"i": [[0, 0], [-2.552, 4.953], [-4.614, 6.9], [0.144, 2.073], [2.7, 0.43], [0.404, 0.027], [22.465, -28.692], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [6.693, -11.989], [3.918, 0.577], [-0.174, -2.518], [-0.294, -0.004], [-24.702, -6.794], [-12.342, 15.972], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.074, -1.791], [35.12, -25.265], [51.454, -41.37], [59.7, -41.66], [55.642, -46.94], [49.799, -48.428], [-35.987, -21.216], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.686, 7.138], [36.073, -1.301], [33.196, -4.998], [15.903, -18.727]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [{"i": [[0, 0], [-1.681, 5.384], [-7.051, 8.364], [0.144, 2.073], [2.7, 0.43], [0.419, -0.062], [21.395, -28.088], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [3.384, -12.783], [0.766, -4.603], [-0.174, -2.518], [-0.294, -0.004], [-22.653, -1.944], [-12.14, 16.13], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.847, -1.644], [33.256, -26.776], [45.236, -49.88], [52.067, -52.935], [48.633, -57.982], [42.056, -56.341], [-35.946, -21.992], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.606, 7.179], [35.846, -1.285], [33.74, -3.993], [15.623, -18.487]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 91, "s": [{"i": [[0, 0], [0.698, 6.56], [-0.938, 10.544], [0.144, 2.073], [2.7, 0.43], [0.46, -0.305], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-1.647, -15.483], [-7.84, -18.747], [-0.174, -2.518], [-0.294, -0.004], [-17.06, 11.302], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.354, -22.694], [28.256, -73.118], [31.223, -83.725], [29.495, -88.135], [20.911, -77.949], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [{"i": [[0, 0], [-1.785, 6.198], [-5.558, 5.97], [1.15, 1.864], [2.7, 0.43], [0.49, -0.06], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [9.849, -5.07]], "o": [[-3.635, -9.422], [3.267, -13.336], [-1.385, -8.765], [-1.088, -2.028], [-0.294, -0.004], [-29.218, 6.438], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.601, -1.264], [32.339, -27.374], [45.439, -56.116], [51.345, -62.846], [48.979, -68.546], [40.502, -63.985], [-36.318, -21.825], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [12.191, -18.486]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 113, "s": [{"i": [[0, 0], [-4.267, 5.836], [-10.178, 1.396], [2.157, 1.656], [2.7, 0.43], [0.521, 0.184], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [12.907, -15.667]], "o": [[-4.03, -12.914], [8.181, -11.188], [5.07, 1.217], [-2.002, -1.537], [-0.294, -0.004], [-32.542, -11.522], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.975, -1.286], [37.486, -28.166], [59.552, -41.479], [71.468, -41.967], [68.462, -48.957], [60.031, -51.038], [-36.804, -19.539], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [9.99, -11.517]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [{"i": [[0, 0], [-0.441, 2.98], [-8.567, 6.565], [2.157, 1.656], [2.7, 0.43], [0.546, -0.083], [16.009, -21.898], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [12.907, -15.667]], "o": [[-5.99, -14.628], [1.999, -13.498], [5.07, 1.217], [-2.002, -1.537], [-0.294, -0.004], [-45.296, 6.874], [-9.96, 13.625], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[34.975, -1.036], [30.986, -30.916], [45.802, -60.229], [51.218, -68.217], [46.962, -71.207], [42.031, -70.038], [-37.304, -22.039], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [10.49, -16.267]], "c": false}]}, {"t": 156, "s": [{"i": [[0, 0], [0, 7.23], [-12.64, 7.17], [0.144, 2.073], [2.7, 0.43], [0.552, 0], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [0, -15.57], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-34.7, 0], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.157, -21.201], [51.318, -57.521], [54.19, -62.721], [49.298, -67.841], [47.997, -67.851], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.172549024224, 0.647058844566, 0.552941203117, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [250.515, 145.164], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "shadow 3", "parent": 12, "tt": 2, "tp": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [262.417, 139.745, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [262.417, 139.745, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0, 7.23], [-12.64, 7.17], [0.144, 2.073], [2.7, 0.43], [0.552, 0], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [0, -15.57], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-34.7, 0], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.157, -21.201], [51.318, -57.521], [54.19, -62.721], [49.298, -67.841], [47.997, -67.851], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [{"i": [[0, 0], [0.595, 7.203], [-4.284, 14.484], [0.144, 2.073], [2.7, 0.43], [0.468, -0.28], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-1.495, -17.885], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-24.231, 14.148], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.41, -21], [32.286, -72.542], [33.419, -76.153], [27.059, -78.322], [25.942, -77.343], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 22, "s": [{"i": [[0, 0], [0.648, 7.201], [-3.533, 15.141], [0.144, 2.073], [2.7, 0.43], [0.46, -0.305], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-1.629, -18.093], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-23.29, 15.419], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.432, -20.982], [29.206, -73.987], [31.553, -77.36], [25.061, -79.264], [23.761, -79.274], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [{"i": [[0, 0], [0.201, 7.449], [-2.761, 13.167], [0.144, 2.073], [2.7, 0.43], [0.452, -0.252], [19.888, -27.603], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-1.041, -17.956], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-22.987, 12.569], [-11.709, 16.467], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.361, -1.328], [31.073, -21.29], [30.424, -70.736], [34.825, -73.576], [28.631, -75.476], [26.274, -77.254], [-35.857, -23.653], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.435, 7.265], [35.361, -1.25], [34.906, -1.843], [15.023, -17.972]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [{"i": [[0, 0], [-0.357, 7.759], [-1.797, 10.704], [0.144, 2.073], [2.7, 0.43], [0.44, -0.187], [21.658, -29.055], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-0.307, -17.786], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-22.61, 9.013], [-11.857, 16.351], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.528, -1.436], [30.782, -23.708], [31.896, -66.878], [38.906, -68.853], [33.086, -70.749], [29.542, -73.748], [-35.888, -23.082], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.494, 7.236], [35.528, -1.262], [34.505, -2.582], [15.229, -18.149]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [{"i": [[0, 0], [-0.337, 9.949], [0.13, 5.779], [0.144, 2.073], [2.7, 0.43], [0.418, -0.056], [25.197, -31.957], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [1.664, -29.646], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-21.854, 1.901], [-12.154, 16.119], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.862, -1.654], [32.353, -24.442], [38.588, -57.337], [47.07, -59.409], [41.995, -61.295], [38.433, -64.836], [-35.949, -21.94], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.611, 7.176], [35.861, -1.286], [33.704, -4.061], [15.642, -18.503]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [{"i": [[0, 0], [-2.87, 9.154], [2.54, -0.379], [0.144, 2.073], [2.7, 0.43], [0.39, 0.108], [29.621, -35.586], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [2.996, -17.02], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-20.909, -6.99], [-12.524, 15.829], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.279, -1.925], [35.468, -23.405], [48.392, -42.968], [57.275, -47.604], [53.132, -49.478], [50.597, -53.001], [-36.025, -20.513], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.759, 7.101], [36.278, -1.316], [32.702, -5.909], [16.157, -18.945]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [{"i": [[0, 0], [-3.788, 7.799], [0.208, 0.166], [0.144, 2.073], [2.7, 0.43], [0.379, 0.174], [29.189, -34.773], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [7.733, -17.044], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-23.146, -9.715], [-12.674, 15.712], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.448, -2.035], [37.458, -24.857], [53.289, -38.272], [61.399, -42.833], [57.632, -44.703], [55.094, -47.862], [-36.056, -19.936], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.818, 7.071], [36.447, -1.328], [32.297, -6.656], [16.366, -19.124]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [{"i": [[0, 0], [-4.707, 6.443], [-2.124, 0.71], [0.144, 2.073], [2.7, 0.43], [0.368, 0.24], [28.756, -33.961], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [12.469, -17.068], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-20.146, -14.174], [-12.824, 15.595], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.617, -2.145], [38.974, -23.557], [60.742, -31.848], [65.523, -38.062], [62.133, -39.928], [59.59, -42.722], [-36.087, -19.359], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.878, 7.041], [36.615, -1.34], [31.892, -7.403], [16.574, -19.302]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [{"i": [[0, 0], [-5.788, 4.449], [-6.161, 1.652], [0.144, 2.073], [2.7, 0.43], [0.348, 0.354], [28.007, -32.554], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [15.104, -11.682], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-19.485, -20.394], [-13.083, 15.392], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.909, -2.335], [43.345, -22.526], [71.433, -22.223], [72.662, -29.803], [69.924, -31.661], [67.048, -34.833], [-36.14, -18.36], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.981, 6.989], [36.907, -1.361], [31.191, -8.695], [16.935, -19.612]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[0, 0], [-6.601, 2.95], [-9.194, 2.361], [0.144, 2.073], [2.7, 0.43], [0.333, 0.44], [27.444, -31.497], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [17.085, -7.636], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-18.989, -25.067], [-13.278, 15.239], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[37.128, -2.478], [46.629, -21.751], [79.466, -14.99], [78.026, -23.597], [75.778, -25.448], [71.917, -29.938], [-36.18, -17.61], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [42.058, 6.95], [37.126, -1.376], [30.664, -9.667], [17.206, -19.844]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[0, 0], [-6.065, 3.215], [-8.75, 1.915], [0.144, 2.073], [2.7, 0.43], [0.343, 0.386], [26.786, -31.126], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [15.565, -9.156], [2.159, -0.88], [-0.174, -2.518], [-0.294, -0.004], [-19.744, -22.651], [-13.154, 15.336], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.989, -2.387], [45.107, -22.216], [75.762, -18.478], [75.603, -25.986], [73.116, -28.29], [68.993, -32.383], [-36.155, -18.087], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [42.009, 6.975], [36.987, -1.367], [30.999, -9.05], [17.034, -19.696]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [{"i": [[0, 0], [-4.528, 3.975], [-6.94, 4.096], [0.144, 2.073], [2.7, 0.43], [0.37, 0.229], [24.896, -30.061], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [11.684, -10.395], [2.929, -0.243], [-0.174, -2.518], [-0.294, -0.004], [-21.913, -15.714], [-12.799, 15.614], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.589, -2.126], [40.738, -23.55], [65.209, -30.293], [68.646, -32.843], [65.471, -36.45], [61.358, -39.522], [-36.082, -19.456], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.868, 7.046], [36.587, -1.338], [31.96, -7.277], [16.539, -19.272]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [{"i": [[0, 0], [-3.21, 4.627], [-5.389, 5.966], [0.144, 2.073], [2.7, 0.43], [0.392, 0.094], [23.276, -29.148], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [8.356, -11.458], [3.588, 0.304], [-0.174, -2.518], [-0.294, -0.004], [-23.772, -9.767], [-12.494, 15.853], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.245, -1.903], [36.992, -24.693], [56.073, -38.47], [62.682, -38.721], [58.918, -43.444], [53.564, -45.224], [-36.019, -20.63], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.747, 7.108], [36.244, -1.313], [32.784, -5.757], [16.115, -18.909]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [{"i": [[0, 0], [-2.552, 4.953], [-4.614, 6.9], [0.144, 2.073], [2.7, 0.43], [0.404, 0.027], [22.465, -28.692], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [6.693, -11.989], [3.918, 0.577], [-0.174, -2.518], [-0.294, -0.004], [-24.702, -6.794], [-12.342, 15.972], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.074, -1.791], [35.12, -25.265], [51.454, -41.37], [59.7, -41.66], [55.642, -46.94], [49.799, -48.428], [-35.987, -21.216], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.686, 7.138], [36.073, -1.301], [33.196, -4.998], [15.903, -18.727]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [{"i": [[0, 0], [-1.681, 5.384], [-7.051, 8.364], [0.144, 2.073], [2.7, 0.43], [0.419, -0.062], [21.395, -28.088], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [3.384, -12.783], [0.766, -4.603], [-0.174, -2.518], [-0.294, -0.004], [-22.653, -1.944], [-12.14, 16.13], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.847, -1.644], [33.256, -26.776], [45.236, -49.88], [52.067, -52.935], [48.633, -57.982], [42.056, -56.341], [-35.946, -21.992], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.606, 7.179], [35.846, -1.285], [33.74, -3.993], [15.623, -18.487]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 91, "s": [{"i": [[0, 0], [0.698, 6.56], [-0.938, 10.544], [0.144, 2.073], [2.7, 0.43], [0.46, -0.305], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-1.647, -15.483], [-7.84, -18.747], [-0.174, -2.518], [-0.294, -0.004], [-17.06, 11.302], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.354, -22.694], [28.256, -73.118], [31.223, -83.725], [29.495, -88.135], [20.911, -77.949], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [{"i": [[0, 0], [-1.785, 6.198], [-5.558, 5.97], [1.15, 1.864], [2.7, 0.43], [0.49, -0.06], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [9.849, -5.07]], "o": [[-3.635, -9.422], [3.267, -13.336], [-1.385, -8.765], [-1.088, -2.028], [-0.294, -0.004], [-29.218, 6.438], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.601, -1.264], [32.339, -27.374], [45.439, -56.116], [51.345, -62.846], [48.979, -68.546], [40.502, -63.985], [-36.318, -21.825], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [12.191, -18.486]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 113, "s": [{"i": [[0, 0], [-4.267, 5.836], [-10.178, 1.396], [2.157, 1.656], [2.7, 0.43], [0.521, 0.184], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [12.907, -15.667]], "o": [[-4.03, -12.914], [8.181, -11.188], [5.07, 1.217], [-2.002, -1.537], [-0.294, -0.004], [-32.542, -11.522], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.975, -1.286], [37.486, -28.166], [59.552, -41.479], [71.468, -41.967], [68.462, -48.957], [60.031, -51.038], [-36.804, -19.539], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [9.99, -11.517]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [{"i": [[0, 0], [-0.441, 2.98], [-8.567, 6.565], [2.157, 1.656], [2.7, 0.43], [0.546, -0.083], [16.009, -21.898], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [12.907, -15.667]], "o": [[-5.99, -14.628], [1.999, -13.498], [5.07, 1.217], [-2.002, -1.537], [-0.294, -0.004], [-45.296, 6.874], [-9.96, 13.625], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[34.975, -1.036], [30.986, -30.916], [45.802, -60.229], [51.218, -68.217], [46.962, -71.207], [42.031, -70.038], [-37.304, -22.039], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [10.49, -16.267]], "c": false}]}, {"t": 156, "s": [{"i": [[0, 0], [0, 7.23], [-12.64, 7.17], [0.144, 2.073], [2.7, 0.43], [0.552, 0], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [0, -15.57], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-34.7, 0], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.157, -21.201], [51.318, -57.521], [54.19, -62.721], [49.298, -67.841], [47.997, -67.851], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.647, 0.553, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [250.515, 145.164], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Cap fill", "parent": 12, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [262.417, 139.745, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [262.417, 139.745, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0, 7.23], [-12.64, 7.17], [0.144, 2.073], [2.7, 0.43], [0.552, 0], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [0, -15.57], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-34.7, 0], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.157, -21.201], [51.318, -57.521], [54.19, -62.721], [49.298, -67.841], [47.997, -67.851], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [{"i": [[0, 0], [0.595, 7.203], [-4.284, 14.484], [0.144, 2.073], [2.7, 0.43], [0.468, -0.28], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-1.495, -17.885], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-24.231, 14.148], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.41, -21], [32.286, -72.542], [33.419, -76.153], [27.059, -78.322], [25.942, -77.343], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 22, "s": [{"i": [[0, 0], [0.648, 7.201], [-3.533, 15.141], [0.144, 2.073], [2.7, 0.43], [0.46, -0.305], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-1.629, -18.093], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-23.29, 15.419], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.432, -20.982], [29.206, -73.987], [31.553, -77.36], [25.061, -79.264], [23.761, -79.274], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [{"i": [[0, 0], [0.201, 7.449], [-2.761, 13.167], [0.144, 2.073], [2.7, 0.43], [0.452, -0.252], [19.888, -27.603], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-1.041, -17.956], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-22.987, 12.569], [-11.709, 16.467], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.361, -1.328], [31.073, -21.29], [30.424, -70.736], [34.825, -73.576], [28.631, -75.476], [26.274, -77.254], [-35.857, -23.653], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.435, 7.265], [35.361, -1.25], [34.906, -1.843], [15.023, -17.972]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [{"i": [[0, 0], [-0.357, 7.759], [-1.797, 10.704], [0.144, 2.073], [2.7, 0.43], [0.44, -0.187], [21.658, -29.055], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-0.307, -17.786], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-22.61, 9.013], [-11.857, 16.351], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.528, -1.436], [30.782, -23.708], [31.896, -66.878], [38.906, -68.853], [33.086, -70.749], [29.542, -73.748], [-35.888, -23.082], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.494, 7.236], [35.528, -1.262], [34.505, -2.582], [15.229, -18.149]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [{"i": [[0, 0], [-0.337, 9.949], [0.13, 5.779], [0.144, 2.073], [2.7, 0.43], [0.418, -0.056], [25.197, -31.957], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [1.664, -29.646], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-21.854, 1.901], [-12.154, 16.119], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.862, -1.654], [32.353, -24.442], [38.588, -57.337], [47.07, -59.409], [41.995, -61.295], [38.433, -64.836], [-35.949, -21.94], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.611, 7.176], [35.861, -1.286], [33.704, -4.061], [15.642, -18.503]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [{"i": [[0, 0], [-2.87, 9.154], [2.54, -0.379], [0.144, 2.073], [2.7, 0.43], [0.39, 0.108], [29.621, -35.586], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [2.996, -17.02], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-20.909, -6.99], [-12.524, 15.829], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.279, -1.925], [35.468, -23.405], [48.392, -42.968], [57.275, -47.604], [53.132, -49.478], [50.597, -53.001], [-36.025, -20.513], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.759, 7.101], [36.278, -1.316], [32.702, -5.909], [16.157, -18.945]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [{"i": [[0, 0], [-3.788, 7.799], [0.208, 0.166], [0.144, 2.073], [2.7, 0.43], [0.379, 0.174], [29.189, -34.773], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [7.733, -17.044], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-23.146, -9.715], [-12.674, 15.712], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.448, -2.035], [37.458, -24.857], [53.289, -38.272], [61.399, -42.833], [57.632, -44.703], [55.094, -47.862], [-36.056, -19.936], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.818, 7.071], [36.447, -1.328], [32.297, -6.656], [16.366, -19.124]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [{"i": [[0, 0], [-4.707, 6.443], [-2.124, 0.71], [0.144, 2.073], [2.7, 0.43], [0.368, 0.24], [28.756, -33.961], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [12.469, -17.068], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-20.146, -14.174], [-12.824, 15.595], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.617, -2.145], [38.974, -23.557], [60.742, -31.848], [65.523, -38.062], [62.133, -39.928], [59.59, -42.722], [-36.087, -19.359], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.878, 7.041], [36.615, -1.34], [31.892, -7.403], [16.574, -19.302]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [{"i": [[0, 0], [-5.788, 4.449], [-6.161, 1.652], [0.144, 2.073], [2.7, 0.43], [0.348, 0.354], [28.007, -32.554], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [15.104, -11.682], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-19.485, -20.394], [-13.083, 15.392], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.909, -2.335], [43.345, -22.526], [71.433, -22.223], [72.662, -29.803], [69.924, -31.661], [67.048, -34.833], [-36.14, -18.36], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.981, 6.989], [36.907, -1.361], [31.191, -8.695], [16.935, -19.612]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[0, 0], [-6.601, 2.95], [-9.194, 2.361], [0.144, 2.073], [2.7, 0.43], [0.333, 0.44], [27.444, -31.497], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [17.085, -7.636], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-18.989, -25.067], [-13.278, 15.239], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[37.128, -2.478], [46.629, -21.751], [79.466, -14.99], [78.026, -23.597], [75.778, -25.448], [71.917, -29.938], [-36.18, -17.61], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [42.058, 6.95], [37.126, -1.376], [30.664, -9.667], [17.206, -19.844]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[0, 0], [-6.065, 3.215], [-8.75, 1.915], [0.144, 2.073], [2.7, 0.43], [0.343, 0.386], [26.786, -31.126], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [15.565, -9.156], [2.159, -0.88], [-0.174, -2.518], [-0.294, -0.004], [-19.744, -22.651], [-13.154, 15.336], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.989, -2.387], [45.107, -22.216], [75.762, -18.478], [75.603, -25.986], [73.116, -28.29], [68.993, -32.383], [-36.155, -18.087], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [42.009, 6.975], [36.987, -1.367], [30.999, -9.05], [17.034, -19.696]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [{"i": [[0, 0], [-4.528, 3.975], [-6.94, 4.096], [0.144, 2.073], [2.7, 0.43], [0.37, 0.229], [24.896, -30.061], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [11.684, -10.395], [2.929, -0.243], [-0.174, -2.518], [-0.294, -0.004], [-21.913, -15.714], [-12.799, 15.614], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.589, -2.126], [40.738, -23.55], [65.209, -30.293], [68.646, -32.843], [65.471, -36.45], [61.358, -39.522], [-36.082, -19.456], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.868, 7.046], [36.587, -1.338], [31.96, -7.277], [16.539, -19.272]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [{"i": [[0, 0], [-3.21, 4.627], [-5.389, 5.966], [0.144, 2.073], [2.7, 0.43], [0.392, 0.094], [23.276, -29.148], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [8.356, -11.458], [3.588, 0.304], [-0.174, -2.518], [-0.294, -0.004], [-23.772, -9.767], [-12.494, 15.853], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.245, -1.903], [36.992, -24.693], [56.073, -38.47], [62.682, -38.721], [58.918, -43.444], [53.564, -45.224], [-36.019, -20.63], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.747, 7.108], [36.244, -1.313], [32.784, -5.757], [16.115, -18.909]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [{"i": [[0, 0], [-2.552, 4.953], [-4.614, 6.9], [0.144, 2.073], [2.7, 0.43], [0.404, 0.027], [22.465, -28.692], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [6.693, -11.989], [3.918, 0.577], [-0.174, -2.518], [-0.294, -0.004], [-24.702, -6.794], [-12.342, 15.972], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[36.074, -1.791], [35.12, -25.265], [51.454, -41.37], [59.7, -41.66], [55.642, -46.94], [49.799, -48.428], [-35.987, -21.216], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.686, 7.138], [36.073, -1.301], [33.196, -4.998], [15.903, -18.727]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [{"i": [[0, 0], [-1.681, 5.384], [-7.051, 8.364], [0.144, 2.073], [2.7, 0.43], [0.419, -0.062], [21.395, -28.088], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [3.384, -12.783], [0.766, -4.603], [-0.174, -2.518], [-0.294, -0.004], [-22.653, -1.944], [-12.14, 16.13], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.847, -1.644], [33.256, -26.776], [45.236, -49.88], [52.067, -52.935], [48.633, -57.982], [42.056, -56.341], [-35.946, -21.992], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.606, 7.179], [35.846, -1.285], [33.74, -3.993], [15.623, -18.487]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 91, "s": [{"i": [[0, 0], [0.698, 6.56], [-0.938, 10.544], [0.144, 2.073], [2.7, 0.43], [0.46, -0.305], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [-1.647, -15.483], [-7.84, -18.747], [-0.174, -2.518], [-0.294, -0.004], [-17.06, 11.302], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.354, -22.694], [28.256, -73.118], [31.223, -83.725], [29.495, -88.135], [20.911, -77.949], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [{"i": [[0, 0], [-1.785, 6.198], [-5.558, 5.97], [1.15, 1.864], [2.7, 0.43], [0.49, -0.06], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [9.849, -5.07]], "o": [[-3.635, -9.422], [3.267, -13.336], [-1.385, -8.765], [-1.088, -2.028], [-0.294, -0.004], [-29.218, 6.438], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.601, -1.264], [32.339, -27.374], [45.439, -56.116], [51.345, -62.846], [48.979, -68.546], [40.502, -63.985], [-36.318, -21.825], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [12.191, -18.486]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 113, "s": [{"i": [[0, 0], [-4.267, 5.836], [-10.178, 1.396], [2.157, 1.656], [2.7, 0.43], [0.521, 0.184], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [12.907, -15.667]], "o": [[-4.03, -12.914], [8.181, -11.188], [5.07, 1.217], [-2.002, -1.537], [-0.294, -0.004], [-32.542, -11.522], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.975, -1.286], [37.486, -28.166], [59.552, -41.479], [71.468, -41.967], [68.462, -48.957], [60.031, -51.038], [-36.804, -19.539], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [9.99, -11.517]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [{"i": [[0, 0], [-0.441, 2.98], [-8.567, 6.565], [2.157, 1.656], [2.7, 0.43], [0.546, -0.083], [16.009, -21.898], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [12.907, -15.667]], "o": [[-5.99, -14.628], [1.999, -13.498], [5.07, 1.217], [-2.002, -1.537], [-0.294, -0.004], [-45.296, 6.874], [-9.96, 13.625], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[34.975, -1.036], [30.986, -30.916], [45.802, -60.229], [51.218, -68.217], [46.962, -71.207], [42.031, -70.038], [-37.304, -22.039], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [10.49, -16.267]], "c": false}]}, {"t": 156, "s": [{"i": [[0, 0], [0, 7.23], [-12.64, 7.17], [0.144, 2.073], [2.7, 0.43], [0.552, 0], [18.47, -26.44], [0, -21.75], [-0.04, -1.02], [-30.979, -2.377], [-12.559, 19.584], [7.182, 11.711], [2.26, 2.68], [0, 0], [6.79, 5.527]], "o": [[-3.24, -5.93], [0, -15.57], [1.892, -1.102], [-0.174, -2.518], [-0.294, -0.004], [-34.7, 0], [-11.59, 16.56], [0, 1.03], [1.529, 2.531], [22.677, 1.74], [-0.726, -7.977], [-1.84, -3], [0, 0], [-6.79, -5.527], [0, 0]], "v": [[35.227, -1.241], [30.157, -21.201], [51.318, -57.521], [54.19, -62.721], [49.298, -67.841], [47.997, -67.851], [-35.833, -24.111], [-54.202, 34.359], [-54.153, 37.429], [-5.734, 67.674], [51.548, 38.599], [41.387, 7.289], [35.227, -1.241], [35.227, -1.251], [14.857, -17.831]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.647, 0.553, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [250.515, 145.164], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 3, "nm": "Head 2", "parent": 16, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.324], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5, "s": [0]}, {"i": {"x": [0.364], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [-6]}, {"i": {"x": [0.331], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [30]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 83, "s": [-25]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 104.857, "s": [4.886]}, {"t": 128, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.324, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [250, 245.63, 0], "to": [-0.833, -0.5, 0], "ti": [4, 1.75, 0]}, {"i": {"x": 0.365, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [245, 242.63, 0], "to": [4.5, 3.375, 0], "ti": [-16.667, 7.792, 0]}, {"i": {"x": 0.331, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [297.696, 249.23, 0], "to": [-18.083, 10.583, 0], "ti": [11.25, 6.75, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [220.977, 246.947, 0], "to": [4.777, 2.13, 0], "ti": [-8.727, 3.113, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 104.857, "s": [253.181, 244.591, 0], "to": [-0.925, 0.431, 0], "ti": [0.754, 0.025, 0]}, {"t": 128, "s": [250, 245.63, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 245.63, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "mask 2", "parent": 16, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.324], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5, "s": [0]}, {"i": {"x": [0.364], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [-6]}, {"i": {"x": [0.331], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [30]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 83, "s": [-25]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 104.857, "s": [4.886]}, {"t": 128, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.324, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [264.495, 246.01, 0], "to": [-0.833, -0.5, 0], "ti": [4, 1.75, 0]}, {"i": {"x": 0.365, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [259.495, 243.01, 0], "to": [4.5, 3.375, 0], "ti": [-16.667, 7.792, 0]}, {"i": {"x": 0.331, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [312.191, 249.609, 0], "to": [-18.083, 10.583, 0], "ti": [11.25, 6.75, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [235.472, 247.326, 0], "to": [4.777, 2.13, 0], "ti": [-8.727, 3.113, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 104.857, "s": [267.676, 244.971, 0], "to": [-0.925, 0.431, 0], "ti": [0.754, 0.025, 0]}, {"t": 128, "s": [264.495, 246.01, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 245.63, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.103, -27.089], [-0.433, -4.711], [-48.84, -30.694], [-4.879, 3.074], [0, 0], [0.344, 3.528]], "o": [[-0.675, 4.46], [0, 0], [4.882, 3.069], [48.727, -30.696], [0.338, -3.671], [-2.779, -28.496]], "v": [[-57.691, -29.397], [-56.632, -13.818], [-8.012, 73.595], [8.007, 73.588], [56.632, -13.818], [57.657, -29.203]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976470589638, 0.78823530674, 0.752941191196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [250, 245.63], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "shadow 2", "parent": 16, "tt": 2, "tp": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.324], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5, "s": [0]}, {"i": {"x": [0.364], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [-6]}, {"i": {"x": [0.331], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [30]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 83, "s": [-25]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 104.857, "s": [4.886]}, {"t": 128, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.324, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [250, 245.63, 0], "to": [-0.833, -0.5, 0], "ti": [4, 1.75, 0]}, {"i": {"x": 0.365, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [245, 242.63, 0], "to": [4.5, 3.375, 0], "ti": [-16.667, 7.792, 0]}, {"i": {"x": 0.331, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [297.696, 249.23, 0], "to": [-18.083, 10.583, 0], "ti": [11.25, 6.75, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [220.977, 246.947, 0], "to": [4.777, 2.13, 0], "ti": [-8.727, 3.113, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 104.857, "s": [253.181, 244.591, 0], "to": [-0.925, 0.431, 0], "ti": [0.754, 0.025, 0]}, {"t": 128, "s": [250, 245.63, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 245.63, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.103, -27.089], [-0.433, -4.711], [-48.84, -30.694], [-4.879, 3.074], [0, 0], [0.344, 3.528]], "o": [[-0.675, 4.46], [0, 0], [4.882, 3.069], [48.727, -30.696], [0.338, -3.671], [-2.779, -28.496]], "v": [[-57.691, -29.397], [-56.632, -13.818], [-8.012, 73.595], [8.007, 73.588], [56.632, -13.818], [57.657, -29.203]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976, 0.788, 0.753, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 245.63], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Head fill", "parent": 16, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.324], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5, "s": [0]}, {"i": {"x": [0.364], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [-6]}, {"i": {"x": [0.331], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [30]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 83, "s": [-25]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 104.857, "s": [4.886]}, {"t": 128, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.324, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [250, 245.63, 0], "to": [-0.833, -0.5, 0], "ti": [4, 1.75, 0]}, {"i": {"x": 0.365, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [245, 242.63, 0], "to": [4.5, 3.375, 0], "ti": [-16.667, 7.792, 0]}, {"i": {"x": 0.331, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [297.696, 249.23, 0], "to": [-18.083, 10.583, 0], "ti": [11.25, 6.75, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [220.977, 246.947, 0], "to": [4.777, 2.13, 0], "ti": [-8.727, 3.113, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 104.857, "s": [253.181, 244.591, 0], "to": [-0.925, 0.431, 0], "ti": [0.754, 0.025, 0]}, {"t": 128, "s": [250, 245.63, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 245.63, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.103, -27.089], [-0.433, -4.711], [-48.84, -30.694], [-4.879, 3.074], [0, 0], [0.344, 3.528]], "o": [[-0.675, 4.46], [0, 0], [4.882, 3.069], [48.727, -30.696], [0.338, -3.671], [-2.779, -28.496]], "v": [[-57.691, -29.397], [-56.632, -13.818], [-8.012, 73.595], [8.007, 73.588], [56.632, -13.818], [57.657, -29.203]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976, 0.788, 0.753, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 245.63], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0.38, 0.02], [7.73, 7.73], [-1.56, 12.58], [-9.63, -9.44]], "o": [[-0.36, 0.04], [-10.12, -0.55], [-9.65, -9.66], [12.44, -1.53], [0, 0]], "v": [[20.697, 24.02], [19.587, 24.05], [-8.223, 11.63], [-20.373, -23.74], [14.677, -11.88]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [{"i": [[0, 0], [0.38, 0.02], [8.446, 6.941], [-0.321, 12.672], [-9.63, -9.44]], "o": [[-0.36, 0.04], [-10.12, -0.55], [-10.549, -8.669], [12.231, -2.74], [0, 0]], "v": [[20.697, 24.02], [19.587, 24.05], [-7.093, 13.044], [-22.646, -20.967], [14.677, -11.88]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [{"i": [[0, 0], [0.38, 0.02], [4.658, 9.89], [-5.72, 11.313], [-9.63, -9.44]], "o": [[-0.36, 0.04], [-10.12, -0.55], [-5.817, -12.353], [12.225, 2.764], [0, 0]], "v": [[20.697, 24.02], [19.587, 24.05], [-7.078, 4.49], [-6.559, -32.905], [14.677, -11.88]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 92, "s": [{"i": [[0, 0], [0.38, 0.02], [7.924, 7.531], [-1.233, 12.616], [-9.63, -9.44]], "o": [[-0.36, 0.04], [-10.12, -0.55], [-9.898, -9.406], [12.396, -1.852], [0, 0]], "v": [[20.697, 24.02], [19.587, 24.05], [-7.985, 11.995], [-21.049, -23.047], [14.677, -11.88]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 113.857, "s": [{"i": [[0, 0], [0.38, 0.02], [6.338, 8.677], [-3.412, 11.983], [-9.63, -9.44]], "o": [[-0.36, 0.04], [-10.12, -0.55], [-7.916, -10.837], [12.313, 0.39], [0, 0]], "v": [[20.697, 24.02], [19.587, 24.05], [-7.544, 8.35], [-14.011, -27.835], [14.677, -11.88]], "c": false}]}, {"t": 137, "s": [{"i": [[0, 0], [0.38, 0.02], [7.73, 7.73], [-1.56, 12.58], [-9.63, -9.44]], "o": [[-0.36, 0.04], [-10.12, -0.55], [-9.65, -9.66], [12.44, -1.53], [0, 0]], "v": [[20.697, 24.02], [19.587, 24.05], [-8.223, 11.63], [-20.373, -23.74], [14.677, -11.88]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [-12.44, -1.53], [9.65, -9.66], [10.12, -0.55], [0.38, 0.04]], "o": [[9.63, -9.44], [1.56, 12.58], [-7.73, 7.73], [-0.39, 0.02], [0, 0]], "v": [[128.397, -11.88], [163.447, -23.74], [151.297, 11.63], [123.487, 24.05], [122.337, 24.02]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [{"i": [[0, 0], [-12.404, 1.8], [6.771, -11.857], [10.12, -0.55], [0.38, 0.04]], "o": [[9.63, -9.44], [4.818, 11.725], [-5.421, 9.493], [-0.39, 0.02], [0, 0]], "v": [[128.397, -11.88], [154.371, -31.528], [151.965, 5.794], [123.487, 24.05], [122.337, 24.02]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [{"i": [[0, 0], [-12.046, -3.463], [11.05, -8.021], [10.12, -0.55], [0.38, 0.04]], "o": [[9.63, -9.44], [-0.433, 12.669], [-8.847, 6.422], [-0.39, 0.02], [0, 0]], "v": [[128.397, -11.88], [167.337, -18.771], [149.788, 14.255], [123.487, 24.05], [122.337, 24.02]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 92, "s": [{"i": [[0, 0], [-12.472, 1.239], [7.299, -11.54], [10.12, -0.55], [0.38, 0.04]], "o": [[9.63, -9.44], [4.285, 11.93], [-5.844, 9.239], [-0.39, 0.02], [0, 0]], "v": [[128.397, -11.88], [154.74, -29.798], [150.655, 7.377], [123.487, 24.05], [122.337, 24.02]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 113.857, "s": [{"i": [[0, 0], [-12.265, -1.045], [9.121, -9.83], [10.12, -0.55], [0.38, 0.04]], "o": [[9.63, -9.44], [1.993, 12.289], [-7.302, 7.87], [-0.39, 0.02], [0, 0]], "v": [[128.397, -11.88], [160.859, -24.442], [150.234, 10.717], [123.487, 24.05], [122.337, 24.02]], "c": false}]}, {"t": 137, "s": [{"i": [[0, 0], [-12.44, -1.53], [9.65, -9.66], [10.12, -0.55], [0.38, 0.04]], "o": [[9.63, -9.44], [1.56, 12.58], [-7.73, 7.73], [-0.39, 0.02], [0, 0]], "v": [[128.397, -11.88], [163.447, -23.74], [151.297, 11.63], [123.487, 24.05], [122.337, 24.02]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.976, 0.788, 0.753, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 1, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "st", "c": {"a": 0, "k": [0.976, 0.788, 0.753, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "fl", "c": {"a": 0, "k": [0.976, 0.788, 0.753, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "fl", "c": {"a": 0, "k": [0.976, 0.788, 0.753, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [178.463, 239.374], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 6, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 3, "nm": "Body 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 37, "s": [5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 78, "s": [-3]}, {"t": 100, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [215, 380.142, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 415.142, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 15, "s": [98, 104, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 28, "s": [103, 98, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 42, "s": [98, 104, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 62, "s": [103, 98, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 83, "s": [98, 104, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 99, "s": [103, 98, 100]}, {"t": 115, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 37, "s": [5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 78, "s": [-3]}, {"t": 100, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [229, 389.142, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 415.142, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 15, "s": [98, 104, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 28, "s": [103, 98, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 42, "s": [98, 104, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 62, "s": [103, 98, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 83, "s": [98, 104, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 99, "s": [103, 98, 100]}, {"t": 115, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 0, "s": [{"i": [[-10.38, -6.73], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.29, 0], [0, 0], [0, 0], [-2.919, -0.574], [-2.958, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [50.996, -22.321], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.38, -6.73], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [13.28, 0]], "v": [[85.72, -12.055], [50.44, -6.015], [48.41, 23.955], [19.27, 16.645], [0, 39.685], [-19.27, 16.645], [-48.41, 23.955], [-50.44, -6.015], [-69.22, -13.555], [-33.18, -24.185], [-27.08, -24.185], [-27.08, -39.625], [-14.757, -37.302], [-4.667, -35.468], [8.95, -67.009], [17.97, -68.538], [48.6, -55.685], [70.6, -36.685], [88.69, -37.685]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[-10.38, -6.73], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.29, 0], [0, 0], [0, 0], [-2.919, -0.574], [-2.958, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.38, -6.73], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [13.28, 0]], "v": [[82.222, -13.465], [50.44, -6.015], [48.41, 23.955], [19.27, 16.645], [0, 39.685], [-19.27, 16.645], [-48.41, 23.955], [-50.44, -6.015], [-69.22, -13.555], [-33.18, -24.185], [-27.08, -24.185], [-27.08, -39.625], [-14.757, -37.302], [-5.003, -41.729], [0.332, -49.688], [12.278, -57.892], [27.1, -39.685], [46.973, -38.17], [66.882, -31.055]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[-10.38, -6.73], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-12.738, 0.495], [0, 0], [0, 0], [-2.919, -0.574], [-2.958, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.38, -6.73], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [13.28, 0]], "v": [[76.347, -13.047], [50.44, -6.015], [48.41, 23.955], [19.27, 16.645], [0, 39.685], [-19.27, 16.645], [-48.41, 23.955], [-50.44, -6.015], [-69.22, -13.555], [-35.504, -23.55], [-29.144, -23.661], [-29.243, -45.841], [-14.757, -37.302], [-5.701, -53.309], [2.919, -54.407], [21.013, -53.189], [25.559, -48.524], [49.375, -40.791], [74.773, -29.482]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 21, "s": [{"i": [[-10.38, -6.73], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.13, -0.071], [0, 0], [0, 0], [-2.919, -0.574], [-2.959, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.914, -7.307], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [13.737, 0.379]], "v": [[89, -46.191], [51.188, -5.644], [49.033, 24.573], [20.268, 16.77], [0.624, 39.686], [-18.77, 16.276], [-47.536, 23.463], [-49.067, -6.383], [-67.846, -14.663], [-30.949, -24.961], [-24.527, -24.825], [-23.63, -45.708], [-14.757, -37.302], [13.852, -68.985], [15.214, -53.003], [34.489, -70.055], [47.735, -74.577], [47.836, -57.09], [53.566, -56.982]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [{"i": [[-9.322, -5.644], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.36, -1.932], [0, 0], [0, 0], [-2.919, -0.574], [-2.958, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [13.908, -10.308], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [12.643, 1.783]], "v": [[87.413, -19.331], [54.003, -2.73], [50.586, 26.533], [22.045, 18.335], [1.612, 40.016], [-16.455, 16.407], [-45.755, 22.601], [-46.795, -7.143], [-65.739, -16.309], [-20.426, -28.464], [-14.1, -27.812], [-8.198, -43.216], [6.2, -43.665], [24.208, -48.897], [26.509, -45.128], [29.838, -46.223], [53.356, -48.167], [53.303, -30.098], [59.076, -29.098]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 35, "s": [{"i": [[-9.322, -5.644], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-14.587, -2.953], [0, 0], [0, 0], [-2.919, -0.574], [-2.959, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [1.467, -4.963], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [14.963, -10.416], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [-1.043, 5.552], [0, 0], [11.436, 2.204]], "v": [[93.741, -18.688], [56.877, -1.235], [52.068, 29.264], [23.751, 19.308], [2.925, 40.774], [-14.327, 16.475], [-43.712, 21.682], [-44.132, -8.115], [-63.026, -18.154], [-15.92, -29.778], [-9.702, -28.93], [-3.015, -49.742], [6.2, -43.665], [27.093, -50.57], [29.393, -46.801], [49.211, -56.5], [65.208, -48.615], [60.234, -30.438], [66.265, -29.336]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [{"i": [[-8.73, -5.711], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-14.587, -2.953], [0, 0], [0, 0], [-2.919, -0.574], [-2.959, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [1.467, -4.963], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [14.963, -10.416], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [-1.043, 5.552], [0, 0], [11.436, 2.204]], "v": [[91.06, -22.161], [57.308, 0.782], [52.914, 30.163], [24.704, 19.958], [3.211, 41.114], [-14.327, 16.475], [-43.351, 21.413], [-42.796, -8.941], [-61.812, -18.91], [-14.743, -30.23], [-8.818, -29.058], [-3.015, -49.742], [6.2, -43.665], [22.507, -55.343], [24.807, -51.574], [44.625, -61.273], [60.283, -53.603], [56.564, -34.98], [62.542, -33.753]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [{"i": [[-8.718, -5.333], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-15.429, -2.419], [0, 0], [0, 0], [-2.919, -0.574], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [1.467, -4.963], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [14.63, -10.692], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [-1.043, 5.552], [0, 0], [13.188, 2.107]], "v": [[91.668, -16.668], [55.456, -0.704], [51.187, 28.67], [23.05, 19.685], [2.45, 41.035], [-15.546, 17.156], [-44.666, 22.59], [-45.351, -7.45], [-64.293, -17.24], [-17.468, -29.464], [-11.487, -28.417], [-7.153, -51.527], [6.2, -43.665], [26.859, -50.43], [31.688, -47.539], [48.042, -54.104], [63.018, -47.313], [57.353, -28.58], [63.449, -27.482]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [{"i": [[-8.922, -5.138], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-17.463, -1.778], [0, 0], [0, 0], [-2.919, -0.574], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [1.467, -4.963], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [14.63, -10.692], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [-1.043, 5.552], [0, 0], [14.116, 2.032]], "v": [[88.413, -27.793], [49.601, -4.331], [47.441, 25.997], [19.209, 18.147], [-0.373, 40.982], [-19.552, 18.896], [-48.62, 26.469], [-50.528, -3.413], [-72.14, -12], [-25.392, -27.37], [-19.788, -26.692], [-18.648, -55.008], [6.2, -43.665], [30.284, -56.817], [35.114, -53.926], [51.468, -60.491], [57.678, -55.929], [52.621, -37.401], [59.133, -36.88]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 68, "s": [{"i": [[-9.304, -3.702], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-16.484, 0.355], [0, 0], [0, 0], [-2.884, 0.733], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [-0.437, -5.621], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [13.153, -12.732], [0, 0], [0, 0], [5.182, 0.935], [1.363, -0.346], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [0.496, 5.473], [0, 0], [13.59, -0.244]], "v": [[83.481, -39.727], [40.803, -9.281], [41.227, 20.752], [12.76, 16.551], [-4.426, 41.228], [-25.504, 21.099], [-53.807, 31.723], [-59.279, 2.104], [-82.414, -4.768], [-37.514, -23.855], [-31.146, -24.097], [-31.431, -41.931], [-18.227, -46.725], [12.258, -69.553], [23.508, -72.127], [32.546, -71.995], [44.108, -70.033], [46.174, -45.916], [51.911, -46.114]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 77, "s": [{"i": [[-9.304, -3.702], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-16.484, 0.355], [0, 0], [0, 0], [-2.884, 0.733], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [-0.437, -5.621], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [12.37, -12.968], [0, 0], [0, 0], [5.182, 0.935], [1.363, -0.346], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [0.496, 5.473], [0, 0], [16.365, -0.389]], "v": [[72.532, -44.374], [38.214, -10.506], [39.773, 19.583], [9.966, 16.776], [-5.375, 42.398], [-27.959, 22.071], [-55.393, 32.982], [-61.893, 3.677], [-86.375, -1.68], [-41.111, -22.818], [-34.975, -23.437], [-36.66, -41.098], [-18.227, -46.725], [3.997, -72.483], [10.814, -74.915], [19.493, -75.165], [32.449, -73.256], [34.057, -50.013], [40.238, -50.25]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [{"i": [[-9.304, -3.702], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-16.647, 0.876], [0, 0], [0, 0], [-2.884, 0.733], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [-0.437, -5.621], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [12.053, -13.031], [0, 0], [0, 0], [5.182, 0.935], [1.363, -0.346], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [0.496, 5.473], [0, 0], [16.908, -0.562]], "v": [[73.74, -42.95], [38.214, -10.506], [39.773, 19.583], [9.966, 16.776], [-5.375, 42.398], [-27.959, 22.071], [-55.393, 32.982], [-61.893, 3.677], [-86.375, -1.68], [-41.753, -22.725], [-35.757, -23.11], [-37.535, -41.497], [-18.227, -46.725], [5.206, -71.058], [12.022, -73.491], [20.702, -73.741], [31.977, -71.422], [34.57, -48.739], [40.697, -49.159]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 91, "s": [{"i": [[-8.21, -3.593], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-16.692, 1.072], [0, 0], [0, 0], [-2.884, 0.733], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-1.095, 2.31], [-0.674, -5.648], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [12.013, -10.621], [0, 0], [0, 0], [5.182, 0.935], [1.363, -0.346], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [0.496, 5.473], [0, 0], [18.484, -0.352]], "v": [[85.88, -38.556], [42.169, -9.193], [42.363, 20.747], [12.967, 15.967], [-3.481, 40.949], [-25.188, 20.267], [-53.473, 30.172], [-58.113, 1.149], [-80.876, -6.217], [-39.13, -23.294], [-33.011, -23.615], [-34.609, -41.627], [-18.227, -46.725], [13.84, -68.711], [20.656, -71.144], [29.336, -71.394], [43.149, -68.408], [46.05, -45.595], [52.111, -45.831]], "c": true}]}, {"t": 105, "s": [{"i": [[-10.38, -6.73], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.29, 0], [0, 0], [0, 0], [-2.919, -0.574], [-2.959, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.38, -6.73], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [13.28, 0]], "v": [[81.714, -41.69], [50.44, -6.015], [48.41, 23.955], [19.27, 16.645], [0, 39.685], [-19.27, 16.645], [-48.41, 23.955], [-50.44, -6.015], [-69.22, -13.555], [-33.18, -24.185], [-27.08, -24.185], [-27.08, -39.625], [-14.757, -37.302], [7.827, -63.603], [19.444, -63.144], [28.464, -64.673], [39.594, -67.82], [39.594, -52.32], [45.684, -52.32]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.92156869173, 0.901960849762, 0.937254965305, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.921568632126, 0.901960790157, 0.937254905701, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [249.903, 343.506], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.316, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0, 0], [0, -0.08]], "o": [[0, 0], [0, 0.08], [0, 0]], "v": [[0, -7.752], [0, 7.504], [0, 7.752]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [-0.002, -0.08]], "o": [[0, 0], [0.002, 0.08], [0, 0]], "v": [[-2.737, -8.316], [-2.375, 6.936], [-2.369, 7.184]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [{"i": [[0, 0], [0, 0], [0.011, -0.079]], "o": [[0, 0], [-0.011, 0.079], [0, 0]], "v": [[12.166, 6.02], [12.111, 11.508], [12.078, 11.753]], "c": false}]}, {"i": {"x": 0.257, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [{"i": [[0, 0], [0, 0], [0.014, -0.079]], "o": [[0, 0], [-0.014, 0.079], [0, 0]], "v": [[19.133, 3.535], [16.387, 12.858], [16.342, 13.101]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [{"i": [[0, 0], [0, 0], [0.015, -0.078]], "o": [[0, 0], [-0.015, 0.079], [0, 0]], "v": [[19.426, 3.412], [17.287, 13.142], [17.24, 13.385]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [{"i": [[0, 0], [0, 0], [0.013, -0.079]], "o": [[0, 0], [-0.013, 0.079], [0, 0]], "v": [[16.845, 5.523], [14.658, 12.312], [14.618, 12.556]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[0, 0], [0, 0], [0.007, -0.079]], "o": [[0, 0], [-0.007, 0.079], [0, 0]], "v": [[7.817, 5.258], [6.478, 9.73], [6.458, 9.975]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [{"i": [[0, 0], [0, 0], [-0.002, -0.08]], "o": [[0, 0], [0.002, 0.08], [0, 0]], "v": [[-5.501, -5.867], [-4.393, 6.298], [-4.386, 6.545]], "c": false}]}, {"i": {"x": 0.39, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0, 0], [0, 0], [-0.005, -0.08]], "o": [[0, 0], [0.005, 0.08], [0, 0]], "v": [[-9.255, -8.507], [-8.203, 5.095], [-8.188, 5.342]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [{"i": [[0, 0], [0, 0], [-0.006, -0.08]], "o": [[0, 0], [0.006, 0.08], [0, 0]], "v": [[-10.887, -11.82], [-8.824, 4.899], [-8.807, 5.146]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [{"i": [[0, 0], [0, 0], [-0.004, -0.08]], "o": [[0, 0], [0.004, 0.08], [0, 0]], "v": [[-7.66, -6.377], [-6.143, 5.69], [-6.131, 5.938]], "c": false}]}, {"t": 104.857421875, "s": [{"i": [[0, 0], [0, 0], [0, -0.08]], "o": [[0, 0], [0, 0.08], [0, 0]], "v": [[0, -7.752], [0, 7.504], [0, 7.752]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.316, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0, 0], [0, -0.08]], "o": [[0, 0], [0, 0.08], [0, 0]], "v": [[-54.18, -7.692], [-54.18, 7.504], [-54.18, 7.752]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [-0.002, -0.08]], "o": [[0, 0], [0.002, 0.08], [0, 0]], "v": [[-56.401, -9.968], [-56.54, 8.224], [-56.534, 8.471]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[0, 0], [0, 0], [0.002, -0.08]], "o": [[0, 0], [-0.002, 0.08], [0, 0]], "v": [[-51.285, -4.13], [-51.691, 6.969], [-51.698, 7.215]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [{"i": [[0, 0], [0, 0], [0.011, -0.079]], "o": [[0, 0], [-0.011, 0.079], [0, 0]], "v": [[-38.786, -1.718], [-41.343, 4.29], [-41.376, 4.535]], "c": false}]}, {"i": {"x": 0.257, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [{"i": [[0, 0], [0, 0], [0.014, -0.079]], "o": [[0, 0], [-0.014, 0.079], [0, 0]], "v": [[-33.514, -7.778], [-36.858, 3.129], [-36.902, 3.372]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [{"i": [[0, 0], [0, 0], [0.015, -0.078]], "o": [[0, 0], [-0.015, 0.079], [0, 0]], "v": [[-31.536, -13.037], [-35.913, 2.884], [-35.96, 3.128]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [{"i": [[0, 0], [0, 0], [0.013, -0.079]], "o": [[0, 0], [-0.013, 0.079], [0, 0]], "v": [[-36.545, -7.565], [-38.627, 3.467], [-38.668, 3.711]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[0, 0], [0, 0], [0.007, -0.079]], "o": [[0, 0], [-0.007, 0.079], [0, 0]], "v": [[-46.855, 1.792], [-47.073, 5.28], [-47.094, 5.525]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [{"i": [[0, 0], [0, 0], [-0.002, -0.08]], "o": [[0, 0], [0.002, 0.08], [0, 0]], "v": [[-58.54, 3.851], [-58.297, 7.69], [-58.291, 7.937]], "c": false}]}, {"i": {"x": 0.39, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0, 0], [0, 0], [-0.005, -0.08]], "o": [[0, 0], [0.005, 0.08], [0, 0]], "v": [[-62.707, 2.128], [-62.232, 8.535], [-62.216, 8.782]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [{"i": [[0, 0], [0, 0], [-0.006, -0.08]], "o": [[0, 0], [0.006, 0.08], [0, 0]], "v": [[-63.819, -1.006], [-62.873, 8.673], [-62.855, 8.92]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [{"i": [[0, 0], [0, 0], [-0.004, -0.08]], "o": [[0, 0], [0.004, 0.08], [0, 0]], "v": [[-60.948, -0.068], [-60.232, 8.318], [-60.22, 8.565]], "c": false}]}, {"t": 104.857421875, "s": [{"i": [[0, 0], [0, 0], [0, -0.08]], "o": [[0, 0], [0, 0.08], [0, 0]], "v": [[-54.18, -7.692], [-54.18, 7.504], [-54.18, 7.752]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 0, "s": [{"i": [[-9.188, -4.808], [0, -25.57], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-21.456, 10.998]], "o": [[21.187, 11.086], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [0, -25.768], [9.068, -4.648]], "v": [[46.825, 12.279], [72.469, 74.128], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-126.658, 74.128], [-90.518, 15.023]], "c": false}]}, {"i": {"x": 0.316, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[-9.273, -4.924], [0, -25.427], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-21.259, 11.063]], "o": [[20.993, 11.148], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [0, -25.623], [9.156, -4.765]], "v": [[42.73, 13.611], [72.469, 74.128], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-126.658, 74.128], [-90.88, 15.21]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[-9.388, -4.703], [-0.604, -25.42], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-20.567, 10.85]], "o": [[21.252, 10.646], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [0, -25.623], [8.858, -4.673]], "v": [[56.017, 11.673], [71.658, 71.818], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-126.658, 74.128], [-92.716, 16.271]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[-9.031, -4.876], [0.084, -25.393], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-21.284, 10.515]], "o": [[20.443, 11.038], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [0.916, -25.735], [9.167, -4.529]], "v": [[73.553, -18.732], [72.358, 74.512], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-125.693, 71.952], [-88.709, 13.905]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [{"i": [[-8.198, -5.28], [1.689, -25.33], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-22.956, 9.732]], "o": [[18.558, 11.952], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [3.05, -25.996], [9.887, -4.192]], "v": [[73.283, 0.282], [73.992, 80.791], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-123.444, 66.882], [-79.369, 8.392]], "c": false}]}, {"i": {"x": 0.257, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [{"i": [[-7.81, -5.468], [2.435, -25.3], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-23.734, 9.368]], "o": [[17.681, 12.378], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [4.043, -26.118], [10.222, -4.035]], "v": [[72.004, 10.27], [74.752, 83.712], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-122.398, 64.524], [-75.026, 5.828]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [{"i": [[-7.729, -5.507], [2.592, -25.294], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-23.898, 9.292]], "o": [[17.496, 12.467], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [4.252, -26.143], [10.293, -4.002]], "v": [[63.621, 9.993], [74.912, 84.327], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-122.178, 64.027], [-74.111, 5.288]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [{"i": [[-7.916, -5.382], [2.153, -25.301], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0.028, 6.093], [0, 0], [-23.51, 9.543]], "o": [[17.921, 12.184], [0, 0], [0.028, 6.093], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [3.706, -26.088], [10.126, -4.11]], "v": [[75.701, 1.056], [74.227, 82.513], [72.225, 95.767], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.88, 96.688], [-122.954, 65.406], [-76.581, 6.605]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[-8.501, -4.994], [0.786, -25.324], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0.116, 6.091], [0, 0], [-22.304, 10.323]], "o": [[19.244, 11.305], [0, 0], [0.116, 6.091], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [2.005, -25.917], [9.606, -4.446]], "v": [[78.378, -11.09], [72.096, 76.87], [71.466, 94.022], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-127.574, 97.808], [-125.37, 69.698], [-84.266, 10.704]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [{"i": [[-9.277, -4.477], [-1.031, -25.353], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0.232, 6.088], [0, 0], [-20.7, 11.36]], "o": [[21.002, 10.135], [0, 0], [0.232, 6.088], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [-0.255, -25.689], [8.916, -4.893]], "v": [[66.593, -16.1], [69.264, 69.37], [70.457, 91.704], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-128.495, 99.297], [-128.581, 75.402], [-94.478, 16.152]], "c": false}]}, {"i": {"x": 0.39, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[-9.55, -4.296], [-1.667, -25.363], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0.273, 6.087], [0, 0], [-20.138, 11.724]], "o": [[21.618, 9.725], [0, 0], [0.273, 6.087], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [-1.048, -25.609], [8.673, -5.049]], "v": [[49.272, -16.541], [68.271, 66.741], [70.104, 90.892], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-128.818, 99.819], [-129.707, 77.401], [-98.059, 18.062]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [{"i": [[-9.594, -4.266], [-1.771, -25.365], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0.28, 6.087], [0, 0], [-20.047, 11.783]], "o": [[21.719, 9.658], [0, 0], [0.28, 6.087], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [-1.177, -25.596], [8.634, -5.075]], "v": [[45.188, -11.425], [68.11, 66.313], [70.046, 90.759], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-128.871, 99.904], [-129.89, 77.727], [-98.642, 18.373]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [{"i": [[-9.497, -4.466], [-1.233, -25.384], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0.195, 6.089], [0, 0], [-20.415, 11.564]], "o": [[21.498, 10.111], [0, 0], [0.195, 6.089], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [-0.819, -25.604], [8.793, -4.981]], "v": [[67.758, -22.585], [69.434, 68.687], [70.782, 92.451], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-128.198, 98.817], [-128.908, 76.634], [-96.284, 17.412]], "c": false}]}, {"t": 105, "s": [{"i": [[-9.273, -4.924], [0, -25.427], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-21.259, 11.063]], "o": [[20.993, 11.148], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [0, -25.623], [9.156, -4.765]], "v": [[49.673, -12.669], [72.469, 74.128], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-126.658, 74.128], [-90.88, 15.21]], "c": false}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.172549024224, 0.647058844566, 0.552941203117, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [277.094, 311.376], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "shadow", "tt": 2, "tp": 17, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 37, "s": [5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 78, "s": [-3]}, {"t": 100, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [215, 380.142, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 415.142, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 15, "s": [98, 104, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 28, "s": [103, 98, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 42, "s": [98, 104, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 62, "s": [103, 98, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 83, "s": [98, 104, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 99, "s": [103, 98, 100]}, {"t": 115, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[-10.38, -6.73], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.29, 0], [0, 0], [0, 0], [-2.919, -0.574], [-2.958, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.38, -6.73], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [13.28, 0]], "v": [[69.22, -13.555], [50.44, -6.015], [48.41, 23.955], [19.27, 16.645], [0, 39.685], [-19.27, 16.645], [-48.41, 23.955], [-50.44, -6.015], [-69.22, -13.555], [-33.18, -24.185], [-27.08, -24.185], [-27.08, -39.625], [-14.757, -37.302], [-4.667, -35.468], [6.95, -35.009], [15.97, -36.538], [27.1, -39.685], [27.1, -24.185], [33.19, -24.185]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[-10.38, -6.73], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-12.738, 0.495], [0, 0], [0, 0], [-2.919, -0.574], [-2.958, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.38, -6.73], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [13.28, 0]], "v": [[69.485, -13.786], [50.44, -6.015], [48.41, 23.955], [19.27, 16.645], [0, 39.685], [-19.27, 16.645], [-48.41, 23.955], [-50.44, -6.015], [-69.22, -13.555], [-35.504, -23.55], [-29.144, -23.661], [-29.243, -45.841], [-14.757, -37.302], [-4.667, -35.468], [6.95, -35.009], [15.97, -36.538], [24.541, -42.548], [24.704, -24.876], [33.491, -25.256]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 21, "s": [{"i": [[-10.38, -6.73], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.13, -0.071], [0, 0], [0, 0], [-2.919, -0.574], [-2.958, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.914, -7.307], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [13.737, 0.379]], "v": [[70.481, -12.674], [51.188, -5.644], [49.033, 24.573], [20.268, 16.77], [0.624, 39.686], [-18.77, 16.276], [-47.536, 23.463], [-49.067, -6.383], [-67.846, -14.663], [-30.949, -24.961], [-24.527, -24.825], [-23.63, -45.708], [-14.757, -37.302], [-4.667, -35.468], [6.95, -35.009], [15.97, -36.538], [29.216, -41.06], [29.317, -23.573], [35.047, -23.465]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [{"i": [[-9.322, -5.644], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.36, -1.932], [0, 0], [0, 0], [-2.919, -0.574], [-2.959, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [13.908, -10.308], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [12.643, 1.783]], "v": [[73.545, -9.646], [54.003, -2.73], [50.586, 26.533], [22.045, 18.335], [1.612, 40.016], [-16.455, 16.407], [-45.755, 22.601], [-46.795, -7.143], [-65.739, -16.309], [-20.426, -28.464], [-14.1, -27.812], [-8.198, -43.216], [6.2, -43.665], [10.34, -39.212], [12.641, -35.443], [15.97, -36.538], [39.488, -38.482], [39.435, -20.413], [45.208, -19.413]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 35, "s": [{"i": [[-9.322, -5.644], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-14.587, -2.953], [0, 0], [0, 0], [-2.919, -0.574], [-2.959, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [1.467, -4.963], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [14.963, -10.416], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [-1.043, 5.552], [0, 0], [11.436, 2.204]], "v": [[76.989, -7.329], [56.877, -1.235], [52.068, 29.264], [23.751, 19.308], [2.925, 40.774], [-14.327, 16.475], [-43.712, 21.682], [-44.132, -8.115], [-63.026, -18.154], [-15.92, -29.778], [-9.702, -28.93], [-3.015, -49.742], [6.2, -43.665], [10.34, -39.212], [12.641, -35.443], [32.459, -45.142], [48.456, -37.256], [43.482, -19.08], [49.513, -17.977]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [{"i": [[-8.73, -5.711], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-14.587, -2.953], [0, 0], [0, 0], [-2.919, -0.574], [-2.959, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [1.467, -4.963], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [14.963, -10.416], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [-1.043, 5.552], [0, 0], [11.436, 2.204]], "v": [[78.893, -6.029], [57.308, 0.782], [52.914, 30.163], [24.704, 19.958], [3.211, 41.114], [-14.327, 16.475], [-43.351, 21.413], [-42.796, -8.941], [-61.812, -18.91], [-14.743, -30.23], [-8.818, -29.058], [-3.015, -49.742], [6.2, -43.665], [10.34, -39.212], [12.641, -35.443], [32.459, -45.142], [48.117, -37.471], [44.397, -18.849], [50.376, -17.622]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [{"i": [[-8.718, -5.333], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-15.429, -2.419], [0, 0], [0, 0], [-2.919, -0.574], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [1.467, -4.963], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [14.63, -10.692], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [-1.043, 5.552], [0, 0], [13.188, 2.107]], "v": [[76.084, -7.705], [55.456, -0.704], [51.187, 28.67], [23.05, 19.685], [2.45, 41.035], [-15.546, 17.156], [-44.666, 22.59], [-45.351, -7.45], [-64.293, -17.24], [-17.468, -29.464], [-11.487, -28.417], [-7.153, -51.527], [6.2, -43.665], [11.275, -41.468], [16.105, -38.577], [32.459, -45.142], [47.434, -38.35], [41.769, -19.617], [47.866, -18.52]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [{"i": [[-8.922, -5.138], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-17.463, -1.778], [0, 0], [0, 0], [-2.919, -0.574], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [1.467, -4.963], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [14.63, -10.692], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [-1.043, 5.552], [0, 0], [14.116, 2.032]], "v": [[69.405, -12.444], [49.601, -4.331], [47.441, 25.997], [19.209, 18.147], [-0.373, 40.982], [-19.552, 18.896], [-48.62, 26.469], [-50.528, -3.413], [-72.14, -12], [-25.392, -27.37], [-19.788, -26.692], [-18.648, -55.008], [6.2, -43.665], [11.275, -41.468], [16.105, -38.577], [32.459, -45.142], [38.669, -40.58], [33.612, -22.051], [40.124, -21.531]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 68, "s": [{"i": [[-9.304, -3.702], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-16.484, 0.355], [0, 0], [0, 0], [-2.884, 0.733], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [-0.437, -5.621], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [13.153, -12.732], [0, 0], [0, 0], [5.182, 0.935], [1.363, -0.346], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [0.496, 5.473], [0, 0], [13.59, -0.244]], "v": [[60.06, -19.555], [40.803, -9.281], [41.227, 20.752], [12.76, 16.551], [-4.426, 41.228], [-25.504, 21.099], [-53.807, 31.723], [-59.279, 2.104], [-82.414, -4.768], [-37.514, -23.855], [-31.146, -24.097], [-31.431, -41.931], [-18.227, -46.725], [-11.163, -49.381], [0.086, -51.956], [9.125, -51.824], [20.686, -49.862], [22.753, -25.745], [28.49, -25.943]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 77, "s": [{"i": [[-9.304, -3.702], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-16.484, 0.355], [0, 0], [0, 0], [-2.884, 0.733], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [-0.437, -5.621], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [12.37, -12.968], [0, 0], [0, 0], [5.182, 0.935], [1.363, -0.346], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [0.496, 5.473], [0, 0], [16.365, -0.389]], "v": [[57.371, -21.273], [38.214, -10.506], [39.773, 19.583], [9.966, 16.776], [-5.375, 42.398], [-27.959, 22.071], [-55.393, 32.982], [-61.893, 3.677], [-86.375, -1.68], [-41.111, -22.818], [-34.975, -23.437], [-36.66, -41.098], [-18.227, -46.725], [-11.163, -49.381], [-4.347, -51.814], [4.333, -52.064], [17.288, -50.155], [18.896, -26.912], [25.078, -27.149]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [{"i": [[-9.304, -3.702], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-16.647, 0.876], [0, 0], [0, 0], [-2.884, 0.733], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [-0.437, -5.621], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [12.053, -13.031], [0, 0], [0, 0], [5.182, 0.935], [1.363, -0.346], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [0.496, 5.473], [0, 0], [16.908, -0.562]], "v": [[57.371, -21.273], [38.214, -10.506], [39.773, 19.583], [9.966, 16.776], [-5.375, 42.398], [-27.959, 22.071], [-55.393, 32.982], [-61.893, 3.677], [-86.375, -1.68], [-41.753, -22.725], [-35.757, -23.11], [-37.535, -41.497], [-18.227, -46.725], [-11.163, -49.381], [-4.347, -51.814], [4.333, -52.064], [15.608, -49.745], [18.201, -27.062], [24.328, -27.482]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 91, "s": [{"i": [[-8.21, -3.593], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-16.692, 1.072], [0, 0], [0, 0], [-2.884, 0.733], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-1.095, 2.31], [-0.674, -5.648], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [12.013, -10.621], [0, 0], [0, 0], [5.182, 0.935], [1.363, -0.346], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [0.496, 5.473], [0, 0], [18.484, -0.352]], "v": [[60.877, -19.226], [42.169, -9.193], [42.363, 20.747], [12.967, 15.967], [-3.481, 40.949], [-25.188, 20.267], [-53.473, 30.172], [-58.113, 1.149], [-80.876, -6.217], [-39.13, -23.294], [-33.011, -23.615], [-34.609, -41.627], [-18.227, -46.725], [-11.163, -49.381], [-4.347, -51.814], [4.333, -52.064], [18.146, -49.078], [21.047, -26.265], [27.108, -26.501]], "c": true}]}, {"t": 105, "s": [{"i": [[-10.38, -6.73], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.29, 0], [0, 0], [0, 0], [-2.919, -0.574], [-2.958, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.38, -6.73], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [13.28, 0]], "v": [[69.22, -13.555], [50.44, -6.015], [48.41, 23.955], [19.27, 16.645], [0, 39.685], [-19.27, 16.645], [-48.41, 23.955], [-50.44, -6.015], [-69.22, -13.555], [-33.18, -24.185], [-27.08, -24.185], [-27.08, -39.625], [-14.757, -37.302], [-4.667, -35.468], [6.95, -35.009], [15.97, -36.538], [27.1, -39.685], [27.1, -24.185], [33.19, -24.185]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.922, 0.902, 0.937, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [0.922, 0.902, 0.937, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [249.903, 343.506], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.316, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0, 0], [0, -0.08]], "o": [[0, 0], [0, 0.08], [0, 0]], "v": [[0, -7.752], [0, 7.504], [0, 7.752]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [-0.002, -0.08]], "o": [[0, 0], [0.002, 0.08], [0, 0]], "v": [[-2.737, -8.316], [-2.375, 6.936], [-2.369, 7.184]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [{"i": [[0, 0], [0, 0], [0.011, -0.079]], "o": [[0, 0], [-0.011, 0.079], [0, 0]], "v": [[12.166, 6.02], [12.111, 11.508], [12.078, 11.753]], "c": false}]}, {"i": {"x": 0.257, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [{"i": [[0, 0], [0, 0], [0.014, -0.079]], "o": [[0, 0], [-0.014, 0.079], [0, 0]], "v": [[19.133, 3.535], [16.387, 12.858], [16.342, 13.101]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [{"i": [[0, 0], [0, 0], [0.015, -0.078]], "o": [[0, 0], [-0.015, 0.079], [0, 0]], "v": [[19.426, 3.412], [17.287, 13.142], [17.24, 13.385]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [{"i": [[0, 0], [0, 0], [0.013, -0.079]], "o": [[0, 0], [-0.013, 0.079], [0, 0]], "v": [[16.845, 5.523], [14.658, 12.312], [14.618, 12.556]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[0, 0], [0, 0], [0.007, -0.079]], "o": [[0, 0], [-0.007, 0.079], [0, 0]], "v": [[7.817, 5.258], [6.478, 9.73], [6.458, 9.975]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [{"i": [[0, 0], [0, 0], [-0.002, -0.08]], "o": [[0, 0], [0.002, 0.08], [0, 0]], "v": [[-5.501, -5.867], [-4.393, 6.298], [-4.386, 6.545]], "c": false}]}, {"i": {"x": 0.39, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0, 0], [0, 0], [-0.005, -0.08]], "o": [[0, 0], [0.005, 0.08], [0, 0]], "v": [[-9.255, -8.507], [-8.203, 5.095], [-8.188, 5.342]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [{"i": [[0, 0], [0, 0], [-0.006, -0.08]], "o": [[0, 0], [0.006, 0.08], [0, 0]], "v": [[-10.887, -11.82], [-8.824, 4.899], [-8.807, 5.146]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [{"i": [[0, 0], [0, 0], [-0.004, -0.08]], "o": [[0, 0], [0.004, 0.08], [0, 0]], "v": [[-7.66, -6.377], [-6.143, 5.69], [-6.131, 5.938]], "c": false}]}, {"t": 104.857421875, "s": [{"i": [[0, 0], [0, 0], [0, -0.08]], "o": [[0, 0], [0, 0.08], [0, 0]], "v": [[0, -7.752], [0, 7.504], [0, 7.752]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.316, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0, 0], [0, -0.08]], "o": [[0, 0], [0, 0.08], [0, 0]], "v": [[-54.18, -7.692], [-54.18, 7.504], [-54.18, 7.752]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [-0.002, -0.08]], "o": [[0, 0], [0.002, 0.08], [0, 0]], "v": [[-56.401, -9.968], [-56.54, 8.224], [-56.534, 8.471]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[0, 0], [0, 0], [0.002, -0.08]], "o": [[0, 0], [-0.002, 0.08], [0, 0]], "v": [[-51.285, -4.13], [-51.691, 6.969], [-51.698, 7.215]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [{"i": [[0, 0], [0, 0], [0.011, -0.079]], "o": [[0, 0], [-0.011, 0.079], [0, 0]], "v": [[-38.786, -1.718], [-41.343, 4.29], [-41.376, 4.535]], "c": false}]}, {"i": {"x": 0.257, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [{"i": [[0, 0], [0, 0], [0.014, -0.079]], "o": [[0, 0], [-0.014, 0.079], [0, 0]], "v": [[-33.514, -7.778], [-36.858, 3.129], [-36.902, 3.372]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [{"i": [[0, 0], [0, 0], [0.015, -0.078]], "o": [[0, 0], [-0.015, 0.079], [0, 0]], "v": [[-31.536, -13.037], [-35.913, 2.884], [-35.96, 3.128]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [{"i": [[0, 0], [0, 0], [0.013, -0.079]], "o": [[0, 0], [-0.013, 0.079], [0, 0]], "v": [[-36.545, -7.565], [-38.627, 3.467], [-38.668, 3.711]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[0, 0], [0, 0], [0.007, -0.079]], "o": [[0, 0], [-0.007, 0.079], [0, 0]], "v": [[-46.855, 1.792], [-47.073, 5.28], [-47.094, 5.525]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [{"i": [[0, 0], [0, 0], [-0.002, -0.08]], "o": [[0, 0], [0.002, 0.08], [0, 0]], "v": [[-58.54, 3.851], [-58.297, 7.69], [-58.291, 7.937]], "c": false}]}, {"i": {"x": 0.39, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0, 0], [0, 0], [-0.005, -0.08]], "o": [[0, 0], [0.005, 0.08], [0, 0]], "v": [[-62.707, 2.128], [-62.232, 8.535], [-62.216, 8.782]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [{"i": [[0, 0], [0, 0], [-0.006, -0.08]], "o": [[0, 0], [0.006, 0.08], [0, 0]], "v": [[-63.819, -1.006], [-62.873, 8.673], [-62.855, 8.92]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [{"i": [[0, 0], [0, 0], [-0.004, -0.08]], "o": [[0, 0], [0.004, 0.08], [0, 0]], "v": [[-60.948, -0.068], [-60.232, 8.318], [-60.22, 8.565]], "c": false}]}, {"t": 104.857421875, "s": [{"i": [[0, 0], [0, 0], [0, -0.08]], "o": [[0, 0], [0, 0.08], [0, 0]], "v": [[-54.18, -7.692], [-54.18, 7.504], [-54.18, 7.752]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 0, "s": [{"i": [[-9.188, -4.808], [0, -25.57], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-21.456, 10.998]], "o": [[21.187, 11.086], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [0, -25.768], [9.068, -4.648]], "v": [[36.825, 15.279], [72.469, 74.128], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-126.658, 74.128], [-90.518, 15.023]], "c": false}]}, {"i": {"x": 0.316, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[-9.273, -4.924], [0, -25.427], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-21.259, 11.063]], "o": [[20.993, 11.148], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [0, -25.623], [9.156, -4.765]], "v": [[37.179, 15.466], [72.469, 74.128], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-126.658, 74.128], [-90.88, 15.21]], "c": false}]}, {"i": {"x": 0.257, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[-9.388, -4.703], [-0.604, -25.42], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-20.567, 10.85]], "o": [[21.252, 10.646], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [0, -25.623], [8.858, -4.673]], "v": [[34.983, 14.012], [71.658, 71.818], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-126.658, 74.128], [-92.716, 16.271]], "c": false}]}, {"i": {"x": 0.39, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [{"i": [[-7.729, -5.507], [2.592, -25.294], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-23.898, 9.292]], "o": [[17.496, 12.467], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [4.252, -26.143], [10.293, -4.002]], "v": [[51.455, 26.124], [74.912, 84.327], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-122.178, 64.027], [-74.111, 5.288]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [{"i": [[-9.594, -4.266], [-1.771, -25.365], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0.28, 6.087], [0, 0], [-20.047, 11.783]], "o": [[21.719, 9.658], [0, 0], [0.28, 6.087], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [-1.177, -25.596], [8.634, -5.075]], "v": [[28.819, 10.252], [68.11, 66.313], [70.046, 90.759], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-128.871, 99.904], [-129.89, 77.727], [-98.642, 18.373]], "c": false}]}, {"t": 104.857421875, "s": [{"i": [[-9.273, -4.924], [0, -25.427], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-21.259, 11.063]], "o": [[20.993, 11.148], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [0, -25.623], [9.156, -4.765]], "v": [[37.179, 15.466], [72.469, 74.128], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-126.658, 74.128], [-90.88, 15.21]], "c": false}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.647, 0.553, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [277.094, 311.376], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "Body fill", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 37, "s": [5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 78, "s": [-3]}, {"t": 100, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [215, 380.142, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 415.142, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 15, "s": [98, 104, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 28, "s": [103, 98, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 42, "s": [98, 104, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 62, "s": [103, 98, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 83, "s": [98, 104, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 99, "s": [103, 98, 100]}, {"t": 115, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[-10.38, -6.73], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.29, 0], [0, 0], [0, 0], [-2.919, -0.574], [-2.958, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.38, -6.73], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [13.28, 0]], "v": [[69.22, -13.555], [50.44, -6.015], [48.41, 23.955], [19.27, 16.645], [0, 39.685], [-19.27, 16.645], [-48.41, 23.955], [-50.44, -6.015], [-69.22, -13.555], [-33.18, -24.185], [-27.08, -24.185], [-27.08, -39.625], [-14.757, -37.302], [-4.667, -35.468], [6.95, -35.009], [15.97, -36.538], [27.1, -39.685], [27.1, -24.185], [33.19, -24.185]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[-10.38, -6.73], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-12.738, 0.495], [0, 0], [0, 0], [-2.919, -0.574], [-2.958, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.38, -6.73], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [13.28, 0]], "v": [[69.485, -13.786], [50.44, -6.015], [48.41, 23.955], [19.27, 16.645], [0, 39.685], [-19.27, 16.645], [-48.41, 23.955], [-50.44, -6.015], [-69.22, -13.555], [-35.504, -23.55], [-29.144, -23.661], [-29.243, -45.841], [-14.757, -37.302], [-4.667, -35.468], [6.95, -35.009], [15.97, -36.538], [24.541, -42.548], [24.704, -24.876], [33.491, -25.256]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 21, "s": [{"i": [[-10.38, -6.73], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.13, -0.071], [0, 0], [0, 0], [-2.919, -0.574], [-2.958, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.914, -7.307], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [13.737, 0.379]], "v": [[70.481, -12.674], [51.188, -5.644], [49.033, 24.573], [20.268, 16.77], [0.624, 39.686], [-18.77, 16.276], [-47.536, 23.463], [-49.067, -6.383], [-67.846, -14.663], [-30.949, -24.961], [-24.527, -24.825], [-23.63, -45.708], [-14.757, -37.302], [-4.667, -35.468], [6.95, -35.009], [15.97, -36.538], [29.216, -41.06], [29.317, -23.573], [35.047, -23.465]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [{"i": [[-9.322, -5.644], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.36, -1.932], [0, 0], [0, 0], [-2.919, -0.574], [-2.959, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [13.908, -10.308], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [12.643, 1.783]], "v": [[73.545, -9.646], [54.003, -2.73], [50.586, 26.533], [22.045, 18.335], [1.612, 40.016], [-16.455, 16.407], [-45.755, 22.601], [-46.795, -7.143], [-65.739, -16.309], [-20.426, -28.464], [-14.1, -27.812], [-8.198, -43.216], [6.2, -43.665], [10.34, -39.212], [12.641, -35.443], [15.97, -36.538], [39.488, -38.482], [39.435, -20.413], [45.208, -19.413]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 35, "s": [{"i": [[-9.322, -5.644], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-14.587, -2.953], [0, 0], [0, 0], [-2.919, -0.574], [-2.959, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [1.467, -4.963], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [14.963, -10.416], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [-1.043, 5.552], [0, 0], [11.436, 2.204]], "v": [[76.989, -7.329], [56.877, -1.235], [52.068, 29.264], [23.751, 19.308], [2.925, 40.774], [-14.327, 16.475], [-43.712, 21.682], [-44.132, -8.115], [-63.026, -18.154], [-15.92, -29.778], [-9.702, -28.93], [-3.015, -49.742], [6.2, -43.665], [10.34, -39.212], [12.641, -35.443], [32.459, -45.142], [48.456, -37.256], [43.482, -19.08], [49.513, -17.977]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [{"i": [[-8.73, -5.711], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-14.587, -2.953], [0, 0], [0, 0], [-2.919, -0.574], [-2.959, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [1.467, -4.963], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [14.963, -10.416], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [-1.043, 5.552], [0, 0], [11.436, 2.204]], "v": [[78.893, -6.029], [57.308, 0.782], [52.914, 30.163], [24.704, 19.958], [3.211, 41.114], [-14.327, 16.475], [-43.351, 21.413], [-42.796, -8.941], [-61.812, -18.91], [-14.743, -30.23], [-8.818, -29.058], [-3.015, -49.742], [6.2, -43.665], [10.34, -39.212], [12.641, -35.443], [32.459, -45.142], [48.117, -37.471], [44.397, -18.849], [50.376, -17.622]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [{"i": [[-8.718, -5.333], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-15.429, -2.419], [0, 0], [0, 0], [-2.919, -0.574], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [1.467, -4.963], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [14.63, -10.692], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [-1.043, 5.552], [0, 0], [13.188, 2.107]], "v": [[76.084, -7.705], [55.456, -0.704], [51.187, 28.67], [23.05, 19.685], [2.45, 41.035], [-15.546, 17.156], [-44.666, 22.59], [-45.351, -7.45], [-64.293, -17.24], [-17.468, -29.464], [-11.487, -28.417], [-7.153, -51.527], [6.2, -43.665], [11.275, -41.468], [16.105, -38.577], [32.459, -45.142], [47.434, -38.35], [41.769, -19.617], [47.866, -18.52]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [{"i": [[-8.922, -5.138], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-17.463, -1.778], [0, 0], [0, 0], [-2.919, -0.574], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [1.467, -4.963], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [14.63, -10.692], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [-1.043, 5.552], [0, 0], [14.116, 2.032]], "v": [[69.405, -12.444], [49.601, -4.331], [47.441, 25.997], [19.209, 18.147], [-0.373, 40.982], [-19.552, 18.896], [-48.62, 26.469], [-50.528, -3.413], [-72.14, -12], [-25.392, -27.37], [-19.788, -26.692], [-18.648, -55.008], [6.2, -43.665], [11.275, -41.468], [16.105, -38.577], [32.459, -45.142], [38.669, -40.58], [33.612, -22.051], [40.124, -21.531]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 68, "s": [{"i": [[-9.304, -3.702], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-16.484, 0.355], [0, 0], [0, 0], [-2.884, 0.733], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [-0.437, -5.621], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [13.153, -12.732], [0, 0], [0, 0], [5.182, 0.935], [1.363, -0.346], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [0.496, 5.473], [0, 0], [13.59, -0.244]], "v": [[60.06, -19.555], [40.803, -9.281], [41.227, 20.752], [12.76, 16.551], [-4.426, 41.228], [-25.504, 21.099], [-53.807, 31.723], [-59.279, 2.104], [-82.414, -4.768], [-37.514, -23.855], [-31.146, -24.097], [-31.431, -41.931], [-18.227, -46.725], [-11.163, -49.381], [0.086, -51.956], [9.125, -51.824], [20.686, -49.862], [22.753, -25.745], [28.49, -25.943]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 77, "s": [{"i": [[-9.304, -3.702], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-16.484, 0.355], [0, 0], [0, 0], [-2.884, 0.733], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [-0.437, -5.621], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [12.37, -12.968], [0, 0], [0, 0], [5.182, 0.935], [1.363, -0.346], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [0.496, 5.473], [0, 0], [16.365, -0.389]], "v": [[57.371, -21.273], [38.214, -10.506], [39.773, 19.583], [9.966, 16.776], [-5.375, 42.398], [-27.959, 22.071], [-55.393, 32.982], [-61.893, 3.677], [-86.375, -1.68], [-41.111, -22.818], [-34.975, -23.437], [-36.66, -41.098], [-18.227, -46.725], [-11.163, -49.381], [-4.347, -51.814], [4.333, -52.064], [17.288, -50.155], [18.896, -26.912], [25.078, -27.149]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [{"i": [[-9.304, -3.702], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-16.647, 0.876], [0, 0], [0, 0], [-2.884, 0.733], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-3.96, 1.362], [-0.437, -5.621], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [12.053, -13.031], [0, 0], [0, 0], [5.182, 0.935], [1.363, -0.346], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [0.496, 5.473], [0, 0], [16.908, -0.562]], "v": [[57.371, -21.273], [38.214, -10.506], [39.773, 19.583], [9.966, 16.776], [-5.375, 42.398], [-27.959, 22.071], [-55.393, 32.982], [-61.893, 3.677], [-86.375, -1.68], [-41.753, -22.725], [-35.757, -23.11], [-37.535, -41.497], [-18.227, -46.725], [-11.163, -49.381], [-4.347, -51.814], [4.333, -52.064], [15.608, -49.745], [18.201, -27.062], [24.328, -27.482]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 91, "s": [{"i": [[-8.21, -3.593], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-16.692, 1.072], [0, 0], [0, 0], [-2.884, 0.733], [-0.821, -0.526], [-4.716, 0.363], [-3.317, 0.741], [-1.095, 2.31], [-0.674, -5.648], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [12.013, -10.621], [0, 0], [0, 0], [5.182, 0.935], [1.363, -0.346], [0.863, 0.614], [3.319, -0.255], [3.435, -0.767], [0.496, 5.473], [0, 0], [18.484, -0.352]], "v": [[60.877, -19.226], [42.169, -9.193], [42.363, 20.747], [12.967, 15.967], [-3.481, 40.949], [-25.188, 20.267], [-53.473, 30.172], [-58.113, 1.149], [-80.876, -6.217], [-39.13, -23.294], [-33.011, -23.615], [-34.609, -41.627], [-18.227, -46.725], [-11.163, -49.381], [-4.347, -51.814], [4.333, -52.064], [18.146, -49.078], [21.047, -26.265], [27.108, -26.501]], "c": true}]}, {"t": 105, "s": [{"i": [[-10.38, -6.73], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.29, 0], [0, 0], [0, 0], [-2.919, -0.574], [-2.958, -0.36], [-4.716, 0.363], [-3.317, 0.741], [-3.959, 1.362], [0, -5.167], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.38, -6.73], [0, 0], [0, 0], [5.182, 0.935], [5.852, 1.152], [3.264, 0.397], [3.319, -0.255], [3.435, -0.767], [0, 5.167], [0, 0], [13.28, 0]], "v": [[69.22, -13.555], [50.44, -6.015], [48.41, 23.955], [19.27, 16.645], [0, 39.685], [-19.27, 16.645], [-48.41, 23.955], [-50.44, -6.015], [-69.22, -13.555], [-33.18, -24.185], [-27.08, -24.185], [-27.08, -39.625], [-14.757, -37.302], [-4.667, -35.468], [6.95, -35.009], [15.97, -36.538], [27.1, -39.685], [27.1, -24.185], [33.19, -24.185]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.922, 0.902, 0.937, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [0.922, 0.902, 0.937, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [249.903, 343.506], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.316, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0, 0], [0, -0.08]], "o": [[0, 0], [0, 0.08], [0, 0]], "v": [[0, -7.752], [0, 7.504], [0, 7.752]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [-0.002, -0.08]], "o": [[0, 0], [0.002, 0.08], [0, 0]], "v": [[-2.737, -8.316], [-2.375, 6.936], [-2.369, 7.184]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [{"i": [[0, 0], [0, 0], [0.011, -0.079]], "o": [[0, 0], [-0.011, 0.079], [0, 0]], "v": [[12.166, 6.02], [12.111, 11.508], [12.078, 11.753]], "c": false}]}, {"i": {"x": 0.257, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [{"i": [[0, 0], [0, 0], [0.014, -0.079]], "o": [[0, 0], [-0.014, 0.079], [0, 0]], "v": [[19.133, 3.535], [16.387, 12.858], [16.342, 13.101]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [{"i": [[0, 0], [0, 0], [0.015, -0.078]], "o": [[0, 0], [-0.015, 0.079], [0, 0]], "v": [[19.426, 3.412], [17.287, 13.142], [17.24, 13.385]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [{"i": [[0, 0], [0, 0], [0.013, -0.079]], "o": [[0, 0], [-0.013, 0.079], [0, 0]], "v": [[16.845, 5.523], [14.658, 12.312], [14.618, 12.556]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[0, 0], [0, 0], [0.007, -0.079]], "o": [[0, 0], [-0.007, 0.079], [0, 0]], "v": [[7.817, 5.258], [6.478, 9.73], [6.458, 9.975]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [{"i": [[0, 0], [0, 0], [-0.002, -0.08]], "o": [[0, 0], [0.002, 0.08], [0, 0]], "v": [[-5.501, -5.867], [-4.393, 6.298], [-4.386, 6.545]], "c": false}]}, {"i": {"x": 0.39, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0, 0], [0, 0], [-0.005, -0.08]], "o": [[0, 0], [0.005, 0.08], [0, 0]], "v": [[-9.255, -8.507], [-8.203, 5.095], [-8.188, 5.342]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [{"i": [[0, 0], [0, 0], [-0.006, -0.08]], "o": [[0, 0], [0.006, 0.08], [0, 0]], "v": [[-10.887, -11.82], [-8.824, 4.899], [-8.807, 5.146]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [{"i": [[0, 0], [0, 0], [-0.004, -0.08]], "o": [[0, 0], [0.004, 0.08], [0, 0]], "v": [[-7.66, -6.377], [-6.143, 5.69], [-6.131, 5.938]], "c": false}]}, {"t": 104.857421875, "s": [{"i": [[0, 0], [0, 0], [0, -0.08]], "o": [[0, 0], [0, 0.08], [0, 0]], "v": [[0, -7.752], [0, 7.504], [0, 7.752]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.316, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0, 0], [0, -0.08]], "o": [[0, 0], [0, 0.08], [0, 0]], "v": [[-54.18, -7.692], [-54.18, 7.504], [-54.18, 7.752]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [-0.002, -0.08]], "o": [[0, 0], [0.002, 0.08], [0, 0]], "v": [[-56.401, -9.968], [-56.54, 8.224], [-56.534, 8.471]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[0, 0], [0, 0], [0.002, -0.08]], "o": [[0, 0], [-0.002, 0.08], [0, 0]], "v": [[-51.285, -4.13], [-51.691, 6.969], [-51.698, 7.215]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [{"i": [[0, 0], [0, 0], [0.011, -0.079]], "o": [[0, 0], [-0.011, 0.079], [0, 0]], "v": [[-38.786, -1.718], [-41.343, 4.29], [-41.376, 4.535]], "c": false}]}, {"i": {"x": 0.257, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [{"i": [[0, 0], [0, 0], [0.014, -0.079]], "o": [[0, 0], [-0.014, 0.079], [0, 0]], "v": [[-33.514, -7.778], [-36.858, 3.129], [-36.902, 3.372]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [{"i": [[0, 0], [0, 0], [0.015, -0.078]], "o": [[0, 0], [-0.015, 0.079], [0, 0]], "v": [[-31.536, -13.037], [-35.913, 2.884], [-35.96, 3.128]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [{"i": [[0, 0], [0, 0], [0.013, -0.079]], "o": [[0, 0], [-0.013, 0.079], [0, 0]], "v": [[-36.545, -7.565], [-38.627, 3.467], [-38.668, 3.711]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[0, 0], [0, 0], [0.007, -0.079]], "o": [[0, 0], [-0.007, 0.079], [0, 0]], "v": [[-46.855, 1.792], [-47.073, 5.28], [-47.094, 5.525]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [{"i": [[0, 0], [0, 0], [-0.002, -0.08]], "o": [[0, 0], [0.002, 0.08], [0, 0]], "v": [[-58.54, 3.851], [-58.297, 7.69], [-58.291, 7.937]], "c": false}]}, {"i": {"x": 0.39, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0, 0], [0, 0], [-0.005, -0.08]], "o": [[0, 0], [0.005, 0.08], [0, 0]], "v": [[-62.707, 2.128], [-62.232, 8.535], [-62.216, 8.782]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [{"i": [[0, 0], [0, 0], [-0.006, -0.08]], "o": [[0, 0], [0.006, 0.08], [0, 0]], "v": [[-63.819, -1.006], [-62.873, 8.673], [-62.855, 8.92]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [{"i": [[0, 0], [0, 0], [-0.004, -0.08]], "o": [[0, 0], [0.004, 0.08], [0, 0]], "v": [[-60.948, -0.068], [-60.232, 8.318], [-60.22, 8.565]], "c": false}]}, {"t": 104.857421875, "s": [{"i": [[0, 0], [0, 0], [0, -0.08]], "o": [[0, 0], [0, 0.08], [0, 0]], "v": [[-54.18, -7.692], [-54.18, 7.504], [-54.18, 7.752]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 0, "s": [{"i": [[-9.188, -4.808], [0, -25.57], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-21.456, 10.998]], "o": [[21.187, 11.086], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [0, -25.768], [9.068, -4.648]], "v": [[36.825, 15.279], [72.469, 74.128], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-126.658, 74.128], [-90.518, 15.023]], "c": false}]}, {"i": {"x": 0.316, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[-9.273, -4.924], [0, -25.427], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-21.259, 11.063]], "o": [[20.993, 11.148], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [0, -25.623], [9.156, -4.765]], "v": [[37.179, 15.466], [72.469, 74.128], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-126.658, 74.128], [-90.88, 15.21]], "c": false}]}, {"i": {"x": 0.257, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[-9.388, -4.703], [-0.604, -25.42], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-20.567, 10.85]], "o": [[21.252, 10.646], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [0, -25.623], [8.858, -4.673]], "v": [[34.983, 14.012], [71.658, 71.818], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-126.658, 74.128], [-92.716, 16.271]], "c": false}]}, {"i": {"x": 0.39, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [{"i": [[-7.729, -5.507], [2.592, -25.294], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-23.898, 9.292]], "o": [[17.496, 12.467], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [4.252, -26.143], [10.293, -4.002]], "v": [[51.455, 26.124], [74.912, 84.327], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-122.178, 64.027], [-74.111, 5.288]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 83, "s": [{"i": [[-9.594, -4.266], [-1.771, -25.365], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0.28, 6.087], [0, 0], [-20.047, 11.783]], "o": [[21.719, 9.658], [0, 0], [0.28, 6.087], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [-1.177, -25.596], [8.634, -5.075]], "v": [[28.819, 10.252], [68.11, 66.313], [70.046, 90.759], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-128.871, 99.904], [-129.89, 77.727], [-98.642, 18.373]], "c": false}]}, {"t": 104.857421875, "s": [{"i": [[-9.273, -4.924], [0, -25.427], [0, 0], [6.054, -0.696], [0, 0], [5.894, -2.463], [16.162, 0], [15.13, 6.294], [0, 0], [6.622, 0.768], [0, 0], [0, 6.094], [0, 0], [-21.259, 11.063]], "o": [[20.993, 11.148], [0, 0], [0, 6.094], [0, 0], [-6.35, 0.736], [-15.194, 6.358], [-16.074, 0], [0, 0], [-6.15, -2.559], [0, 0], [-6.054, -0.696], [0, 0], [0, -25.623], [9.156, -4.765]], "v": [[37.179, 15.466], [72.469, 74.128], [72.469, 96.328], [61.849, 108.243], [38.626, 110.93], [20.177, 115.752], [-27.334, 125.285], [-74.613, 115.848], [-74.773, 115.784], [-94.021, 110.778], [-116.037, 108.243], [-126.658, 96.328], [-126.658, 74.128], [-90.88, 15.21]], "c": false}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.647, 0.553, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2233-elf').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [277.094, 311.376], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 1, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.922, 0.902, 0.937], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.976, 0.788, 0.753], "ix": 1}}]}, {"ty": 5, "nm": "tertiary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.173, 0.647, 0.553], "ix": 1}}]}], "ip": 0, "op": 267, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-pinch", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 166, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-pinch", "dr": 156}], "props": {}}