import { useMemo } from "react";
import NextLink from "next/link";
import {
  TVariants, TSizes,
} from "@/common/components/atoms/link/types";
import { themeElements } from "@/common/theme/themeElements";
import { AnimatedBorderButton } from "../button/animatedBorderButton";
import { LinkProps } from "./types";

const getVariantClasses = (variant: keyof typeof TVariants) => themeElements.links[variant].style;
const getSizeClasses = (variant: keyof typeof TVariants, size: keyof typeof TSizes) => themeElements.links[variant].size[size];

export const Link = ({
  width = '', href, size = 'md', children, variant = 'gradient', className, ...rest
}: LinkProps) => {
  const computedClasses = useMemo(() => {
    const variantClasses = getVariantClasses(variant);
    const sizeClasses = getSizeClasses(variant, size);
    const extraClasses = className || '';
    return [variantClasses, sizeClasses, extraClasses].join(' ');
  }, [className, size, variant]);

  if (variant === 'outline') {
    const getBorderRadiusClass = themeElements.links["outline"].borderRadius[size];
    const sizeClasses = getSizeClasses(variant, size);

    return (
      <AnimatedBorderButton
        href={href}
        width={width}
        disabled={false}
        borderRadiusClass={getBorderRadiusClass}
        containerClassName={sizeClasses}
        className={className}
        {...rest}
      >
        {children}
      </AnimatedBorderButton>
    );
  }

  return (
    <NextLink href={href}
      className={`${width} ${computedClasses}`}
      {...rest}
    >
      {children}
    </NextLink>
  )
}
