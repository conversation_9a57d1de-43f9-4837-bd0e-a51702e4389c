{"env": {"browser": true, "es2021": true}, "extends": ["plugin:@typescript-eslint/recommended", "next/core-web-vitals"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"varsIgnorePattern": "ref"}], "@typescript-eslint/no-explicit-any": "warn", "react/no-unescaped-entities": 0, "space-before-blocks": "error", "block-spacing": "error", "space-before-function-paren": "error", "object-curly-newline": ["error", {"minProperties": 2, "consistent": true}], "object-curly-spacing": ["error", "always"], "object-property-newline": "error", "indent": ["error", 2], "comma-dangle": [2, "always-multiline"], "keyword-spacing": "error", "key-spacing": ["error", {"mode": "strict"}], "arrow-spacing": ["error", {"before": true, "after": true}], "space-in-parens": ["error", "never"], "no-console": ["error", {"allow": ["warn", "error"]}], "comma-spacing": ["error", {"before": false, "after": true}], "semi-spacing": "error", "space-infix-ops": "error", "curly": "error", "react/display-name": "off"}}