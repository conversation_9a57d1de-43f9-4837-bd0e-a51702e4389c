import { ReactNode } from "react";
import { Metada<PERSON> } from "next";

export const metadata: Metadata = {
  title: 'Privacy',
  alternates: {
    canonical: `https://dreamstartr.com/privacy`,
  },
  openGraph: {
    title: 'Privacy | DreamStartr',
    url: `https://dreamstartr.com/privacy`,
  },
  twitter: {
    title: 'Privacy | DreamStartr',
  },
}

export default function Layout ({
  children,
}: {
  children: ReactNode;
}) {
  return (
    children
  )
}
