/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[5].use[2]!./node_modules/@next/font/local/target.css?{"path":"common\\utils\\localFont.ts","import":"","arguments":[{"src":[{"path":"../../public/fonts/Latinotype-Trenda-Thin.otf","weight":"200"},{"path":"../../public/fonts/Latinotype-Trenda-Light.otf","weight":"300"},{"path":"../../public/fonts/Latinotype-Trenda-Regular.otf","weight":"400"},{"path":"../../public/fonts/Latinotype-Trenda-Semibold.otf","weight":"500"},{"path":"../../public/fonts/Latinotype-Trenda-Bold.otf","weight":"600"},{"path":"../../public/fonts/Latinotype-Trenda-Black.otf","weight":"700"},{"path":"../../public/fonts/Latinotype-Trenda-Heavy.otf","weight":"800"}],"variable":"--font-primaryFont"}],"variableName":"primaryFont"} ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@font-face {
font-family: '__primaryFont_c01c3f';
src: url(/_next/static/media/0b7af075f7660209-s.p.otf) format('opentype');
font-display: swap;
font-weight: 200;
}

@font-face {
font-family: '__primaryFont_c01c3f';
src: url(/_next/static/media/d484e9b17ca3f03b-s.p.otf) format('opentype');
font-display: swap;
font-weight: 300;
}

@font-face {
font-family: '__primaryFont_c01c3f';
src: url(/_next/static/media/24e9cb0c292a332d-s.p.otf) format('opentype');
font-display: swap;
font-weight: 400;
}

@font-face {
font-family: '__primaryFont_c01c3f';
src: url(/_next/static/media/01e656180b07cd7a-s.p.otf) format('opentype');
font-display: swap;
font-weight: 500;
}

@font-face {
font-family: '__primaryFont_c01c3f';
src: url(/_next/static/media/2ed056e8ed38ddfd-s.p.otf) format('opentype');
font-display: swap;
font-weight: 600;
}

@font-face {
font-family: '__primaryFont_c01c3f';
src: url(/_next/static/media/4376516b8ac60665-s.p.otf) format('opentype');
font-display: swap;
font-weight: 700;
}

@font-face {
font-family: '__primaryFont_c01c3f';
src: url(/_next/static/media/ae5858bd2755ee46-s.p.otf) format('opentype');
font-display: swap;
font-weight: 800;
}.__className_c01c3f {font-family: '__primaryFont_c01c3f'
}.__variable_c01c3f {--font-primaryFont: '__primaryFont_c01c3f'
}

/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[5].use[2]!./node_modules/@next/font/local/target.css?{"path":"common\\utils\\localFont.ts","import":"","arguments":[{"src":[{"path":"../../public/fonts/Chillax-Extralight.ttf","weight":"200"},{"path":"../../public/fonts/Chillax-Light.ttf","weight":"300"},{"path":"../../public/fonts/Chillax-Regular.ttf","weight":"400"},{"path":"../../public/fonts/Chillax-Medium.ttf","weight":"500"},{"path":"../../public/fonts/Chillax-Semibold.ttf","weight":"600"},{"path":"../../public/fonts/Chillax-Bold.ttf","weight":"700"},{"path":"../../public/fonts/Chillax-Variable.ttf","weight":"800"}],"variable":"--font-secondaryFont"}],"variableName":"secondaryFont"} ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@font-face {
font-family: '__secondaryFont_3ac742';
src: url(/_next/static/media/cf8997a7a33528e6-s.p.ttf) format('truetype');
font-display: swap;
font-weight: 200;
}

@font-face {
font-family: '__secondaryFont_3ac742';
src: url(/_next/static/media/6ea76a8048ccb40d-s.p.ttf) format('truetype');
font-display: swap;
font-weight: 300;
}

@font-face {
font-family: '__secondaryFont_3ac742';
src: url(/_next/static/media/acbf4c03ae747bfa-s.p.ttf) format('truetype');
font-display: swap;
font-weight: 400;
}

@font-face {
font-family: '__secondaryFont_3ac742';
src: url(/_next/static/media/92d34b2f07349df3-s.p.ttf) format('truetype');
font-display: swap;
font-weight: 500;
}

@font-face {
font-family: '__secondaryFont_3ac742';
src: url(/_next/static/media/b20fa509c3f37e60-s.p.ttf) format('truetype');
font-display: swap;
font-weight: 600;
}

@font-face {
font-family: '__secondaryFont_3ac742';
src: url(/_next/static/media/aebcf9a493e7a0d3-s.p.ttf) format('truetype');
font-display: swap;
font-weight: 700;
}

@font-face {
font-family: '__secondaryFont_3ac742';
src: url(/_next/static/media/ba72c5d62cc86985-s.p.ttf) format('truetype');
font-display: swap;
font-weight: 800;
}.__className_3ac742 {font-family: '__secondaryFont_3ac742'
}.__variable_3ac742 {--font-secondaryFont: '__secondaryFont_3ac742'
}

/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/*
! tailwindcss v3.4.1 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden] {
  display: none;
}

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}
.container {
  width: 100%;
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
.pointer-events-none {
  pointer-events: none;
}
.visible {
  visibility: visible;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: 0px;
}
.inset-0\.5 {
  inset: 0.125rem;
}
.-inset-x-10 {
  left: -2.5rem;
  right: -2.5rem;
}
.inset-x-0 {
  left: 0px;
  right: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.-left-0 {
  left: -0px;
}
.-left-0\.5 {
  left: -0.125rem;
}
.-left-1\/2 {
  left: -50%;
}
.-top-1\/2 {
  top: -50%;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-10 {
  bottom: 2.5rem;
}
.bottom-4 {
  bottom: 1rem;
}
.left-0 {
  left: 0px;
}
.left-1 {
  left: 0.25rem;
}
.left-1\/2 {
  left: 50%;
}
.left-3 {
  left: 0.75rem;
}
.left-4 {
  left: 1rem;
}
.left-\[calc\(50\%-var\(--size\)\/2\)\] {
  left: calc(50% - var(--size) / 2);
}
.right-0 {
  right: 0px;
}
.right-1 {
  right: 0.25rem;
}
.right-10 {
  right: 2.5rem;
}
.right-2 {
  right: 0.5rem;
}
.right-4 {
  right: 1rem;
}
.right-6 {
  right: 1.5rem;
}
.top-0 {
  top: 0px;
}
.top-1\/2 {
  top: 50%;
}
.top-16 {
  top: 4rem;
}
.top-2 {
  top: 0.5rem;
}
.top-20 {
  top: 5rem;
}
.top-4 {
  top: 1rem;
}
.top-40 {
  top: 10rem;
}
.top-\[11\%\] {
  top: 11%;
}
.top-\[calc\(100\%\+2px\)\] {
  top: calc(100% + 2px);
}
.top-\[calc\(50\%-var\(--size\)\/2\)\] {
  top: calc(50% - var(--size) / 2);
}
.isolate {
  isolation: isolate;
}
.-z-10 {
  z-index: -10;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[-9999\] {
  z-index: -9999;
}
.z-\[100\] {
  z-index: 100;
}
.z-\[200\] {
  z-index: 200;
}
.z-\[70\] {
  z-index: 70;
}
.z-\[9999\] {
  z-index: 9999;
}
.m-auto {
  margin: auto;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.-ml-4 {
  margin-left: -1rem;
}
.-mt-4 {
  margin-top: -1rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-6 {
  margin-left: 1.5rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mt-0 {
  margin-top: 0px;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-10 {
  margin-top: 2.5rem;
}
.mt-12 {
  margin-top: 3rem;
}
.mt-16 {
  margin-top: 4rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-40 {
  margin-top: 10rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-auto {
  margin-top: auto;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.aspect-\[1\/1\] {
  aspect-ratio: 1/1;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.size-1\/2 {
  width: 50%;
  height: 50%;
}
.size-full {
  width: 100%;
  height: 100%;
}
.h-0 {
  height: 0px;
}
.h-0\.5 {
  height: 0.125rem;
}
.h-1 {
  height: 0.25rem;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\/3 {
  height: 66.666667%;
}
.h-20 {
  height: 5rem;
}
.h-3 {
  height: 0.75rem;
}
.h-32 {
  height: 8rem;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-96 {
  height: 24rem;
}
.h-\[100dvh\] {
  height: 100dvh;
}
.h-\[140px\] {
  height: 140px;
}
.h-\[200px\] {
  height: 200px;
}
.h-\[220px\] {
  height: 220px;
}
.h-\[260px\] {
  height: 260px;
}
.h-\[300px\] {
  height: 300px;
}
.h-\[52px\] {
  height: 52px;
}
.h-\[600px\] {
  height: 600px;
}
.h-\[calc\(100\%px\)\] {
  height: calc(100%px);
}
.h-\[var\(--size\)\] {
  height: var(--size);
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.h-screen {
  height: 100vh;
}
.max-h-10 {
  max-height: 2.5rem;
}
.max-h-16 {
  max-height: 4rem;
}
.max-h-60 {
  max-height: 15rem;
}
.max-h-\[40px\] {
  max-height: 40px;
}
.max-h-\[420px\] {
  max-height: 420px;
}
.max-h-\[70vh\] {
  max-height: 70vh;
}
.min-h-screen {
  min-height: 100vh;
}
.\!w-full {
  width: 100% !important;
}
.w-0 {
  width: 0px;
}
.w-0\.5 {
  width: 0.125rem;
}
.w-1 {
  width: 0.25rem;
}
.w-1\/2 {
  width: 50%;
}
.w-1\/3 {
  width: 33.333333%;
}
.w-1\/4 {
  width: 25%;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\/3 {
  width: 66.666667%;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-3 {
  width: 0.75rem;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-56 {
  width: 14rem;
}
.w-6 {
  width: 1.5rem;
}
.w-60 {
  width: 15rem;
}
.w-8 {
  width: 2rem;
}
.w-9 {
  width: 2.25rem;
}
.w-\[220px\] {
  width: 220px;
}
.w-\[2px\] {
  width: 2px;
}
.w-\[600px\] {
  width: 600px;
}
.w-\[calc\(100\%-12px\)\] {
  width: calc(100% - 12px);
}
.w-\[calc\(100\%-5rem\)\] {
  width: calc(100% - 5rem);
}
.w-\[var\(--size\)\] {
  width: var(--size);
}
.w-auto {
  width: auto;
}
.w-full {
  width: 100%;
}
.w-min {
  width: -moz-min-content;
  width: min-content;
}
.w-px {
  width: 1px;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-6 {
  min-width: 1.5rem;
}
.\!max-w-\[1200px\] {
  max-width: 1200px !important;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-\[100vw\] {
  max-width: 100vw;
}
.max-w-\[1200px\] {
  max-width: 1200px;
}
.max-w-\[125px\] {
  max-width: 125px;
}
.max-w-\[220px\] {
  max-width: 220px;
}
.max-w-\[420px\] {
  max-width: 420px;
}
.max-w-\[80px\] {
  max-width: 80px;
}
.max-w-\[calc\(100\%-40px\)\] {
  max-width: calc(100% - 40px);
}
.max-w-full {
  max-width: 100%;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xl {
  max-width: 36rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-none {
  flex: none;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.grow {
  flex-grow: 1;
}
.grow-0 {
  flex-grow: 0;
}
.basis-full {
  flex-basis: 100%;
}
.origin-top-left {
  transform-origin: top left;
}
.origin-top-right {
  transform-origin: top right;
}
.-translate-x-1 {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-50 {
  --tw-scale-x: .5;
  --tw-scale-y: .5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-75 {
  --tw-scale-x: .75;
  --tw-scale-y: .75;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform-gpu {
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform-none {
  transform: none;
}
@keyframes animatedText {

  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}
@keyframes pulse {

  0% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.85;
  }
}
.animate-animatedText {
  animation: animatedText 3s ease-in-out infinite, pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes animatedText {

  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}
@keyframes pulse {

  0% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.85;
  }
}
.animate-dreamyText {
  animation: animatedText 2s ease-in-out infinite 1s, pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite 1s;
}
@keyframes moveInCircle {

  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
.animate-fifth {
  animation: moveInCircle 20s ease infinite;
}
@keyframes moveVertical {

  0% {
    transform: translateY(-50%);
  }

  50% {
    transform: translateY(50%);
  }

  100% {
    transform: translateY(-50%);
  }
}
.animate-first {
  animation: moveVertical 30s ease infinite;
}
@keyframes moveHorizontal {

  0% {
    transform: translateX(-50%) translateY(-10%);
  }

  50% {
    transform: translateX(50%) translateY(10%);
  }

  100% {
    transform: translateX(-50%) translateY(-10%);
  }
}
.animate-fourth {
  animation: moveHorizontal 40s ease infinite;
}
@keyframes glowTransition {

  0% {
    text-shadow: 0 0 15px #7E5EF2;
  }

  50% {
    text-shadow: 0 0 15px #FFFFFF20;
  }

  100% {
    text-shadow: 0 0 15px #7E5EF2;
  }
}
.animate-glowTransition {
  animation: glowTransition 4s ease-in-out infinite;
}
@keyframes ldsLoader {

  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
.animate-ldsLoader {
  animation: ldsLoader 1s cubic-bezier(0.5, 0, 0.5, 1) infinite;
}
@keyframes animatedText {

  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}
.animate-progressAnimation {
  animation: animatedText 6s ease-in-out infinite;
}
@keyframes pulse {

  0% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.85;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes rotatingGradient {

  0% {
    background: #a855f7;
  }

  50% {
    background: #6366f1;
  }

  100% {
    background: #a855f7;
  }
}
.animate-rotatingGradient {
  animation: rotatingGradient 2s cubic-bezier(0.5, 0, 0.5, 1) infinite alternate;
}
@keyframes moveInCircle {

  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
.animate-second {
  animation: moveInCircle 20s reverse infinite;
}
@keyframes moveInCircle {

  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
.animate-third {
  animation: moveInCircle 40s linear infinite;
}
.cursor-pointer {
  cursor: pointer;
}
.resize-none {
  resize: none;
}
.resize {
  resize: both;
}
.list-disc {
  list-style-type: disc;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.flex-row {
  flex-direction: row;
}
.flex-col {
  flex-direction: column;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.\!justify-start {
  justify-content: flex-start !important;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-12 {
  gap: 3rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-x-2 {
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}
.gap-y-1 {
  row-gap: 0.25rem;
}
.gap-y-2 {
  row-gap: 0.5rem;
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.self-start {
  align-self: flex-start;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-clip {
  overflow: clip;
}
.overflow-visible {
  overflow: visible;
}
.overflow-y-auto {
  overflow-y: auto;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-3xl {
  border-radius: 1.5rem;
}
.rounded-\[10px\] {
  border-radius: 10px;
}
.rounded-\[inherit\] {
  border-radius: inherit;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-\[3px\] {
  border-width: 3px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-solid {
  border-style: solid;
}
.border-none {
  border-style: none;
}
.\!border-tulip {
  --tw-border-opacity: 1 !important;
  border-color: rgb(254 137 137 / var(--tw-border-opacity)) !important;
}
.\!border-white\/10 {
  border-color: rgb(255 255 255 / 0.1) !important;
}
.border-gray-200\/5 {
  border-color: rgb(229 231 235 / 0.05);
}
.border-light-yellow\/15 {
  border-color: rgb(230 214 155 / 0.15);
}
.border-light-yellow\/30 {
  border-color: rgb(230 214 155 / 0.3);
}
.border-light-yellow\/5 {
  border-color: rgb(230 214 155 / 0.05);
}
.border-violets-are-blue\/10 {
  border-color: rgb(126 94 242 / 0.1);
}
.border-violets-are-blue\/15 {
  border-color: rgb(126 94 242 / 0.15);
}
.border-white\/10 {
  border-color: rgb(255 255 255 / 0.1);
}
.border-white\/15 {
  border-color: rgb(255 255 255 / 0.15);
}
.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}
.border-white\/5 {
  border-color: rgb(255 255 255 / 0.05);
}
.border-yellow-500\/10 {
  border-color: rgb(234 179 8 / 0.1);
}
.border-violets-are-blue\/30 {
  border-color: rgb(126 94 242 / 0.3);
}
.border-b-light-yellow {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(230 214 155 / var(--tw-border-opacity));
}
.bg-\[\#f6f6f6\] {
  --tw-bg-opacity: 1;
  background-color: rgb(246 246 246 / var(--tw-bg-opacity));
}
.bg-amber-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 230 138 / var(--tw-bg-opacity));
}
.bg-amber-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity));
}
.bg-amber-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(180 83 9 / var(--tw-bg-opacity));
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}
.bg-black\/20 {
  background-color: rgb(0 0 0 / 0.2);
}
.bg-cyan-500\/20 {
  background-color: rgb(6 182 212 / 0.2);
}
.bg-eerie-black {
  --tw-bg-opacity: 1;
  background-color: rgb(14 17 17 / var(--tw-bg-opacity));
}
.bg-eerie-black\/50 {
  background-color: rgb(14 17 17 / 0.5);
}
.bg-eerie-black\/75 {
  background-color: rgb(14 17 17 / 0.75);
}
.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}
.bg-green-500\/20 {
  background-color: rgb(34 197 94 / 0.2);
}
.bg-indigo-500\/20 {
  background-color: rgb(99 102 241 / 0.2);
}
.bg-light-yellow {
  --tw-bg-opacity: 1;
  background-color: rgb(230 214 155 / var(--tw-bg-opacity));
}
.bg-light-yellow\/20 {
  background-color: rgb(230 214 155 / 0.2);
}
.bg-light-yellow\/70 {
  background-color: rgb(230 214 155 / 0.7);
}
.bg-neutral-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
}
.bg-neutral-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity));
}
.bg-neutral-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(38 38 38 / var(--tw-bg-opacity));
}
.bg-neutral-800\/50 {
  background-color: rgb(38 38 38 / 0.5);
}
.bg-neutral-900\/50 {
  background-color: rgb(23 23 23 / 0.5);
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}
.bg-transparent {
  background-color: transparent;
}
.bg-tulip {
  --tw-bg-opacity: 1;
  background-color: rgb(254 137 137 / var(--tw-bg-opacity));
}
.bg-violets-are-blue\/10 {
  background-color: rgb(126 94 242 / 0.1);
}
.bg-violets-are-blue\/20 {
  background-color: rgb(126 94 242 / 0.2);
}
.bg-violets-are-blue\/5 {
  background-color: rgb(126 94 242 / 0.05);
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\/5 {
  background-color: rgb(255 255 255 / 0.05);
}
.bg-yellow-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 224 71 / var(--tw-bg-opacity));
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}
.bg-yellow-500\/10 {
  background-color: rgb(234 179 8 / 0.1);
}
.bg-yellow-500\/5 {
  background-color: rgb(234 179 8 / 0.05);
}
.bg-yellow-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity));
}
.bg-opacity-20 {
  --tw-bg-opacity: 0.2;
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.bg-\[linear-gradient\(to_bottom\2c var\(--tw-gradient-stops\)\)\] {
  background-image: linear-gradient(to bottom,var(--tw-gradient-stops));
}
.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-bl {
  background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.bg-gradient-to-tr {
  background-image: linear-gradient(to top right, var(--tw-gradient-stops));
}
.from-\[\#66E1FF\] {
  --tw-gradient-from: #66E1FF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(102 225 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#B2B2B2\] {
  --tw-gradient-from: #B2B2B2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(178 178 178 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#FF808C\] {
  --tw-gradient-from: #FF808C var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 128 140 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#FFBFC5\] {
  --tw-gradient-from: #FFBFC5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 191 197 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-eerie-black {
  --tw-gradient-from: #0e1111 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(14 17 17 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-eerie-black\/95 {
  --tw-gradient-from: rgb(14 17 17 / 0.95) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(14 17 17 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-han-purple {
  --tw-gradient-from: #5606FF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(86 6 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-han-purple\/10 {
  --tw-gradient-from: rgb(86 6 255 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(86 6 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-500 {
  --tw-gradient-from: #6366f1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-light-cobalt-blue {
  --tw-gradient-from: #8f94fb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(143 148 251 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-light-yellow {
  --tw-gradient-from: #e6d69b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(230 214 155 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500 {
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-tulip {
  --tw-gradient-from: #FE8989 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(254 137 137 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-tulip\/10 {
  --tw-gradient-from: rgb(254 137 137 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(254 137 137 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-violet-500 {
  --tw-gradient-from: #8b5cf6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 92 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-violets-are-blue {
  --tw-gradient-from: #7E5EF2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(126 94 242 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-violets-are-blue\/30 {
  --tw-gradient-from: rgb(126 94 242 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(126 94 242 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-200\/5 {
  --tw-gradient-from: rgb(254 240 138 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(254 240 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-300 {
  --tw-gradient-from: #fde047 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(253 224 71 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-violets-are-blue\/20 {
  --tw-gradient-from: rgb(126 94 242 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(126 94 242 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[0\%\] {
  --tw-gradient-from-position: 0%;
}
.via-han-purple\/20 {
  --tw-gradient-to: rgb(86 6 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(86 6 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-indigo-500 {
  --tw-gradient-to: rgb(99 102 241 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #6366f1 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-neutral-200 {
  --tw-gradient-to: rgb(229 229 229 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #e5e5e5 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-pink-500 {
  --tw-gradient-to: rgb(236 72 153 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #ec4899 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-purple-500 {
  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #a855f7 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-space-cadet\/30 {
  --tw-gradient-to: rgb(42 35 89 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(42 35 89 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-transparent {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-yellow-100\/10 {
  --tw-gradient-to: rgb(254 249 195 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(254 249 195 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-yellow-100\/20 {
  --tw-gradient-to: rgb(254 249 195 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(254 249 195 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-yellow-200 {
  --tw-gradient-to: rgb(254 240 138 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fef08a var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-\[10\%\] {
  --tw-gradient-via-position: 10%;
}
.to-\[\#191919\] {
  --tw-gradient-to: #191919 var(--tw-gradient-to-position);
}
.to-\[\#C2F3FF\] {
  --tw-gradient-to: #C2F3FF var(--tw-gradient-to-position);
}
.to-eerie-black {
  --tw-gradient-to: #0e1111 var(--tw-gradient-to-position);
}
.to-eerie-black\/90 {
  --tw-gradient-to: rgb(14 17 17 / 0.9) var(--tw-gradient-to-position);
}
.to-han-purple {
  --tw-gradient-to: #5606FF var(--tw-gradient-to-position);
}
.to-indigo-500 {
  --tw-gradient-to: #6366f1 var(--tw-gradient-to-position);
}
.to-light-yellow {
  --tw-gradient-to: #e6d69b var(--tw-gradient-to-position);
}
.to-purple-500 {
  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);
}
.to-red-500 {
  --tw-gradient-to: #ef4444 var(--tw-gradient-to-position);
}
.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.to-tulip {
  --tw-gradient-to: #FE8989 var(--tw-gradient-to-position);
}
.to-tulip\/30 {
  --tw-gradient-to: rgb(254 137 137 / 0.3) var(--tw-gradient-to-position);
}
.to-violets-are-blue {
  --tw-gradient-to: #7E5EF2 var(--tw-gradient-to-position);
}
.to-yellow-200 {
  --tw-gradient-to: #fef08a var(--tw-gradient-to-position);
}
.to-yellow-200\/5 {
  --tw-gradient-to: rgb(254 240 138 / 0.05) var(--tw-gradient-to-position);
}
.to-yellow-600 {
  --tw-gradient-to: #ca8a04 var(--tw-gradient-to-position);
}
.to-han-purple\/20 {
  --tw-gradient-to: rgb(86 6 255 / 0.2) var(--tw-gradient-to-position);
}
.to-\[99\%\] {
  --tw-gradient-to-position: 99%;
}
.bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}
.bg-repeat {
  background-repeat: repeat;
}
.fill-black {
  fill: #000;
}
.fill-white {
  fill: #fff;
}
.fill-zinc-800 {
  fill: #27272a;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.\!p-2 {
  padding: 0.5rem !important;
}
.p-0 {
  padding: 0px;
}
.p-0\.5 {
  padding: 0.125rem;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\.5 {
  padding: 0.375rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-px {
  padding: 1px;
}
.p-8 {
  padding: 2rem;
}
.\!px-0 {
  padding-left: 0px !important;
  padding-right: 0px !important;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.py-\[3px\] {
  padding-top: 3px;
  padding-bottom: 3px;
}
.py-\[7px\] {
  padding-top: 7px;
  padding-bottom: 7px;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pl-0 {
  padding-left: 0px;
}
.pl-16 {
  padding-left: 4rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pl-6 {
  padding-left: 1.5rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-20 {
  padding-right: 5rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-16 {
  padding-top: 4rem;
}
.pt-20 {
  padding-top: 5rem;
}
.pt-24 {
  padding-top: 6rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-44 {
  padding-top: 11rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.\!text-base {
  font-size: 1rem !important;
  line-height: 1.5rem !important;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl {
  font-size: 3rem;
  line-height: 1;
}
.text-\[22px\] {
  font-size: 22px;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.\!font-medium {
  font-weight: 500 !important;
}
.font-bold {
  font-weight: 700;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.italic {
  font-style: italic;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}
.text-cyan-400 {
  --tw-text-opacity: 1;
  color: rgb(34 211 238 / var(--tw-text-opacity));
}
.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}
.text-green-300 {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity));
}
.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity));
}
.text-indigo-400 {
  --tw-text-opacity: 1;
  color: rgb(129 140 248 / var(--tw-text-opacity));
}
.text-light-yellow {
  --tw-text-opacity: 1;
  color: rgb(230 214 155 / var(--tw-text-opacity));
}
.text-neutral-200 {
  --tw-text-opacity: 1;
  color: rgb(229 229 229 / var(--tw-text-opacity));
}
.text-neutral-300 {
  --tw-text-opacity: 1;
  color: rgb(212 212 212 / var(--tw-text-opacity));
}
.text-neutral-400 {
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity));
}
.text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity));
}
.text-transparent {
  color: transparent;
}
.text-tulip {
  --tw-text-opacity: 1;
  color: rgb(254 137 137 / var(--tw-text-opacity));
}
.text-violets-are-blue {
  --tw-text-opacity: 1;
  color: rgb(126 94 242 / var(--tw-text-opacity));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.text-white\/50 {
  color: rgb(255 255 255 / 0.5);
}
.text-white\/70 {
  color: rgb(255 255 255 / 0.7);
}
.text-white\/90 {
  color: rgb(255 255 255 / 0.9);
}
.text-zinc-500 {
  --tw-text-opacity: 1;
  color: rgb(113 113 122 / var(--tw-text-opacity));
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.opacity-25 {
  opacity: 0.25;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-80 {
  opacity: 0.8;
}
.opacity-\[\.03\] {
  opacity: .03;
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-black {
  --tw-shadow-color: #000;
  --tw-shadow: var(--tw-shadow-colored);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-white\/15 {
  --tw-ring-color: rgb(255 255 255 / 0.15);
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-2xl {
  --tw-blur: blur(40px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[100px\] {
  --tw-blur: blur(100px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[120px\] {
  --tw-blur: blur(120px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[20px\] {
  --tw-blur: blur(20px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[80px\] {
  --tw-blur: blur(80px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-lg {
  --tw-blur: blur(16px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-sm {
  --tw-blur: blur(4px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-2xl {
  --tw-backdrop-blur: blur(40px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-lg {
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-100 {
  transition-duration: 100ms;
}
.duration-150 {
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.duration-75 {
  transition-duration: 75ms;
}
.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.text-shadow-glow {
  text-shadow: 0 0 10px rgba(249, 213, 110, 0.7);
}
.\[appearance\:textfield\] {
  -webkit-appearance: textfield;
     -moz-appearance: textfield;
          appearance: textfield;
}
.\[background\:radial-gradient\(circle_at_center\2c _rgba\(var\(--fifth-color\)\2c _0\.8\)_0\2c _rgba\(var\(--fifth-color\)\2c _0\)_50\%\)_no-repeat\] {
  background: radial-gradient(circle at center, rgba(var(--fifth-color), 0.8) 0, rgba(var(--fifth-color), 0) 50%) no-repeat;
}
.\[background\:radial-gradient\(circle_at_center\2c _rgba\(var\(--fourth-color\)\2c _0\.8\)_0\2c _rgba\(var\(--fourth-color\)\2c _0\)_50\%\)_no-repeat\] {
  background: radial-gradient(circle at center, rgba(var(--fourth-color), 0.8) 0, rgba(var(--fourth-color), 0) 50%) no-repeat;
}
.\[background\:radial-gradient\(circle_at_center\2c _rgba\(var\(--pointer-color\)\2c _0\.8\)_0\2c _rgba\(var\(--pointer-color\)\2c _0\)_50\%\)_no-repeat\] {
  background: radial-gradient(circle at center, rgba(var(--pointer-color), 0.8) 0, rgba(var(--pointer-color), 0) 50%) no-repeat;
}
.\[background\:radial-gradient\(circle_at_center\2c _rgba\(var\(--second-color\)\2c _0\.8\)_0\2c _rgba\(var\(--second-color\)\2c _0\)_50\%\)_no-repeat\] {
  background: radial-gradient(circle at center, rgba(var(--second-color), 0.8) 0, rgba(var(--second-color), 0) 50%) no-repeat;
}
.\[background\:radial-gradient\(circle_at_center\2c _rgba\(var\(--third-color\)\2c _0\.8\)_0\2c _rgba\(var\(--third-color\)\2c _0\)_50\%\)_no-repeat\] {
  background: radial-gradient(circle at center, rgba(var(--third-color), 0.8) 0, rgba(var(--third-color), 0) 50%) no-repeat;
}
.\[background\:radial-gradient\(circle_at_center\2c _var\(--first-color\)_0\2c _var\(--first-color\)_50\%\)_no-repeat\] {
  background: radial-gradient(circle at center, var(--first-color) 0, var(--first-color) 50%) no-repeat;
}
.\[box-shadow\:0_0_0_1px_rgba\(0\2c 0\2c 0\2c \.03\)\2c 0_2px_4px_rgba\(0\2c 0\2c 0\2c \.05\)\2c 0_12px_24px_rgba\(0\2c 0\2c 0\2c \.05\)\] {
  box-shadow: 0 0 0 1px rgba(0,0,0,.03),0 2px 4px rgba(0,0,0,.05),0 12px 24px rgba(0,0,0,.05);
}
.\[contain\:layout_paint_size\] {
  contain: layout paint size;
}
.\[filter\:url\(\#blurMe\)_blur\(40px\)\] {
  filter: url(#blurMe) blur(40px);
}
.\[mask-image\:linear-gradient\(to_bottom\2c transparent_0\%\2c black_10\%\2c black_90\%\2c transparent_100\%\)\] {
  -webkit-mask-image: linear-gradient(to bottom,transparent 0%,black 10%,black 90%,transparent 100%);
          mask-image: linear-gradient(to bottom,transparent 0%,black 10%,black 90%,transparent 100%);
}
.\[mask-image\:radial-gradient\(900px_at_center\2c transparent_30\%\2c white\)\] {
  -webkit-mask-image: radial-gradient(900px at center,transparent 30%,white);
          mask-image: radial-gradient(900px at center,transparent 30%,white);
}
.\[mix-blend-mode\:var\(--blending-value\)\] {
  mix-blend-mode: var(--blending-value);
}
.\[perspective\:1000px\] {
  perspective: 1000px;
}
.\[perspective\:800px\] {
  perspective: 800px;
}
.\[transform-origin\:calc\(50\%\+400px\)\] {
  transform-origin: calc(50% + 400px);
}
.\[transform-origin\:calc\(50\%-200px\)\] {
  transform-origin: calc(50% - 200px);
}
.\[transform-origin\:calc\(50\%-200px\)_calc\(50\%\+200px\)\] {
  transform-origin: calc(50% - 200px) calc(50% + 200px);
}
.\[transform-origin\:calc\(50\%-400px\)\] {
  transform-origin: calc(50% - 400px);
}
.\[transform-origin\:center_center\] {
  transform-origin: center center;
}
.\[transform-style\:preserve-3d\] {
  transform-style: preserve-3d;
}
html,
body {
  height: 100%;
}

html {
  scroll-behavior: smooth;
}

::-webkit-scrollbar {
   	width: 4px;
	background-color: #141420;
}
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
  background-color: #181926;
}

::-webkit-scrollbar-thumb {
  background-color: white;
  border-radius: 4px;
}

.no-visible-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}

.no-visible-scrollbar::-webkit-scrollbar {
  display: none;
}

@keyframes subtle-noise {
  0% {
    transform: translate(0, 0) scale(1.2);
  }
  25% {
    transform: translate(2px, 1px) scale(1.2);
  }
  50% {
    transform: translate(0, 2px) scale(1.2);
  }
  75% {
    transform: translate(-1px, 1px) scale(1.2);
  }
  100% {
    transform: translate(0, 0) scale(1.2);
  }
}

.buttonWithGradient defs stop:first-child {
  stop-color: white;
}
.buttonWithGradient defs stop:last-child {
  stop-color: white;
}
.buttonWithGradient:hover defs stop:first-child, .activeButton defs stop:first-child {
  stop-color: #FE8989;
}
.buttonWithGradient:hover defs stop:last-child, .activeButton defs stop:last-child {
  stop-color: #5606FF;
}

.uploadIconWithGradient defs stop:first-child {
  stop-color: #9ca3af;
}
.uploadIconWithGradient defs stop:last-child {
  stop-color: #9ca3af;
}
.uploadIconWithGradient:hover defs stop:first-child {
  stop-color: #FFF;
}
.uploadIconWithGradient:hover defs stop:last-child {
  stop-color: #FFF;
}

.gradientText{
  background-size: 200% 200%;
}

@media (min-width: 768px) {
  .container {
    padding-left: 16px;
    padding-right: 16px;
  }
}

.container {
    max-width: 100%;
}

.placeholder\:text-gray-600::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.placeholder\:text-gray-600::placeholder {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.hover\:translate-y-px:hover {
  --tw-translate-y: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:\!scale-\[1\.04\]:hover {
  --tw-scale-x: 1.04 !important;
  --tw-scale-y: 1.04 !important;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;
}

.hover\:border-violets-are-blue:hover {
  --tw-border-opacity: 1;
  border-color: rgb(126 94 242 / var(--tw-border-opacity));
}

.hover\:bg-black\/30:hover {
  background-color: rgb(0 0 0 / 0.3);
}

.hover\:bg-violets-are-blue\/10:hover {
  background-color: rgb(126 94 242 / 0.1);
}

.hover\:bg-white\/15:hover {
  background-color: rgb(255 255 255 / 0.15);
}

.hover\:bg-yellow-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity));
}

.hover\:bg-yellow-200\/80:hover {
  background-color: rgb(254 240 138 / 0.8);
}

.hover\:bg-opacity-70:hover {
  --tw-bg-opacity: 0.7;
}

.hover\:from-han-purple\/90:hover {
  --tw-gradient-from: rgb(86 6 255 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(86 6 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-violets-are-blue\/70:hover {
  --tw-gradient-from: rgb(126 94 242 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(126 94 242 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-han-purple\/70:hover {
  --tw-gradient-to: rgb(86 6 255 / 0.7) var(--tw-gradient-to-position);
}

.hover\:to-tulip\/90:hover {
  --tw-gradient-to: rgb(254 137 137 / 0.9) var(--tw-gradient-to-position);
}

.hover\:pl-4:hover {
  padding-left: 1rem;
}

.hover\:font-medium:hover {
  font-weight: 500;
}

.hover\:\!text-yellow-500:hover {
  --tw-text-opacity: 1 !important;
  color: rgb(234 179 8 / var(--tw-text-opacity)) !important;
}

.hover\:text-black:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.hover\:text-light-yellow:hover {
  --tw-text-opacity: 1;
  color: rgb(230 214 155 / var(--tw-text-opacity));
}

.hover\:text-transparent:hover {
  color: transparent;
}

.hover\:text-violets-are-blue\/80:hover {
  color: rgb(126 94 242 / 0.8);
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:opacity-90:hover {
  opacity: 0.9;
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-han-purple\/20:hover {
  --tw-shadow-color: rgb(86 6 255 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.focus\:translate-y-px:focus {
  --tw-translate-y: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.focus\:border-violets-are-blue:focus {
  --tw-border-opacity: 1;
  border-color: rgb(126 94 242 / var(--tw-border-opacity));
}

.focus\:border-violets-are-blue\/50:focus {
  border-color: rgb(126 94 242 / 0.5);
}

.focus\:border-yellow-500\/50:focus {
  border-color: rgb(234 179 8 / 0.5);
}

.focus\:shadow-md:focus {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:shadow-han-purple\/20:focus {
  --tw-shadow-color: rgb(86 6 255 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.focus\:\!outline-none:focus {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.hover\:enabled\:bg-gradient-to-tr:enabled:hover {
  background-image: linear-gradient(to top right, var(--tw-gradient-stops));
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:border-white\/10:disabled {
  border-color: rgb(255 255 255 / 0.1);
}

.disabled\:border-white\/50:disabled {
  border-color: rgb(255 255 255 / 0.5);
}

.disabled\:bg-light-yellow\/20:disabled {
  background-color: rgb(230 214 155 / 0.2);
}

.disabled\:bg-white\/10:disabled {
  background-color: rgb(255 255 255 / 0.1);
}

.disabled\:bg-white\/15:disabled {
  background-color: rgb(255 255 255 / 0.15);
}

.disabled\:bg-zinc-600:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 91 / var(--tw-bg-opacity));
}

.disabled\:from-han-purple\/40:disabled {
  --tw-gradient-from: rgb(86 6 255 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(86 6 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.disabled\:from-violets-are-blue\/50:disabled {
  --tw-gradient-from: rgb(126 94 242 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(126 94 242 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.disabled\:to-han-purple\/50:disabled {
  --tw-gradient-to: rgb(86 6 255 / 0.5) var(--tw-gradient-to-position);
}

.disabled\:to-tulip\/40:disabled {
  --tw-gradient-to: rgb(254 137 137 / 0.4) var(--tw-gradient-to-position);
}

.disabled\:text-gray-200:disabled {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.disabled\:text-neutral-200:disabled {
  --tw-text-opacity: 1;
  color: rgb(229 229 229 / var(--tw-text-opacity));
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:shadow-none:disabled {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group:hover .group-hover\:translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-x-0\.5 {
  --tw-translate-x: 0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:rotate-3 {
  --tw-rotate: 3deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-yellow-500\/20 {
  background-color: rgb(234 179 8 / 0.2);
}

.group:hover .group-hover\:fill-\[\#A9C6D8\] {
  fill: #A9C6D8;
}

.group:hover .group-hover\:fill-\[\#C8DAEA\] {
  fill: #C8DAEA;
}

.group:hover .group-hover\:fill-white {
  fill: #fff;
}

.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.data-\[state\=checked\]\:translate-x-4[data-state=checked] {
  --tw-translate-x: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=unchecked\]\:translate-x-0[data-state=unchecked] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes accordion-up {

  from {
    height: var(--radix-accordion-content-height);
    opacity: 1;
  }

  to {
    height: 0;
    opacity: 0;
  }
}

.data-\[state\=closed\]\:animate-accordion-up[data-state=closed] {
  animation: accordion-up 0.2s ease-out;
}

@keyframes accordion-down {

  from {
    height: 0;
    opacity: 0;
  }

  to {
    height: var(--radix-accordion-content-height);
    opacity: 1;
  }
}

.data-\[state\=open\]\:animate-accordion-down[data-state=open] {
  animation: accordion-down 0.2s ease-out;
}

.data-\[state\=unchecked\]\:bg-white\/10[data-state=unchecked] {
  background-color: rgb(255 255 255 / 0.1);
}

.data-\[state\=checked\]\:bg-gradient-to-tr[data-state=checked] {
  background-image: linear-gradient(to top right, var(--tw-gradient-stops));
}

@media (min-width: 640px) {

  .sm\:mb-12 {
    margin-bottom: 3rem;
  }

  .sm\:mt-8 {
    margin-top: 2rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:h-\[320px\] {
    height: 320px;
  }

  .sm\:max-h-\[80vh\] {
    max-height: 80vh;
  }

  .sm\:w-\[320px\] {
    width: 320px;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-min {
    width: -moz-min-content;
    width: min-content;
  }

  .sm\:max-w-3xl {
    max-width: 48rem;
  }

  .sm\:max-w-\[150px\] {
    max-width: 150px;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:justify-center {
    justify-content: center;
  }

  .sm\:rounded-2xl {
    border-radius: 1rem;
  }

  .sm\:pt-48 {
    padding-top: 12rem;
  }

  .sm\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .sm\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 768px) {

  .md\:right-12 {
    right: 3rem;
  }

  .md\:top-\[18\%\] {
    top: 18%;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .md\:my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .md\:mb-8 {
    margin-bottom: 2rem;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-10 {
    margin-top: 2.5rem;
  }

  .md\:mt-24 {
    margin-top: 6rem;
  }

  .md\:mt-8 {
    margin-top: 2rem;
  }

  .md\:block {
    display: block;
  }

  .md\:inline-block {
    display: inline-block;
  }

  .md\:inline {
    display: inline;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-12 {
    height: 3rem;
  }

  .md\:h-20 {
    height: 5rem;
  }

  .md\:h-5 {
    height: 1.25rem;
  }

  .md\:h-8 {
    height: 2rem;
  }

  .md\:h-\[400px\] {
    height: 400px;
  }

  .md\:h-\[500px\] {
    height: 500px;
  }

  .md\:h-screen {
    height: 100vh;
  }

  .md\:max-h-12 {
    max-height: 3rem;
  }

  .md\:\!w-auto {
    width: auto !important;
  }

  .md\:w-20 {
    width: 5rem;
  }

  .md\:w-5 {
    width: 1.25rem;
  }

  .md\:w-8 {
    width: 2rem;
  }

  .md\:w-\[600px\] {
    width: 600px;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:\!max-w-\[450px\] {
    max-width: 450px !important;
  }

  .md\:max-w-\[250px\] {
    max-width: 250px;
  }

  .md\:max-w-\[820px\] {
    max-width: 820px;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:items-end {
    align-items: flex-end;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:gap-10 {
    gap: 2.5rem;
  }

  .md\:gap-2 {
    gap: 0.5rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-8 {
    gap: 2rem;
  }

  .md\:overflow-visible {
    overflow: visible;
  }

  .md\:border {
    border-width: 1px;
  }

  .md\:bg-eerie-black {
    --tw-bg-opacity: 1;
    background-color: rgb(14 17 17 / var(--tw-bg-opacity));
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .md\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .md\:py-3\.5 {
    padding-top: 0.875rem;
    padding-bottom: 0.875rem;
  }

  .md\:pl-16 {
    padding-left: 4rem;
  }

  .md\:pl-4 {
    padding-left: 1rem;
  }

  .md\:pr-24 {
    padding-right: 6rem;
  }

  .md\:pt-32 {
    padding-top: 8rem;
  }

  .md\:pt-48 {
    padding-top: 12rem;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 1024px) {

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .lg\:my-16 {
    margin-top: 4rem;
    margin-bottom: 4rem;
  }

  .lg\:mb-12 {
    margin-bottom: 3rem;
  }

  .lg\:mb-3 {
    margin-bottom: 0.75rem;
  }

  .lg\:mb-4 {
    margin-bottom: 1rem;
  }

  .lg\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .lg\:mb-8 {
    margin-bottom: 2rem;
  }

  .lg\:mt-0 {
    margin-top: 0px;
  }

  .lg\:mt-20 {
    margin-top: 5rem;
  }

  .lg\:mt-24 {
    margin-top: 6rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:h-16 {
    height: 4rem;
  }

  .lg\:h-8 {
    height: 2rem;
  }

  .lg\:h-\[200px\] {
    height: 200px;
  }

  .lg\:max-h-16 {
    max-height: 4rem;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-16 {
    width: 4rem;
  }

  .lg\:w-5\/12 {
    width: 41.666667%;
  }

  .lg\:w-7\/12 {
    width: 58.333333%;
  }

  .lg\:w-8 {
    width: 2rem;
  }

  .lg\:max-w-\[70\%\] {
    max-width: 70%;
  }

  .lg\:max-w-full {
    max-width: 100%;
  }

  .lg\:max-w-sm {
    max-width: 24rem;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:justify-start {
    justify-content: flex-start;
  }

  .lg\:gap-6 {
    gap: 1.5rem;
  }

  .lg\:gap-8 {
    gap: 2rem;
  }

  .lg\:overflow-visible {
    overflow: visible;
  }

  .lg\:p-4 {
    padding: 1rem;
  }

  .lg\:p-6 {
    padding: 1.5rem;
  }

  .lg\:p-8 {
    padding: 2rem;
  }

  .lg\:px-24 {
    padding-left: 6rem;
    padding-right: 6rem;
  }

  .lg\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .lg\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .lg\:pt-0 {
    padding-top: 0px;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .lg\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .lg\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .lg\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .lg\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1280px) {

  .xl\:mt-0 {
    margin-top: 0px;
  }

  .xl\:flex {
    display: flex;
  }

  .xl\:hidden {
    display: none;
  }
}

@media (prefers-color-scheme: dark) {

  .dark\:bg-transparent {
    background-color: transparent;
  }

  .dark\:via-neutral-700 {
    --tw-gradient-to: rgb(64 64 64 / 0)  var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), #404040 var(--tw-gradient-via-position), var(--tw-gradient-to);
  }

  .dark\:\[border\:1px_solid_rgba\(255\2c 255\2c 255\2c \.1\)\] {
    border: 1px solid rgba(255,255,255,.1);
  }

  .dark\:\[box-shadow\:0_-20px_80px_-20px_\#ffffff1f_inset\] {
    box-shadow: 0 -20px 80px -20px #ffffff1f inset;
  }
}

.\[\&\:\:-webkit-inner-spin-button\]\:appearance-none::-webkit-inner-spin-button {
  -webkit-appearance: none;
          appearance: none;
}

.\[\&\:\:-webkit-outer-spin-button\]\:appearance-none::-webkit-outer-spin-button {
  -webkit-appearance: none;
          appearance: none;
}

.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state=open]>svg {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}


