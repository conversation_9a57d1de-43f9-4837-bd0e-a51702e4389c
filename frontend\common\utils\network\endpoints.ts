const agentBaseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:8000'
export const devAgentBaseUrl = process.env.NEXT_PUBLIC_DEV_AGENT_URL || 'http://localhost:5173'

export const pinataUploadUrl = '/api/pinata'
export const categoriesUrl = '/api/categories'
export const revalidateUrl = '/api/revalidate'
export const getTransfersFromMoralisUrl = `https://deep-index.moralis.io/api/v2.2/erc20/%tokenAddress%/transfers?chain=%chainId%&order=DESC`
export const getTokenBalanceMoralisUrl = `https://deep-index.moralis.io/api/v2.2/%userAddress%/erc20?chain=%chainId%&token_addresses=%tokenAddress%`
export const getTokenTransfersMoralisUrl = `https://deep-index.moralis.io/api/v2.2/erc20/%tokenAddress%/stats?chain=%chainId%`
export const getTokenMetadataMoralisUrl = `https://deep-index.moralis.io/api/v2.2/erc20/metadata?chain=%chainId%%addresses%`
export const getWalletTokensMoralisUrl = `https://deep-index.moralis.io/api/v2.2/%address%/erc20?chain=%chainId%%addresses%`
export const getOwnersFromMoralisUrl = `https://deep-index.moralis.io/api/v2.2/erc20/%tokenAddress%/owners?chain=%chainId%&order=DESC`
export const startTwitterAgentUrl = `${agentBaseUrl}/start-twitter-agent`
export const startLinkedInAgentUrl = `${agentBaseUrl}/start-linkedin-agent`
export const updateCharacterUrl = `${agentBaseUrl}/agents/%agentId%/set`
export const updateTweetUrl = `${agentBaseUrl}/agents/%agentId%/update-tweet`
export const updateTweetImageUrl = `${agentBaseUrl}/agents/%agentId%/update-tweet-image`
export const deleteTweetImageUrl = `${agentBaseUrl}/agents/%agentId%/remove-tweet-image`
export const fetchNewTweetsUrl = `${agentBaseUrl}/agents/%agentId%/new-tweets`
export const fetchAgentSettingsUrl = `${agentBaseUrl}/agents/%agentId%`
export const fetchTweetsUrl = `${agentBaseUrl}/agents/%agentId%/tweets`
export const stopAgentUrl = `${agentBaseUrl}/stop-agent/%agentId%`
export const getAllAgentsUrl = `${agentBaseUrl}/agents`
export const createDevAgentUrl = `${devAgentBaseUrl}/api/apps`
