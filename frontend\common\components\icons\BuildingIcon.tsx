import type { SVGProps } from "react";

export const BuildingIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <path fill="#66E1FF" d="M17.572 3.225H2.427v15.942h15.145V3.225Z" />
      <path fill="#C2F3FF" d="M16.576 3.225H2.427v14.148L16.576 3.225Z" />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M.833 19.167h18.333M17.572 3.225H2.427v15.942h15.145V3.225ZM.833 3.225h18.333"
      />
      <path
        fill="#B2B2B2"
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M16.775 3.225V1.63a.797.797 0 0 0-.797-.797H4.022a.797.797 0 0 0-.797.797v1.595h13.55Z"
      />
      <path
        fill="#fff"
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M8.804 19.167v-2.79a1.196 1.196 0 0 1 2.391 0v2.79h-2.39Z"
      />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M4.819 15.978h2.39M12.79 15.978h2.391M4.819 12.79h3.188M11.992 12.79h3.189M4.819 9.601h3.188M11.992 9.601h3.189M4.819 6.413h3.188M11.992 6.413h3.189"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h20v20H0z" />
      </clipPath>
    </defs>
  </svg>
)
