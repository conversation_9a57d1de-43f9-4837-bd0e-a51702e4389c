import {
  ReactNode, LinkHTMLAttributes,
} from "react";
import { LinkProps as NextLinkProps } from "next/link";
  
export type Variants = 'outline' | 'outline-rounded' | 'gradient' | 'text-violet';
export const TVariants = {
  outline: 'outline',
  'text-violet': 'text-violet',
  gradient: 'gradient',
  'outline-rounded': 'outline-rounded',
}
export type Sizes = 'xs' | 'sm' | 'md' | 'lg';
export const TSizes = {
  xs: 'xs',
  sm: 'sm',
  md: 'md',
  lg: 'lg',
}
export type LinkProps = LinkHTMLAttributes<HTMLAnchorElement> & NextLinkProps & {
    /** Width of the link */
    width?: string;
    /** Href of the link */
    href: string;
    /** Size of the link */
    size: Sizes;
    /** Children of the link */
    children: ReactNode;
    /** Variant of the link */
    variant: Variants;
    /** Data-cy of the link */
    'data-cy'?: string;
    /** Class name of the link */
    className?: string;
    /** Target of the link */
    target?: string;
    /** Relationship between the current document and the linked document */
    rel?: string;
  };
  