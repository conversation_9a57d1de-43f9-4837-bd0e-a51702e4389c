'use client'
import {
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import {
  IdeaTokenType,
  IdeaType,
  IdeaTypeWithDomains,
  TokenInfoType,
} from '@/common/types';
import { SubdomainType } from '@/middleware';
import { createClient } from '@/common/utils/supabase/client';
import {
  getChainForMoralis, POLYGON_WHITELIST_ORDERED, SupabaseTables,
} from '@/common/constants';
import { getTokenMetadataMoralisUrl } from '@/common/utils/network/endpoints';

const POLYGON_WHITELIST_MAP = new Map(
  POLYGON_WHITELIST_ORDERED.map((address, index) => [address, index]),
);
interface TokenMarketData {
  address: string;
  marketCap: number;
}

const formatAddressesForMoralisUrl = (addresses: Array<string>) => {
  return addresses.reduce((url, address, index) => {
    const formattedAddress = address.toLowerCase();
    const param = `addresses%5B${index}%5D=${formattedAddress}`;
    return url + (index === 0 ? param : `&${param}`);
  }, '&');
};

export const useGetIdeas = ({
  ideaTokens,
  shouldFetchMetadata = true,
  initialBatchSize = 8,
} : {
  ideaTokens: Array<IdeaType> | [],
  shouldFetchMetadata?: boolean,
  initialBatchSize?: number,
}) => {
  const isPolygon = process.env.NEXT_PUBLIC_CURRENT_CHAIN === "POLYGON_MAIN"
  const [subdomains, setSubdomains] = useState<Array<SubdomainType>>([])
  const [tokensInfo, setTokensInfo] = useState<Array<TokenInfoType>>([])
  const [tokenMarketData, setTokenMarketData] = useState<Array<TokenMarketData>>([])
  const [visibleCount, setVisibleCount] = useState(initialBatchSize)
  const supabase = createClient();

  useEffect(() => {
    const getSubdomains = async () => {
      const { data: subdomainsData } = await supabase.from(SupabaseTables.Subdomains).select('*')
      if (subdomainsData?.length) {
        setSubdomains(subdomainsData)
      }
    }
    const getTokensInfo = async () => {
      const { data: allTokens } = await supabase.from(SupabaseTables.NewIdeas).select('*')
      if (allTokens?.length) {
        setTokensInfo(allTokens)
      }
    }
    const getTokenMarketData = async () => {
      try {
        const addressesUrl = formatAddressesForMoralisUrl(POLYGON_WHITELIST_ORDERED);
        const fullUrl = getTokenMetadataMoralisUrl
          .replace('%chainId%', getChainForMoralis())
          .replace('%addresses%', addressesUrl);
        const marketCapTokens = await fetch(fullUrl, {
          headers: {
            'X-API-Key': process.env.NEXT_PUBLIC_X_API_KEY || '',
          },
        })
        const marketCapJson = await marketCapTokens.json()
        if (marketCapJson) {
          const processedData = marketCapJson.map((token: {
            address: string,
            market_cap: string
          }) => ({
            address: token.address.toLowerCase(),
            marketCap: parseFloat(token.market_cap || '0'),
          }));

          setTokenMarketData(processedData);
        }
      } catch (error) {
        console.error('Error fetching token market data:', error);
      }
    }
    getSubdomains()
    getTokensInfo()
    if (shouldFetchMetadata) {
      getTokenMarketData()
    }
  }, [supabase, ideaTokens, shouldFetchMetadata])

  const allIdeas = useMemo<IdeaTypeWithDomains>(() => {
    if (!ideaTokens?.length || !Array.isArray(ideaTokens) || !subdomains?.length) {
      return []
    }

    const processedTokens = ideaTokens
      .map((item: unknown) => {
        const itemWithType = item as IdeaTokenType
        const tokenAddress = itemWithType.tokenAddress.toLowerCase()

        const subdomain = subdomains.find((d: SubdomainType) =>
          d.address.toLowerCase() === tokenAddress,
        )

        const tokenInfo = tokensInfo.find((d: TokenInfoType) =>
          d.address?.toLowerCase() === tokenAddress,
        )

        const marketCapData = tokenMarketData.find((t) => t.address === tokenAddress)

        return {
          idea: itemWithType,
          holdersCount: tokenInfo?.ownersCount || 0,
          marketCap: marketCapData?.marketCap || 0,
          subdomain: subdomain?.subdomain || '',
          whitelistIndex: POLYGON_WHITELIST_MAP.get(tokenAddress) ?? -1,
        }
      });

    if (isPolygon) {
      return processedTokens
        .filter(token => token.whitelistIndex !== -1)
        .sort((a, b) => a.whitelistIndex - b.whitelistIndex);
    } else {
      return processedTokens
        .sort((a, b) => {
          const aFunding = a.idea.fundingRaised
          const bFunding = b.idea.fundingRaised
          return (bFunding - aFunding)
        });
    }
  }, [ideaTokens, subdomains, tokensInfo, isPolygon, tokenMarketData])

  const ideas = useMemo(() => {
    return allIdeas.slice(0, visibleCount);
  }, [allIdeas, visibleCount])

  const loadMoreIdeas = useCallback(() => {
    setVisibleCount(prev => Math.min(prev + initialBatchSize, allIdeas.length));
  }, [allIdeas.length, initialBatchSize])

  useEffect(() => {
    const handleScroll = () => {
      if (window.innerHeight + window.scrollY >= document.body.offsetHeight - 1000 && ideas.length < allIdeas.length) {
        loadMoreIdeas();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [ideas.length, allIdeas.length, loadMoreIdeas]);

  return {
    ideas,
    totalIdeasCount: allIdeas.length,
    visibleCount,
    loadMoreIdeas,
  }
}
