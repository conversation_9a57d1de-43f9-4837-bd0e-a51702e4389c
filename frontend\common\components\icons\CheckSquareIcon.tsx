import type { SVGProps } from "react";

export const CheckSquareIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    {...props}
  >
    <path
      fill="#78EB7B"
      d="M13.593 5.808c.441 0 .799.358.799.799v9.582a.798.798 0 0 1-.799.798H4.011a.798.798 0 0 1-.798-.798V6.607c0-.441.357-.799.798-.799h9.582Z"
    />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M18.384 1.416 8.403 13.792 4.411 9.8"
    />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M15.988 9.402v8.384c0 .44-.357.798-.798.798H2.414a.798.798 0 0 1-.798-.798V5.01c0-.441.357-.799.798-.799h9.183"
    />
  </svg>
)
