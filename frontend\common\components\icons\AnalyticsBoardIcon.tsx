import type { SVGProps } from "react";

export const AnalyticsBoardIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    {...props}
  >
    <path
      fill="#FFEF5E"
      d="M.833 4.022a.797.797 0 0 1 .798-.797H18.37a.797.797 0 0 1 .797.797v11.956a.797.797 0 0 1-.797.797H1.63a.797.797 0 0 1-.797-.797V4.022Z"
    />
    <path
      fill="#FFF9BF"
      d="M1.63 3.225a.797.797 0 0 0-.797.797v11.956a.784.784 0 0 0 .332.63L14.548 3.225H1.631Z"
    />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="m4.021 12.79 2.05-3.416a.4.4 0 0 1 .684 0l1.76 2.937a.398.398 0 0 0 .649.05l1.327-1.594a.398.398 0 0 1 .612 0l1.32 1.584a.398.398 0 0 0 .651-.058l2.904-5.083"
    />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M.833 4.022a.797.797 0 0 1 .798-.797H18.37a.797.797 0 0 1 .797.797v11.956a.797.797 0 0 1-.797.797H1.63a.797.797 0 0 1-.797-.797V4.022Z"
    />
  </svg>
)
