{"c": ["app/home/<USER>", "app/layout", "webpack", "_app-pages-browser_common_components_molecules_index_ts"], "r": [], "m": ["(app-pages-browser)/./app/home/<USER>", "(app-pages-browser)/./app/home/<USER>", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ccharl%5C%5COneDrive%5C%5CDocuments%5C%5CWeb%20Development%5C%5Cdreamstartr%5C%5Cfrontend%5C%5Capp%5C%5Chome%5C%5CdreamathonSection.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ccharl%5C%5COneDrive%5C%5CDocuments%5C%5CWeb%20Development%5C%5Cdreamstartr%5C%5Cfrontend%5C%5Capp%5C%5Chome%5C%5Chero.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ccharl%5C%5COneDrive%5C%5CDocuments%5C%5CWeb%20Development%5C%5Cdreamstartr%5C%5Cfrontend%5C%5Capp%5C%5Chome%5C%5CluminaCasaSection.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ccharl%5C%5COneDrive%5C%5CDocuments%5C%5CWeb%20Development%5C%5Cdreamstartr%5C%5Cfrontend%5C%5Capp%5C%5Chome%5C%5CstatsSection.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ccharl%5C%5COneDrive%5C%5CDocuments%5C%5CWeb%20Development%5C%5Cdreamstartr%5C%5Cfrontend%5C%5Ccommon%5C%5Ccomponents%5C%5Catoms%5C%5Cindex.ts%22%2C%22ids%22%3A%5B%22Loader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ccharl%5C%5COneDrive%5C%5CDocuments%5C%5CWeb%20Development%5C%5Cdreamstartr%5C%5Cfrontend%5C%5Ccommon%5C%5Ccomponents%5C%5Corganisms%5C%5Cfooter%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ccharl%5C%5COneDrive%5C%5CDocuments%5C%5CWeb%20Development%5C%5Cdreamstartr%5C%5Cfrontend%5C%5Ccommon%5C%5Ccomponents%5C%5Corganisms%5C%5Cheader%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ccharl%5C%5COneDrive%5C%5CDocuments%5C%5CWeb%20Development%5C%5Cdreamstartr%5C%5Cfrontend%5C%5Ccommon%5C%5Ccomponents%5C%5Corganisms%5C%5Cmodal%5C%5CdefaultModal.tsx%22%2C%22ids%22%3A%5B%22DefaultModal%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ccharl%5C%5COneDrive%5C%5CDocuments%5C%5CWeb%20Development%5C%5Cdreamstartr%5C%5Cfrontend%5C%5Ccommon%5C%5Ccomponents%5C%5Corganisms%5C%5Cmodal%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22Modal%22%2C%22ModalBody%22%2C%22ModalContent%22%2C%22ModalFooter%22%2C%22ModalTriggerCreateToken%22%2C%22ModalDeleteCommentTrigger%22%2C%22ModalTriggerFooter%22%2C%22ModalTrigger%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ccharl%5C%5COneDrive%5C%5CDocuments%5C%5CWeb%20Development%5C%5Cdreamstartr%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cdynamic-bailout-to-csr.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ccharl%5C%5COneDrive%5C%5CDocuments%5C%5CWeb%20Development%5C%5Cdreamstartr%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cpreload-css.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"]}