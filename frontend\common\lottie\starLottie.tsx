'use client'
import <PERSON><PERSON> from "react-lottie";
import * as animData from '@/common/lottie/star-animation.json'

export const StarLottie = () => {
  const animOptions = {
    loop: true,
    autoplay: false,
    animationData: animData,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  };
  return (
    <Lottie options={animOptions}
      style={{ 
        margin: '0',
        pointerEvents: 'none',
      }}
      height={32}
      width={32}
    />
  )
}
