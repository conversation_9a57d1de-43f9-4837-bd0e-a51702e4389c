'use client'

import {
  motion, useInView,
} from 'framer-motion';
import { useRef } from 'react';
import Image from 'next/image';

const Teams = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, {
    once: true,
    amount: 0.2,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  const testimonials = [
    {
      quote: `Visionary Founder of Lumina Casa and the Magickal Life Academy. A background in law and global media communications who transitioned to spiritual leadership, focusing on conscious living, tantric practices, and Web3 integration. "Cosmic Architect" and "Sacred Systems Designer.`,
      name: "<PERSON><PERSON>",
      title: "Founder",
      image: "https://kajabi-storefronts-production.kajabi-cdn.com/kajabi-storefronts-production/file-uploads/themes/**********/settings_images/5b77fe1-1e5-62d5-f85c-df366423c2c5_<PERSON>ie_Pray_.jpeg",
    },
    {
      quote: "Scientist and educator with a PhD in Biology. Co-founder of Lumina Casa, In2infinity, and The Musiverse. Published author focusing on sacred geometry and a geometric theory of the universe. Leads workshops worldwide on consciousness expansion.",
      name: "Dr. Heike Bielek",
      title: "Co-founder",
      image: "https://kajabi-storefronts-production.kajabi-cdn.com/kajabi-storefronts-production/file-uploads/themes/**********/settings_images/215aaa2-fea4-fe70-d114-ea4e7de5542_Heike_upload.png",
    },
    {
      quote: "A pioneering mathematician specializing in multidimensional models and sacred geometry applications for next-generation computing systems. Working at the intersection of ancient mathematical principles and cutting-edge technology, they provide the foundational mathematical frameworks for Musiverse's conscious protocol and In2Infinity's educational systems.",
      name: "Colin Power",
      title: "In2Infinity & Musiverse Science and Math Expert",
      image: "/colin_image.jpeg",
    },
    {
      quote: `Alistair Kinnear exemplifies transformation through adversity. Born with childhood polio, he defied limitations to become both a competitive swimmer and successful tech entrepreneur. After experiencing post-polio syndrome in his 50s, Alistair developed his pioneering healing methodology through deep immersion in meditation, hypnosis, and Chi Gong.`,
      name: "Alistair Kinnear",
      title: "Self-Healing Guide | Resilience Mentor",
      image: "https://kajabi-storefronts-production.kajabi-cdn.com/kajabi-storefronts-production/file-uploads/themes/**********/settings_images/30f00d-7dd1-3c06-d6b0-ae3c46ecfdd_Ali_.jpeg",
    },
    {
      quote: "Luke Scott III is a visionary mentor and master healer dedicated to raising global consciousness. As the founder of Quantum Regression and Integration Therapy (Q-RIT) and co-founder of multiple conscious organizations including the Quantum Healers Alliance and StandOut Speakers Academy, Luke bridges spiritual awakening with practical transformation.",
      name: "Luke Scott III",
      title: "Spiritual Mentor | Quantum Healer | International Speaker",
      image: "https://kajabi-storefronts-production.kajabi-cdn.com/kajabi-storefronts-production/file-uploads/themes/**********/settings_images/876178-e61e-802-8126-5b0621c8f60_Luke_Up_.png",
    },
    {
      quote: "Former CMO of Polygon who helped scale it to a $20B blockchain ecosystem. Founder of DreamStarter.xyz, a platform helping creators launch soul-aligned projects. Experienced in community building and Web3 strategy.",
      name: "Arun Philips",
      title: "Visionary Technologist | Dream Architect | Former CMO of Polygon",
      image: "https://kajabi-storefronts-production.kajabi-cdn.com/kajabi-storefronts-production/file-uploads/themes/**********/settings_images/3318008-bd7f-bc11-808-368b08ce845e_Arun_.jpg",
    },
    {
      quote: "Web3 and AI architect, founder of MyriadFlow and CyreneAI. Focuses on creating platforms for AI agents to collaborate and evolve autonomously. Has over seven years of experience in Web3, cybersecurity, and decentralized infrastructure.",
      name: "Shachindra",
      title: "Web3 and AI architect",
      image: "https://kajabi-storefronts-production.kajabi-cdn.com/kajabi-storefronts-production/file-uploads/themes/**********/settings_images/fe46f60-6f2-211-681b-4748f270e2ea_Shachindra.jpeg",
    },
    {
      quote: "Tony's journey from elite athlete to spiritual guide embodies transformation at its deepest level. With roots in military service and construction, Tony experienced a profound awakening that led him from Australia to India, where he underwent a complete rebirth through sacred movement and self-discovery.",
      name: "Tony",
      title: "Ecstatic Dance DJ | Movement Guide | Strength & Muay Thai Coach",
      image: "https://kajabi-storefronts-production.kajabi-cdn.com/kajabi-storefronts-production/file-uploads/themes/**********/settings_images/436372d-4cba-7885-57cf-82fdcb6f2ed4_Tony_.jpeg",
    },
  ];

  const institutionalPartners = [
    {
      icon: <Image src="/baselogo.png" width={24} height={24} alt="base" />,
      name: "Base",
      color: "bg-cyan-500/20",
      textColor: "text-cyan-400",
    },
    {
      icon: <Image src="/cyreneailogo.png" width={24} height={24} alt="base" />,
      name: "CyreneAI",
      color: "bg-indigo-500/20",
      textColor: "text-indigo-400",
    },
    {
      icon: <Image src="/dragonfirelogo.svg" width={24} height={24} alt="base" />,
      name: "DragonFire",
      color: "bg-green-500/20",
      textColor: "text-green-400",
    },
  ];

  return (
    <section
      id="team"
      ref={sectionRef}
      className="py-4 lg:py-16 lg:pt-0 relative w-full container px-2 md:px-8 mx-auto !max-w-[1200px]"
    >
      <div className="max-w-[1200px] mx-auto">
        {/* Team Section */}
        <div className="text-center mb-4 flex flex-col items-center">
          <h2 className={`text-xl text-center md:text-2xl lg:text-3xl font-semibold text-white my-2`}>
            Our Team
          </h2>
          <div className="rounded-full h-1 w-20 bg-gradient-to-tr from-light-yellow to-yellow-600 mx-auto mb-6"></div>
          <p className="text-neutral-300 mb-4 text-center text-sm md:text-base max-w-3xl">
            Meet the dedicated team behind Lumina Casa who bring our vision to life
          </p>
        </div>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6"
        >
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-violets-are-blue/5 border border-violets-are-blue/15 rounded-3xl p-4 lg:p-6 flex flex-col"
            >
              <div className="flex-1">
                <svg className="h-8 w-8 text-light-yellow mb-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                </svg>
                <p className="text-gray-300 text-sm italic">
                  "{testimonial.quote}"
                </p>
              </div>
              <div className="flex items-center mt-4">
                <div className='mr-3'>
                  <div className="relative md:h-20 w-16 h-16 md:w-20 rounded-full overflow-hidden">
                    <Image
                      src={testimonial.image}
                      alt={testimonial.name}
                      quality={20}
                      width={160}
                      height={160}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              
                <div>
                  <h4 className="text-white font-medium">{testimonial.name}</h4>
                  <p className="text-gray-400 text-sm">{testimonial.title}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
        {/* Institutional Partners Section */}
        <div className="my-8 lg:my-16">
          <div className="text-center mb-4 lg:mb-8 flex flex-col items-center">
            <h2 className={`text-xl text-center md:text-2xl lg:text-3xl font-semibold text-white my-2`}>
              Partners
            </h2>
            <div className="rounded-full h-1 w-20 bg-gradient-to-tr from-light-yellow to-yellow-600 mx-auto mb-6"></div>
          </div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            className="grid grid-cols-3 gap-4 lg:gap-8 max-w-4xl mx-auto"
          >
            {institutionalPartners.map((partner, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="flex flex-col items-center"
              >
                {/* <div className={`${partner.color} rounded-full w-12 lg:w-16 h-12 lg:h-16 flex items-center justify-center mb-2 lg:mb-4`}>
                  <span className={`${partner.textColor} font-bold lg:text-lg`}>{partner.id}</span>
                </div> */}
                <div className={`${partner.color} p-2 rounded-full flex items-center justify-center mb-2`}>
                  {partner.icon}
                </div>
                <p className="text-white text-center text-xl font-semibold">{partner.name}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Teams;

