import type { SVGProps } from "react";

export const HeartBreakIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    {...props}
  >
    <path
      fill="#FFBFC5"
      d="m7.215 10.022 2.792-5.517-1.276-1.164a4.658 4.658 0 0 0-7.41 1.187 4.521 4.521 0 0 0 .867 5.27L9.762 18l2.643-7.978h-5.19Z"
    />
    <path
      fill="#FF808C"
      d="M18.698 4.528a4.658 4.658 0 0 0-7.41-1.187l-1.276 1.164-2.792 5.517h5.186L9.763 18l7.819-8.052a4.49 4.49 0 0 0 1.116-5.42Z"
    />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M9.762 18 2.188 9.799a4.521 4.521 0 0 1-.866-5.271A4.658 4.658 0 0 1 8.73 3.34l1.276 1.164 1.277-1.164a4.66 4.66 0 0 1 7.414 1.187 4.49 4.49 0 0 1-1.117 5.42L9.762 18Z"
    />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="m10.007 4.505-2.792 5.517h5.186L9.758 18"
    />
  </svg>
)
