import type { SVGProps } from "react";

export const DeleteIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <path
        fill="#FF808C"
        d="M12 22.542c5.822 0 10.542-4.72 10.542-10.542S17.822 1.458 12 1.458 1.46 6.178 1.46 12 6.179 22.542 12 22.542Z"
      />
      <path
        fill="#FFBFC5"
        d="M4.546 19.454A10.542 10.542 0 0 1 19.454 4.546L4.546 19.454Z"
      />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M12 22.542c5.822 0 10.542-4.72 10.542-10.542S17.822 1.458 12 1.458 1.46 6.178 1.46 12 6.179 22.542 12 22.542ZM7.417 7.417l9.166 9.166M16.583 7.417l-9.166 9.166"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h24v24H0z" />
      </clipPath>
    </defs>
  </svg>
)
