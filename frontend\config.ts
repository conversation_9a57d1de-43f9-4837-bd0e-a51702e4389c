import { createPublicClient } from 'viem'
import {
  http,
} from 'wagmi'
import { 
  createConfig,
} from '@privy-io/wagmi';
import {
  polygon,
  sepolia,
} from 'wagmi/chains'

const isPolygon = process.env.NEXT_PUBLIC_CURRENT_CHAIN === "POLYGON_MAIN"

export const config = createConfig({
  chains: [
    isPolygon ? polygon : sepolia,
  ],
  transports: {
    [polygon.id]: http(),
    [sepolia.id]: http(),
  },
})


export const publicClient = createPublicClient({
  chain: isPolygon ? polygon : sepolia,
  transport: isPolygon ? http() : http(`https://sepolia.infura.io/v3/${process.env.NEXT_PUBLIC_RPC_URL}`),
})
