// app/robots.ts
import { MetadataRoute } from "next";

export default function robots (): MetadataRoute.Robots {
  const isProduction = process.env.VERCEL_ENV === "production";

  if (!isProduction) {
    return {
      rules: {
        userAgent: "*",
        disallow: "/",
      },
    };
  }

  return {
    rules: {
      userAgent: "*",
      disallow: [
        "/accelerate",
        "/accelerate/twitter",
        "/my-dreams",
        "/api",
        "/media-pilot",
        "/dev",
      ],
    },
    sitemap: "https://dreamstartr.com/sitemap.xml",
  };
}
