'use client'

import React, {
  useState, useRef, useEffect,
  useMemo,
} from 'react';
import { motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import { useOutsideClick } from '@/common/hooks';
import { Token } from '@0xsquid/squid-types';
import { secondaryFont } from '@/common/utils/localFont';

interface TokenSelectorProps {
  tokens: Token[];
  currentToken: Token | undefined;
  setCurrentToken: (token: Token) => void;
}

export const TokenSelector: React.FC<TokenSelectorProps> = ({
  tokens,
  currentToken,
  setCurrentToken,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchMode, setIsSearchMode] = useState(false);
  const [visibleCount, setVisibleCount] = useState(20);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  useOutsideClick({
    isVisible: isOpen,
    ref: dropdownRef,
    callback: () => {
      setIsOpen(false);
      setIsSearchMode(false);
      setSearchQuery('');
      setVisibleCount(20);
    },
  });

  useEffect(() => {
    if (isSearchMode && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isSearchMode]);
  useEffect(() => {
    setVisibleCount(20);
  }, [searchQuery]);

  const filteredTokens = useMemo(() => 
    tokens.filter((token) =>
      token.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
      token.name.toLowerCase().includes(searchQuery.toLowerCase()),
    ).sort((a, b) => a.name.localeCompare(b.name)),
  [tokens, searchQuery]);

  const visibleTokens = useMemo(() => 
    filteredTokens.slice(0, visibleCount),
  [filteredTokens, visibleCount]);

  const handleTokenSelect = (token: Token) => {
    setCurrentToken(token);
    setIsOpen(false);
    setIsSearchMode(false);
    setSearchQuery('');
    setVisibleCount(20);
  };
  const handleScroll = () => {
    if (!listRef.current) {
      return;
    }     
    const { 
      scrollTop, scrollHeight, clientHeight,
    } = listRef.current;
    
    if (scrollTop + clientHeight >= scrollHeight - 50 && visibleCount < filteredTokens.length) {
      setVisibleCount(prev => Math.min(prev + 20, filteredTokens.length));
    }
  };
  return (
    <div className="relative w-full" ref={dropdownRef}>
      <div className="relative w-full">
        {isSearchMode ? (
          <input
            ref={inputRef}
            type="text"
            placeholder="Search..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="rounded-xl w-full border border-yellow-500/10 bg-violets-are-blue/5 backdrop-blur-sm py-3 px-4 text-white text-sm focus:outline-none focus:border-yellow-500/50"
            onBlur={() => {
              if (searchQuery === '') {
                setIsSearchMode(false);
              }
            }}
            onClick={(e) => {
              e.stopPropagation();
              setIsOpen(true);
            }}
          />
        ) : (
          <button
            className="w-full flex items-center justify-between rounded-xl border border-yellow-500/10 bg-violets-are-blue/5 backdrop-blur-sm py-2.5 px-4 text-white text-sm focus:outline-none focus:border-yellow-500/50 transition-all duration-200"
            onClick={() => {
              if (!isOpen) {
                setIsOpen(true);
                setIsSearchMode(true);
              }
            }}
          >
            <div className="flex items-center gap-2">
              {currentToken?.logoURI && (
                // eslint-disable-next-line @next/next/no-img-element
                <img
                  src={currentToken.logoURI}
                  alt={currentToken.symbol}
                  className="w-6 h-6 rounded-full"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              )}
              <span>
                {currentToken?.symbol || 'Select Token'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <ChevronDown className={`transition-transform duration-200 w-5 h-5 ${isOpen ? 'rotate-180' : ''}`} />
            </div>
          </button>
        )}
      </div>

      {isOpen && (
        <motion.div
          initial={{
            opacity: 0,
            y: -10,
          }}
          animate={{
            opacity: 1,
            y: 0,
          }}
          exit={{
            opacity: 0,
            y: -10,
          }}
          transition={{ duration: 0.2 }}
          className="absolute z-50 w-full mt-2 rounded-xl shadow-lg overflow-hidden border border-yellow-500/10 bg-light-yellow/70 backdrop-blur-sm"
        >
          <div ref={listRef} className="max-h-60 overflow-y-auto py-1" onScroll={handleScroll}>
            {visibleTokens.length > 0 ? (
              visibleTokens.map((token, idx) => (
                <button
                  key={`${token.address}-${idx}`}
                  className="flex items-center w-full px-4 py-3 text-sm hover:bg-violets-are-blue/10 transition-colors duration-150 text-white"
                  onClick={() => handleTokenSelect(token)}
                >
                  <div className="flex items-center gap-2 w-full">
                    {token.logoURI && (
                      // eslint-disable-next-line @next/next/no-img-element
                      <img
                        src={token.logoURI}
                        alt={token.symbol}
                        className="w-6 h-6 rounded-full"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    )}
                    <span>
                      {token.name}
                    </span>

                    <div className={`ml-auto px-2 py-1 text-xs font-medium rounded-full bg-violets-are-blue/10 text-white ${secondaryFont.className}`}>
                      {token.symbol}
                    </div>
                  </div>
                </button>
              ))
            ) : (
              <div className="px-4 py-3 text-sm text-gray-400">
                No tokens found
              </div>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};
