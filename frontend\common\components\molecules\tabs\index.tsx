"use client";

import {
  useEffect, useRef, useState,
} from "react";
import { motion } from "framer-motion";
import { cn } from "@/common/utils/helpers";

export type Tab = {
  title: string;
  value: string;
  renderKey?: number;
  error?: boolean;
  content?: string | React.ReactNode | any;
  step: number;
};

export const Tabs = ({
  tabs: propTabs,
  containerClassName,
  activeTabClassName,
  tabClassName,
  contentClassName,
  handleMutations,
}: {
  tabs: Tab[];
  containerClassName?: string;
  activeTabClassName?: string;
  tabClassName?: string;
  contentClassName?: string;
  handleMutations?: (value: Tab) => void;
}) => {
  const sortedTabs = [...propTabs].sort((a, b) => a.step - b.step);
  const instructionTab = sortedTabs.find(tab => tab.value === "instruction");
  const defaultTab = instructionTab || sortedTabs[0];

  const [active, setActive] = useState<Tab>(defaultTab);
  const [tabs, setTabs] = useState<Tab[]>(sortedTabs);
  const [containerWidth, setContainerWidth] = useState(0);
  const parentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!parentRef.current) {
      return;
    }
    const updateWidth = () => {
      const parentWidth = parentRef.current?.offsetWidth ?? 0;
      setContainerWidth(parentWidth);
    };

    updateWidth();

    const resizeObserver = new ResizeObserver(updateWidth);
    if (parentRef.current) {
      resizeObserver.observe(parentRef.current);
    }

    return () => {
      if (parentRef.current) {
        resizeObserver.unobserve(parentRef.current);
      }
    };
  }, []);

  useEffect(() => {
    const newSortedTabs = [...propTabs].sort((a, b) => a.step - b.step);
    setTabs(newSortedTabs);
    const currentActive = newSortedTabs.find(tab => tab.value === active.value);
    if (currentActive) {
      if (currentActive.renderKey !== active.renderKey) {
        setActive(currentActive);
      }
    }
  }, [propTabs, active.value, active.renderKey]);

  const moveSelectedTabToTop = (idx: number) => {
    const newTabs = [...tabs];
    const selectedTab = newTabs.splice(idx, 1)[0];
    newTabs.unshift(selectedTab);
    setTabs(newTabs);
    setActive(selectedTab);
  };

  return (
    <div ref={parentRef} className="w-full">
      <motion.div
        initial={{
          opacity: 0,
          y: 10,
        }}
        animate={{
          opacity: 1,
          y: 0,
        }}
        transition={{
          delay: 2.1,
        }}
        style={{
          width: `${containerWidth}px`,
        }}
        className={cn(
          "flex flex-row items-center mt-0 gap-1 xl:mt-0 justify-start [perspective:1000px] border-t border-b border-violets-are-blue/10 bg-violets-are-blue/5 rounded-2xl relative overflow-auto lg:overflow-visible no-visible-scrollbar max-w-full w-full",
          containerClassName,
        )}
      >
        {tabs.sort((a, b) => a.step - b.step).map((tab, idx) => (
          <button
            key={`${tab.value}-content-${tab.renderKey ?? ''}`}
            type="button"
            onClick={() => {
              moveSelectedTabToTop(idx);
              if (handleMutations) {
                handleMutations(tab);
              }
            }}
            className={cn("relative px-4 py-1.5 rounded-full", tabClassName)}
            style={{
              transformStyle: "preserve-3d",
            }}
          >
            {active.value === tab.value && (
              <motion.div
                layoutId={`clickedbutton`}
                transition={{
                  type: "spring",
                  bounce: 0.3,
                  duration: 0.6,
                }}
                className={cn(
                  "absolute inset-0 bg-gradient-to-tr from-han-purple to-tulip rounded-2xl",
                  activeTabClassName,
                )}
              />
            )}
            {tab.error && active.value !== tab.value && (
              <div
                className={cn(
                  "absolute inset-0 bg-tulip rounded-lg",
                  activeTabClassName,
                )}
              />
            )}
            <span
              className={`relative flex gap-1 items-center whitespace-nowrap font-medium text-sm ${
                active.value === tab.value ? "text-white" : "text-neutral-300"
              }`}
            >
              {tab.title}
            </span>
          </button>
        ))}
      </motion.div>
      <motion.div
        initial={{
          opacity: 0,
        }}
        animate={{
          opacity: 1,
        }}
        transition={{
          delay: 2,
        }}
      >
        <FadeInDiv
          active={active}
          className={cn("", contentClassName)}
        />
      </motion.div>
    </div>
  );
};

export const FadeInDiv = ({
  className,
  active,
}: {
  className?: string;
  active: Tab;
}) => {
  return (
    <div className="relative w-full h-full">
      <div
        className={cn("w-full h-full transform-none", className)}
      >
        {active.content}
      </div>
    </div>
  );
};
