import { ReactNode } from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: 'Terms',
  alternates: {
    canonical: `https://dreamstartr.com/terms`,
  },
  openGraph: {
    title: 'Terms | DreamStartr',
    url: `https://dreamstartr.com/terms`,
  },
  twitter: {
    title: 'Terms | DreamStartr',
  },
}

export default function Layout ({
  children,
}: {
  children: ReactNode;
}) {
  return (
    children
  )
}
