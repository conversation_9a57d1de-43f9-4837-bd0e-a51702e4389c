const header = {
  connectButton: {
    connectWallet: 'Login',
    wrongNetwork: 'Wrong Network',
    myIdeas: 'My Projects',
    logout: 'Logout',
    copyAddress: 'Copy Address',
    fundAccount: 'Add Funds',
    connectedTo: 'Connected to',
  },
  myDreams: 'My Projects',
  generate: 'Launch ISO',
  searchDreams: 'Search Projects',
  bigger: 'Build',
  dream: 'Launch',
  faster: '& Scale',
  dreamsSubHeading: 'Discover subscription projects',
  pilotSubheading: "Build your community",
  dreamathonSubheading: "Join builder events",
  searchIdeas: {
    placeholder: 'Search for Projects',
    noIdeasFound: 'No Projects found',
  },
}

const notFound = {
  heading: 'Something went wrong',
  subHeading1: 'Brace yourself till we get the error fixed',
  subHeading2: 'You may also refresh the page or try again later',
  buttonTitle: 'Return Home',
}

const footer = {
  terms: 'Terms',
  aboutUs: 'About Us',
  privacyPolicy: 'Privacy',
  contactUs: 'Contact us',
  contactUsModal: {
    heading: 'Contact Us',
    submitButton: 'Send',
    youCanContact: 'You can contact us at',
  },
}

const createIdea = {
  categories: {
    noTagsFound: 'No Categories found',
  },
  addCategoryError: 'Unable to add new category',
  accountWrongError: 'Please connect with your account to edit this dream',
  generateError: 'Something went wrong. Please try again later.',
  transactionError: 'Transaction reverted. Please try again.',
  aiRegenerateError: "Failed to regenerate content. Please try again.",
  insufficientBalance: 'You have insufficient balance to create a dream',
  whatToImprove: 'What do you want to improve?',
  reservedDomain: 'This domain is reserved. Please choose another ticker',
  heading: 'Create Token',
  subHeading: 'Launch Your Dream',
  subHeadingCreated: 'Design Your Dream',
  subHeadingFundingReached: 'Funding Reached',
  subHeading2Part1: 'Share your dream in detail to connect with potential believers. We will create an',
  subHeading2Part2: 'that you can trade on',
  subHeading2FundingReached: "We would suggest not to update the dream details as the funding target has been met and keep the original idea intact.",
  tokenCreationFeeLabel: 'One Time Fee',
  maxSupplyLabel: 'Max Supply',
  initialMintLabel: 'Initial Supply',
  tokensSuffix: ' Tokens',
  targetInfoPart1: 'After funding target of ',
  targetInfoPart2: 'is met, a liquidity pool will be created on Uniswap.',
  ideaNotFound: "Dream not found!",
  ideaCreatedMessage: 'Dream successfully created!',
  ideaDetailsUpdated: 'Dream details updated!',
  errorOccurred: "Something went wrong. Please try again!",
  cantEditError: "You can't update as you are not the owner of this dream!",
  validationErrors: {
    nameRequired: 'Please enter a name for your dream',
    nameMinError: 'Please enter more than 2 characters',
    nameMaxError: 'Please enter less than 20 characters',
    tickerRequired: 'Please enter a name for your ticker',
    tickerMinError: 'Please enter more than 2 characters',
    tickerMaxError: 'Please enter less than 20 characters',
    logoRequired: 'Please upload logo or image depicting your dream',
    descriptionRequired: 'Please provide a description for your dream',
    descriptionMinError: 'Please enter more than 10 characters',
    descriptionMaxError: 'Please enter less than 1000 characters',
    categoriesRequired: 'Please select a category',
    websiteRequired: 'Please provide a valid link',
    twitterInvalid: 'Please provide a valid X link',
    telegramInvalid: 'Please provide a valid telegram link',
  },
  uploadDifferent: 'Upload a different image',
  imageUpload: {
    title: 'Dream Logo',
    postImage: 'Post Image',
    imageSizeError: 'Please upload image less than 5 mb',
    imageType: 'This is a wrong file format. Only image files are allowed.',
    uploadError: 'Could not add file. Please try again.',
    uploading: 'Uploading...',
    uploadLabel: 'Upload here',
  },
  form: {
    name: 'Project Title',
    address: 'Address',
    ticker: 'Subscription Token',
    description: 'Project Description',
    category: 'Select Category',
    website: 'Website',
    websitePreview: 'Project Preview',
    twitter: 'X(Twitter)',
    telegram: 'Telegram',
    submitLabel: 'Submit',
    connectWallet: 'Connect Wallet',
    brandingDetails: 'Project Identity',
    dreamDetails: 'Describe your Project',
    generateButtonLabel: 'Restart',
    optional: 'Recommended',
    communityChannels: 'Community Channels',
    createIdea: 'Create Project',
    confirm: 'Confirm',
    subdomainExisting: 'Please note you will be assigned a alternate subdomain as this subdomain is already taken',
    viewDream: 'View Project',
    socialAssistants: 'Community Tools',
    note: 'Note: You can always edit some parts of the project later',
  },
}

const generateIdea = {
  promptPlaceholders: [
    "Create a subscription-based fitness coaching platform",
    "Launch a premium newsletter for tech professionals",
    "Build a membership site for creative professionals",
    "Create an exclusive learning community for developers",
    "Launch a subscription box service for artisan products",
  ],
  promptLoadingStates: [
    {
      text: "Analyzing Market Opportunity",
    },
    {
      text: "Designing Subscription Model",
    },
    {
      text: "Creating Brand Identity",
    },
    {
      text: "Building Member Experience",
    },
    {
      text: "Setting Up Payment Systems",
    },
    {
      text: "Preparing ISO Launch",
    },
  ],
  greeting: {
    morning: "Good morning, tell me about your project idea",
    afternoon: "Good afternoon, tell me about your project idea",
    evening: "Good evening, tell me about your project idea",
  },
  whatsYourDream: "Tell me about your project idea",
  generateError: 'Something went wrong. Please try again later.',
  poweredBy: 'powered by',
  readyTo: 'You are one step away from launching your subscription business',
  continue: 'Continue to Launch ISO',
  proceed: 'Proceed',
  orEnhance: 'Or keep refining the project',
  h1: 'Generate your Subscription Business with AI',
}

const ideas = {
  ideaCard: {
    raised: 'Revenue',
    holders: 'Subscribers',
    trade: 'Subscribe',
    marketcap: 'Total Value',
    uniswapLabel: 'SUB',
    openUniswap: 'View Subscription',
    dexLabel: 'ISO',
    openX: 'Open X',
    openWebsite: 'View Website',
    openDex: 'View ISO Details',
    openTelegram: 'Open Telegram',
  },
  currentIdeas: 'Project Gallery',
  currentIdeasSubHeading: 'Discover successful subscription projects ready for community support',
  noIdeasHeading: 'No projects yet in this category',
  noIdeasSubHeading: 'Be the first to contribute! Share your innovative subscription projects and help grow this category.',
  registerIdea: 'Register Existing Project',
  launchNew: 'Launch New ISO',
  detailedView: 'Detailed View',
  compactView: 'Compact View',
  refreshData: 'Refresh Data',
}

const homePage = {
  timeline: {
    heading: 'How to Launch Your Project?',
    subHeading: 'Transform your innovative ideas into sustainable subscription-based projects with our comprehensive ISO Hub',
  },
  subHeading: 'The ultimate platform for builders to launch, grow, and monetize their projects through Initial Subscription Offerings',
  h1: 'Build. Launch. Scale.',
  subHeading1: "Every great project starts with a vision.",
  subHeading2: 'What if you could turn your next big idea into a thriving subscription business?',
  subHeading3: "From concept to community-driven success. Launch your Initial Subscription Offering and build sustainable revenue streams with engaged subscribers.",
  heroWords: [
    {
      text: "Build.",
    },
    {
      text: "Launch.",
    },
    {
      text: "Scale.",
      className: "gradientText",
    },
  ],
  answeredByHuman: 'Supported by Expert Advisors',
  haveDream: 'Have an existing project?',
  register: 'Register Project',
  answeredByAi: 'Powered by AI Tools',
  fundButtonLabel: 'Register Existing Project',
  generateButtonLabel: 'Launch New ISO',
  trendingIdeas: 'Featured Projects',
  trendingIdeasSubHeading: 'Discover successful subscription-based projects built by our community',
  readyToTurn: 'Ready to Launch Your Subscription Business?',
  readyToTurnSubheading: 'Join DreamStartr ISO Hub today and transform your ideas into sustainable revenue streams. Launch your Initial Subscription Offering in minutes.',
  dreamGallery: 'Explore Projects',
  stats: {
    heading: 'Platform Overview',
    description: 'See how builders are creating sustainable subscription businesses on our platform',
    dreamsLaunched: 'Projects launched on platform',
    agentsRunning: 'AI agents actively running',
    aiPowered: 'AI-powered development support',
    fundsRaised: 'Total funds raised for dreams',
    activeAgents: 'Running',
  },
  developmentProcess: {
    heading: 'Process',
    stepOneTitle: 'Dream Validation',
    stepOneInfo: "From raw concept to refined vision",
    stepOnePoints: `AI-enhanced ideation 💡 with expert community feedback 💬 and direct market validation from target users 🎯`,
    stepTwoTitle: 'AI-Powered Development',
    stepTwoInfo: "Where vision meets execution",
    stepTwoPoints: `Rapid development with our AI-powered IDE 💻 and real-time community-driven validation 💬`,
    stepThreeTitle: 'Sustainable Launch',
    stepThreeInfo: "Built for lasting impact",
    stepThreePoints: `Customized token economics 💰, engaged early users 👥 and proven growth strategies 📈`,
    stepFourTitle: 'Accelerated Growth',
    stepFourInfo: 'Scaling with purpose',
    stepFourPoints: `Access strategic partnerships 🤝, expand market reach 🌐 and track measurable impact 📊`,
  },
}

const ideaPage = {
  createdBy: 'Created by',
  believers: 'Believers',
  tokenAddress: 'Token address',
  fundingRaised: 'Raised',
  visitWebsite: 'View website',
  attachImage: 'Attach image',
  categories: 'Categories',
  connectWallet: 'Connect Wallet',
  tokenDetails: 'Token Details',
  transfers: 'Transfers',
  trade: 'Trade',
  uniswapLabel: 'UNI',
  conversations: 'Comments',
  post: 'Post',
  noComments: 'No comments yet. Be the first to start the conversation!',
  preview: 'Preview',
  buyFeed: 'Buy Feed',
  notActive: "This idea is no longer active. Redirecting to homepage.",
  sellFeed: 'Sell Feed',
  owners: 'Believers',
  chart: 'Buy Trend',
  stakeholders: 'Believers',
  stakeholdersDesc: 'Our visionary believers making this dream possible',
  checkTransHeading: 'Check out ongoing trades on',
  transactionsTable: {
    columnOne: 'Account',
    columnTwo: 'Value',
    columnThree: 'Time',
    columnFour: 'Transaction',
  },
  ownersTable: {
    columnOne: 'Account',
    columnTwo: 'Percentage',
  },
  limitedTokensError: 'Limited tokens. There is only %amount% tokens left.',
  purchaseSuccess: 'Congratulations! You have purchased',
  purchaseError: 'Purchase could not be completed. Please try again!',
  postError: 'Unable to add new post',
  bondingCurveProgress: 'Progress',
  buyTokensFor: 'Buy tokens for',
  availableTokens: 'Available tokens',
  purchaseLabel: 'Get Quote',
  fetchQuoteError: 'Unable to fetch quote',
  approveTokenError: 'Unable to approve token',
  swapTokensSuccess: 'You have successfully swapped your tokens',
  transactionSwapError: 'Transaction failed. Please check your balance and try again',
  swapGeneralError: 'An error occurred while processing the swap',
  youWillReceive: 'You will receive',
  maxButton: 'Max',
  deleteComment: 'Delete comment',
  likeComment: 'Like comment',
  areYouSureToDelete: 'Are you sure you want to delete this comment?',
  editIdea: 'Edit Dream',
  codeAssist: 'Code Assist',
  boostIdea: 'Social Agents',
  bondingCurve: 'Bonding Curve Progress',
  buyPriceFetchError: 'Unable to fetch buying price',
  calculateTokensError: 'Unable to calculate tokens',
  priceQuoteError: 'Unable to get price quote',
  confirm: 'Confirm',
  for: 'for',
  confirmPurchase: 'Confirm purchase',
  buyTokens: 'Support Dream',
  buy: 'Buy',
  remaining: ' Remaining',
  swapTokens: 'Swap Tokens',
  swapTokensDesc: 'You can buy/sell tokens from the liquidity pool',
  openUniswap: 'Open Uniswap',
  dexLabel: 'DEX',
  openDex: 'Open DEX Screener',
  openTelegram: 'Open Telegram',
  openX: 'Open X',
  swapping: 'Swapping...',
  swap: 'Swap',
  buyTokensDesc: 'Join our community and shape the future - get tokens now 🚀',
  ensure: 'Ensure you have enough funds in your account',
  likelyFail: 'Note: This transaction will likely fail as you do not have enough funds',
  buyNow: 'Confirm',
  bondingCurveInfo: "When the market cap reaches %goal% %currency%, all the liquidity from the bonding curve will be deposited into Uniswap, and the LP tokens will be burned. Progression increases as the price goes up.",
}

const profile = {
  heading: 'Manage Projects',
  startAllAgents: 'Start All Tools',
  subheading: "Track your project progress and manage your subscription business",
  noIdeasHeading: 'You have not created any Project',
  noIdeasSubHeading: 'Create your first Project',
  registerIdea: 'Register Existing Project',
  launchNew: 'Launch New ISO',
  table: {
    ideaName: 'Project name',
    status: 'Status',
    ideaAddress: 'Address',
    ticker: 'Token',
    actions: 'Actions',
    view: 'View',
    dev: 'Dev Tools',
    review: 'Community Tools',
    edit: 'Edit Project',
  },
}
const dreamathon = {
  title: 'Builder Events:',
  animatedText: 'Connect & Create',
  description: 'Join our global community of builders creating the next generation of subscription businesses. Network, learn, and launch together.',
  ongoingEvents: 'Live Events',
  cities: 'Global Community',
  prizesTitle: 'Funding & Support',
  prizesDescription: 'Get funding and mentorship for your ISO',
  communityTitle: 'Builder Network',
  communityDescription: 'Connect with fellow builders and industry experts',
  viewMoreDetails: 'Join Builder Network',
}

const enhancedDevelopmentProcess = {
  sectionTitle: 'How ISO Hub Works',
  sectionDescription: 'Everything you need to launch and scale your subscription-based project',
  cards: {
    blockchain: {
      title: 'Subscription Management',
      description: 'Launch Initial Subscription Offerings with smart contract automation. Manage recurring payments, subscriber tiers, and revenue distribution seamlessly.',
    },
    mediaPilot: {
      title: 'Community Building',
      description: 'Build engaged subscriber communities with integrated social tools, exclusive content delivery, and member engagement analytics.',
    },
    dreamathons: {
      title: 'Builder Network',
      description: 'Connect with fellow builders, share resources, and collaborate on projects. Access mentorship and partnership opportunities within our ecosystem.',
    },
    devAgent: {
      title: 'Development Tools',
      description: 'Comprehensive development environment with AI-assisted coding, project templates, and deployment tools specifically designed for subscription businesses.',
      terminal: {
        command: '> create subscription service for fitness coaching',
        step1: '✔ Setting up subscription tiers and pricing.',
        step2: '✔ Generating member portal and content delivery.',
        step3: '✔ Configuring payment processing and analytics.',
        success: 'Success! Your ISO is ready to launch.',
      },
    },
  },
}

const manageIdea = {
  heading: ' Community Tools',
  subHeading: 'Meet your personal team of AI experts, each uniquely trained to help grow and manage your subscription business.',
  twitterDescription: 'This tool creates personalized content for your subscription business, schedules posts, engages with potential subscribers, and grows your community.',
  tweets: 'Posts Generated',
  posts: 'Content Created',
  postsSingular: 'Content Created',
  tweetsSingular: 'Post Generated',
  engagement: 'Subscriber Metrics',
  createSandboxError: 'Failed to create development environment. Please try again.',
  linkedInDescription: 'This tool creates professional content for your subscription business, schedules posts, engages with potential subscribers, and builds your professional network.',
  manageAgent: 'Manage Persona',
  modifyCharacter: 'Target X Users',
  agentSettings: 'Engagement Settings',
  upcomingPosts: 'Upcoming Tweets',
  upcomingPostsLinkedin: 'Upcoming Posts',
  devAgent: 'Dev Tools',
  openAgent: 'Open Tools',
  createSandbox: 'Create Environment',
  openSandbox: 'Open Environment',
  addNewDev: 'Add New',
  active: 'Active',
  yourDreams: 'Your Projects',
  partneredDreams: 'Partnered Projects',
  owned: 'Owned',
  drafts: 'Drafts',
  draft: 'Draft',
  back: 'Back to Project',
  devAgentDesc: 'The Development Tools provide a comprehensive environment to build your subscription business. Create member portals, payment systems, and content delivery platforms.',
  enableFollowing: 'Enable Following',
  enableFollowDescription: 'Allow agent to follow relevant accounts',
  enableActions: 'Enable Interactions',
  enableActionsDescription: 'Check and engage with tweets',
  enablePosts: 'Enable Tweeting',
  postInterval: 'Tweet Interval',
  postIntervalDesc: 'Duration between each tweet',
  interactionInterval: 'Interaction Interval',
  interactionIntervalDesc: 'Frequency of interactions(likes, replies)',
  followInterval: 'Follow Interval',
  followIntervalDesc: 'Frequency of following new accounts',
  enablePostsDescription: 'Allow agent to post personalized tweets',
  replies: 'Replies to Tweets',
  repliesSingular: 'Reply to Tweets',
  likes: 'Tweets Liked',
  likesSingular: 'Tweet Liked',
  retweets: 'Tweets Retweeted',
  retweetsSingular: 'Tweet Retweeted',
  followers: 'Accounts Followed',
  followersSingular: 'Account Followed',
  prev: 'Prev',
  next: 'Next',
  skip: 'Skip',
  agentRunning: 'Running since',
  twitterForm: {
    username: 'Username',
    password: 'Password',
    email: 'Email',
    submit: 'Start Agent',
    connect: 'Connect Wallet',
    stopAgent: 'Pause Agent',
    viewCharacter: 'View Settings',
    updateCharacter: 'Update Character',
    hideCharacter: 'Hide Settings',
  },
  character: {
    exampleOne: 'Example 1',
    exampleTwo: 'Example 2',
    exampleUserLabel: 'User',
    exampleAgentLabel: 'Agent',
    styleAll: 'All',
    styleChat: 'Chat',
    stylePost: 'Post',
    professional: 'Professional',
  },
  promptLoadingStates: [
    {
      text: "Crafting Your Digital Identity",
    },
    {
      text: "Preparing Your Agent",
    },
    {
      text: "Processing Platform Data",
    },
    {
      text: "Designing Engagement Plan",
    },
    {
      text: "Optimizing Post Schedule",
    },
  ],
  devLoadingStates: [
    {
      text: "Creating Sandbox",
    },
    {
      text: "Deploying Agent",
    },
    {
      text: "Building Dream",
    },
    {
      text: "Testing Dream",
    },
    {
      text: "Launching Dream",
    },
  ],
}

export const lang = {
  header,
  profile,
  createIdea,
  manageIdea,
  homePage,
  ideas,
  generateIdea,
  footer,
  notFound,
  ideaPage,
  dreamathon,
  enhancedDevelopmentProcess,
};

export default lang;
