import React, {
  forwardRef, useCallback, ChangeEvent, useEffect, useState, useRef,
} from 'react';
import {
  Minus, Plus,
} from 'lucide-react';

interface NumberInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  min?: number;
  max?: number;
  step?: number;
  value: number;
  width?: string;
  setValue: (value: number) => void;
  disabled?: boolean;
  error?: boolean;
  errorMessage?: string;
  onChange?: (value: number) => void;
}

const baseInputClasses = `flex w-full rounded-full text-sm font-semibold py-1.5 text-white border border-white/5
placeholder:text-gray-600 outline-none leading-tight
px-4 disabled:cursor-not-allowed bg-white/5 backdrop-blur-sm disabled:bg-white/15 disabled:border-white/50 disabled:text-neutral-200`;

const buttonClasses = `flex items-center justify-center p-1 shadow-sm top-1/2 -translate-y-1/2 h-[calc(100%px)] aspect-square rounded-full bg-white/5 hover:enabled:bg-gradient-to-tr from-han-purple to-tulip duration-200 ease-in-out transition-all
 text-white disabled:opacity-50 disabled:cursor-not-allowed`;

function useDebounce<T extends (...args: any[]) => void>(callback: T, delay: number) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const debouncedFn = useCallback((...args: any[]) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedFn;
}

const NumberInput = forwardRef<HTMLInputElement, NumberInputProps>(({
  min = 0,
  max = Infinity,
  step = 30,
  value,
  onChange,
  setValue,
  width = 'w-20',
  disabled = false,
  error = false,
  errorMessage,
  ...props
}, ref) => {
  const [localValue, setLocalValue] = useState(value);

  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  const updateParentValue = useCallback((newValue: number) => {
    setValue(newValue);
    onChange?.(newValue);
  }, [setValue, onChange]);

  const debouncedOrThrottledUpdate = useDebounce(updateParentValue, 1000);

  const handleIncrement = useCallback((): void => {
    const newValue = Math.min(localValue + step, max);
    setLocalValue(newValue);
    debouncedOrThrottledUpdate(newValue);
  }, [localValue, step, max, debouncedOrThrottledUpdate]);

  const handleDecrement = useCallback((): void => {
    const newValue = Math.max(localValue - step, min);
    setLocalValue(newValue);
    debouncedOrThrottledUpdate(newValue);
  }, [localValue, step, min, debouncedOrThrottledUpdate]);

  const handleInputChange = useCallback((e: ChangeEvent<HTMLInputElement>): void => {
    const newValue = parseInt(e.target.value) || min;
    const clampedValue = Math.max(min, Math.min(newValue, max));
    setLocalValue(clampedValue); // Update local state immediately for UI
    debouncedOrThrottledUpdate(clampedValue);
  }, [min, max, debouncedOrThrottledUpdate]);

  return (
    <div className='flex flex-col gap-1 items-end'>
      <div className={`${width} flex flex-col gap-2 relative`}>
        <div className="flex items-center gap-2">
          <button
            type="button"
            className={`${buttonClasses} absolute left-1 top-0 z-50`}
            onClick={handleDecrement}
            disabled={disabled || localValue <= min}
            aria-label="Decrease value"
          >
            <Minus size={14} strokeWidth={2} />
          </button>

          <input
            {...props}
            ref={ref}
            type="number"
            className={`${baseInputClasses} pointer-events-none text-center [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none`}
            value={localValue}
            onChange={handleInputChange}
            min={min}
            max={max}
            step={step}
            disabled={disabled}
            aria-invalid={error}
            aria-describedby={error && errorMessage ? `${props.name}-error` : undefined}
          />

          <button
            type="button"
            className={`${buttonClasses} absolute right-1 top-0 z-50`}
            onClick={handleIncrement}
            disabled={disabled || localValue >= max}
            aria-label="Increase value"
          >
            <Plus size={14} strokeWidth={2} />
          </button>
        </div>

        {error && errorMessage && (
          <p
            className="text-sm text-tulip"
            id={`${props.name}-error`}
            role="alert"
          >
            {errorMessage}
          </p>
        )}
      </div>
      <div className='text-neutral-400 text-xs'>{value > 1 ? "hours" : "hour"}</div>
    </div>
  );
});

NumberInput.displayName = "NumberInput";

export default NumberInput;
