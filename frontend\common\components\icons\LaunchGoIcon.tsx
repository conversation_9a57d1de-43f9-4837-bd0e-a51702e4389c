import type { SVGProps } from "react";

export const LaunchGoIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={15}
    fill="none"
    {...props}
  >
    <path fill="#FFEF5E" d="M1.44.957h21.12v13.39H1.44V.958Z" />
    <path fill="#FFF9BF" d="M1.44 14.348V.957h21.12L1.44 14.347Z" />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M1.44.957h21.12v13.39H1.44V.958Z"
    />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M16.131 9.087a1.378 1.378 0 1 1-2.754 0v-2.87a1.38 1.38 0 1 1 2.754 0v2.87ZM10.621 6.696v-.479a1.38 1.38 0 1 0-2.753 0v2.87a1.378 1.378 0 1 0 2.753 0v-.478h-.917"
    />
  </svg>
)
