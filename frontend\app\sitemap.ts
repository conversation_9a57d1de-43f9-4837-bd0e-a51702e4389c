import { SupabaseTables } from "@/common/constants";
import { MetadataRoute } from 'next';
import { createClient } from "@/common/utils/supabase/client";

type Subdomain = {
  url: string;
  lastModified: Date;
}

export default async function sitemap (): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'dreamstartr.com';
  const supabase = createClient();

  
  const { data: Subdomains } = await supabase
    .from(SupabaseTables.Subdomains)
    .select('subdomain')
  
  const subdomainUrls: Array<Subdomain> = [];
  
  if (Subdomains?.length) {
    Subdomains.forEach((subdomain) => {
      const data = {
        url: `https://${subdomain.subdomain.toLowerCase()}.${baseUrl}`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.8,
      }
      subdomainUrls.push(data as Subdomain)
    })
  }

  return [
    {
      url: `https://${baseUrl}`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `https://${baseUrl}/generate`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `https://${baseUrl}/dreamathon`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `https://${baseUrl}/dreams`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `https://lumina.${baseUrl}`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `https://${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `https://${baseUrl}/new`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    ...subdomainUrls,
  ]
}
