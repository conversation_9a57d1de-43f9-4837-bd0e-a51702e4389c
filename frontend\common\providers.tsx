'use client'

import {
  ReactNode,
  useEffect,
} from "react";
import {
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";
import { PrivyProvider } from '@privy-io/react-auth';
import { WagmiProvider } from '@privy-io/wagmi';
import { config } from "@/config";
import {
  optimism,
  sepolia,
  base,
  celo,
  arbitrum,
  mainnet,
  bsc,
  polygon,
  avalanche,
  linea,
  scroll,
  mantle,
  blast,
  immutableZkEvm,
  fraxtal,
  moonbeam,
  fantom,
  filecoin,
  kava,
} from "viem/chains";
import { usePathname } from "next/navigation";
import { useIsClient } from "./hooks";

const queryClient = new QueryClient()
const isPolygon = process.env.NEXT_PUBLIC_CURRENT_CHAIN === "POLYGON_MAIN"
export function Providers ({
  children,
}: {
  children: ReactNode;
}) {
  useEffect(() => {
    window.history.scrollRestoration = 'manual'
  }, []);
  const pathname = usePathname()
  const isClient = useIsClient()
  const isLuminaPage = pathname === '/lumina' || (isClient && window.location.hostname.startsWith('lumina.'))

  return (
    isLuminaPage ? (
      <PrivyProvider
        appId="cm5e1jvec04vlc6i7cy790b36"
        clientId="client-WY5fMYTPGVoB9z6sghJQ9qUbxYHDWt3ZnbbmDFk2ne5br"
        config={{
          supportedChains: [
            optimism,
            base,
            celo,
            arbitrum,
            mainnet,
            bsc,
            polygon,
            avalanche,
            linea,
            scroll,
            mantle,
            blast,
            immutableZkEvm,
            fraxtal,
            moonbeam,
            fantom,
            filecoin,
            kava,
          ],
          appearance: {
            landingHeader: 'Connect to your Dreams',
            loginMessage: 'Connect using Google or a Web3 account',
            theme: '#0e1111',
            showWalletLoginFirst: true,
            walletList: ['wallet_connect', 'rainbow', 'metamask'],
            accentColor: '#7E5EF2',
            logo: 'https://bronze-deep-gazelle-81.mypinata.cloud/ipfs/bafkreic7bpkufqn2q3k6tfnj255dgwfg43jm6cg2ldn4i4mqaep6tqszxa',
          },
          embeddedWallets: {
            createOnLogin: 'users-without-wallets',
            requireUserPasswordOnCreate: true,
            showWalletUIs: true,
          },
          defaultChain: base,
          fundingMethodConfig: { 
            moonpay: { 
              paymentMethod: 'credit_debit_card',
              uiConfig: { 
                accentColor: '#7E5EF2', 
                theme: 'dark',
              },
            }, 
          }, 
        }}
      >
        <QueryClientProvider client={queryClient}>
          <WagmiProvider config={config}>
            {children}
          </WagmiProvider>
        </QueryClientProvider>
      </PrivyProvider>
    ) : (
      <PrivyProvider
        appId="cm5e1jvec04vlc6i7cy790b36"
        clientId="client-WY5fMYTPGVoB9z6sghJQ9qUbxYHDWt3ZnbbmDFk2ne5br"
        config={{
          supportedChains: isPolygon ? [polygon] : [sepolia],
          appearance: {
            landingHeader: 'Connect to your Dreams',
            loginMessage: 'Connect using Web2 and Web3 login methods',
            theme: '#0e1111',
            showWalletLoginFirst: true,
            walletList: ['wallet_connect', 'rainbow', 'metamask'],
            accentColor: '#7E5EF2',
            logo: 'https://bronze-deep-gazelle-81.mypinata.cloud/ipfs/bafkreic7bpkufqn2q3k6tfnj255dgwfg43jm6cg2ldn4i4mqaep6tqszxa',
          },
          embeddedWallets: {
            createOnLogin: 'users-without-wallets',
            requireUserPasswordOnCreate: true,
            showWalletUIs: true,
          },
          defaultChain: isPolygon ? polygon : sepolia,
          fundingMethodConfig: { 
            moonpay: { 
              paymentMethod: 'credit_debit_card',
              uiConfig: { 
                accentColor: '#7E5EF2', 
                theme: 'dark',
              },
            }, 
          }, 
        }}
      >
        <QueryClientProvider client={queryClient}>
          <WagmiProvider config={config}>
            {children}
          </WagmiProvider>
        </QueryClientProvider>
      </PrivyProvider>
    )
  )
}
