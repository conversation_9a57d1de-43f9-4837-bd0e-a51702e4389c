'use client'

import React, {
  useState, useRef, useEffect,
} from 'react';
import { motion } from 'framer-motion';
import {
  ChevronDown,
} from 'lucide-react';
import { useOutsideClick } from '@/common/hooks';
import { ChainData } from '@0xsquid/squid-types';
import { secondaryFont } from '@/common/utils/localFont';
import { CircularSpinner } from '@/common/components/atoms';

interface ChainSelectorProps {
  chains: ChainData[];
  fetchTokensAndSwitchChain: (chain: string) => void;
  currentChainId: string | bigint;
  setCurrentChainId: (chainId: string) => void;
}

export const ChainSelector: React.FC<ChainSelectorProps> = ({
  chains,
  currentChainId,
  setCurrentChainId,
  fetchTokensAndSwitchChain,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchMode, setIsSearchMode] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useOutsideClick({
    isVisible: isOpen,
    ref: dropdownRef,
    callback: () => {
      setIsOpen(false);
      setIsSearchMode(false);
      setSearchQuery('');
    },
  });

  useEffect(() => {
    if (isSearchMode && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isSearchMode]);

  const getChainData = (
    chains: ChainData[],
    chainId: string | bigint,
  ): ChainData | undefined => chains?.find((chain) => chain.chainId == chainId);

  const currentChain = getChainData(chains, currentChainId);

  const filteredChains = chains
    .filter((chain) =>
      chain.networkName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      chain.networkIdentifier.toLowerCase().includes(searchQuery.toLowerCase()),
    )
    .sort((a, b) => a.networkName.localeCompare(b.networkName));

  const handleChainSelect = (chainId: string) => {
    setCurrentChainId(chainId);
    fetchTokensAndSwitchChain(chainId)
    setIsOpen(false);
    setIsSearchMode(false);
    setSearchQuery('');
  };

  return (
    <div className="relative w-full" ref={dropdownRef}>
      <div className="relative w-full">
        {isSearchMode ? (
          <input
            ref={inputRef}
            type="text"
            placeholder="Search..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="rounded-xl w-full border border-yellow-500/10 bg-violets-are-blue/5 backdrop-blur-sm py-3 px-4 text-white text-sm focus:outline-none focus:border-yellow-500/50"
            onBlur={() => {
              if (searchQuery === '') {
                setIsSearchMode(false);
              }
            }}
            onClick={(e) => {
              e.stopPropagation();
              setIsOpen(true);
            }}
          />
        ) : (
          <button
            className="w-full flex items-center justify-between rounded-xl border border-yellow-500/10 bg-violets-are-blue/5 backdrop-blur-sm py-2.5 px-4 text-white text-sm focus:outline-none focus:border-yellow-500/50 transition-all duration-200"
            onClick={() => {
              if (!isOpen) {
                setIsOpen(true);
                setIsSearchMode(true);
              }
            }}
          >
            <div className="flex items-center gap-2">
              {currentChain?.chainIconURI && (
                // eslint-disable-next-line @next/next/no-img-element
                <img
                  src={currentChain.chainIconURI}
                  alt={currentChain.networkIdentifier}
                  className="w-6 h-6 rounded-full"
                />
              )}
              <span>
                {currentChain?.networkName || <CircularSpinner />}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <ChevronDown className={`transition-transform duration-200 w-5 h-5 ${isOpen ? 'rotate-180' : ''}`} />
            </div>
          </button>
        )}
      </div>

      {isOpen && (
        <motion.div
          initial={{
            opacity: 0,
            y: -10,
          }}
          animate={{
            opacity: 1,
            y: 0,
          }}
          exit={{
            opacity: 0,
            y: -10,
          }}
          transition={{ duration: 0.2 }}
          className="absolute z-50 w-full mt-2 rounded-xl shadow-lg overflow-hidden border border-yellow-500/10 bg-light-yellow/70 backdrop-blur-sm"
        >
          <div className="max-h-60 overflow-y-auto py-1">
            {filteredChains.length > 0 ? (
              filteredChains.map((chain) => (
                <button
                  key={chain.chainId}
                  className="flex items-center w-full px-4 py-3 text-sm hover:bg-violets-are-blue/10 transition-colors duration-150 text-white"
                  onClick={() => handleChainSelect(chain.chainId)}
                >
                  <div className="flex items-center gap-2 w-full">
                    {chain.chainIconURI && (
                      // eslint-disable-next-line @next/next/no-img-element
                      <img
                        src={chain.chainIconURI}
                        alt={chain.networkIdentifier}
                        className="w-6 h-6 rounded-full"
                      />
                    )}
                    <span>
                      {chain.networkName}
                    </span>

                    <div className={`ml-auto px-2 py-1 text-xs font-medium rounded-full bg-violets-are-blue/10 text-white ${secondaryFont.className}`}>
                      {chain.nativeCurrency.symbol}
                    </div>
                  </div>
                </button>
              ))
            ) : (
              <div className="px-4 py-3 text-sm text-gray-400">
                No chains found
              </div>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};
