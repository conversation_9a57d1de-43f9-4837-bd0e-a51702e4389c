globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/home/<USER>"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/home/<USER>":{"*":{"id":"(ssr)/./app/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/home/<USER>":{"*":{"id":"(ssr)/./app/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./common/components/atoms/index.ts":{"*":{"id":"(ssr)/./common/components/atoms/index.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./common/components/organisms/footer/index.tsx":{"*":{"id":"(ssr)/./common/components/organisms/footer/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./common/components/organisms/header/index.tsx":{"*":{"id":"(ssr)/./common/components/organisms/header/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./common/components/organisms/modal/defaultModal.tsx":{"*":{"id":"(ssr)/./common/components/organisms/modal/defaultModal.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./common/components/organisms/modal/index.tsx":{"*":{"id":"(ssr)/./common/components/organisms/modal/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":{"*":{"id":"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js":{"*":{"id":"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./common/providers.tsx":{"*":{"id":"(ssr)/./common/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/error.tsx":{"*":{"id":"(ssr)/./app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./common/components/atoms/loader/style.css":{"*":{"id":"(ssr)/./common/components/atoms/loader/style.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\hero.tsx":{"id":"(app-pages-browser)/./app/home/<USER>","name":"*","chunks":["app/home/<USER>","static/chunks/app/home/<USER>"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\luminaCasaSection.tsx":{"id":"(app-pages-browser)/./app/home/<USER>","name":"*","chunks":["app/home/<USER>","static/chunks/app/home/<USER>"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\index.ts":{"id":"(app-pages-browser)/./common/components/atoms/index.ts","name":"*","chunks":["app/home/<USER>","static/chunks/app/home/<USER>"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\organisms\\footer\\index.tsx":{"id":"(app-pages-browser)/./common/components/organisms/footer/index.tsx","name":"*","chunks":["app/home/<USER>","static/chunks/app/home/<USER>"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\organisms\\header\\index.tsx":{"id":"(app-pages-browser)/./common/components/organisms/header/index.tsx","name":"*","chunks":["app/home/<USER>","static/chunks/app/home/<USER>"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\organisms\\modal\\defaultModal.tsx":{"id":"(app-pages-browser)/./common/components/organisms/modal/defaultModal.tsx","name":"*","chunks":["app/home/<USER>","static/chunks/app/home/<USER>"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\organisms\\modal\\index.tsx":{"id":"(app-pages-browser)/./common/components/organisms/modal/index.tsx","name":"*","chunks":["app/home/<USER>","static/chunks/app/home/<USER>"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":["app/home/<USER>","static/chunks/app/home/<USER>"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\esm\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":["app/home/<USER>","static/chunks/app/home/<USER>"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\preload-css.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":["app/home/<USER>","static/chunks/app/home/<USER>"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\esm\\shared\\lib\\lazy-dynamic\\preload-css.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":["app/home/<USER>","static/chunks/app/home/<USER>"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\providers.tsx":{"id":"(app-pages-browser)/./common/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\@next\\font\\local\\target.css?{\"path\":\"common\\\\utils\\\\localFont.ts\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"../../public/fonts/Latinotype-Trenda-Thin.otf\",\"weight\":\"200\"},{\"path\":\"../../public/fonts/Latinotype-Trenda-Light.otf\",\"weight\":\"300\"},{\"path\":\"../../public/fonts/Latinotype-Trenda-Regular.otf\",\"weight\":\"400\"},{\"path\":\"../../public/fonts/Latinotype-Trenda-Semibold.otf\",\"weight\":\"500\"},{\"path\":\"../../public/fonts/Latinotype-Trenda-Bold.otf\",\"weight\":\"600\"},{\"path\":\"../../public/fonts/Latinotype-Trenda-Black.otf\",\"weight\":\"700\"},{\"path\":\"../../public/fonts/Latinotype-Trenda-Heavy.otf\",\"weight\":\"800\"}],\"variable\":\"--font-primaryFont\"}],\"variableName\":\"primaryFont\"}":{"id":"(app-pages-browser)/./node_modules/@next/font/local/target.css?{\"path\":\"common\\\\utils\\\\localFont.ts\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"../../public/fonts/Latinotype-Trenda-Thin.otf\",\"weight\":\"200\"},{\"path\":\"../../public/fonts/Latinotype-Trenda-Light.otf\",\"weight\":\"300\"},{\"path\":\"../../public/fonts/Latinotype-Trenda-Regular.otf\",\"weight\":\"400\"},{\"path\":\"../../public/fonts/Latinotype-Trenda-Semibold.otf\",\"weight\":\"500\"},{\"path\":\"../../public/fonts/Latinotype-Trenda-Bold.otf\",\"weight\":\"600\"},{\"path\":\"../../public/fonts/Latinotype-Trenda-Black.otf\",\"weight\":\"700\"},{\"path\":\"../../public/fonts/Latinotype-Trenda-Heavy.otf\",\"weight\":\"800\"}],\"variable\":\"--font-primaryFont\"}],\"variableName\":\"primaryFont\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\@next\\font\\local\\target.css?{\"path\":\"common\\\\utils\\\\localFont.ts\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"../../public/fonts/Chillax-Extralight.ttf\",\"weight\":\"200\"},{\"path\":\"../../public/fonts/Chillax-Light.ttf\",\"weight\":\"300\"},{\"path\":\"../../public/fonts/Chillax-Regular.ttf\",\"weight\":\"400\"},{\"path\":\"../../public/fonts/Chillax-Medium.ttf\",\"weight\":\"500\"},{\"path\":\"../../public/fonts/Chillax-Semibold.ttf\",\"weight\":\"600\"},{\"path\":\"../../public/fonts/Chillax-Bold.ttf\",\"weight\":\"700\"},{\"path\":\"../../public/fonts/Chillax-Variable.ttf\",\"weight\":\"800\"}],\"variable\":\"--font-secondaryFont\"}],\"variableName\":\"secondaryFont\"}":{"id":"(app-pages-browser)/./node_modules/@next/font/local/target.css?{\"path\":\"common\\\\utils\\\\localFont.ts\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"../../public/fonts/Chillax-Extralight.ttf\",\"weight\":\"200\"},{\"path\":\"../../public/fonts/Chillax-Light.ttf\",\"weight\":\"300\"},{\"path\":\"../../public/fonts/Chillax-Regular.ttf\",\"weight\":\"400\"},{\"path\":\"../../public/fonts/Chillax-Medium.ttf\",\"weight\":\"500\"},{\"path\":\"../../public/fonts/Chillax-Semibold.ttf\",\"weight\":\"600\"},{\"path\":\"../../public/fonts/Chillax-Bold.ttf\",\"weight\":\"700\"},{\"path\":\"../../public/fonts/Chillax-Variable.ttf\",\"weight\":\"800\"}],\"variable\":\"--font-secondaryFont\"}],\"variableName\":\"secondaryFont\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\error.tsx":{"id":"(app-pages-browser)/./app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\loader\\style.css":{"id":"(app-pages-browser)/./common/components/atoms/loader/style.css","name":"*","chunks":["app/loading","static/chunks/app/loading.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\":[],"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\error":["static/css/app/error.css"],"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\loading":["static/css/app/loading.css"],"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\not-found":["static/css/app/not-found.css"],"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\page":["static/css/app/home/<USER>"],"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\layout":["static/css/app/home/<USER>"]}}