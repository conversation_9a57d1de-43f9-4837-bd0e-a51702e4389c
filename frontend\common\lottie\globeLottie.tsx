'use client'
import <PERSON><PERSON> from "react-lottie";
import * as globeData from '@/common/lottie/globe-animation.json'

export const GlobeLottie = ({ 
  enableGlobeAnim, width = 32, height = 32,
} : { 
  enableGlobeAnim: boolean;
  width?: number;
  height?: number;
}) => {
  const animOptions = {
    loop: true,
    autoplay: false,
    animationData: globeData,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  };
  return (
    <Lottie options={animOptions}
      isStopped={!enableGlobeAnim}
      style={{ margin: '0' }}
      height={height}
      width={width}
    />
  )
}
