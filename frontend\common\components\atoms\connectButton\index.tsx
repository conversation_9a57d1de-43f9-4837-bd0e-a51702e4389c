'use client'
import {
  useEffect,
  useMemo,
} from 'react';
import { 
  useCopyToClipboard, useWindowDimensions,
} from '@/common/hooks';
import {
  ChevronDown,
} from 'lucide-react';
import {
  usePrivy, useWallets,
} from '@privy-io/react-auth';
import {
  Menu,
  Transition,
} from '@headlessui/react'
import { useFundWallet } from '@privy-io/react-auth';
import {
  polygon, sepolia,
} from "viem/chains";
import { Fragment } from 'react'
import lang from '@/common/lang';
import {
  usePathname,
  useRouter,
} from 'next/navigation'
import toast from 'react-hot-toast';
import { routes } from '@/common/routes';
import { themeElements } from '@/common/theme/themeElements';
import {
  cn,
  getConnectChainName, getSupportedChainId, isCorrectNetwork,
} from '@/common/utils/helpers';
import { Button } from '../button';
import { EnsResolver } from '../ensResolver';
import { 
  CopyPasteIcon,
  LogoutIcon,
  PolygonIcon, MoneyBagIcon, WalletIcon,
} from '../../icons';

const isPolygon = process.env.NEXT_PUBLIC_CURRENT_CHAIN === "POLYGON_MAIN"

const {
  header: {
    connectButton: connectButtonCopy,
  },
} = lang

export const ConnectButton = () => {
  const pathname = usePathname()
  const router = useRouter()
  const {
    windowSize,
  } = useWindowDimensions()
  const [, copyToClipboard] = useCopyToClipboard();
  const avatarGradient = useMemo(() => {
    const colorArr = [
      "linear-gradient(-225deg, #00c3ff 0%, #ffff1c 100%)",
      "linear-gradient(-225deg, #A770EF 0%, #CF8BF3 50% , #FDB99B 100%)",
      "linear-gradient(-225deg, #FDFC47 0%, #24FE41 100%)",
      "linear-gradient(-225deg, #12c2e9 0%, #c471ed 50%, #f64f59 100%)",
      "linear-gradient(-225deg, #00c6ff 0%, #0072ff 100%)",
    ];
    return colorArr[Math.floor(Math.random() * colorArr.length)];
  }, []);
  const {
    ready, authenticated, user, login, logout,
  } = usePrivy();

  const {
    wallets,
    ready: walletsReady,
  } = useWallets()

  const { fundWallet } = useFundWallet();

  useEffect(() => {
    const handleSwitchCorrect = async () => {
      if (walletsReady && wallets?.length && !isCorrectNetwork(wallets[0].chainId.split(":")[1])) {
        wallets[0].switchChain(getSupportedChainId())
      }
    }
    handleSwitchCorrect()
  }, [wallets, walletsReady])

  useEffect(() => {
    if (wallets.length === 0 && authenticated && walletsReady) {
      logout()
    }
  }, [logout, wallets, authenticated, walletsReady])

  return (
    <div
      {...(!ready && {
        'aria-hidden': true,
        'style': {
          opacity: 0,
          pointerEvents: 'none',
          userSelect: 'none',
        },
      })}
    >
      {(() => {
        if (!authenticated) {
          if (windowSize === "mobile" || windowSize === "tablet") {
            return (
              <button type="button" onClick={login} className="p-2">
                <span className='hidden md:inline'>{connectButtonCopy.connectWallet}</span>
                <WalletIcon />
              </button>
            );
          } else {
            return (
              <Button type="button" size="lg" onClick={login} variant="outline" className="">
                <span className='hidden md:inline'>{connectButtonCopy.connectWallet}</span>
                <WalletIcon />
              </Button>
            );
          }
         
        }
        return (
          <div className='flex gap-3 relative'>
            <Menu as="div" className="relative inline-block text-left">
              <div>
                <Menu.Button
                  className={cn(themeElements.buttons.gradient.style, themeElements.buttons.gradient.size.lg, "px-3 md:px-4 py-1.5 md:py-3.5 max-h-[40px]") }
                  type="button"
                >
                  <span className='rounded-full flex justify-center items-center bg-white p-0.5'>
                    <div className='w-4 h-4 md:w-5 md:h-5 rounded-full' style={{ background: avatarGradient }}>
                    </div>
                  </span>
                  <span className='hidden sm:inline'>
                    {user?.wallet?.address ? <EnsResolver defaultStyle={false} address={user?.wallet?.address} /> : ''}
                  </span>
                  <ChevronDown strokeWidth={2.5} width={20} height={20} className='w-4 h-4 md:w-5 md:h-5' />
                </Menu.Button>
              </div>
              <Transition
                as={Fragment}
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <Menu.Items className="absolute right-0 mt-2 w-56 origin-top-right rounded-2xl overflow-hidden bg-violets-are-blue/5 backdrop-blur-lg border !border-white/10 focus:outline-none">
                  {authenticated ? (
                    <div className='px-4 pt-3 pb-2 text-white text-sm flex items-center'>
                      {connectButtonCopy.connectedTo}
                      <span className='ml-1 whitespace-nowrap font-semibold bg-gradient-to-b from-indigo-500 to-purple-500 text-transparent bg-clip-text'>{getConnectChainName()}</span>
                      <span className='ml-1'><PolygonIcon /></span>
                    </div>
                  ) : null}
                  <hr className='border-white/15' />
                  <div className="px-1 py-1 ">
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          type="button"
                          onClick={async () => {
                            if (user?.wallet?.address) {
                              const success = await copyToClipboard(user.wallet.address);
                              if (success) {
                                toast.success('Address copied to clipboard');
                              } else {
                                toast.error('Failed to copy address');
                              }
                            }
                          }}
                          className={`${
                            active ? 'bg-[#f6f6f6] text-black' : 'text-neutral-200'
                          } group flex hover:font-medium w-full gap-1 items-center rounded-xl hover:pl-4 duration-300 transition-all ease-in-out px-3 py-2 text-sm`}
                        >
                          <CopyPasteIcon />
                          {connectButtonCopy.copyAddress}
                        </button>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          type="button"
                          onClick={async () => {
                            if (!user?.wallet?.address) {
                              return;
                            }
                            await fundWallet(user?.wallet?.address || '', {
                              chain: isPolygon ? polygon : sepolia,
                            });
                          }}
                          className={`${
                            active ? 'bg-[#f6f6f6] text-black' : 'text-neutral-200'
                          } group flex hover:font-medium w-full gap-1 items-center rounded-xl hover:pl-4 duration-300 transition-all ease-in-out px-3 py-2 text-sm`}
                        >
                          <MoneyBagIcon />
                          {connectButtonCopy.fundAccount}
                        </button>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          type="button"
                          onClick={async () => {
                            await logout()
                            if (pathname.includes(routes.profilePath)) {
                              router.push(routes.homePath)
                            }
                          }}
                          className={`${
                            active ? 'bg-[#f6f6f6] text-black' : 'text-neutral-200'
                          } group flex w-full hover:font-medium items-center gap-1 rounded-xl hover:pl-4 duration-300 transition-all ease-in-out px-3 py-2 text-sm`}
                        >
                          <LogoutIcon />
                          {connectButtonCopy.logout}
                        </button>
                      )}
                    </Menu.Item>
                  </div>
                </Menu.Items>
              </Transition>
            </Menu>
          </div>
        );
      })()}
    </div>
  );
};
