import { FC } from 'react';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { ethers } from 'ethers';
import { ExternalLink } from 'lucide-react';
import { getBaseChainTransactionLink } from '@/common/constants';
import { AddressTransaction } from '../hooks/useAddressTransactions';

dayjs.extend(relativeTime);

interface RecentTransactionsCardProps {
  transactions: AddressTransaction[];
  isLoading: boolean;
}

export const RecentTransactionsCard: FC<RecentTransactionsCardProps> = ({
  transactions, isLoading,
}) => {
  if (isLoading) {
    return (
      <div className="w-full bg-gradient-to-br from-yellow-200/5 via-yellow-100/10 to-yellow-200/5 rounded-xl shadow-lg p-4 border border-yellow-500/10 backdrop-blur-sm">
        <h3 className="text-sm text-gray-300 mb-3">Recent Transactions</h3>
        <div className="space-y-3">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="flex justify-between items-center text-xs bg-white/5 rounded-lg p-3">
              <div className="w-24 h-4 bg-white/10 animate-pulse rounded"></div>
              <div className="w-16 h-4 bg-white/10 animate-pulse rounded"></div>
              <div className="w-20 h-4 bg-white/10 animate-pulse rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!transactions.length) {
    return (
      <div className="w-full bg-gradient-to-br from-yellow-200/5 via-yellow-100/10 to-yellow-200/5 rounded-xl shadow-lg p-4 border border-yellow-500/10 backdrop-blur-sm">
        <h3 className="text-sm text-gray-300 mb-3">Recent Transactions</h3>
        <div className="text-xs text-gray-400 bg-white/5 rounded-lg p-3 text-center">
          No recent transactions found
        </div>
      </div>
    );
  }

  return (
    <div className="w-full mt-4">
      <h3 className="text-xs text-gray-300 mb-1">Recent Transactions</h3>
      <div className="space-y-2">
        {transactions.map((tx) => {
          const valueToFormat = tx.native_transfers && tx.native_transfers.length > 0
            ? tx.native_transfers[0].value
            : tx.value || '0';

          const ethValue = parseFloat(ethers.formatEther(valueToFormat));
          const formattedValue = ethValue > 0 && ethValue < 0.001 ? ethValue.toString() : ethValue.toFixed(2);

          const txHash = tx.hash || tx.transaction_hash || '';
          const shortHash = txHash ? `${txHash.slice(0, 6)}...${txHash.slice(-4)}` : 'Unknown';

          const timeAgo = dayjs(tx.block_timestamp).fromNow();
          
          return (
            <div
              key={txHash || `tx-${tx.block_number}-${tx.from_address}`}
              className="flex justify-between items-center gap-2 text-xs bg-black/20 hover:bg-black/30 transition-colors rounded-lg p-3"
            >
              <a
                href={txHash ? getBaseChainTransactionLink(txHash) : '#'}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-light-yellow font-medium flex items-center gap-1 flex-1"
              >
                {shortHash}
                <ExternalLink size={10} />
              </a>
              <div className="text-light-yellow font-medium">
                {formattedValue} ETH
              </div>
              <div className="text-gray-400 text-right">
                {timeAgo}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default RecentTransactionsCard;
