"use client";
import "./style.css";

export function BlockchainAnimation () {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      xmlSpace="preserve"
      height={200}
      className="w-full lg:h-[200px] h-[140px]"
      viewBox="0 0 2000 1200"
    >
      <path
        fill="none"
        stroke="#FE8989"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
        strokeWidth={3.493}
        d="m355 952 1075.3-636.6"
      />
      <linearGradient
        id="a"
        x1={-26}
        x2={2010}
        y1={616}
        y2={616}
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#202333",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#5c1b99",
          }}
        />
      </linearGradient>
      <linearGradient
        id="b"
        x1={-101.175}
        x2={2085.175}
        y1={174.328}
        y2={1057.672}
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#202333",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#5c6399",
          }}
        />
      </linearGradient>
      <linearGradient
        id="c"
        x1={195.622}
        x2={1744.367}
        y1={293.028}
        y2={918.762}
        gradientTransform="matrix(1 0 0 -1 0 1200)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#202333",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#5c6399",
          }}
        />
      </linearGradient>
      <linearGradient
        id="g"
        x1={415.5}
        x2={1526.257}
        y1={357.141}
        y2={805.916}
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#202333",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#5c6399",
          }}
        />
      </linearGradient>
      <path
        fill="none"
        stroke="url(#g)"
        strokeMiterlimit={10}
        d="m197.9 898.1 1303.3-782.7 237 163.8-1263.5 782.3z"
      />
      <path fill="#202333" d="m1345 596.3 81 49.7 84-48.7-82-49.3z" />
      <g fill="#202333" opacity={0.65}>
        <path d="m1585 596.3 81 49.7 84-48.7-82-49.3zM1464 527.3l81 49.7 84-48.7-82-49.3zM1047 224.3l81 49.7 84-48.7-82-49.3zM1720 524.3l81 49.7 84-48.7-82-49.3zM1831 596.3l81 49.7 84-48.7-82-49.3zM1680.3 496.1l.8 1.3-2.1 1.2-.8-1.3 2.1-1.2zm-6 3.5.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.3.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.3.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm2.4-23.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.6-11.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-25.9-1.4-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11.1-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-18.9 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.7-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-18.9 11.1-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.3-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.7-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-2.4 23.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.5 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.6-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.6-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.5 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm92.3-35.6-.8-1.3-3.7 2.1.8 1.3 3.7-2.1zm6.5-4c-.1-.1-.2-.1-.4-.2-.3-.2-.8-.5-1.2-.8-.9-.5-1.8-1.1-1.8-1.1l-.8 1.3s.4.2.7.4c.2.1.4.2.5.3l-1.5.8.8 1.3 3.7-2c.1.1.1.1 0 0zm-10-6-.8 1.3 3.6 2.2.8-1.3-3.6-2.2zm-70.7-42.6-.8 1.3 1.3.8.8-1.3-1.3-.8zm-2.3-1.3s-.1 0-.1.1c-.1.1-.3.2-.4.3l-.6.4.8 1.3s.1-.1.3-.1c.1 0 .1-.1.2-.1l.5.3.8-1.3-1.5-.9c.1-.1 0-.1 0 0zm-3.5 2 .8 1.3 1.3-.7-.8-1.3-1.3.7zm-74.1 43 .8 1.3 3.6-2.1-.8-1.3-3.6 2.1zm-6.4 3.9c.1.1.2.1.4.2.3.2.8.5 1.2.8.9.5 1.8 1.1 1.8 1.1l.8-1.3s-.4-.2-.7-.4c-.2-.1-.4-.2-.5-.3l1.4-.8-.8-1.3-3.6 2c-.1-.1-.1 0 0 0zm9.8 6.1.8-1.3-3.6-2.2-.8 1.3 3.6 2.2zm69.9 42.9.8-1.3-1.3-.8-.8 1.3 1.3.8zm2.3 1.3s.1 0 .1-.1c.1-.1.3-.2.4-.3l.6-.4-.8-1.3s-.1.1-.3.1c-.1 0-.1.1-.2.1l-.5-.3-.8 1.3 1.5.9c-.1.1 0 .1 0 0zm3.5-2-.8-1.3-1.3.7.8 1.3 1.3-.7zM1021.3 196.1l.8 1.3-2.1 1.2-.8-1.3 2.1-1.2zm-6 3.5.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.3.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.3.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.1-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm2.4-23.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.6-11.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-25.9-1.4-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11.1-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-18.9 11.1-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.3-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.7-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-2.4 23.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.5 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.6-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.6-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.5 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm92.3-35.6-.8-1.3-3.7 2.1.8 1.3 3.7-2.1zm6.5-4c-.1-.1-.2-.1-.4-.2-.3-.2-.8-.5-1.2-.8-.9-.5-1.8-1.1-1.8-1.1l-.8 1.3s.4.2.7.4c.2.1.4.2.5.3l-1.5.8.8 1.3 3.7-2c.1.1.1.1 0 0zm-10-6-.8 1.3 3.6 2.2.8-1.3-3.6-2.2zm-70.7-42.6-.8 1.3 1.3.8.8-1.3-1.3-.8zm-2.3-1.3s-.1 0-.1.1c-.1.1-.3.2-.4.3l-.6.4.8 1.3s.1-.1.3-.1c.1 0 .1-.1.2-.1l.5.3.8-1.3-1.5-.9c.1-.1 0-.1 0 0zm-3.5 2 .8 1.3 1.3-.7-.8-1.3-1.3.7zm-74.1 43 .8 1.3 3.6-2.1-.8-1.3-3.6 2.1zm-6.4 3.9c.1.1.2.1.4.2.3.2.8.5 1.2.8.9.5 1.8 1.1 1.8 1.1l.8-1.3s-.4-.2-.7-.4c-.2-.1-.4-.2-.5-.3l1.4-.8-.8-1.3-3.6 2c-.1-.1-.1 0 0 0zm9.8 6.1.8-1.3-3.6-2.2-.8 1.3 3.6 2.2zm69.8 42.9.8-1.3-1.3-.8-.8 1.3 1.3.8zm2.4 1.3.1-.1c.1-.1.3-.2.4-.3l.6-.4-.8-1.3s-.1.1-.3.1c-.1 0-.1.1-.2.1l-.5-.3-.8 1.3 1.5.9c-.1.1 0 .1 0 0zm3.5-2-.8-1.3-1.3.7.8 1.3 1.3-.7zM881 1022.3l81 49.7 84-48.7-82-49.3zM855.3 994.1l.8 1.3-2.1 1.2-.8-1.3 2.1-1.2zm-6 3.5.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.3.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.1-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm2.4-23.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.3.8-1.3-2.1-1.2-.8 1.3 2.1 1.2zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.2-.8 1.3 2.1 1.2zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zM871.9 923l.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.6-11.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-25.9-1.4-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11.1-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-18.9 11.1-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.3-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.7-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-2.4 23.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.5 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.6-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.6-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.5 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm92.3-35.6-.8-1.3-3.7 2.1.8 1.3 3.7-2.1zm6.5-4c-.1-.1-.2-.1-.4-.2-.3-.2-.8-.5-1.2-.8-.9-.5-1.8-1.1-1.8-1.1l-.8 1.3s.4.2.7.4c.2.1.4.2.5.3l-1.5.8.8 1.3 3.7-2c.1.1.1.1 0 0zm-10-6-.8 1.3 3.6 2.2.8-1.3-3.6-2.2zm-70.7-42.6-.8 1.3 1.3.8.8-1.3-1.3-.8zm-2.3-1.3s-.1 0-.1.1c-.1.1-.3.2-.4.3l-.6.4.8 1.3s.1-.1.3-.1c.1 0 .1-.1.2-.1l.5.3.8-1.3-1.5-.9c.1-.1 0-.1 0 0zm-3.5 2 .8 1.3 1.3-.7-.8-1.3-1.3.7zm-74.1 43 .8 1.3 3.6-2.1-.8-1.3-3.6 2.1zm-6.4 3.9c.1.1.2.1.4.2.3.2.8.5 1.2.8.9.5 1.8 1.1 1.8 1.1l.8-1.3s-.4-.2-.7-.4c-.2-.1-.4-.2-.5-.3l1.4-.8-.8-1.3-3.6 2c-.1-.1-.1 0 0 0zm9.8 6.1.8-1.3-3.6-2.2-.8 1.3 3.6 2.2zm69.8 42.9.8-1.3-1.3-.8-.8 1.3 1.3.8zm2.4 1.3s.1 0 .1-.1c.1-.1.3-.2.4-.3l.6-.4-.8-1.3s-.1.1-.3.1c-.1 0-.1.1-.2.1l-.5-.3-.8 1.3 1.5.9c-.1.1 0 .1 0 0zm3.5-2-.8-1.3-1.3.7.8 1.3 1.3-.7zM841.3 1136.1l.8 1.3-2.1 1.2-.8-1.3 2.1-1.2zm-6 3.5.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.3.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.1-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm2.4-23.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.6-11.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-25.9-1.4-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11.1-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-18.9 11.1-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.3-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.7-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-2.4 23.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.5 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.6-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.6-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.5 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm92.3-35.6-.8-1.3-3.7 2.1.8 1.3 3.7-2.1zm6.5-4c-.1-.1-.2-.1-.4-.2-.3-.2-.8-.5-1.2-.8-.9-.5-1.8-1.1-1.8-1.1l-.8 1.3s.4.2.7.4c.2.1.4.2.5.3l-1.5.8.8 1.3 3.7-2c.1.1.1.1 0 0zm-10-6-.8 1.3 3.6 2.2.8-1.3-3.6-2.2zm-70.7-42.6-.8 1.3 1.3.8.8-1.3-1.3-.8zm-2.3-1.3s-.1 0-.1.1c-.1.1-.3.2-.4.3l-.6.4.8 1.3s.1-.1.3-.1c.1 0 .1-.1.2-.1l.5.3.8-1.3-1.5-.9c.1-.1 0-.1 0 0zm-3.5 2 .8 1.3 1.3-.7-.8-1.3-1.3.7zm-74.1 43 .8 1.3 3.6-2.1-.8-1.3-3.6 2.1zm-6.4 3.9c.1.1.2.1.4.2.3.2.8.5 1.2.8.9.5 1.8 1.1 1.8 1.1l.8-1.3s-.4-.2-.7-.4c-.2-.1-.4-.2-.5-.3l1.4-.8-.8-1.3-3.6 2c-.1-.1-.1 0 0 0zm9.8 6.1.8-1.3-3.6-2.2-.8 1.3 3.6 2.2zm69.8 42.9.8-1.3-1.3-.8-.8 1.3 1.3.8zm2.4 1.3.1-.1c.1-.1.3-.2.4-.3l.6-.4-.8-1.3s-.1.1-.3.1c-.1 0-.1.1-.2.1l-.5-.3-.8 1.3 1.5.9c-.1.1 0 .1 0 0zm3.5-2-.8-1.3-1.3.7.8 1.3 1.3-.7zM1559.3 998.1l.8 1.3-2.1 1.2-.8-1.3 2.1-1.2zm-6 3.5.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.3.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.1-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm2.4-23.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.6-11.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-25.9-1.4-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11.1-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-18.9 11.1-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.3-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.7-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-2.4 23.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.5 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.6-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.6-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.5 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm92.3-35.6-.8-1.3-3.7 2.1.8 1.3 3.7-2.1zm6.5-4c-.1-.1-.2-.1-.4-.2-.3-.2-.8-.5-1.2-.8-.9-.5-1.8-1.1-1.8-1.1l-.8 1.3s.4.2.7.4c.2.1.4.2.5.3l-1.5.8.8 1.3 3.7-2c.1.1.1.1 0 0zm-10-6-.8 1.3 3.6 2.2.8-1.3-3.6-2.2zm-70.7-42.6-.8 1.3 1.3.8.8-1.3-1.3-.8zm-2.3-1.3s-.1 0-.1.1c-.1.1-.3.2-.4.3l-.6.4.8 1.3s.1-.1.3-.1c.1 0 .1-.1.2-.1l.5.3.8-1.3-1.5-.9c.1-.1 0-.1 0 0zm-3.5 2 .8 1.3 1.3-.7-.8-1.3-1.3.7zm-74.1 43 .8 1.3 3.6-2.1-.8-1.3-3.6 2.1zm-6.4 3.9c.1.1.2.1.4.2.3.2.8.5 1.2.8.9.5 1.8 1.1 1.8 1.1l.8-1.3s-.4-.2-.7-.4c-.2-.1-.4-.2-.5-.3l1.4-.8-.8-1.3-3.6 2c-.1-.1-.1 0 0 0zm9.8 6.1.8-1.3-3.6-2.2-.8 1.3 3.6 2.2zm69.8 42.9.8-1.3-1.3-.8-.8 1.3 1.3.8zm2.4 1.3s.1 0 .1-.1c.1-.1.3-.2.4-.3l.6-.4-.8-1.3s-.1.1-.3.1c-.1 0-.1.1-.2.1l-.5-.3-.8 1.3 1.5.9c-.1.1 0 .1 0 0zm3.5-2-.8-1.3-1.3.7.8 1.3 1.3-.7zM1720 386.3l81 49.7 84-48.7-82-49.3zM261 599.3l81 49.7 84-48.7-82-49.3zM140 530.3l81 49.7 84-48.7-82-49.3zM21 597.3l81 49.7 84-48.7-82-49.3zM356.3 499.1l.8 1.3-2.1 1.2-.8-1.3 2.1-1.2zm-6 3.5.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.3.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.3.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm2.4-23.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.3.8-1.3-2.1-1.2-.8 1.3 2.1 1.2zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.2-.8 1.3 2.1 1.2zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zM372.9 428l.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.6-11.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-25.9-1.4-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11.1-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-18.9 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.7-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-18.9 11.1-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.3-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.7-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-2.4 23.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.5 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.6-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.6-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.5 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm92.3-35.6-.8-1.3-3.7 2.1.8 1.3 3.7-2.1zm6.5-4c-.1-.1-.2-.1-.4-.2-.3-.2-.8-.5-1.2-.8-.9-.5-1.8-1.1-1.8-1.1l-.8 1.3s.4.2.7.4c.2.1.4.2.5.3l-1.5.8.8 1.3 3.7-2c.1.1.1.1 0 0zm-10-6-.8 1.3 3.6 2.2.8-1.3-3.6-2.2zm-70.7-42.6-.8 1.3 1.3.8.8-1.3-1.3-.8zm-2.3-1.3s-.1 0-.1.1c-.1.1-.3.2-.4.3l-.6.4.8 1.3s.1-.1.3-.1c.1 0 .1-.1.2-.1l.5.3.8-1.3-1.5-.9c.1-.1 0-.1 0 0zm-3.5 2 .8 1.3 1.3-.7-.8-1.3-1.3.7zm-74.1 43 .8 1.3 3.6-2.1-.8-1.3-3.6 2.1zm-6.4 3.9c.1.1.2.1.4.2.3.2.8.5 1.2.8.9.5 1.8 1.1 1.8 1.1l.8-1.3s-.4-.2-.7-.4c-.2-.1-.4-.2-.5-.3l1.4-.8-.8-1.3-3.6 2c-.1-.1-.1 0 0 0zm9.8 6.1.8-1.3-3.6-2.2-.8 1.3 3.6 2.2zm69.8 42.9.8-1.3-1.3-.8-.8 1.3 1.3.8zm2.4 1.3s.1 0 .1-.1c.1-.1.3-.2.4-.3l.6-.4-.8-1.3s-.1.1-.3.1c-.1 0-.1.1-.2.1l-.5-.3-.8 1.3 1.5.9c-.1.1 0 .1 0 0zm3.5-2-.8-1.3-1.3.7.8 1.3 1.3-.7zM235.3 714.1l.8 1.3-2.1 1.2-.8-1.3 2.1-1.2zm-6 3.5.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.3.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm19.2-11.1.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.9 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm-3.8 2.2.8 1.3 2.1-1.2-.8-1.3-2.1 1.2zm2.4-23.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.3.8-1.3-2.1-1.2-.8 1.3 2.1 1.2zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.2-.8 1.3 2.1 1.2zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zM251.9 643l.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.6-11.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-18.7-11.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.8 2.2.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm3.7 2.3.8-1.3-2.1-1.3-.8 1.3 2.1 1.3zm-25.9-1.4-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11.1-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-19 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-18.9 11-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.7-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-18.9 11.1-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.8-2.3-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm3.7-2.2-.8-1.3-2.1 1.2.8 1.3 2.1-1.2zm-2.4 23.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.5 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.6-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.6-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.4 11.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.5 11.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.2-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm92.3-35.6-.8-1.3-3.7 2.1.8 1.3 3.7-2.1zm6.5-4c-.1-.1-.2-.1-.4-.2-.3-.2-.8-.5-1.2-.8-.9-.5-1.8-1.1-1.8-1.1l-.8 1.3s.4.2.7.4c.2.1.4.2.5.3l-1.5.8.8 1.3 3.7-2c.1.1.1.1 0 0zm-10-6-.8 1.3 3.6 2.2.8-1.3-3.6-2.2zm-70.7-42.6-.8 1.3 1.3.8.8-1.3-1.3-.8zm-2.3-1.3s-.1 0-.1.1c-.1.1-.3.2-.4.3l-.6.4.8 1.3s.1-.1.3-.1c.1 0 .1-.1.2-.1l.5.3.8-1.3-1.5-.9c.1-.1 0-.1 0 0zm-3.5 2 .8 1.3 1.3-.7-.8-1.3-1.3.7zm-74.1 43 .8 1.3 3.6-2.1-.8-1.3-3.6 2.1zm-6.4 3.9c.1.1.2.1.4.2.3.2.8.5 1.2.8.9.5 1.8 1.1 1.8 1.1l.8-1.3s-.4-.2-.7-.4c-.2-.1-.4-.2-.5-.3l1.4-.8-.8-1.3-3.6 2c-.1-.1-.1 0 0 0zm9.8 6.1.8-1.3-3.6-2.2-.8 1.3 3.6 2.2zm69.8 42.9.8-1.3-1.3-.8-.8 1.3 1.3.8zm2.4 1.3s.1 0 .1-.1c.1-.1.3-.2.4-.3l.6-.4-.8-1.3s-.1.1-.3.1c-.1 0-.1.1-.2.1l-.5-.3-.8 1.3 1.5.9c-.1.1 0 .1 0 0zm3.5-2-.8-1.3-1.3.7.8 1.3 1.3-.7zM134 382.3l81 49.7 84-48.7-82-49.3zM390 530.3l81 49.7 84-48.7-82-49.3zM390 386.3l81 49.7 84-48.7-82-49.3z" />
      </g>
      <linearGradient
        id="h"
        x1={596.295}
        x2={531.755}
        y1={1492.351}
        y2={1455.089}
        gradientTransform="matrix(1 0 0 -1 0 1900)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "gray",
          }}
        />
        <stop
          offset={0.263}
          style={{
            stopColor: "#6f6f6f",
          }}
        />
        <stop
          offset={0.719}
          style={{
            stopColor: "#575757",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#4e4e4e",
          }}
        />
      </linearGradient>
      <path
        fill="url(#h)"
        d="M612.3 427.9c-.1-.8-3.3.2-3.3.2v1.3l.1-.1-48.9-28s-4.5-3.1-10.6.6l-48.8 28s-.7.1-.7.3V429s-2.7-1-2.8-.2c-.4 2.3-.1 1.9 0 3.5 0 1.1.8 2.3 3.1 3.5l47.5 27.5s7.1 3.9 14.6 0L610 435s2.3-1.4 2.5-3.4c.3-1.6.2-1.5-.2-3.7z"
      />
      <linearGradient
        id="i"
        x1={536.146}
        x2={577.024}
        y1={1498.017}
        y2={1439.638}
        gradientTransform="matrix(1 0 0 -1 0 1900)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#9a9a9a",
          }}
        />
        <stop
          offset={0.312}
          style={{
            stopColor: "#9f9f9f",
          }}
        />
        <stop
          offset={0.72}
          style={{
            stopColor: "#b0b0b0",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#bfbfbf",
          }}
        />
      </linearGradient>
      <path
        fill="url(#i)"
        d="m500.4 432.8 47.4 27.5s7.1 3.9 14.6 0L610 432s6.3-3.6-.8-7.4l-49-28s-4.6-3.1-10.7.6l-48.5 28s-7.8 3.8-.6 7.6z"
      />
      <linearGradient
        id="j"
        x1={596.302}
        x2={524.327}
        y1={1506.155}
        y2={1464.6}
        gradientTransform="matrix(1 0 0 -1 0 1900)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.23}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.437}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.634}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.825}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path
        fill="url(#j)"
        d="M612.3 414.1c-.1-.8-3.3.2-3.3.2v1.3l.1-.1-48.9-28s-4.5-3.1-10.6.6l-48.8 28s-.7.1-.7.3v-1.2s-2.7-1-2.8-.2c-.4 2.3-.1 1.9 0 3.5 0 1.1.8 2.3 3.1 3.5l47.5 27.5s7.1 3.9 14.6 0l47.6-28.3s2.3-1.4 2.5-3.4c.2-1.6.1-1.5-.3-3.7z"
      />
      <linearGradient
        id="k"
        x1={536.139}
        x2={577.016}
        y1={1511.828}
        y2={1453.449}
        gradientTransform="matrix(1 0 0 -1 0 1900)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path
        fill="url(#k)"
        d="m500.4 419 47.4 27.5s7.1 3.9 14.6 0l47.6-28.3s6.3-3.6-.8-7.4l-49-28s-4.6-3.1-10.7.6l-48.5 28s-7.8 3.8-.6 7.6z"
      />
      <linearGradient
        id="l"
        x1={596.287}
        x2={531.747}
        y1={1531.346}
        y2={1494.084}
        gradientTransform="matrix(1 0 0 -1 0 1900)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "gray",
          }}
        />
        <stop
          offset={0.263}
          style={{
            stopColor: "#6f6f6f",
          }}
        />
        <stop
          offset={0.719}
          style={{
            stopColor: "#575757",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#4e4e4e",
          }}
        />
      </linearGradient>
      <path
        fill="url(#l)"
        d="M612.3 388.9c-.1-.8-3.3.2-3.3.2v1.3l.1-.1-48.9-28s-4.5-3.1-10.6.6l-48.8 28s-.7.1-.7.3V390s-2.7-1-2.8-.2c-.4 2.3-.1 1.9 0 3.5 0 1.1.8 2.3 3.1 3.5l47.5 27.5s7.1 3.9 14.6 0l47.6-28.3s2.3-1.4 2.5-3.4c.2-1.5.1-1.5-.3-3.7z"
      />
      <linearGradient
        id="m"
        x1={536.152}
        x2={577.03}
        y1={1537.005}
        y2={1478.625}
        gradientTransform="matrix(1 0 0 -1 0 1900)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#9a9a9a",
          }}
        />
        <stop
          offset={0.312}
          style={{
            stopColor: "#9f9f9f",
          }}
        />
        <stop
          offset={0.72}
          style={{
            stopColor: "#b0b0b0",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#bfbfbf",
          }}
        />
      </linearGradient>
      <path
        fill="url(#m)"
        d="m500.4 393.8 47.4 27.5s7.1 3.9 14.6 0L610 393s6.3-3.6-.8-7.4l-49-28s-4.6-3.1-10.7.6l-48.5 28c0 .1-7.8 3.8-.6 7.6z"
      />
      <g fill="black">
        <path d="M551 388.2c-.3-.8-.5-1.6-.8-2.3-.1-.2-.4-.4-.6-.6-.4-.4-1.1-.7-1.2-1.2-.8-1.9-1.5-3.8-2.1-5.7-.4-1.3 1.1-2.7 3.4-3.2 2.2-.4 4.4.2 4.9 1.5.7 1.9 1.4 3.7 2 5.6.1.4.1.8-.1 1.2-.4.5-.3 1-.1 1.5.2.4.3.8.5 1.2.1.2.1.4.2.6 2.5-1.1 4.1-3.3 3.6-4.9-.7-2-1.4-4.1-2.1-6.1-.1-.3-.2-.5-.4-.7-1.4-2-5.4-3-9.1-2.3-4 .8-6.9 3-6.8 5.4 0 .7.3 1.5.6 2.2.6 1.6 1 3.3 1.9 4.9.7 1.9 3 2.7 6.2 2.9zM565.2 394.4c-.4-1-1.3-1.8-2.7-2.3-1.1-.5-2.4-.7-3.9-.8.3.9.6 1.7.9 2.6 0 .1.2.2.3.2.9.3 1.3.9 1.5 1.5.6 1.7 1.3 3.4 1.8 5.1.1.4.2.9 0 1.4-.4 1.2-2.2 2.1-4.2 2.2s-3.6-.5-4-1.6c-.6-1.7-1.3-3.4-1.9-5.1-.2-.5-.3-1.1 0-1.6.3-.7.5-1.2.2-1.8-.1-.2 0-.5-.1-.7l-.3-.9c-2.9 1.5-4.3 3.1-3.8 5 .5 2 1.2 3.9 1.9 5.9.8 2.1 3.4 3.4 7 3.4 5.3 0 10.1-3.2 9.3-6.2-.4-2.2-1.3-4.3-2-6.3z" />
        <path d="M555.3 383.1c-.3-.8-1.6-1.1-2.8-.8-1.2.2-2 1-1.7 1.7.7 1.9 3.6 10.3 4.4 12.2.1.3.4.6.7.8.7.3 2 .1 2.7-.2.7-.3 1.3-.9 1.1-1.5-1-2.7-4-11-4.4-12.2z" />
      </g>
      <linearGradient
        id="n"
        x1={554.679}
        x2={554.679}
        y1={1464.331}
        y2={1574.447}
        gradientTransform="matrix(1 0 0 -1 0 1900)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={0.291}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.998}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
      </linearGradient>
      <path
        fill="url(#n)"
        d="m612.2 401.4-.4-91.4H497.1l.5 91.2c0 3.4.3 5 3.6 6.7l46.4 27.5s7.1 4 14.6.1l47.6-28.6s2.9-1.5 2.4-5.5z"
        opacity={0.25}
      />
      <circle cx={553.5} cy={364.8} r={1.5} fill="#FE8989" />
      <circle cx={518.5} cy={307.8} r={0.5} fill="#FE8989" />
      <circle cx={600.2} cy={379.4} r={0.7} fill="#FFF" />
      <circle cx={581.8} cy={310.5} r={0.7} fill="#FE8989" />
      <circle cx={564.5} cy={353.8} r={0.7} fill="#FFF" />
      <circle cx={602.9} cy={394.5} r={0.7} fill="#FFF" />
      <circle cx={596.7} cy={336.1} r={0.7} fill="#FFF" />
      <circle cx={576.7} cy={339.6} r={0.7} fill="#FE8989" />
      <circle cx={573.3} cy={388.6} r={0.7} fill="#FFF" />
      <circle cx={509.4} cy={371} r={0.7} fill="#FFF" />
      <circle cx={532.5} cy={363.8} r={0.7} fill="#FFF" />
      <circle cx={604.2} cy={357.9} r={1.4} fill="#FE8989" />
      <circle cx={521.8} cy={341} r={0.7} fill="#FFF" />
      <circle cx={506.9} cy={322} r={0.7} fill="#FE8989" />
      <circle cx={513} cy={361.5} r={0.7} fill="#FE8989" />
      <circle cx={554.8} cy={313.4} r={1} fill="#FE8989" />
      <circle cx={539.3} cy={341.7} r={1.4} fill="#FE8989" />
      <circle cx={508.1} cy={391.9} r={1.4} fill="#FFF" />
      <circle cx={590.4} cy={371} r={1.4} fill="#FFF" />
      <g fill="#D6D6D6">
        <path d="m584.8 438.5 1.3-2.1-4.8-3.1-2 1.5zM586.995 439.936l1.292-2.023 4.804 3.068-1.292 2.022zM594.151 444.538l1.292-2.023 4.804 3.068-1.292 2.023zM601.333 449.034l1.292-2.022 4.804 3.067-1.292 2.023zM608.358 453.572l1.291-2.022 4.804 3.067-1.291 2.023zM615.566 458.183l1.292-2.023 4.804 3.068-1.292 2.023zM622.75 462.588l1.292-2.023 4.804 3.068-1.292 2.023zM629.821 467.23l1.292-2.023 4.804 3.067-1.292 2.023zM636.978 471.832l1.292-2.023 4.804 3.068-1.292 2.023z" />
      </g>
      <g fill="#D6D6D6">
        <path d="m1146.14 792.584 1.291-2.023 4.804 3.068-1.292 2.023zM1153.212 797.226l1.291-2.023 4.804 3.068-1.292 2.022zM1160.368 801.829l1.292-2.023 4.804 3.068-1.292 2.022zM1167.439 806.469l1.291-2.023 4.804 3.068-1.292 2.023zM1124.51 779l1.291-2.022 4.804 3.068-1.291 2.023zM1131.666 783.696l1.292-2.022 4.804 3.067-1.292 2.023zM1138.739 788.245l1.291-2.023 4.804 3.068-1.291 2.023zM1174.575 810.862l1.291-2.023 4.804 3.068-1.291 2.023zM1181.7 815.42l1.292-2.022 4.804 3.067-1.292 2.023zM1188.856 820.022l1.292-2.023 4.804 3.068-1.292 2.022zM1196.038 824.52l1.292-2.023 4.804 3.068-1.292 2.023zM1203.063 829.057l1.292-2.023 4.804 3.068-1.292 2.023zM1210.686 833.634l1.292-2.023 4.804 3.068-1.292 2.023zM1217.843 838.236l1.292-2.023 4.804 3.068-1.292 2.022z" />
      </g>
      <linearGradient
        id="o"
        x1={1292.107}
        x2={1227.524}
        y1={1031.543}
        y2={994.256}
        gradientTransform="matrix(1 0 0 -1 0 1900)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "gray",
          }}
        />
        <stop
          offset={0.263}
          style={{
            stopColor: "#6f6f6f",
          }}
        />
        <stop
          offset={0.719}
          style={{
            stopColor: "#575757",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#4e4e4e",
          }}
        />
      </linearGradient>
      <path
        fill="url(#o)"
        d="M1308.2 888.8c-.1-.8-3.2.2-3.2.2v1.3s0-.1-.1-.1l-49-28s-4.5-3.1-10.7.6l-48.7 28s-.6.1-.6.3v-1.2s-2.8-1-2.9-.2c-.4 2.3-.2 1.9-.1 3.5 0 1.1.8 2.3 3.1 3.5l47.5 27.5s7.1 3.9 14.6 0l47.6-28.3s2.4-1.4 2.5-3.4c.4-1.6.4-1.5 0-3.7z"
      />
      <linearGradient
        id="p"
        x1={1231.855}
        x2={1272.733}
        y1={1037.11}
        y2={978.731}
        gradientTransform="matrix(1 0 0 -1 0 1900)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#9a9a9a",
          }}
        />
        <stop
          offset={0.312}
          style={{
            stopColor: "#9f9f9f",
          }}
        />
        <stop
          offset={0.72}
          style={{
            stopColor: "#b0b0b0",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#bfbfbf",
          }}
        />
      </linearGradient>
      <path
        fill="url(#p)"
        d="m1196.1 893.7 47.4 27.5s7.1 3.9 14.6 0l47.6-28.3s6.3-3.6-.8-7.4l-49-28s-4.6-3.1-10.7.6l-48.5 28c.1 0-7.8 3.8-.6 7.6z"
      />
      <linearGradient
        id="q"
        x1={1292.114}
        x2={1220.092}
        y1={1045.347}
        y2={1003.765}
        gradientTransform="matrix(1 0 0 -1 0 1900)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.23}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.437}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.634}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.825}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path
        fill="url(#q)"
        d="M1308.2 875c-.1-.8-3.2.2-3.2.2v1.3s0-.1-.1-.1l-49-28s-4.5-3.1-10.7.6l-48.7 28s-.6.1-.6.3v-1.2s-2.8-1-2.9-.2c-.4 2.3-.2 1.9-.1 3.5 0 1.1.8 2.3 3.1 3.5l47.5 27.5s7.1 3.9 14.6 0l47.6-28.3s2.4-1.4 2.5-3.4c.4-1.6.4-1.5 0-3.7z"
      />
      <linearGradient
        id="r"
        x1={1231.848}
        x2={1272.725}
        y1={1050.92}
        y2={992.541}
        gradientTransform="matrix(1 0 0 -1 0 1900)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path
        fill="url(#r)"
        d="m1196.1 879.9 47.4 27.5s7.1 3.9 14.6 0l47.6-28.3s6.3-3.6-.8-7.4l-49-28s-4.6-3.1-10.7.6l-48.5 28c.1 0-7.8 3.8-.6 7.6z"
      />
      <linearGradient
        id="s"
        x1={1292.099}
        x2={1227.516}
        y1={1070.539}
        y2={1033.252}
        gradientTransform="matrix(1 0 0 -1 0 1900)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "gray",
          }}
        />
        <stop
          offset={0.263}
          style={{
            stopColor: "#6f6f6f",
          }}
        />
        <stop
          offset={0.719}
          style={{
            stopColor: "#575757",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#4e4e4e",
          }}
        />
      </linearGradient>
      <path
        fill="url(#s)"
        d="M1308.2 849.8c-.1-.8-3.2.2-3.2.2v1.3s0-.1-.1-.1l-49-28s-4.5-3.1-10.7.6l-48.7 28s-.6.1-.6.3v-1.2s-2.8-1-2.9-.2c-.4 2.3-.2 1.9-.1 3.5 0 1.1.8 2.3 3.1 3.5l47.5 27.5s7.1 3.9 14.6 0l47.6-28.3s2.4-1.4 2.5-3.4c.4-1.5.4-1.5 0-3.7z"
      />
      <linearGradient
        id="t"
        x1={1231.861}
        x2={1272.739}
        y1={1076.097}
        y2={1017.718}
        gradientTransform="matrix(1 0 0 -1 0 1900)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#9a9a9a",
          }}
        />
        <stop
          offset={0.312}
          style={{
            stopColor: "#9f9f9f",
          }}
        />
        <stop
          offset={0.72}
          style={{
            stopColor: "#b0b0b0",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#bfbfbf",
          }}
        />
      </linearGradient>
      <path
        fill="url(#t)"
        d="m1196.1 854.7 47.4 27.5s7.1 3.9 14.6 0l47.6-28.3s6.3-3.6-.8-7.4l-49-28s-4.6-3.1-10.7.6l-48.5 28c.1.1-7.8 3.8-.6 7.6z"
      />
      <g fill="black">
        <path d="M1246.8 849.1c-.3-.8-.5-1.6-.8-2.3-.1-.2-.4-.4-.6-.6-.4-.4-1.1-.7-1.2-1.2-.8-1.9-1.5-3.8-2.1-5.7-.4-1.3 1.1-2.7 3.4-3.2 2.2-.4 4.4.2 4.9 1.5.7 1.9 1.4 3.7 2 5.6.1.4.1.8-.1 1.2-.4.5-.3 1-.1 1.5.2.4.3.8.5 1.2.1.2.1.4.2.6 2.5-1.1 4.1-3.3 3.6-4.9-.7-2-1.4-4.1-2.1-6.1-.1-.3-.2-.5-.4-.7-1.4-2-5.4-3-9.1-2.3-4 .8-6.9 3-6.8 5.4 0 .7.3 1.5.6 2.2.6 1.6 1.1 3.3 1.9 4.9.6 1.9 2.9 2.7 6.2 2.9zM1260.9 855.3c-.4-1-1.3-1.8-2.7-2.3-1.1-.5-2.4-.7-3.9-.8.3.9.6 1.7.9 2.6 0 .1.2.2.3.2.9.3 1.3.9 1.5 1.5.6 1.7 1.3 3.4 1.8 5.1.1.4.2.9 0 1.4-.4 1.2-2.2 2.1-4.2 2.2-2 .1-3.6-.5-4-1.7-.6-1.7-1.3-3.4-1.9-5.1-.2-.5-.3-1.1 0-1.6.3-.7.5-1.2.2-1.8-.1-.2 0-.5-.1-.7l-.3-.9c-2.9 1.5-4.3 3.1-3.8 5 .5 2 1.2 3.9 1.9 5.9.8 2.1 3.4 3.4 7 3.4 5.3 0 10.1-3.2 9.3-6.2-.4-2.2-1.2-4.2-2-6.2z" />
        <path d="M1251 844c-.3-.8-1.6-1.1-2.8-.8-1.2.2-2 1-1.7 1.7.7 1.9 3.6 10.3 4.4 12.2.1.3.4.6.7.8.7.3 2 .1 2.7-.2s1.3-.9 1.1-1.5c-.9-2.7-4-11-4.4-12.2z" />
      </g>
      <linearGradient
        id="u"
        x1={1250.43}
        x2={1250.43}
        y1={1003.338}
        y2={1112.571}
        gradientTransform="matrix(1 0 0 -1 0 1900)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={0.291}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.998}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
      </linearGradient>
      <path
        fill="url(#u)"
        d="m1307.9 862.9-.4-90.9h-114.6l.5 90.7c0 3.4.3 4.7 3.6 6.5l46.4 27.3s7.1 4 14.6 0l47.6-28.4s2.8-1.3 2.3-5.2z"
        opacity={0.25}
      />
      <circle cx={1249.2} cy={825.7} r={1.5} fill="#FE8989" />
      <circle cx={1214.2} cy={768.7} r={0.5} fill="#FE8989" />
      <circle cx={1295.9} cy={840.3} r={0.7} fill="#FFF" />
      <circle cx={1277.5} cy={771.4} r={0.7} fill="#FE8989" />
      <circle cx={1260.2} cy={814.7} r={0.7} fill="#FFF" />
      <circle cx={1298.6} cy={855.4} r={0.7} fill="#FFF" />
      <circle cx={1292.4} cy={797} r={0.7} fill="#FFF" />
      <circle cx={1272.4} cy={800.5} r={0.7} fill="#FE8989" />
      <circle cx={1269.1} cy={849.5} r={0.7} fill="#FFF" />
      <circle cx={1205.1} cy={831.9} r={0.7} fill="#FFF" />
      <circle cx={1228.2} cy={824.7} r={0.7} fill="#FFF" />
      <circle cx={1300} cy={818.8} r={1.4} fill="#FE8989" />
      <circle cx={1217.5} cy={801.9} r={0.7} fill="#FFF" />
      <circle cx={1202.6} cy={782.9} r={0.7} fill="#FE8989" />
      <circle cx={1208.8} cy={822.4} r={0.7} fill="#FE8989" />
      <circle cx={1250.5} cy={774.3} r={1} fill="#FE8989" />
      <circle cx={1235.1} cy={802.6} r={1.4} fill="#FE8989" />
      <circle cx={1203.9} cy={852.8} r={1.4} fill="#FFF" />
      <circle cx={1286.2} cy={831.9} r={1.4} fill="#FFF" />
      <g opacity={0.7}>
        <linearGradient
          id="v"
          x1={947.494}
          x2={990.41}
          y1={880.592}
          y2={903.561}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#262626",
            }}
          />
          <stop
            offset={0.277}
            style={{
              stopColor: "#2f2f2f",
            }}
          />
          <stop
            offset={0.728}
            style={{
              stopColor: "#474747",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#595959",
            }}
          />
        </linearGradient>
        <path fill="url(#v)" d="M948.4 267.8v53l43 26 .1-50.1z" />
        <linearGradient
          id="w"
          x1={997.866}
          x2={1033.562}
          y1={909.679}
          y2={873.984}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.213}
            style={{
              stopColor: "#969696",
            }}
          />
          <stop
            offset={0.446}
            style={{
              stopColor: "#717171",
            }}
          />
          <stop
            offset={0.662}
            style={{
              stopColor: "#565656",
            }}
          />
          <stop
            offset={0.854}
            style={{
              stopColor: "#464646",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#404040",
            }}
          />
        </linearGradient>
        <path fill="url(#w)" d="m991.5 296.7-.1 50.1 47.3-26v-53z" />
        <linearGradient
          id="x"
          x1={980.297}
          x2={1008.467}
          y1={911.257}
          y2={955.356}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.219}
            style={{
              stopColor: "#a2a2a2",
            }}
          />
          <stop
            offset={0.561}
            style={{
              stopColor: "#7b7b7b",
            }}
          />
          <stop
            offset={0.833}
            style={{
              stopColor: "#636363",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#5a5a5a",
            }}
          />
        </linearGradient>
        <path fill="url(#x)" d="m948.8 267.8 42.3 28.9 47.5-28.8-43.9-27.8z" />
        <g fill="#FFF" opacity={0.12}>
          <path d="M959.4 275.5v39.2l32 19.2.1-37.1zM1025.2 275.5v39.2l-33.8 19.2.1-37.1z" />
          <path d="m959.3 275.5 31.9 21.3 34.9-21.3-32.7-20.6z" />
        </g>
        <linearGradient
          id="y"
          x1={1010.615}
          x2={1020.138}
          y1={921.881}
          y2={912.358}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#y)" d="M992 296.7v1l46.9-28.9.1-.9z" />
        <linearGradient
          id="z"
          x1={2747.812}
          x2={2755.372}
          y1={920.898}
          y2={913.339}
          gradientTransform="rotate(-180 1861.027 599.993)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#z)" d="M992 296.7v1l-43-28.9v-1z" />
        <linearGradient
          id="A"
          x1={979.219}
          x2={1004.225}
          y1={890.444}
          y2={865.439}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#A)" d="m992.3 297.2-.8.1-.1 49.5.9-.3z" />
        <linearGradient
          id="B"
          x1={789.505}
          x2={832.421}
          y1={786.574}
          y2={809.543}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#262626",
            }}
          />
          <stop
            offset={0.277}
            style={{
              stopColor: "#2f2f2f",
            }}
          />
          <stop
            offset={0.728}
            style={{
              stopColor: "#474747",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#595959",
            }}
          />
        </linearGradient>
        <path fill="url(#B)" d="M790.4 361.8v53l43 26 .1-50.1z" />
        <linearGradient
          id="C"
          x1={839.877}
          x2={875.573}
          y1={815.661}
          y2={779.965}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.213}
            style={{
              stopColor: "#969696",
            }}
          />
          <stop
            offset={0.446}
            style={{
              stopColor: "#717171",
            }}
          />
          <stop
            offset={0.662}
            style={{
              stopColor: "#565656",
            }}
          />
          <stop
            offset={0.854}
            style={{
              stopColor: "#464646",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#404040",
            }}
          />
        </linearGradient>
        <path fill="url(#C)" d="m833.5 390.7-.1 50.1 47.3-26v-53z" />
        <linearGradient
          id="D"
          x1={822.308}
          x2={850.478}
          y1={817.238}
          y2={861.338}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.219}
            style={{
              stopColor: "#a2a2a2",
            }}
          />
          <stop
            offset={0.561}
            style={{
              stopColor: "#7b7b7b",
            }}
          />
          <stop
            offset={0.833}
            style={{
              stopColor: "#636363",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#5a5a5a",
            }}
          />
        </linearGradient>
        <path fill="url(#D)" d="m790.8 361.8 42.3 28.9 47.5-28.8-43.9-27.8z" />
        <g fill="#FFF" opacity={0.12}>
          <path d="M801.4 369.5v39.2l32 19.2.1-37.1zM867.2 369.5v39.2l-33.8 19.2.1-37.1z" />
          <path d="m801.3 369.5 31.9 21.3 34.9-21.3-32.7-20.6z" />
        </g>
        <linearGradient
          id="E"
          x1={852.626}
          x2={862.149}
          y1={827.863}
          y2={818.34}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#E)" d="M834 390.7v1l46.9-28.9.1-.9z" />
        <linearGradient
          id="F"
          x1={2905.801}
          x2={2913.361}
          y1={826.88}
          y2={819.32}
          gradientTransform="rotate(-180 1861.027 599.993)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#F)" d="M834 390.7v1l-43-28.9v-1z" />
        <linearGradient
          id="G"
          x1={821.23}
          x2={846.236}
          y1={796.426}
          y2={771.42}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#G)" d="m834.3 391.2-.8.1-.1 49.5.9-.3z" />
        <path
          fill="none"
          stroke="#B3B3B3"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeMiterlimit={10}
          strokeWidth={3.221}
          d="m881 362.1 91.4-55.1"
        />
        <image
          xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAAzCAYAAADciPtuAAAACXBIWXMAAAsSAAALEgHS3X78AAAA GXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAACoJJREFUeNrsmsuPHUcZxb/qx33O yzYxjomyjIgQgg1SLIUNCpsss+ZfZIHYwSYKQmxYkCAExCiJHNsYY8eeOzN37u3uquJ3qno8M048 c3M9jlhMR+V+3OrqOnXOd+qrmphdHpfH5XF5rHG4V/2BuN43ovt/BHYOGPfNr1wsSPeKAXH/Aac3 np3s88+dLZena928GW1ry+zXXN9L/8SXBedeDaBfcX/V7BYgNifcD8yKp9TqnHUdLzzX76oy296O tk+99iuzT16L9gCg9jsq/mMtgO7CAX0AoHYGK94sPHJ2OOP8prMFgALAblJtdOKV+xQ/jlbxcPzf aOUhrV6J5rj/ZArA3z7P4Erg3MWAOgGofUxHF87m3zebL51t7zmrK4DCQOOdVQH2TjQUYMnNog1o cVpFezKJ1j4AJLRW34v2MfcPf68RiK8U2HMs5Ri6teXsTRiawdDTHWdXW8C1znbrwkLjbINS7ACc PnVcx5DfLutoBSir/WiHag3mhj7Y0yCU0Tb3otXjtcC5lwP1vtk71+i4JHfN7AoMzfcL68bOpshu CbAl56F675ypv74GmCgDSemQHixRzeYN16NgzYJK42Aj7heDYIOT4P7Eb5+uJEm3tvRu7WZjCMjv yYBrfo/0cAy45aCwAddtWyA73gNJC7CKvui/1BSstQWx5JEh+lwKGHpsOsBQPM9KKi3Gx+D+eo2Y +/DIUM4EVq0HqsqMHNSM9IazEUAWB4XVAIhToACoo3gHFSVy7HjGdUggXZ62OBUwFopgHcVgRzVq BoCmrRqagc9GMLvY5GLP7OfU/yMSv58biGeAq9YD9R9AEUvjuUM+nJeFzUNpFSBioHuuVATR8dK6 WNIFntPtMsCXPulzq56nzgWLXld0Pnpqcc3rXWM2xEDiEAWodkeNh3zv3aimT0hyfcaeyVbyS0wJ FJLr6Ea9ByhPxwGBmMyXACoqwHBtZTq7okyAXdEzhgR9MpCQAHn+q5xX0MG2S/0uiph+bQC3yQA2 fPOQ+0gso/vzjmpFtrJRKKYkv21G/hBDqGQODZ0e03mAeV9ZbGmzrBJjMVTiyVxfotrB8p1UVMQk PSByTwld+lIbM5NSWsBYBnPqDaMtkeiMeH77S8j7mdmDA8XaC+W4ImNY+jtTlyZcxVSF/Crkt1zQ 2VFmxAGqKmv6W9FdAIaaDlf9N+AgyRNQhF1wPVtIT3wJVA69mMazKGOSnlpyTAkt/feYjEctE0ym UjYzXY+xU2zdQh7TR1mC28hiIWNgvGMsMhsC5QACGB8BV0CritqPR6XM5qEW6WiMuusSODGpKOxC BlbLUDQ9KGZbpgBkv8VodFdgdDUqqpXY2qSPB3eRIEwd1tn9ZBTOq7MUSa9jfKNYqunagOeDBFSs SaaKteiyIzqBUkxJqK61EASNoMNIQkHEEY8FpRV0WAr8pIyl0xyY6I7n9bo4l633GaVizkjdxJ0O s2lovmoEjDhzoWek6Jlyg1QcwDLAIdIbpvui/40G6OAgDWyIGbiMJ6BTJ5YwEZWhRDzKzXdQFfzK Tlecy5YjBVLu95qCepIn37LVSGPlva0LXOwBRidwuTgnMHUCYs/O1GEAJGGBUuxFAZIcYz5LpMkS koOaTTCcwrsViFoV2BtZ03MY8/skuMiikTQYzVruJkvvwWXWslnExEgGke77c0j1svUfgcmZiMvG crTmjP19/1ODmRRkJelTFwFMi8Mrlpcdh2h7EylMmTDroFGlc5XLcuwn42AZgDuSV8wxmOIrJVfK QI6APDul9CoZv9ybeCtwxKLNc5gn8+hyOB7Xf1lgWvHOWSBqLeVZlthW7pQTMGURvvfumIGmDENs cJ8ds8hzYJ9GJR5inr+cHqQ0H3/FNIqYS8QRHUUgg2YJ6lVVXgWkxdvsIqTIkVxJydvApRBRV4J3 qfOOUgqE6xPbeFJKeV7KUygd1oXl1En36Rqrz27oE20B+3MhZyPIgrwRbwUUyYztDmNqr1ot0Ioz U6iDgxf8NDx+1fUJbZJKKjGNuEbfJUa6nBxqzkpJYsdzIjd26TpocvacuS+VgYhB8q0IwAZgNeeB sow6ZyBXUc72P2nuX2duAp3NmH+RvbbHbcqsJK9IbIQ06fYMqJMug3LMVQmMNcr+qNHCWpOem35T Kck8C2UgMFn7NDAl2caSgdK6rXqIO9LCxzvR7jzg47tnrruqM1PFyeTEe4/7Ve9GTLJSkXQULxmQ T51KeV8fU1mCPtlGkqUAi0XYiYVANQlw1EgFzgDzykSYpGPjbUxdj4nsiS2+N8IdH/2BFcztl8w8 tHu0tRXtMRIYfBUlezuQ9WO9wz7Q1YkiUUvRiCfjiGkrKskqZhtPMdYPgOoJnHKLAFvRt4ktkkEb FDnNMlbTLZKc8I3FLNqUb37xutmTzyytzc6Z1M4Gpi2+n5IglPdywn44yUwVkl2dO6qFYpXyva4n OmZgduzRsff0IwNJ8SXW+nhzsKdE2PFMrCqP7DhXrDTnU+1cRRvzvSd/jnZwJ66y/D8bGHjsR9ou A8hTWBsCbKjkBwke+t7FmGTKMrPiQwYleRYuLy6PBlagUvwlZ+zZpYS2S8YipgQqUBa0qe2Bdspg YBybfPuLGyuztUISDGVX3xMoZ3vE1mABLyWA0v6FT7YuErz6JgY133QhAYqaeDSvxT6bV2e01i8F OrujABY1caU4RNIBSbdRex6Ak7sCaod3796I9tk92Lq7EltnAes9nMY+Yh324+1oG0yMe3R8OCKI D4Mtg+snV/7VujGlsTHPTWIruATOEfAaA+UgglwW2TVlErJ1xVyQlIce+SHrJWrQO02wHcDdfT3a 7b9EW/w9rsrWC+3+1Gjc/w1N7TMEC86TzJofYMWMvkZYciIYmAI6MvA2uZ0s3XtKt+QZ9h6Wyd6r YgmIJpmFo1Sl4qvDJDrmLG81a69uDFgAXwHUvZs9qL+dAuUuJPMQa9pL17bzxizv9ZHEKeUmOfVp vyJUebKNlTrbwE6DG1IqSsjXleavGtCYRQugyLylXM0DasR6ZAFD+7tw+lWW3ylQB98K1Jn14qnf f+Ds+ntmP2H13LImazad7fDbHMkp468X2s4tsADqsxgcKI8M/bgprEiJlkvikw4PMQKxvZS77uF2 2Pqc+G1k6W12P9l6iqmPvjVTK8XgaXAsNK//8hic38kZ/8YBqS55ZAtILeEVehvaEzmxxCgY8RkZ e6TTI+x7tK9YizZjbqr3Y3LTCeEuk5ClP1laNordtUCtVP/0vuIJcDUdn+0BBPa0g+TJrbYAXGn6 2hFz/TusDirY0kJghvF0zItDTGi6y7XPgK4jc6VKt8n/jllaG9SqwJ6r95bZjV+4tFP09pfHALWD 5LR4Q4paYz4L30W/XPl3vwsFkCnZzBhQVzdz7tcCSPnfnhLb3VPOt+6fg1Z6L36t/g+VCMPeu6xm rmWAk4ZHY0t7Fd94zNJaOmXnAqOcbxvJ3RldKKB1/9rydfa0K3vjMQwC0q6f08KHJ8B8as9L7iL/ drzO38de8O5bK2w9nwLzSgBdxF8012krflf/P8aFtRu/6w9eHpfH5XF5vIrjfwIMALqN111XT2n9 AAAAAElFTkSuQmCC"
          width={54}
          height={51}
          opacity={0.2}
          overflow="visible"
          transform="translate(870.271 328.242) scale(.9804)"
        />
        <linearGradient
          id="H"
          x1={2660.311}
          x2={2673.176}
          y1={-777.698}
          y2={-769.562}
          gradientTransform="matrix(-1 0 0 1 3563.187 1126.798)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.839}
            style={{
              stopColor: "#fff",
              stopOpacity: 0,
            }}
          />
        </linearGradient>
        <path fill="url(#H)" d="m888.9 355 2.6 4.3 12.9-8.3-2.5-4.2z" />
        <linearGradient
          id="I"
          x1={628.516}
          x2={671.432}
          y1={689.555}
          y2={712.524}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#262626",
            }}
          />
          <stop
            offset={0.277}
            style={{
              stopColor: "#2f2f2f",
            }}
          />
          <stop
            offset={0.728}
            style={{
              stopColor: "#474747",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#595959",
            }}
          />
        </linearGradient>
        <path fill="url(#I)" d="M629.4 458.8v53l43 26 .1-50.1z" />
        <linearGradient
          id="J"
          x1={678.889}
          x2={714.584}
          y1={718.642}
          y2={682.947}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.213}
            style={{
              stopColor: "#969696",
            }}
          />
          <stop
            offset={0.446}
            style={{
              stopColor: "#717171",
            }}
          />
          <stop
            offset={0.662}
            style={{
              stopColor: "#565656",
            }}
          />
          <stop
            offset={0.854}
            style={{
              stopColor: "#464646",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#404040",
            }}
          />
        </linearGradient>
        <path fill="url(#J)" d="m672.5 487.7-.1 50.1 47.3-26v-53z" />
        <linearGradient
          id="K"
          x1={661.319}
          x2={689.49}
          y1={720.219}
          y2={764.319}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.219}
            style={{
              stopColor: "#a2a2a2",
            }}
          />
          <stop
            offset={0.561}
            style={{
              stopColor: "#7b7b7b",
            }}
          />
          <stop
            offset={0.833}
            style={{
              stopColor: "#636363",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#5a5a5a",
            }}
          />
        </linearGradient>
        <path fill="url(#K)" d="m629.8 458.8 42.3 28.9 47.5-28.8-43.9-27.8z" />
        <g fill="#FFF" opacity={0.12}>
          <path d="M640.4 466.5v39.2l32 19.2.1-37.1zM706.2 466.5v39.2l-33.8 19.2.1-37.1z" />
          <path d="m640.3 466.5 31.9 21.3 34.9-21.3-32.7-20.6z" />
        </g>
        <linearGradient
          id="L"
          x1={691.638}
          x2={701.16}
          y1={730.844}
          y2={721.321}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#L)" d="M673 487.7v1l46.9-28.9.1-.9z" />
        <linearGradient
          id="M"
          x1={3066.79}
          x2={3074.349}
          y1={729.861}
          y2={722.302}
          gradientTransform="rotate(-180 1861.027 599.993)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#M)" d="M673 487.7v1l-43-28.9v-1z" />
        <linearGradient
          id="N"
          x1={660.242}
          x2={685.247}
          y1={699.407}
          y2={674.402}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#N)" d="m673.3 488.2-.8.1-.1 49.5.9-.3z" />
        <path
          fill="none"
          stroke="#B3B3B3"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeMiterlimit={10}
          strokeWidth={3.221}
          d="m720 459.1 94.4-58.8"
        />
        <image
          xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAAzCAYAAADciPtuAAAACXBIWXMAAAsSAAALEgHS3X78AAAA GXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAACoJJREFUeNrsmsuPHUcZxb/qx33O yzYxjomyjIgQgg1SLIUNCpsss+ZfZIHYwSYKQmxYkCAExCiJHNsYY8eeOzN37u3uquJ3qno8M048 c3M9jlhMR+V+3OrqOnXOd+qrmphdHpfH5XF5rHG4V/2BuN43ovt/BHYOGPfNr1wsSPeKAXH/Aac3 np3s88+dLZena928GW1ry+zXXN9L/8SXBedeDaBfcX/V7BYgNifcD8yKp9TqnHUdLzzX76oy296O tk+99iuzT16L9gCg9jsq/mMtgO7CAX0AoHYGK94sPHJ2OOP8prMFgALAblJtdOKV+xQ/jlbxcPzf aOUhrV6J5rj/ZArA3z7P4Erg3MWAOgGofUxHF87m3zebL51t7zmrK4DCQOOdVQH2TjQUYMnNog1o cVpFezKJ1j4AJLRW34v2MfcPf68RiK8U2HMs5Ri6teXsTRiawdDTHWdXW8C1znbrwkLjbINS7ACc PnVcx5DfLutoBSir/WiHag3mhj7Y0yCU0Tb3otXjtcC5lwP1vtk71+i4JHfN7AoMzfcL68bOpshu CbAl56F675ypv74GmCgDSemQHixRzeYN16NgzYJK42Aj7heDYIOT4P7Eb5+uJEm3tvRu7WZjCMjv yYBrfo/0cAy45aCwAddtWyA73gNJC7CKvui/1BSstQWx5JEh+lwKGHpsOsBQPM9KKi3Gx+D+eo2Y +/DIUM4EVq0HqsqMHNSM9IazEUAWB4XVAIhToACoo3gHFSVy7HjGdUggXZ62OBUwFopgHcVgRzVq BoCmrRqagc9GMLvY5GLP7OfU/yMSv58biGeAq9YD9R9AEUvjuUM+nJeFzUNpFSBioHuuVATR8dK6 WNIFntPtMsCXPulzq56nzgWLXld0Pnpqcc3rXWM2xEDiEAWodkeNh3zv3aimT0hyfcaeyVbyS0wJ FJLr6Ea9ByhPxwGBmMyXACoqwHBtZTq7okyAXdEzhgR9MpCQAHn+q5xX0MG2S/0uiph+bQC3yQA2 fPOQ+0gso/vzjmpFtrJRKKYkv21G/hBDqGQODZ0e03mAeV9ZbGmzrBJjMVTiyVxfotrB8p1UVMQk PSByTwld+lIbM5NSWsBYBnPqDaMtkeiMeH77S8j7mdmDA8XaC+W4ImNY+jtTlyZcxVSF/Crkt1zQ 2VFmxAGqKmv6W9FdAIaaDlf9N+AgyRNQhF1wPVtIT3wJVA69mMazKGOSnlpyTAkt/feYjEctE0ym UjYzXY+xU2zdQh7TR1mC28hiIWNgvGMsMhsC5QACGB8BV0CritqPR6XM5qEW6WiMuusSODGpKOxC BlbLUDQ9KGZbpgBkv8VodFdgdDUqqpXY2qSPB3eRIEwd1tn9ZBTOq7MUSa9jfKNYqunagOeDBFSs SaaKteiyIzqBUkxJqK61EASNoMNIQkHEEY8FpRV0WAr8pIyl0xyY6I7n9bo4l633GaVizkjdxJ0O s2lovmoEjDhzoWek6Jlyg1QcwDLAIdIbpvui/40G6OAgDWyIGbiMJ6BTJ5YwEZWhRDzKzXdQFfzK Tlecy5YjBVLu95qCepIn37LVSGPlva0LXOwBRidwuTgnMHUCYs/O1GEAJGGBUuxFAZIcYz5LpMkS koOaTTCcwrsViFoV2BtZ03MY8/skuMiikTQYzVruJkvvwWXWslnExEgGke77c0j1svUfgcmZiMvG crTmjP19/1ODmRRkJelTFwFMi8Mrlpcdh2h7EylMmTDroFGlc5XLcuwn42AZgDuSV8wxmOIrJVfK QI6APDul9CoZv9ybeCtwxKLNc5gn8+hyOB7Xf1lgWvHOWSBqLeVZlthW7pQTMGURvvfumIGmDENs cJ8ds8hzYJ9GJR5inr+cHqQ0H3/FNIqYS8QRHUUgg2YJ6lVVXgWkxdvsIqTIkVxJydvApRBRV4J3 qfOOUgqE6xPbeFJKeV7KUygd1oXl1En36Rqrz27oE20B+3MhZyPIgrwRbwUUyYztDmNqr1ot0Ioz U6iDgxf8NDx+1fUJbZJKKjGNuEbfJUa6nBxqzkpJYsdzIjd26TpocvacuS+VgYhB8q0IwAZgNeeB sow6ZyBXUc72P2nuX2duAp3NmH+RvbbHbcqsJK9IbIQ06fYMqJMug3LMVQmMNcr+qNHCWpOem35T Kck8C2UgMFn7NDAl2caSgdK6rXqIO9LCxzvR7jzg47tnrruqM1PFyeTEe4/7Ve9GTLJSkXQULxmQ T51KeV8fU1mCPtlGkqUAi0XYiYVANQlw1EgFzgDzykSYpGPjbUxdj4nsiS2+N8IdH/2BFcztl8w8 tHu0tRXtMRIYfBUlezuQ9WO9wz7Q1YkiUUvRiCfjiGkrKskqZhtPMdYPgOoJnHKLAFvRt4ktkkEb FDnNMlbTLZKc8I3FLNqUb37xutmTzyytzc6Z1M4Gpi2+n5IglPdywn44yUwVkl2dO6qFYpXyva4n OmZgduzRsff0IwNJ8SXW+nhzsKdE2PFMrCqP7DhXrDTnU+1cRRvzvSd/jnZwJ66y/D8bGHjsR9ou A8hTWBsCbKjkBwke+t7FmGTKMrPiQwYleRYuLy6PBlagUvwlZ+zZpYS2S8YipgQqUBa0qe2Bdspg YBybfPuLGyuztUISDGVX3xMoZ3vE1mABLyWA0v6FT7YuErz6JgY133QhAYqaeDSvxT6bV2e01i8F OrujABY1caU4RNIBSbdRex6Ak7sCaod3796I9tk92Lq7EltnAes9nMY+Yh324+1oG0yMe3R8OCKI D4Mtg+snV/7VujGlsTHPTWIruATOEfAaA+UgglwW2TVlErJ1xVyQlIce+SHrJWrQO02wHcDdfT3a 7b9EW/w9rsrWC+3+1Gjc/w1N7TMEC86TzJofYMWMvkZYciIYmAI6MvA2uZ0s3XtKt+QZ9h6Wyd6r YgmIJpmFo1Sl4qvDJDrmLG81a69uDFgAXwHUvZs9qL+dAuUuJPMQa9pL17bzxizv9ZHEKeUmOfVp vyJUebKNlTrbwE6DG1IqSsjXleavGtCYRQugyLylXM0DasR6ZAFD+7tw+lWW3ylQB98K1Jn14qnf f+Ds+ntmP2H13LImazad7fDbHMkp468X2s4tsADqsxgcKI8M/bgprEiJlkvikw4PMQKxvZS77uF2 2Pqc+G1k6W12P9l6iqmPvjVTK8XgaXAsNK//8hic38kZ/8YBqS55ZAtILeEVehvaEzmxxCgY8RkZ e6TTI+x7tK9YizZjbqr3Y3LTCeEuk5ClP1laNordtUCtVP/0vuIJcDUdn+0BBPa0g+TJrbYAXGn6 2hFz/TusDirY0kJghvF0zItDTGi6y7XPgK4jc6VKt8n/jllaG9SqwJ6r95bZjV+4tFP09pfHALWD 5LR4Q4paYz4L30W/XPl3vwsFkCnZzBhQVzdz7tcCSPnfnhLb3VPOt+6fg1Z6L36t/g+VCMPeu6xm rmWAk4ZHY0t7Fd94zNJaOmXnAqOcbxvJ3RldKKB1/9rydfa0K3vjMQwC0q6f08KHJ8B8as9L7iL/ drzO38de8O5bK2w9nwLzSgBdxF8012krflf/P8aFtRu/6w9eHpfH5XF5vIrjfwIMALqN111XT2n9 AAAAAElFTkSuQmCC"
          width={54}
          height={51}
          opacity={0.2}
          overflow="visible"
          transform="translate(738.271 406.242) scale(.9804)"
        />
        <linearGradient
          id="O"
          x1={2792.311}
          x2={2805.176}
          y1={-699.698}
          y2={-691.562}
          gradientTransform="matrix(-1 0 0 1 3563.187 1126.798)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.839}
            style={{
              stopColor: "#fff",
              stopOpacity: 0,
            }}
          />
        </linearGradient>
        <path fill="url(#O)" d="m756.9 433 2.6 4.3 12.9-8.3-2.5-4.2z" />
        <linearGradient
          id="P"
          x1={471.528}
          x2={514.444}
          y1={593.178}
          y2={616.147}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#262626",
            }}
          />
          <stop
            offset={0.277}
            style={{
              stopColor: "#2f2f2f",
            }}
          />
          <stop
            offset={0.728}
            style={{
              stopColor: "#474747",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#595959",
            }}
          />
        </linearGradient>
        <path fill="url(#P)" d="M472.4 555.2v53l43 26 .1-50.1z" />
        <linearGradient
          id="Q"
          x1={521.9}
          x2={557.596}
          y1={622.265}
          y2={586.569}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.213}
            style={{
              stopColor: "#969696",
            }}
          />
          <stop
            offset={0.446}
            style={{
              stopColor: "#717171",
            }}
          />
          <stop
            offset={0.662}
            style={{
              stopColor: "#565656",
            }}
          />
          <stop
            offset={0.854}
            style={{
              stopColor: "#464646",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#404040",
            }}
          />
        </linearGradient>
        <path fill="url(#Q)" d="m515.5 584.1-.1 50.1 47.3-26v-53z" />
        <linearGradient
          id="R"
          x1={504.331}
          x2={532.501}
          y1={623.842}
          y2={667.942}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.219}
            style={{
              stopColor: "#a2a2a2",
            }}
          />
          <stop
            offset={0.561}
            style={{
              stopColor: "#7b7b7b",
            }}
          />
          <stop
            offset={0.833}
            style={{
              stopColor: "#636363",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#5a5a5a",
            }}
          />
        </linearGradient>
        <path fill="url(#R)" d="m472.8 555.2 42.3 28.9 47.5-28.8-43.9-27.9z" />
        <g fill="#FFF" opacity={0.12}>
          <path d="M483.4 562.8V602l32 19.3.1-37.1zM549.2 562.9V602l-33.8 19.3.1-37.1z" />
          <path d="m483.3 562.8 31.9 21.4 34.9-21.3-32.7-20.6z" />
        </g>
        <linearGradient
          id="S"
          x1={534.648}
          x2={544.172}
          y1={634.467}
          y2={624.943}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#S)" d="M516 584.1v.9l46.9-28.8.1-.9z" />
        <linearGradient
          id="T"
          x1={3223.778}
          x2={3231.338}
          y1={633.484}
          y2={625.924}
          gradientTransform="rotate(-180 1861.027 599.993)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#T)" d="M516 584.1v.9l-43-28.8v-1z" />
        <linearGradient
          id="U"
          x1={503.253}
          x2={528.259}
          y1={603.03}
          y2={578.025}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#U)" d="m516.3 584.6-.8.1-.1 49.5.9-.4z" />
        <path
          fill="none"
          stroke="#B3B3B3"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeMiterlimit={10}
          strokeWidth={3.221}
          d="m563 555.5 85.3-53.6"
        />
        <image
          xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAAyCAYAAAAX1CjLAAAACXBIWXMAAAsSAAALEgHS3X78AAAA GXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAACntJREFUeNrsWsuOHEkVvTcis57d 7Se2x5hZjpAQgg3SWIINGjZeesWCL+Df+AU0AqSReGkeQoMZMGZkS+PBptrdXV1dlRlxOedGVj/8 6i53jzWLTiucWVn5iBPn3HNvRLXI+Xa+nW/f5KZv4yW22ntMv83A7Gye/cYg9S2Awbm72N06wRN+ g/bIzgKgfnOAfoVzl0XuTPHtQqXpTu9siqT24PrRhknd6z78T+TT75h8tfEykCuB0zMGVdi5C3aa LZV5EgkEsqeye10k9xWgRBYAa0mlHppUA9z1H5FBNokRx5dMFOc+DiZfj/HI3+IVf18ZnJ4dS2Do Tr+wkw6B2Z2rXJqqd3rRqBhYzHXpo6Lz+lSk3hPZAsgFrulPTMYKwFcLwE/HYPDDlcHpmbB0Fyw1 G5AbOrkzUZleBJisksHK3higcHkCqBFAxou4pX9w97On3RMBLO6aDFqTbRzr1gHAz66sDE5PBwos 3X6m8u4IIw7pbQLQEJ/bjQKqvx0kDfEZ9zQ1gOUCMmMfwJYEkSkeaXOwFk16ANb2sqS5yTRmCYi/ 9W1zya4ITk8VS7cBYPxYZXalSG60EyQgjnrjIItZkNoCblRZBJUKKALA5sguARiA6IKPApgq43vs AUzqLE0LUOMss5B9ANY3XwbutcCqN0sRd0TeHxdQlN1wFx0OQfoA0iSAWQTRGnS0QTKurzMBBmnR lwoggUuaRRmvCGCWMhjOdBCpHLTIHN8PEWOznGV7R+Q6zKX6Hm5ptIwzR+TV4HRFtjpQV1TWnqhM eirrQhYAAu6QFPs9AETPQiQQ9DAQHD5DfoaGnpfHtdwBFKGDGVGA0yQ9gZWiNZZkDlBhF8xBlmOw OIQ0PxnBLT9Cl/7xWtaqU4HqAcReBcmxofMJrGgvSpsriQClBIS9cU8YAGnWDTglaeg4Og+6AC+7 ROd+HRSJ1/VJOiR4AXlvAVV4HJK19dO54tG4uqsuv8OgKL9QRemBnQosZQAKPfYYA4ZjAajEYwJT ggo8he9AJJSUAcyMoMAS2JKINJ5bAITdtC2TnQzwnc2STBCHw1RY++xPiLUH9joTOSEwul91EFN9 jN5eG6XqBWcmaoWYArC2dhUYPhsBce+2QWBgzmNtKUGYB4G5JhN9E5ImMDQAC8jkqqUtwOg25Drc yXINcfiXdZOHf3ytHKsTSZCJdx3B/BDuR6NoYyc/sKQJFFRkqwaDNaQFkAaAODbs1QpzotGfxTD0 sM8ERaZwwlpP1tRpQEsJDXFF86HpLPD/BvJbe0lKaXbtWDmewBXvlmpiCxLcXUPUDKB15Kc4RIeY iKTILYOdkAAIYLL2HJwDJJtoGTJUxBCHK/uwFfkZXNJpdAYBSKnTJBHu2iDptXBBXl8RZ90ZkNhx vT4eGOu+elLYurANCTZ0ObDVMF5KXEU+RwuILKxoe3g12AsFqL9HS05zKbLzYFC1cUmKeyPcEdd4 LGoBUKP1Yfk2A5gFkz7yJerMvCFvBOyIDJt/IwG3ha11mMeIXsdyCbkq0drYchdPsWbWclAK1oxF ofKYhhKcNQ6LWyNiJ/urcokxMkFzMVYlwbugrgmwjGK4bUrPWESD1uNYC8fKcI5yqcFDb3CqkUqp lMBWi5dTLjQG7zizKgaKNYbsy7DEGQEWsLEzl+ApwN8PQ1HGv8ealji0A2PzQnnuqW2V7Rhgt1xV MrvBYAdASKJB1U7tRyuBHbsETMZo8+qts3wC1QJcfexDx0qRpdq+RDoWcZZpAGAMexI35y3416vM /WcI9sLwlDHGiS8jZJvzKMTXBlU0Uo+BxFazc2XUQ9fhrMFjpcTTko1umqK2b/cEsowvP4ad0ykD nMLwmQpdAGBamDQ4HlGdLFzqov5TMUbCdjeLrhfzrk5jadSqd5w82DKNZHXXKwpS74VfpUVh3nEH Aidkr7FXz1/JMzaTNUsrqxC5sHqLJRZZt6CalnpgMkQxXM3sJFI83hXbttixTxBTkSSb992WVl3Y yAWPy4gXqZVa0CXmNp07UK2bh3R1IZN01GL/bvd0SGNBDAZ75jOBCS6v4Yhr5OJDtC9Om8eW23Yp heIa5JHLqUZKzDOp+imvJA46xs47S1YqejKglvw8Kw0zPAGNxym3zl4VCQ5lVptdpi2kOWD84b19 lFP3kaQn/+z682pnXAEYKo8qdGbF4G7NRz9oYUG70WfdF1hJ+ITSSs7qGHXGlmVUB8rCouQzArRS I0beAylqXfY7iLP4DFKEJCcAOB3YcTXh8cA2MEoT1Il1zUGCLDC7VciDL6wYE20uYDx2kpdHRaEF yDLeWD5wABhrykJXClMElaUUvxJwnMoASZ0AH9OYPZMFpioBk9DLCIf4B+lqxFNIkStgP+6VVaRA Nx+5W2NUmSkhuopBzzSNEA+lkHUssTBl+/ZOeLkrmzgIBNFCZm0RdGrAPNhKrbOZOB9rIMupyTZY CmBrA+//5KLJE5rH62V4PLBHaD+QsjS22YfGMXK9bZN5vxRvtGe6WUQn3V9xXRtKNaGs5Dvb9zDr JpVLkyBDkQDRLLZ0KWeboPocrHmWKd4R4PUXMFAP30F8IbamX55oalIduzJ7+QOCUtmGtimLllJi fWEeRJgQYuSpntqcKUIIbp/huXRiJb7cVhGLlCz2hn2lRX5tN3OOwyQzgGph71cg+3dvmnz+MWLr 8xOx9UrQdvi7m79W+eFTzGynmIvBPfrjIONZwLS91IkR5cCcs2UUxJGTTc69ON0I6sWyl4NaKvfg aQCcwFYDQZGfXsLgFLAzyC/sIeLGAAVlDCC7q5eyPHpkcu8egE33u/fGy28HhfB3VX75gcjm4yBP LqP6QI0TOH3YicjXwSebC4ALANVD7djiXAKwyHW2bkqivq7IfATYYCzBcDjfCtatc/SzM8X1jV3a CmLrIr5fg/s9eCBy/z5B2ZksmL6StcU6Fz7BCJwyssKPZUUqtWXNIxpXrEplooeeb7DsmmYD42GF 0XSLOAuA7IEhynzeA1MAMMYgfHULIfBXOPI9OQzqpCtQJ1waAGvXwNqPMHtmIbwEJz2A5ZwJwDIq /tlekAEA00FT0rJw023RqxgwhZMDmNBsAfbAFEukKZ1vgnIJMboOlh7eMPkCMbX3N+OK6qqgTgLs 0DU3Ae4XR8FxRWkdnwPmag3k18d5LoraGkD4clsHCpV5mInsIEUYzGAwxucdk52qACK/47ok4Afv QHqIqenvXjCKM/215ehK1fPg6NYXAVALwCHqymYOKV5d/vDQvWULbZcmjLw04jK2SP+/LwKa/BnS Q7E9fYgbn63M0qrAnrvuPUw6f67yM7r91ypP5x1AxJ9cByh0mtOMfOieWj1Viz7uFnAhVf7g8AKg L5csnQrUKivBz13/fZCHKUz8qcj7owJwBqkZ5Lm7B2BzOZjOMJshdkaQZzXz+bP8C6XRFqoI+b28 CtBpf7xb6d6XsvfeOurjn6i0o3LqNjp/KR197kdoE856Y5lyOJhBty54toBO8zPSS+4FgzLex/rC kt+R/h/5cOaAzuIXzTd9jr2NP104qx/Xv31/XHK+nW/n2/n2Jtv/BRgAncjBLDSQ4WYAAAAASUVO RK5CYII="
          width={54}
          height={50}
          opacity={0.2}
          overflow="visible"
          transform="matrix(.98 0 0 .98 590.282 497.242)"
        />
        <linearGradient
          id="V"
          x1={2940.311}
          x2={2953.176}
          y1={-609.339}
          y2={-601.203}
          gradientTransform="matrix(-1 0 0 1 3563.187 1126.798)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.839}
            style={{
              stopColor: "#fff",
              stopOpacity: 0,
            }}
          />
        </linearGradient>
        <path fill="url(#V)" d="m608.9 523.4 2.6 4.2 12.9-8.2-2.5-4.3z" />
      </g>
      <linearGradient
        id="W"
        x1={885.786}
        x2={885.947}
        y1={-920.659}
        y2={-787.501}
        gradientTransform="matrix(.3592 -.3065 1.2019 .6834 1339.393 1550.123)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#fff",
          }}
        />
      </linearGradient>
      <path
        fill="url(#W)"
        d="m733.6 720-50.2 47.8-192.3-124.4 64.5-34.5z"
        className="particlespoly"
        opacity={0.25}
      />
      <linearGradient
        id="X"
        x1={1042.459}
        x2={993.024}
        y1={-985.611}
        y2={-790.89}
        gradientTransform="matrix(.3592 -.3065 1.2019 .6834 1339.393 1550.123)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={0.211}
          style={{
            stopColor: "#d7daff",
            stopOpacity: 0.2113,
          }}
        />
        <stop
          offset={0.445}
          style={{
            stopColor: "#FE8989",
            stopOpacity: 0.4448,
          }}
        />
        <stop
          offset={0.662}
          style={{
            stopColor: "#FE8989",
            stopOpacity: 0.6617,
          }}
        />
        <stop
          offset={0.854}
          style={{
            stopColor: "#FE8989",
            stopOpacity: 0.8535,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path
        fill="url(#X)"
        d="m777.3 682.4-59 41.6-224.8-146.8 62.5-39.3z"
        className="particlespoly"
        opacity={0.75}
      />
      <g fill="#FFF">
        <circle cx={548.8} cy={631.8} r={1.6} />
        <circle cx={537.9} cy={613.6} r={1.6} />
        <circle cx={606.4} cy={676} r={1.6} />
        <circle cx={725.1} cy={657.4} r={1.6} />
        <circle cx={542.7} cy={596.8} r={1.6} />
        <circle cx={623.7} cy={724.8} r={1.6} />
        <circle cx={696.2} cy={708} r={1.6} />
        <circle cx={611.1} cy={610.4} r={1.6} />
        <circle cx={564.1} cy={553.5} r={1.6} />
        <circle cx={554.2} cy={589.4} r={1.6} />
        <circle cx={670} cy={625} r={1.6} />
        <circle
          cx={510.9}
          cy={630.2}
          r={3.2}
          transform="rotate(-71.567 510.854 630.206)"
        />
      </g>
      <linearGradient
        id="Y"
        x1={891.158}
        x2={874.257}
        y1={-968.806}
        y2={-817.394}
        gradientTransform="matrix(.3592 -.3065 1.2019 .6834 1339.393 1550.123)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0.07}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#fff",
          }}
        />
      </linearGradient>
      <path
        fill="url(#Y)"
        d="M672 693.7v66.7L492.3 643.8l.4-66.6z"
        className="particlespoly"
        opacity={0.35}
      />
      <linearGradient
        id="Z"
        x1={889.041}
        x2={876.613}
        y1={-943.059}
        y2={-831.72}
        gradientTransform="matrix(.3592 -.3065 1.2019 .6834 1339.393 1550.123)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#fff",
          }}
        />
      </linearGradient>
      <path
        fill="url(#Z)"
        d="M672 717v19.3L492.3 619.6l.4-19.1z"
        className="particlespoly"
        opacity={0.35}
      />
      <linearGradient
        id="aa"
        x1={1096.162}
        x2={1062.966}
        y1={-979.262}
        y2={-821.514}
        gradientTransform="matrix(.3592 -.3065 1.2019 .6834 1339.393 1550.123)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#fff",
          }}
        />
      </linearGradient>
      <path
        fill="url(#aa)"
        d="m733.3 655.8-1 12.7L555 550.9v-13.2z"
        className="particlespoly"
        opacity={0.2}
      />
      <g fill="#FFF" className="particlespoly" opacity={0.25}>
        <path d="m602.1 626.4 3.1-15.4 27.7-2.9 3.5-3.7-34.7 3.3-4 21.5z" />
        <path d="m594 618.4 3.2-15.4 28.2-3.5 2.9-3.2-34.6 3.4-4 21.5z" />
      </g>
      <g fill="#BFBFBF">
        <path d="m1428.767 315.562 2.404-1.435.615 1.03-2.404 1.435zM1432.368 313.355l2.405-1.435.615 1.031-2.405 1.435zM1435.884 311.2l2.404-1.435.615 1.03-2.405 1.435zM1439.671 309.063l2.405-1.435.614 1.03-2.404 1.435zM1443.272 306.856l2.405-1.435.615 1.03-2.405 1.436zM1446.75 304.664l2.404-1.435.615 1.03-2.405 1.435zM1450.464 302.553l2.405-1.434.615 1.03-2.405 1.435zM1454.052 300.371l2.405-1.434.615 1.03-2.405 1.435zM1457.767 298.261l2.405-1.435.615 1.03-2.405 1.436z" />
      </g>
      <path
        fill="none"
        stroke="#FE8989"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
        strokeWidth={3.493}
        d="m1096.5 511.7 79.7-47.4M938.5 605.7l79.7-47.4M781.4 699.4l79.6-47.3M624.2 791.7l79.7-47.4M468 885.5l79.7-47.3M1257.5 417.7l79.7-47.4"
      />
      <linearGradient
        id="ab"
        x1={3650.86}
        x2={3659.948}
        y1={114.599}
        y2={168.212}
        gradientTransform="matrix(-.3592 -.3065 -1.2019 .6834 2623.369 1550.123)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#ab)" d="M1114 501.7V579l88-56-59.7-37z" />
      <linearGradient
        id="ac"
        x1={3814.621}
        x2={3799.076}
        y1={74.987}
        y2={152.319}
        gradientTransform="matrix(-.3592 -.3065 -1.2019 .6834 2623.369 1550.123)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#ac)" d="M1050.3 461.1 1113 502l91-51.4-62.7-41.6z" />
      <circle cx={1115.2} cy={508.7} r={1.5} fill="#FE8989" />
      <circle cx={1080.2} cy={451.7} r={0.5} fill="#FE8989" />
      <circle cx={1161.9} cy={523.3} r={0.7} fill="#FFF" />
      <circle cx={1143.5} cy={454.4} r={0.7} fill="#FE8989" />
      <circle cx={1126.2} cy={497.7} r={0.7} fill="#FFF" />
      <circle cx={1164.6} cy={538.4} r={0.7} fill="#FFF" />
      <circle cx={1158.4} cy={480} r={0.7} fill="#FFF" />
      <circle cx={1138.4} cy={483.5} r={0.7} fill="#FE8989" />
      <circle cx={1135.1} cy={532.5} r={0.7} fill="#FFF" />
      <circle cx={1071.1} cy={514.9} r={0.7} fill="#FFF" />
      <circle cx={1094.2} cy={507.7} r={0.7} fill="#FFF" />
      <circle cx={1166} cy={501.8} r={1.4} fill="#FE8989" />
      <circle cx={1083.5} cy={484.9} r={0.7} fill="#FFF" />
      <circle cx={1068.6} cy={465.9} r={0.7} fill="#FE8989" />
      <circle cx={1074.8} cy={505.4} r={0.7} fill="#FE8989" />
      <circle cx={1116.5} cy={457.3} r={1} fill="#FE8989" />
      <circle cx={1101.1} cy={485.6} r={1.4} fill="#FE8989" />
      <circle cx={1069.9} cy={535.8} r={1.4} fill="#FFF" />
      <circle cx={1152.2} cy={514.9} r={1.4} fill="#FFF" />
      <linearGradient
        id="ad"
        x1={3634.565}
        x2={3643.653}
        y1={382.386}
        y2={435.998}
        gradientTransform="matrix(-.3592 -.3065 -1.2019 .6834 2623.369 1550.123)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#ad)" d="M798 689.7V767l88-56-59.7-37z" opacity={0.35} />
      <linearGradient
        id="ae"
        x1={3798.326}
        x2={3782.781}
        y1={342.774}
        y2={420.107}
        gradientTransform="matrix(-.3592 -.3065 -1.2019 .6834 2623.369 1550.123)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path
        fill="url(#ae)"
        d="M734.3 649.1 797 690l91-51.4-62.7-41.6z"
        opacity={0.35}
      />
      <circle cx={799.2} cy={696.7} r={1.5} fill="#FE8989" />
      <circle cx={764.2} cy={639.7} r={0.5} fill="#FE8989" />
      <circle cx={845.9} cy={711.3} r={0.7} fill="#FFF" />
      <circle cx={827.5} cy={642.4} r={0.7} fill="#FE8989" />
      <circle cx={810.2} cy={685.7} r={0.7} fill="#FFF" />
      <circle cx={848.6} cy={726.4} r={0.7} fill="#FFF" />
      <circle cx={842.4} cy={668} r={0.7} fill="#FFF" />
      <circle cx={822.4} cy={671.5} r={0.7} fill="#FE8989" />
      <circle cx={819.1} cy={720.5} r={0.7} fill="#FFF" />
      <circle cx={755.1} cy={702.9} r={0.7} fill="#FFF" />
      <circle cx={778.2} cy={695.7} r={0.7} fill="#FFF" />
      <circle cx={850} cy={689.8} r={1.4} fill="#FE8989" />
      <circle cx={767.5} cy={672.9} r={0.7} fill="#FFF" />
      <circle cx={752.6} cy={653.9} r={0.7} fill="#FE8989" />
      <circle cx={758.8} cy={693.4} r={0.7} fill="#FE8989" />
      <circle cx={800.5} cy={645.3} r={1} fill="#FE8989" />
      <circle cx={785.1} cy={673.6} r={1.4} fill="#FE8989" />
      <circle cx={753.9} cy={723.8} r={1.4} fill="#FFF" />
      <circle cx={836.2} cy={702.9} r={1.4} fill="#FFF" />
      <linearGradient
        id="af"
        x1={3629.489}
        x2={3638.577}
        y1={516.193}
        y2={569.806}
        gradientTransform="matrix(-.3592 -.3065 -1.2019 .6834 2623.369 1550.123)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#af)" d="M639 782.7V860l88-56-59.7-37z" opacity={0.35} />
      <linearGradient
        id="ag"
        x1={3793.25}
        x2={3777.705}
        y1={476.582}
        y2={553.915}
        gradientTransform="matrix(-.3592 -.3065 -1.2019 .6834 2623.369 1550.123)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path
        fill="url(#ag)"
        d="M575.3 742.1 638 783l91-51.4-62.7-41.6z"
        opacity={0.35}
      />
      <circle cx={640.2} cy={789.7} r={1.5} fill="#FE8989" />
      <circle cx={605.2} cy={732.7} r={0.5} fill="#FE8989" />
      <circle cx={686.9} cy={804.3} r={0.7} fill="#FFF" />
      <circle cx={668.5} cy={735.4} r={0.7} fill="#FE8989" />
      <circle cx={651.2} cy={778.7} r={0.7} fill="#FFF" />
      <circle cx={689.6} cy={819.4} r={0.7} fill="#FFF" />
      <circle cx={683.4} cy={761} r={0.7} fill="#FFF" />
      <circle cx={663.4} cy={764.5} r={0.7} fill="#FE8989" />
      <circle cx={660.1} cy={813.5} r={0.7} fill="#FFF" />
      <circle cx={596.1} cy={795.9} r={0.7} fill="#FFF" />
      <circle cx={619.2} cy={788.7} r={0.7} fill="#FFF" />
      <circle cx={691} cy={782.8} r={1.4} fill="#FE8989" />
      <circle cx={608.5} cy={765.9} r={0.7} fill="#FFF" />
      <circle cx={593.6} cy={746.9} r={0.7} fill="#FE8989" />
      <circle cx={599.8} cy={786.4} r={0.7} fill="#FE8989" />
      <circle cx={641.5} cy={738.3} r={1} fill="#FE8989" />
      <circle cx={626.1} cy={766.6} r={1.4} fill="#FE8989" />
      <circle cx={594.9} cy={816.8} r={1.4} fill="#FFF" />
      <circle cx={677.2} cy={795.9} r={1.4} fill="#FFF" />
      <linearGradient
        id="ah"
        x1={3619.959}
        x2={3629.047}
        y1={648.004}
        y2={701.616}
        gradientTransform="matrix(-.3592 -.3065 -1.2019 .6834 2623.369 1550.123)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#ah)" d="M484 875.7V953l88-56-59.7-37z" opacity={0.35} />
      <linearGradient
        id="ai"
        x1={3783.72}
        x2={3768.175}
        y1={608.393}
        y2={685.726}
        gradientTransform="matrix(-.3592 -.3065 -1.2019 .6834 2623.369 1550.123)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
            stopOpacity: 0,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path
        fill="url(#ai)"
        d="M420.3 835.1 483 876l91-51.4-62.7-41.6z"
        opacity={0.35}
      />
      <circle cx={485.2} cy={882.7} r={1.5} fill="#FE8989" />
      <circle cx={450.2} cy={825.7} r={0.5} fill="#FE8989" />
      <circle cx={531.9} cy={897.3} r={0.7} fill="#FFF" />
      <circle cx={513.5} cy={828.4} r={0.7} fill="#FE8989" />
      <circle cx={496.2} cy={871.7} r={0.7} fill="#FFF" />
      <circle cx={534.6} cy={912.4} r={0.7} fill="#FFF" />
      <circle cx={528.4} cy={854} r={0.7} fill="#FFF" />
      <circle cx={508.4} cy={857.5} r={0.7} fill="#FE8989" />
      <circle cx={505.1} cy={906.5} r={0.7} fill="#FFF" />
      <circle cx={441.1} cy={888.9} r={0.7} fill="#FFF" />
      <circle cx={464.2} cy={881.7} r={0.7} fill="#FFF" />
      <circle cx={536} cy={875.8} r={1.4} fill="#FE8989" />
      <circle cx={453.5} cy={858.9} r={0.7} fill="#FFF" />
      <circle cx={438.6} cy={839.9} r={0.7} fill="#FE8989" />
      <circle cx={444.8} cy={879.4} r={0.7} fill="#FE8989" />
      <circle cx={486.5} cy={831.3} r={1} fill="#FE8989" />
      <circle cx={471.1} cy={859.6} r={1.4} fill="#FE8989" />
      <circle cx={439.9} cy={909.8} r={1.4} fill="#FFF" />
      <circle cx={522.2} cy={888.9} r={1.4} fill="#FFF" />
      <g opacity={0.25}>
        <linearGradient
          id="aj"
          x1={1460.707}
          x2={1522.706}
          y1={-405.416}
          y2={-438.599}
          gradientTransform="translate(0 700)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#494949",
            }}
          />
          <stop
            offset={0.426}
            style={{
              stopColor: "#525252",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#656565",
            }}
          />
        </linearGradient>
        <path fill="url(#aj)" d="M1462 219.7V297l62 37.9v-73.1z" />
        <linearGradient
          id="ak"
          x1={1533}
          x2={1584.1}
          y1={-447.2}
          y2={-396.1}
          gradientTransform="translate(0 700)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#b8b8b8",
            }}
          />
          <stop
            offset={0.053}
            style={{
              stopColor: "#b0b0b0",
            }}
          />
          <stop
            offset={0.334}
            style={{
              stopColor: "#8b8b8b",
            }}
          />
          <stop
            offset={0.594}
            style={{
              stopColor: "#707070",
            }}
          />
          <stop
            offset={0.824}
            style={{
              stopColor: "#5f5f5f",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#5a5a5a",
            }}
          />
        </linearGradient>
        <path fill="url(#ak)" d="M1524 261.8v73.1l67-37.9v-77.3z" />
        <linearGradient
          id="al"
          x1={1507.235}
          x2={1547.978}
          y1={-450.455}
          y2={-514.237}
          gradientTransform="translate(0 700)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#787878",
            }}
          />
          <stop
            offset={0.598}
            style={{
              stopColor: "#636363",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#5a5a5a",
            }}
          />
        </linearGradient>
        <path
          fill="url(#al)"
          d="m1461.7 219.7 61.9 42.1 67.3-42.1-62.8-40.5z"
        />
      </g>
      <path
        fill="#B3B3B3"
        d="m1513 186.3-.8-1.3 2.3-1.4.8 1.3-2.3 1.4zm6.3-3.8-.8-1.3-2.3 1.4.8 1.3 2.3-1.4zm4.1-2.5-.8-1.3-2.3 1.4.8 1.3 2.3-1.4zm-20.3 12.3-.8-1.3-2.3 1.4.8 1.3 2.3-1.4zm4.1-2.5-.8-1.3-2.3 1.4.8 1.3 2.3-1.4zm4-2.4-.8-1.3-2.3 1.4.8 1.3 2.3-1.4zm-20.3 12.3-.8-1.3-2.3 1.4.8 1.3 2.3-1.4zm4.1-2.5-.8-1.3-2.3 1.4.8 1.3 2.3-1.4zm4-2.4-.8-1.3-2.3 1.4.8 1.3 2.3-1.4zm-20.2 12.3-.8-1.3-2.3 1.4.8 1.3 2.3-1.4zm4-2.5-.8-1.3-2.3 1.4.8 1.3 2.3-1.4zm4.1-2.5-.8-1.3-2.3 1.4.8 1.3 2.3-1.4zm-20.3 12.3-.8-1.3-2.3 1.4.8 1.3 2.3-1.4zm4.1-2.4-.8-1.3-2.3 1.4.8 1.3 2.3-1.4zm4-2.5-.8-1.3-2.3 1.4.8 1.3 2.3-1.4zM1461 233h-2v3h2v-3zm0-4h-2v2h2v-2zm0-5h-2v2h2v-2zm0 24h-2v2h2v-2zm0-5h-2v3h2v-3zm0-5h-2v2h2v-2zm0 24h-2v2h2v-2zm0-5h-2v3h2v-3zm0-4h-2v2h2v-2zm0 23h-2v3h2v-3zm0-5h-2v3h2v-3zm0-5h-2v3h2v-3zm0 24h-2v3h2v-3zm0-4h-2v2h2v-2zm0-5h-2v3h2v-3zm10.8 24.6-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.8-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.9 11.7-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.8-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.8-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.9 11.7-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.8-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.9 11.7-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.8-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.7-2.4-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm18.9 11.7-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.8-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm-3.8-2.3-.8 1.3 2.1 1.3.8-1.3-2.1-1.3zm27.1 1.4.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm-4.1 2.3.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm-4.1 2.4.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm20.5-11.8.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm-4.1 2.3.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm-4.1 2.4.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm20.6-11.8.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm-4.1 2.4.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm-4.1 2.3.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm20.5-11.8.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm-4.1 2.4.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm-4.1 2.3.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm20.6-11.7.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm-4.1 2.3.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm-4.1 2.4.8 1.4 2.3-1.3-.8-1.4-2.3 1.3zm15-23.4h1.6v-2.6h-1.6v2.6zm0 4.8h1.6v-2.6h-1.6v2.6zm0 4.7h1.6v-2.6h-1.6v2.6zm0-23.6h1.6v-2.6h-1.6v2.6zm0 4.7h1.6v-2.6h-1.6v2.6zm0 4.7h1.6v-2.6h-1.6v2.6zm0-23.6h1.6v-2.6h-1.6v2.6zm0 4.7h1.6v-2.6h-1.6v2.6zm0 4.7h1.6V262h-1.6v2.6zm-.1-23.5h1.6v-2.6h-1.6v2.6zm0 4.7h1.6v-2.6h-1.6v2.6zm.1 4.7h1.6v-2.6h-1.6v2.6zm-.1-23.6h1.6v-2.6h-1.6v2.6zm0 4.7h1.6V229h-1.6v2.6zm0 4.7h1.6v-2.6h-1.6v2.6zm-11.8-24.7.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm3.8 2.5.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm3.8 2.5.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm-19.1-12.5.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm3.8 2.5.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm3.8 2.5.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm-19.1-12.5.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm3.8 2.5.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm3.8 2.5.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm-19.2-12.5.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm3.9 2.5.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm3.8 2.4.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm-19.2-12.5.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm3.8 2.5.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm3.9 2.5.9-1.3-2.1-1.4-.9 1.3 2.1 1.4zm-81.6 29.9.8 1.3 1.3-.8-.8-1.3-1.3.8zm-2.1 1.4v2.2h2v-1.3h-.6l.4-.3-.9-1.3-1.1.8c.1-.2.2-.1.2-.1zm-.1 4.1h1.6v-1.5h-1.6v1.5zm-.2 74.4h1.6v-1.5h-1.6v1.5zm.2 1.7.1.5c.1.1.3.5.4.6.3.2.6.5.6.5l.8-1.3s-.1-.1-.2-.1-.1-.1-.2-.1v-1h-.1.1-1.4v1c0-.1-.2-.1-.1-.1zm3.4 3 .8-1.3-1.2-.8-.8 1.3 1.2.8zm59.1 36.7.8-1.3-1.3-.8-.8 1.3 1.3.8zm2.5 1.4s.1-.1.2-.1c.1-.1.3-.2.5-.3.3-.2.7-.4.7-.4l-.8-1.4s-.1.1-.3.2c-.1 0-.1.1-.2.1l-.5-.3-.8 1.3 1.2.9c-.1.1-.1.1 0 0zm3.7-2.1-.8-1.4-1.3.8.8 1.4 1.3-.8zm64.2-36.7-.8-1.4-1.3.8.8 1.4 1.3-.8zm2.2-1.4v-.7c0-.4-.2-.8-.2-.8h-1.8v.5h.6l-.5.3.9 1.4 1.1-.8c0 .2-.1.1-.1.1zm.1-4.2h-1.6v1.5h1.6v-1.5zm0-73.5h-1.6v1.4h1.6v-1.4zm-.1-2.2-.1-.2c-.1-.1-.3-.2-.4-.3l-.6-.4-.9 1.3s.1.1.2.1.1.1.2.1v.4h.1-.5v.1l1.8.1.2-1.2c.1 0 .1 0 0 0zm-3.2-2.4-.9 1.3 1.2.8.9-1.3-1.2-.8zm-60-39.3-.9 1.3 1.4.9.9-1.3-1.4-.9zm-2.6-1.5s-.1.1-.2.1c-.1.1-.3.2-.5.3-.3.2-.7.4-.7.4l.8 1.3s.1-.1.3-.2c.1 0 .1-.1.2-.1l.5.4.9-1.3-1.3-.9c.1-.1.1-.1 0 0zm-3.8 2.3.8 1.3 1.4-.8-.8-1.3-1.4.8z"
      />
      <linearGradient
        id="am"
        x1={1299.607}
        x2={1361.603}
        y1={-311.316}
        y2={-344.497}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.286}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.75}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#am)" d="M1300.9 313.8v77.3l62 37.9v-73.1z" />
      <linearGradient
        id="an"
        x1={1372.5}
        x2={1424.6}
        y1={-353.6}
        y2={-301.5}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.17}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.416}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.644}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.846}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#an)" d="M1363 355.9V429l69-37.9v-77.3z" />
      <linearGradient
        id="ao"
        x1={1347.056}
        x2={1388.141}
        y1={-355.867}
        y2={-420.186}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.059}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.641}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#ao)" d="m1300.9 313.8 61.8 42.1 69.2-42-64-40.6z" />
      <g fill="#FFF" opacity={0.12}>
        <path d="M1317 325v57.1l46 28.1v-54.1zM1413 325v57.1l-50 28.1v-54.1z" />
        <path d="m1316.3 325 46.6 31.1 50.8-31.1-47.6-30z" />
      </g>
      <linearGradient
        id="ap"
        x1={1390.3}
        x2={1404.45}
        y1={-371.4}
        y2={-357.25}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8bafd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c83fb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c5bf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6843f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#613af8",
          }}
        />
      </linearGradient>
      <path fill="url(#ap)" d="M1363 355.9v1.5l68.9-42.1-.1-1.3z" />
      <linearGradient
        id="aq"
        x1={3884.661}
        x2={3895.327}
        y1={-369.738}
        y2={-359.073}
        gradientTransform="matrix(-1 0 0 1 5222 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8bafd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c83fb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c5bf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6843f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#613af8",
          }}
        />
      </linearGradient>
      <path fill="url(#aq)" d="M1363 355.9v1.5l-62-42.1v-1.5z" />
      <linearGradient
        id="ar"
        x1={1345.25}
        x2={1381.6}
        y1={-325.45}
        y2={-289.1}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8bafd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c83fb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c5bf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6843f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#613af8",
          }}
        />
      </linearGradient>
      <path fill="url(#ar)" d="m1364 356.6-1 .2V429l1-.5z" />
      <linearGradient
        id="as"
        x1={352.218}
        x2={414.981}
        y1={250.792}
        y2={217.2}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.286}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.75}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#as)" d="M353.4 875.7V953l63 37.9v-73.1z" />
      <linearGradient
        id="at"
        x1={425.25}
        x2={476.85}
        y1={208.55}
        y2={260.15}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#9999ff",
          }}
        />
        <stop
          offset={0.17}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.416}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.644}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.846}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#at)" d="M416 917.8v73.1l68-37.9v-77.3z" />
      <linearGradient
        id="au"
        x1={399.308}
        x2={440.561}
        y1={206.185}
        y2={141.606}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.059}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.641}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#au)" d="m353.1 875.8 62.9 42.8 68.4-42.9-64.1-40.6z" />
      <g fill="#FFF" opacity={0.12}>
        <path d="M370 886.8V944l46 28v-54zM466 886.8V944l-50 28v-54z" />
        <path d="m369.8 886.8 46.5 31.2 50.9-31.2-47.7-29.9z" />
      </g>
      <linearGradient
        id="av"
        x1={443.31}
        x2={457.309}
        y1={190.46}
        y2={204.459}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8bafd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c83fb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c5bf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6843f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#613af8",
          }}
        />
      </linearGradient>
      <path fill="url(#av)" d="M416 917.8v1.4l68.7-42.1v-1.3z" />
      <linearGradient
        id="aw"
        x1={4831.91}
        x2={4843.076}
        y1={191.859}
        y2={203.026}
        gradientTransform="matrix(-1 0 0 1 5222 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8bafd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c83fb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c5bf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6843f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#613af8",
          }}
        />
      </linearGradient>
      <path fill="url(#aw)" d="M416 917.8v1.4l-63-42.1v-1.4z" />
      <linearGradient
        id="ax"
        x1={398.7}
        x2={435.262}
        y1={235.9}
        y2={272.462}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8bafd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c83fb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c5bf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6843f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#613af8",
          }}
        />
      </linearGradient>
      <path fill="url(#ax)" d="m418 917.5-2 1.1v72.3l2-1.1z" />
      <path fill="url(#am)" d="M1300.9 313.8v77.3l62 37.9v-73.1z" />
      <path fill="url(#an)" d="M1363 355.9V429l69-37.9v-77.3z" />
      <path fill="url(#ao)" d="m1300.9 313.8 61.8 42.1 69.2-42-64-40.6z" />
      <g fill="#FFF" opacity={0.12}>
        <path d="M1317 325v57.1l46 28.1v-54.1zM1413 325v57.1l-50 28.1v-54.1z" />
        <path d="m1316.3 325 46.6 31.1 50.8-31.1-47.6-30z" />
      </g>
      <path fill="url(#ap)" d="M1363 355.9v1.5l68.9-42.1-.1-1.3z" />
      <path fill="url(#aq)" d="M1363 355.9v1.5l-62-42.1v-1.5z" />
      <path fill="url(#ar)" d="m1364 356.6-1 .2V429l1-.5z" />
      <linearGradient
        id="ay"
        x1={1141.607}
        x2={1203.603}
        y1={-217.316}
        y2={-250.497}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.286}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.75}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#ay)" d="M1142.9 407.8v77.3l62 37.9v-73.1z" />
      <linearGradient
        id="az"
        x1={1214.5}
        x2={1266.6}
        y1={-259.6}
        y2={-207.5}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.17}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.416}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.644}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.846}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#az)" d="M1205 449.9V523l69-37.9v-77.3z" />
      <linearGradient
        id="aA"
        x1={1189.056}
        x2={1230.141}
        y1={-261.867}
        y2={-326.186}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.059}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.641}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#aA)" d="m1142.9 407.8 61.8 42.1 69.2-42-64-40.6z" />
      <g fill="#FFF" opacity={0.12}>
        <path d="M1159 419v57.1l46 28.1v-54.1zM1255 419v57.1l-50 28.1v-54.1z" />
        <path d="m1158.3 419 46.6 31.1 50.8-31.1-47.6-30z" />
      </g>
      <linearGradient
        id="aB"
        x1={1232.3}
        x2={1246.45}
        y1={-277.4}
        y2={-263.25}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8c8fd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c9cfb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c7cf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6868f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#6161f8",
          }}
        />
      </linearGradient>
      <path fill="url(#aB)" d="M1205 449.9v1.5l68.9-42.1-.1-1.3z" />
      <linearGradient
        id="aC"
        x1={4042.661}
        x2={4053.327}
        y1={-275.739}
        y2={-265.073}
        gradientTransform="matrix(-1 0 0 1 5222 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8c8fd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c9cfb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c7cf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6868f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#6161f8",
          }}
        />
      </linearGradient>
      <path fill="url(#aC)" d="M1205 449.9v1.5l-62-42.1v-1.5z" />
      <linearGradient
        id="aD"
        x1={1187.25}
        x2={1223.6}
        y1={-231.45}
        y2={-195.1}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8c8fd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c9cfb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c7cf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6868f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#6161f8",
          }}
        />
      </linearGradient>
      <path fill="url(#aD)" d="m1206 450.6-1 .2V523l1-.5z" />
      <linearGradient
        id="aE"
        x1={980.607}
        x2={1042.603}
        y1={-123.316}
        y2={-156.497}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.286}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.75}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#aE)" d="M981.9 501.8v77.3l62 37.9v-73.1z" />
      <linearGradient
        id="aF"
        x1={1053.5}
        x2={1105.6}
        y1={-165.6}
        y2={-113.5}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.17}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.416}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.644}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.846}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#aF)" d="M1044 543.9V617l69-37.9v-77.3z" />
      <linearGradient
        id="aG"
        x1={1028.056}
        x2={1069.141}
        y1={-167.867}
        y2={-232.186}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.059}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.641}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#aG)" d="m981.9 501.8 61.8 42.1 69.2-42-64-40.6z" />
      <g fill="#FFF" opacity={0.12}>
        <path d="M998 513v57.1l46 28.1v-54.1zM1094 513v57.1l-50 28.1v-54.1z" />
        <path d="m997.3 513 46.6 31.1 50.8-31.1-47.6-30z" />
      </g>
      <linearGradient
        id="aH"
        x1={1071.3}
        x2={1085.45}
        y1={-183.4}
        y2={-169.25}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8c8fd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c9cfb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c7cf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6868f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#6161f8",
          }}
        />
      </linearGradient>
      <path fill="url(#aH)" d="M1044 543.9v1.5l68.9-42.1-.1-1.3z" />
      <linearGradient
        id="aI"
        x1={4203.662}
        x2={4214.327}
        y1={-181.739}
        y2={-171.073}
        gradientTransform="matrix(-1 0 0 1 5222 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8c8fd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c9cfb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c7cf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6868f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#6161f8",
          }}
        />
      </linearGradient>
      <path fill="url(#aI)" d="M1044 543.9v1.5l-62-42.1v-1.5z" />
      <linearGradient
        id="aJ"
        x1={1026.25}
        x2={1062.6}
        y1={-137.45}
        y2={-101.1}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8c8fd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c9cfb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c7cf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6868f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#6161f8",
          }}
        />
      </linearGradient>
      <path fill="url(#aJ)" d="m1045 544.6-1 .2V617l1-.5z" />
      <linearGradient
        id="aK"
        x1={822.607}
        x2={884.603}
        y1={-29.316}
        y2={-62.497}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.286}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.75}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#aK)" d="M823.9 595.8v77.3l62 37.9v-73.1z" />
      <linearGradient
        id="aL"
        x1={895.5}
        x2={947.6}
        y1={-71.6}
        y2={-19.5}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.17}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.416}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.644}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.846}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#aL)" d="M886 637.9V711l69-37.9v-77.3z" />
      <linearGradient
        id="aM"
        x1={870.056}
        x2={911.141}
        y1={-73.867}
        y2={-138.186}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.059}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.641}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#aM)" d="m823.9 595.8 61.8 42.1 69.2-42-64-40.6z" />
      <g fill="#FFF" opacity={0.12}>
        <path d="M840 607v57.1l46 28.1v-54.1zM936 607v57.1l-50 28.1v-54.1z" />
        <path d="m839.3 607 46.6 31.1 50.8-31.1-47.6-30z" />
      </g>
      <linearGradient
        id="aN"
        x1={913.3}
        x2={927.45}
        y1={-89.4}
        y2={-75.25}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8bafd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c83fb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c5bf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6843f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#613af8",
          }}
        />
      </linearGradient>
      <path fill="url(#aN)" d="M886 637.9v1.5l68.9-42.1-.1-1.3z" />
      <linearGradient
        id="aO"
        x1={4361.662}
        x2={4372.327}
        y1={-87.739}
        y2={-77.073}
        gradientTransform="matrix(-1 0 0 1 5222 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8bafd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c83fb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c5bf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6843f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#613af8",
          }}
        />
      </linearGradient>
      <path fill="url(#aO)" d="M886 637.9v1.5l-62-42.1v-1.5z" />
      <linearGradient
        id="aP"
        x1={868.25}
        x2={904.6}
        y1={-43.45}
        y2={-7.1}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8bafd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c83fb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c5bf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6843f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#613af8",
          }}
        />
      </linearGradient>
      <path fill="url(#aP)" d="m887 638.6-1 .2V711l1-.5z" />
      <linearGradient
        id="aQ"
        x1={665.507}
        x2={727.503}
        y1={64.484}
        y2={31.303}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.286}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.75}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#aQ)" d="M666.8 689.6v77.3l62 37.9v-73.1z" />
      <linearGradient
        id="aR"
        x1={738.5}
        x2={790.6}
        y1={22.2}
        y2={74.3}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.17}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.416}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.644}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.846}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#aR)" d="M729 731.7v73.1l69-37.9v-77.3z" />
      <linearGradient
        id="aS"
        x1={712.842}
        x2={753.928}
        y1={19.854}
        y2={-44.465}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.059}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.641}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#aS)" d="m666.7 689.5 61.9 42.2 69.1-42.1-64-40.6z" />
      <g fill="#FFF" opacity={0.12}>
        <path d="M682 700.7v57.2l47 28v-54zM779 700.7v57.2l-50 28v-54z" />
        <path d="m682.1 700.7 46.6 31.2 50.8-31.2-47.6-29.9z" />
      </g>
      <linearGradient
        id="aT"
        x1={756.335}
        x2={770.435}
        y1={4.335}
        y2={18.435}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8bafd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c83fb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c5bf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6843f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#613af8",
          }}
        />
      </linearGradient>
      <path fill="url(#aT)" d="M729 731.7v1.4l68.9-42.1-.1-1.3z" />
      <linearGradient
        id="aU"
        x1={4518.66}
        x2={4529.326}
        y1={6.01}
        y2={16.675}
        gradientTransform="matrix(-1 0 0 1 5222 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8bafd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c83fb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c5bf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6843f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#613af8",
          }}
        />
      </linearGradient>
      <path fill="url(#aU)" d="M729 731.7v1.4L667 691v-1.4z" />
      <linearGradient
        id="aV"
        x1={711.2}
        x2={747.55}
        y1={50.3}
        y2={86.65}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8bafd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c83fb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c5bf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6843f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#613af8",
          }}
        />
      </linearGradient>
      <path fill="url(#aV)" d="m730 732.4-1 .1v72.3l1-.6z" />
      <linearGradient
        id="aW"
        x1={508.307}
        x2={570.303}
        y1={156.684}
        y2={123.503}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.286}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.75}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#aW)" d="M509.6 781.8v77.3l62 37.9v-73.1z" />
      <linearGradient
        id="aX"
        x1={581.25}
        x2={632.85}
        y1={114.65}
        y2={166.25}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.17}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.416}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.644}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.846}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#aX)" d="M572 823.9V897l68-37.9v-77.3z" />
      <linearGradient
        id="aY"
        x1={555.756}
        x2={596.842}
        y1={112.132}
        y2={47.814}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.059}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.641}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#FE8989",
          }}
        />
      </linearGradient>
      <path fill="url(#aY)" d="m509.6 781.8 61.8 42.1 69.2-42-64.1-40.6z" />
      <g fill="#FFF" opacity={0.12}>
        <path d="M526 792.9v57.2l46 28v-54zM622 792.9v57.2l-50 28v-54z" />
        <path d="m525 792.9 46.5 31.2 50.8-31.2-47.6-29.9z" />
      </g>
      <linearGradient
        id="aZ"
        x1={599.35}
        x2={613.4}
        y1={96.55}
        y2={110.6}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8c8fd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c9cfb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c7cf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6868f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#6161f8",
          }}
        />
      </linearGradient>
      <path fill="url(#aZ)" d="M572 823.9v1.4l68.8-42.1v-1.3z" />
      <linearGradient
        id="ba"
        x1={4675.675}
        x2={4686.341}
        y1={98.225}
        y2={108.891}
        gradientTransform="matrix(-1 0 0 1 5222 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8c8fd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c9cfb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c7cf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6868f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#6161f8",
          }}
        />
      </linearGradient>
      <path fill="url(#ba)" d="M572 823.9v1.4l-62-42.1v-1.4z" />
      <linearGradient
        id="bb"
        x1={554.5}
        x2={591.35}
        y1={142.3}
        y2={179.15}
        gradientTransform="translate(0 700)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.271}
          style={{
            stopColor: "#c8c8fd",
          }}
        />
        <stop
          offset={0.518}
          style={{
            stopColor: "#9c9cfb",
          }}
        />
        <stop
          offset={0.73}
          style={{
            stopColor: "#7c7cf9",
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#6868f8",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#6161f8",
          }}
        />
      </linearGradient>
      <path fill="url(#bb)" d="m574 824.6-2 .2V897l2-.5z" />
      <path fill="url(#as)" d="M353.4 875.7V953l63 37.9v-73.1z" />
      <path fill="url(#at)" d="M416 917.8v73.1l68-37.9v-77.3z" />
      <path fill="url(#au)" d="m353.1 875.8 62.9 42.8 68.4-42.9-64.1-40.6z" />
      <g fill="#FFF" opacity={0.12}>
        <path d="M370 886.8V944l46 28v-54zM466 886.8V944l-50 28v-54z" />
        <path d="m369.8 886.8 46.5 31.2 50.9-31.2-47.7-29.9z" />
      </g>
      <path fill="url(#av)" d="M416 917.8v1.4l68.7-42.1v-1.3z" />
      <path fill="url(#aw)" d="M416 917.8v1.4l-63-42.1v-1.4z" />
      <path fill="url(#ax)" d="m418 917.5-2 1.1v72.3l2-1.1z" />
      <g opacity={0.7}>
        <linearGradient
          id="bc"
          x1={1381.453}
          x2={1424.369}
          y1={601.629}
          y2={624.598}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#262626",
            }}
          />
          <stop
            offset={0.277}
            style={{
              stopColor: "#2f2f2f",
            }}
          />
          <stop
            offset={0.728}
            style={{
              stopColor: "#474747",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#595959",
            }}
          />
        </linearGradient>
        <path fill="url(#bc)" d="M1382.3 546.9v52.9l43.1 26v-50.1z" />
        <linearGradient
          id="bd"
          x1={1431.825}
          x2={1467.522}
          y1={630.718}
          y2={595.022}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.213}
            style={{
              stopColor: "#969696",
            }}
          />
          <stop
            offset={0.446}
            style={{
              stopColor: "#717171",
            }}
          />
          <stop
            offset={0.662}
            style={{
              stopColor: "#565656",
            }}
          />
          <stop
            offset={0.854}
            style={{
              stopColor: "#464646",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#404040",
            }}
          />
        </linearGradient>
        <path fill="url(#bd)" d="M1425.4 575.7v50.1l47.2-25.9v-53z" />
        <linearGradient
          id="be"
          x1={1414.256}
          x2={1442.426}
          y1={632.294}
          y2={676.394}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.219}
            style={{
              stopColor: "#a2a2a2",
            }}
          />
          <stop
            offset={0.561}
            style={{
              stopColor: "#7b7b7b",
            }}
          />
          <stop
            offset={0.833}
            style={{
              stopColor: "#636363",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#5a5a5a",
            }}
          />
        </linearGradient>
        <path
          fill="url(#be)"
          d="m1382.7 546.8 42.4 28.9 47.4-28.8-43.9-27.8z"
        />
        <g fill="#FFF" opacity={0.12}>
          <path d="M1393.3 554.5v39.2l32.1 19.2v-37zM1459.1 554.5v39.2l-33.7 19.2v-37z" />
          <path d="m1393.2 554.5 32 21.4 34.8-21.4-32.7-20.5z" />
        </g>
        <linearGradient
          id="bf"
          x1={1444.642}
          x2={1454.148}
          y1={642.914}
          y2={633.409}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#bf)" d="M1426 575.7v1l46.9-28.9.1-.8z" />
        <linearGradient
          id="bg"
          x1={2313.779}
          x2={2321.339}
          y1={641.935}
          y2={634.376}
          gradientTransform="rotate(-180 1861.027 599.993)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#bg)" d="M1426 575.7v1l-43-28.9v-.9z" />
        <linearGradient
          id="bh"
          x1={1413.178}
          x2={1438.183}
          y1={611.482}
          y2={586.477}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#bh)" d="m1426.2 576.2-.8.1v49.5l.8-.3z" />
        <linearGradient
          id="bi"
          x1={1223.464}
          x2={1266.38}
          y1={507.611}
          y2={530.58}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#262626",
            }}
          />
          <stop
            offset={0.277}
            style={{
              stopColor: "#2f2f2f",
            }}
          />
          <stop
            offset={0.728}
            style={{
              stopColor: "#474747",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#595959",
            }}
          />
        </linearGradient>
        <path fill="url(#bi)" d="M1224.3 640.9v52.9l43.1 26v-50.1z" />
        <linearGradient
          id="bj"
          x1={1273.836}
          x2={1309.533}
          y1={536.699}
          y2={501.003}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.213}
            style={{
              stopColor: "#969696",
            }}
          />
          <stop
            offset={0.446}
            style={{
              stopColor: "#717171",
            }}
          />
          <stop
            offset={0.662}
            style={{
              stopColor: "#565656",
            }}
          />
          <stop
            offset={0.854}
            style={{
              stopColor: "#464646",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#404040",
            }}
          />
        </linearGradient>
        <path fill="url(#bj)" d="M1267.4 669.7v50.1l47.2-25.9v-53z" />
        <linearGradient
          id="bk"
          x1={1256.267}
          x2={1284.437}
          y1={538.275}
          y2={582.375}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.219}
            style={{
              stopColor: "#a2a2a2",
            }}
          />
          <stop
            offset={0.561}
            style={{
              stopColor: "#7b7b7b",
            }}
          />
          <stop
            offset={0.833}
            style={{
              stopColor: "#636363",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#5a5a5a",
            }}
          />
        </linearGradient>
        <path
          fill="url(#bk)"
          d="m1224.7 640.8 42.4 28.9 47.4-28.8-43.9-27.8z"
        />
        <g fill="#FFF" opacity={0.12}>
          <path d="M1235.3 648.5v39.2l32.1 19.2v-37zM1301.1 648.5v39.2l-33.7 19.2v-37z" />
          <path d="m1235.2 648.5 32 21.4 34.8-21.4-32.7-20.5z" />
        </g>
        <linearGradient
          id="bl"
          x1={1286.654}
          x2={1296.159}
          y1={548.895}
          y2={539.39}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#bl)" d="M1268 669.7v1l46.9-28.9.1-.8z" />
        <linearGradient
          id="bm"
          x1={2471.768}
          x2={2479.328}
          y1={547.917}
          y2={540.357}
          gradientTransform="rotate(-180 1861.027 599.993)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#bm)" d="M1268 669.7v1l-43-28.9v-.9z" />
        <linearGradient
          id="bn"
          x1={1255.189}
          x2={1280.194}
          y1={517.464}
          y2={492.459}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#bn)" d="m1268.2 670.2-.8.1v49.5l.8-.3z" />
        <path
          fill="none"
          stroke="#B3B3B3"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeMiterlimit={10}
          strokeWidth={3.221}
          d="m1314.9 641.1 91.4-55.1"
        />
        <image
          xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAAzCAYAAADciPtuAAAACXBIWXMAAAsSAAALEgHS3X78AAAA GXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAACmNJREFUeNrsms+PHEcVx9+r7p6Z /b22g3GWiGNEBIgTB0vhgsIlx5z5FzkgbnCJghAXDiQIBWyURI4djImz3t8z0131+Lyqnv0Vez2e XUcctq3enp7urq5vfb/vW+/VWOR6u96ut+ttgU1f9wtssXeY/j8CewkYff4jVwtSXzMgzj/g8Nbx QT7/XGUyOXvX1pbJ+rrIb/j8KP+xy4LT1wPo15zfFLkLiLVlzgci4Rl3dSpdxwPn+l3XIhsbJvvc 134j8sn3TB4DVH7Pjf9YCKBeOaAPANTuwkoUSV+rHO1y/KHKGEB3ADY618hX7HHJpObC0n9NqiNa vWGinH+yAsDfnWdwLnB6NaBOAWqf0tGxyuH3RQ4nKht7KpZUIteD9ymdbSjBku6aDGhxpTbZXjZp HwMSWus3TD7m/MkffATstQI7x1KJobvrAOgB7b+hcrMVOToIcrgEKFhaAaCtqqTAPZ0DLU9XjUng u3rf5Mhbg7lhTPIsOUqTtT2TZmkhcHopUO+/z99byA7JHWyeAOoA1BwGJIYE2yBLNc8prEXADR1R 38IYFml2CMCjluYrIwaT6HKS0dRkPEgyOA3uz9xzby5J6sLSu7sDSwDZ3y+gNiqYaAKjH2TKsam9 yypBgySXYsU1jsXZVTQAQguYGobaaZIKKbYJMICLNefodrx0Au5vt4i5D2eGciGwejFQMLCCpHZu AAhWqmWYganGglTrQcIYfVlFxzlHazFypA0L7LBm9CmYy9AAnfgebRJklcTcX5qWGmbBJyM0Ol7j w57ILxiIP7WK4fjo+Ki8EFy9GKj/FJaWiJ0jZ2KXziMq/xd9nAO7VlJxLXCUIcccYICCWfUmHQOM KAFnMRJ3qTDMBcZEuimPAd6GSN370HH3EwbxXStSvndhv+v5I+w5oDq6USO7ybiCBTo+8jGvpQKM phpWOPdeagEcYEz74Uo5zlJG6P/qEEUBLTDilzKb3DgF3NqhypR3HnFut7i49tLe1nOyhVEgjfRE ZOc0KJhoYGcKIAVAAowD0qqmU3U+twpAoDC3CWTpbKm7nklmx4Gpsqcuy7QFa3KVsSdibnDIk0OT CQ/uMp288yXk/Vzk8YHH2gvlWM8nQSxdp1j5NjEFa9n1YAFumHgBZDU2DUM9ILMGabEHQFpdbFA9 7kJuOeYYYfSNT9oVUA7YxUhfA4bi0vPWFMds+S5Gy1PFMm5Zezazclkp9hKc9BKsDmgcM/DY6oib 1FQFlDaMNF3hqMy6ITSlfb5T383ZKlIshMzYCt79DLSzArhBlh33Jt5hUDhFIes4ancDpc4XOHMA I2NY2xY5IDXaWCtsjWs3glACZcaUgwKQwpbooCSIgJRjxqqepyII0x6YU9UDAgrGQ8SlYjateG7J t8gT95euKVnMxdXAi4GdiS1PZBONH2Lta2QPQ2zd3Q402RhiDnXaARACPQHFrhkYksxmHyTz5U7n pmEl3lLupP+NfQzCkpYJ3edyGxWH7A64K8499YaLLxNbOTuH/zuN5jiNAx9FZEgnWmfBGXGTiL3s sgR7UBztmLkSdz4IWZr9gDgQdJwn8mAFvEszW4L/4fIyphKizkHUvMDeKoN/dEdyftce4Vqw5w4F styZPAHTSau8szOzKAxaZrEq3/WS9IFIHps+v4n2phZO8iCbJRV6XN5MMZPABK7VFQHz4tDrqJb5 5WhSbH554BIpcnFJlg76vFVlYKkHYTMndDB+j2e7/XMzPdlMV9h/st4R+RxwxNBazkAimUcXS1dV rwiYE+bF4RR9x026uClZNp5BeICnnBqVVMm/TxaOpeVA3AkLCD0OX+0B+NFlYJZm4sufPX9UUqzo 85gzxbGuSxWQi7fdSwPTXMbnitf17bFFhzsyAw8Vd6oUS+5X9dKZxYCzota7X5aWA5iZRNmV+et4 cg7FIWNKeRrI5tKQBHPeAI45XnaGbjo+t9nlGfO1idNlvMdZPg9yzER2tr4ZzaZnJf/zzqkfmYBz Ythl51PFxK11KdAOn0NXrrFXGnM2EqjJPHdsAdXQ3sCzjKZkIDeZfjb+ycv+deEiUHilKrOmcS8t dCpFTi4XcjpLPZgsq9hnuL42QKfpbHJbVd+nBRjn5uD6YwYPQGcxue03gKOtGnBTZFm5HEnn3KA/ 3jR58BhAOxfWXfWFqeLWFjnZ4/7Zp30ro1IcVg6oSgR6yqlRBuMsqMdWV5RJ55gYMpUO3hmc3ecA kznIqae6mUWbAUOaNo2yxPMRE9lrSqyNYPDrP1LB3L+kFH1JzOXlRd5AXRZY74TKls8VL/U9MaKh zyJcas6OhWneveDI9NqEa5NynH1OE5jwdTiupzaz5TnjgOOAgRqOaIkKejSk/X2TFV7+xZsi2/7I 3svWIl+SUvkS30/WkQGsVRjG0XLJuit0n5pZkRhzVpHl5OlOKlVxiqnkGtkZrcSeF5RaJOr3S44/ j7M2g3Km3UyKO1LKoIbDFV+5Mlnifdt/MTl4YPOU/xcDe8T+40FZMXpG4A4BNnQZAuiIjjuwAUVi 8GWB0n3SLADwXaX9vJU0W3mxySLFnCe67NRLFRgCYBX8POZ9zMTlywOHDJCvYG3y7i9IErY/m4ut OZJgKLv5noNS2VtFjmNkCCMNVlxbSdxql6cPflOYchNJs6LSZnZZGLMcY710Z/FURWkBVXH0Sro1 X/MAnMfwNMkmx4dvmnz2CLYezsXWRcCsPEtjH62o/HTDZJWJca/2RZUkQ3Q+SaW+90O03lAKZ3mJ wOeAFLR0IfXXYFmr1Dsl9/XzVkd2myovVRiwCWqoCqgbgHq0ZXL/rwzopzYvWy80jzOj8dVvaWqf IRhzRIq+LBYJ6gqpJF+r8HmoZm87JnM3Dne3KYXhhMKw381NZIKFYyrWm0Vspa5aZNzJUizTQkPt 1TFwGs+B+vsZUHr5JFjK8rKvpfuy8+puWesTwJFyk5yW9YrkwDxeajoqvk9JWvsdIBV7DVjj3Oeu GSBf947RTSfKAQzt72BG3xBTeg7UwSuBuvA+O3P9Byq33xP52aHmDH9KwbnJtUMMwrP9xpfb6pA9 zqi2h6YnCWssh5b5KMLCECMYuTG4mezhdtj6IfE7ZdBW2uJ+bus5pj56ZabmisGz4LYA96sTcNFX jQiw1QPMnjyyBaSX8B5zqyNM5XSJgZT3ydiN0mOEfY/2ywS/y3nD50gMLhPuD+8US/e5KhvFzkKg 5rr/7KLOKXANHd/dA8haqc98+XodwLUztOmzev8MZU89sbySsDti9mL6GGJCKzt8jgXQbWTuqdJ9 8r8TlhYGNS+wc/e9TTX9S80rRe98eQLQV5D0htt+yf6Pw3fcp8v/LvOcG/EKk/4SoG6uldyvBZDn f3ue2O6ccb5Ffw6a6zn71v0/kryweftdqplbBeAyvtAuSV7pfe62K7nk8OzcwXjOt4HkHoyuFNCi v7Z8mz1flb3zFAYBKbdf0sKHp8Dck/OSu8rfjhf5fewFz749x9LzGTCvBdBV/KK5SFv2Xf1/jCtr 177rF15v19v1dr29ju1/AgwAjPnYVBycb7QAAAAASUVORK5CYII="
          width={54}
          height={51}
          opacity={0.2}
          overflow="visible"
          transform="translate(1304.271 607.242) scale(.9804)"
        />
        <linearGradient
          id="bo"
          x1={2226.385}
          x2={2239.25}
          y1={-498.684}
          y2={-490.549}
          gradientTransform="matrix(-1 0 0 1 3563.187 1126.798)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.839}
            style={{
              stopColor: "#fff",
              stopOpacity: 0,
            }}
          />
        </linearGradient>
        <path fill="url(#bo)" d="m1322.8 634 2.6 4.3 13-8.3-2.6-4.2z" />
        <linearGradient
          id="bp"
          x1={1062.476}
          x2={1105.391}
          y1={410.592}
          y2={433.561}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#262626",
            }}
          />
          <stop
            offset={0.277}
            style={{
              stopColor: "#2f2f2f",
            }}
          />
          <stop
            offset={0.728}
            style={{
              stopColor: "#474747",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#595959",
            }}
          />
        </linearGradient>
        <path fill="url(#bp)" d="M1063.3 737.9v52.9l43.1 26v-50.1z" />
        <linearGradient
          id="bq"
          x1={1112.848}
          x2={1148.544}
          y1={439.68}
          y2={403.984}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.213}
            style={{
              stopColor: "#969696",
            }}
          />
          <stop
            offset={0.446}
            style={{
              stopColor: "#717171",
            }}
          />
          <stop
            offset={0.662}
            style={{
              stopColor: "#565656",
            }}
          />
          <stop
            offset={0.854}
            style={{
              stopColor: "#464646",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#404040",
            }}
          />
        </linearGradient>
        <path fill="url(#bq)" d="M1106.4 766.7v50.1l47.2-25.9v-53z" />
        <linearGradient
          id="br"
          x1={1095.278}
          x2={1123.448}
          y1={441.257}
          y2={485.356}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.219}
            style={{
              stopColor: "#a2a2a2",
            }}
          />
          <stop
            offset={0.561}
            style={{
              stopColor: "#7b7b7b",
            }}
          />
          <stop
            offset={0.833}
            style={{
              stopColor: "#636363",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#5a5a5a",
            }}
          />
        </linearGradient>
        <path
          fill="url(#br)"
          d="m1063.7 737.8 42.4 28.9 47.4-28.8-43.9-27.8z"
        />
        <g fill="#FFF" opacity={0.12}>
          <path d="M1074.3 745.5v39.2l32.1 19.2v-37zM1140.1 745.5v39.2l-33.7 19.2v-37z" />
          <path d="m1074.2 745.5 32 21.4 34.8-21.4-32.7-20.5z" />
        </g>
        <linearGradient
          id="bs"
          x1={1125.665}
          x2={1135.17}
          y1={451.877}
          y2={442.372}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#bs)" d="M1107 766.7v1l46.9-28.9.1-.8z" />
        <linearGradient
          id="bt"
          x1={2632.757}
          x2={2640.316}
          y1={450.898}
          y2={443.338}
          gradientTransform="rotate(-180 1861.027 599.993)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#bt)" d="M1107 766.7v1l-43-28.9v-.9z" />
        <linearGradient
          id="bu"
          x1={1094.2}
          x2={1119.206}
          y1={420.445}
          y2={395.44}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#bu)" d="m1107.2 767.2-.8.1v49.5l.8-.3z" />
        <path
          fill="none"
          stroke="#B3B3B3"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeMiterlimit={10}
          strokeWidth={3.221}
          d="m1153.9 738.1 94.4-58.8"
        />
        <image
          xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAAzCAYAAADciPtuAAAACXBIWXMAAAsSAAALEgHS3X78AAAA GXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAACmlJREFUeNrsmstzHUcZxb/umbkP yVcPOxhFpFimSAEFGxauChsqsMgyaxas+d9YsYNNCopiw4IkRQE2laScGIyJkfW8j5np5ne65+oV W7q5klIsNPJoZu7M9O3T53ynv69ls9vtdrvdbrclNnfTXxCX+47o/h+BXQLGvfyV6wXpbhgQ1+9x eOP4YJ984mw6PfvU9na0tTWzX3H+JP2KVwXnbgbQz7m+a/YAEKMVrntm/gVPNc6ahhfO9bsszdbX ox3wXP1fsw+/Ee0pQO03PPi3pQC6awf0HoDqPVhpzcIXzsZ7HL/tbAKgLYANzjXyT/Z2GK3kxvA/ 0YoxrW5Gc1x/uArAX59ncCFw7npAnQJUP6ejE2dH3zQ7mjpb33cWg7OW+159CmcbCrDk9qL1aHG1 jLazEq1+CkhoLV+L9gHXz36rEYg3CuwcSzmGHqwBoAN08Jqzu7XZ+NDb0RBQsLQKwHjHWfA80who fruoonk+Kw+ijdUazPXbYC+CUEYb7UerhkuBc1cC9e67/L6H7JDc4cYJoAZA1ZFHYkiw9jYsec/B Wgu4vhB1LUxgkWb7ABzXNF9EYjCYWwk2mEWb9IL1ToP7I888XEiSbmnpPdiFJYAcHGRQ6wVMVJ7R 9zbjWJXqsjPvvAVJseAex+zszpwHhMtgShiqZ8EKpFgHwACuLblGt5PhCbiP7hFz788N5UJg5XKg YGAVSe1uAghWihWYgakqeivWvPkJ+ooFHecarbUtR9qInh3WIn3yUTKMgA58jjYJssLa1F+athJm wWcDNDoZcbJv9mMG4g+1w3A0OhqVV4IrlwP178zSkNgZi4k9Oo+o9NNqnD27K6zgnudofY4pwAAF s05NCgOMOAIuti1xFzLD3GBMrJnxGuBjH6mrDw1PP2MQ345Zyg8v7He5eIS9BFRDN0pkN50UsEDH Bxrz0grAuFDCCtfqpcuAPYy5brhCirOQEOqn9K05QBuM6FZikwdngBsdOZvxnWOu4z1uji7tbbkg WxgF0gjPzHZPg4KJCnZmAHIACIARIFeUdKpM17EAECiibAJZii0n14uW2BEw59hDk2RagzVIZeyB mOsd8WY/2pQX95hO3voM8n5k9vRQsfZKOZaLSRBLdzOsfIeYgrXkerAAN0y8AIolNg1DHaAYK6TF 7gEZy2yDTnHnU8ttihFGP3LmmgxKgCVG+uoxFElPrTkcs+azto1pqljBLUtlM6tXlWInwWknweKQ xjEDxVZD3ISqyKBcxUjTFY6OWdf7KrfPZ057FFtZipmQOVte3U9Am5gBV8iy4dnAd0QonKGQNRy1 2USpiwXOAsDIGEY7ZoekRuujzNaklBH4HChzpgQKQA62zPVygghIO2as6HjKgoiuAyaqOkBAwXiI uJDNpjbllnyKPHF/a6qcxVxcDbwa2JnYUiIbaPwIax+RPfSxdbkdaJIxtCnUaQdACPQEFLtLwJBk MntviS85nUwj5ngLqZP63XYxCEsuT+iay+MgO2RzyFPtwlOvv/g2sZWyc/jfqlyK07anUUSGdKIW C2JEJtF2sksS7EBxjMfM5bjTICRpdgMiIOg4TeQ+ZvCSZrIE/eL2CqbiW7cAUYsCeyMP/njLUn5X j3Et2JNDgSx1Jk3AdDIW6uzcLDKDMbFY5M86SWoggmJT85u5ztT8SR4U50mFOy5vZpiJZwJ3xTUB U3GoOqpmfhlPs82v9CSRLBdJMndQ81aRgIUORJw7ocDoGWW73XtzPcW5rrD/EDtH5NzjiL6OKQNp yTyaNnfVuWsCJsJUHM7Qd7tBFzcsyUYZhAI8pNQop0r6PER/LC0BkRNmEO44fF0HQEfJIMYwF186 V/7oSLFazWNiimNZ5iogFW97VwbmUhmfKl7pW7FFhxsyA4WKnCq0OfcrOunMY0CsuNi5X5KWAMxN Iu+O+et4cvbZIdsQ0jSQzKUiCea6AhxzvO32ZTqa2+LVGdPaxOkyXnGWrr0dM5GcrWvGJdOLOf9T 55yOTMApMWyS8zmHicdaUqAdzn2T77EXrk3ZiKcmU+5YA6qivZ6yjCpnIHeZftb/zpf948JFIP+V qsySxlVauJllOUku5HQxdGCSrNouw9XaAJ2ms0G26rTPMjCuo8B1xwQegGIxyPYrwNFWCbgZsiwk R9I5GfQHG9EePwXQ7oV1V3lhqri9TU72tHv3edfKIBeHhQAVgUAPKTVKYMSCU2w1WZl0jokhUSnw YnD+nACGKJAzpbqJxTgHhjTjrLUh77eYyH6VY20Ag1/8ngrm0RWlqCUxyUtFXs9JFljvlMqW84Iv 1R4YUd9lEZKa2Il+lnYVHIneOOXeNB/n52EKE1qH436oE1vKGXscewxUf0BLVNCDPu0fRFvlyz99 3WxHr+xfthZ5SUqlJb7vrSEDWCswjPFKzroLdB+qeZHYpqwiyUnpTshVcWhDzjWSM8YceyooXZao nrcUf4qzOoES0zKT7I6UMqjhaFUrV9GGfN/On6IdPo6LlP8XA3vC/t1eXjF6QeD2AdaXDAE0puMC 1qNI9FoWyN0nzQIAnxWum7eCS1aebTJLMeWJkp1TqQJDACy8rtu0T5i4tDxwxABpBWuD7/6UJGHn 44XYWiAJhrK77wiUs/07yHGCDGGkworLmBO3UvLU4FeZKZlImBeVcW6XmbGYYqyT7jyeitZqQBUc VUnXUWsegFMMz4JtcPz89WgfP4Gtzxdi65X34+l727909v3nRAvlyiFZfTX0NppSNYeccahanpIM e8oX/cTGd2mCzxO3dYVlNwG7IiS5CZQP2Xgazgf9Ni3k+AlqcBnUJqCebEd79GcG9K/xNFtLL7+d ZPjfcvazHzIpk069uE9dBCiv8uGgIAmm2Ox5mwHON0qduKcUgQk7Td5dDqRpQS7qWmUTir0MqKIW aWVCOGBchU0UMcUwGuat9XgK1F/ozuHCoBYExrb1C1iDMSXBM2qyFcBFis/Dmbc++WKtZbcplTSf VcF3axodsKkdp0WN5AW4XtEBmjJPAa5PMbmvzGKH55DzaID8tpYGdRmwU/dh7f47Zj84OgG3wb0j GFK2X2m5rfTJ4wS4H91JwtrmQ8181AKsD4CBjEFmso/bYetHxO8Mk1its/vJ1lNM/e4ryW/hBdOz 4LYB99MTcK1WjXC8OzDpySMlS5Xwcvw7A0zldIlxwD8y9kjpMcC+BwdZmntcV5y3xOAKPiaWZOma q5JR7C4FaqHnzy7qnAKHV9jePkBGuT7T8vUagEsxtKFZvXuHsqecxrSSsIfEGqaPPrJc3eW8zYDu b8aUKj0i/zthaWlQiwI799ybxNxPXFopeuuzE4BaQXKbsv2c/R8nNZMuXf5Xnuc0w6wy6Q8BdXeU c78aQMr/9pXY7p6Zp5b9c9BC78UvPf8dSwub99+mmrmXAa6QGdVDSyu9L932LJUcys4FRjnfOpJ7 PLhWQMv+teXL7GlVdot5rgSk3b+khfdPgXlo5yV3nX87XubvY694980Flp7PgLkRQNfxF81l2opf 1//HuLZ249f9hbfb7Xa73W43sf1PgAEAToyz1pOXdYIAAAAASUVORK5CYII="
          width={54}
          height={51}
          opacity={0.2}
          overflow="visible"
          transform="translate(1172.271 685.242) scale(.9804)"
        />
        <linearGradient
          id="bv"
          x1={2358.385}
          x2={2371.25}
          y1={-420.684}
          y2={-412.549}
          gradientTransform="matrix(-1 0 0 1 3563.187 1126.798)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.839}
            style={{
              stopColor: "#fff",
              stopOpacity: 0,
            }}
          />
        </linearGradient>
        <path fill="url(#bv)" d="m1190.8 712 2.6 4.3 13-8.3-2.6-4.2z" />
        <linearGradient
          id="bw"
          x1={905.486}
          x2={948.402}
          y1={314.215}
          y2={337.184}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#262626",
            }}
          />
          <stop
            offset={0.277}
            style={{
              stopColor: "#2f2f2f",
            }}
          />
          <stop
            offset={0.728}
            style={{
              stopColor: "#474747",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#595959",
            }}
          />
        </linearGradient>
        <path fill="url(#bw)" d="M906.3 834.2v53l43.1 26v-50.1z" />
        <linearGradient
          id="bx"
          x1={955.859}
          x2={991.555}
          y1={343.303}
          y2={307.607}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.213}
            style={{
              stopColor: "#969696",
            }}
          />
          <stop
            offset={0.446}
            style={{
              stopColor: "#717171",
            }}
          />
          <stop
            offset={0.662}
            style={{
              stopColor: "#565656",
            }}
          />
          <stop
            offset={0.854}
            style={{
              stopColor: "#464646",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#404040",
            }}
          />
        </linearGradient>
        <path fill="url(#bx)" d="M949.4 863.1v50.1l47.2-26v-53z" />
        <linearGradient
          id="by"
          x1={938.289}
          x2={966.46}
          y1={344.879}
          y2={388.979}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.219}
            style={{
              stopColor: "#a2a2a2",
            }}
          />
          <stop
            offset={0.561}
            style={{
              stopColor: "#7b7b7b",
            }}
          />
          <stop
            offset={0.833}
            style={{
              stopColor: "#636363",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#5a5a5a",
            }}
          />
        </linearGradient>
        <path fill="url(#by)" d="m906.7 834.2 42.4 28.9 47.4-28.8-43.9-27.9z" />
        <g fill="#FFF" opacity={0.12}>
          <path d="M917.3 841.9v39.2l32.1 19.2v-37.1zM983.1 841.9v39.2l-33.7 19.2v-37.1z" />
          <path d="m917.2 841.9 32 21.3 34.8-21.3-32.7-20.6z" />
        </g>
        <linearGradient
          id="bz"
          x1={968.676}
          x2={978.181}
          y1={355.499}
          y2={345.994}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#bz)" d="M950 863.1v1l46.9-28.9.1-.9z" />
        <linearGradient
          id="bA"
          x1={2789.746}
          x2={2797.305}
          y1={354.521}
          y2={346.961}
          gradientTransform="rotate(-180 1861.027 599.993)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#bA)" d="M950 863.1v1l-43-28.9v-1z" />
        <linearGradient
          id="bB"
          x1={937.212}
          x2={962.217}
          y1={324.067}
          y2={299.062}
          gradientTransform="matrix(1 .00012 .00012 -1 -.015 1199.769)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.296}
            style={{
              stopColor: "#d3d3d3",
            }}
          />
          <stop
            offset={0.604}
            style={{
              stopColor: "#ababab",
            }}
          />
          <stop
            offset={0.85}
            style={{
              stopColor: "#939393",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#8a8a8a",
            }}
          />
        </linearGradient>
        <path fill="url(#bB)" d="m950.2 863.6-.8.1v49.5l.8-.4z" />
        <path
          fill="none"
          stroke="#B3B3B3"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeMiterlimit={10}
          strokeWidth={3.221}
          d="m996.9 834.5 85.3-53.6"
        />
        <image
          xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAAyCAYAAAAX1CjLAAAACXBIWXMAAAsSAAALEgHS3X78AAAA GXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAACsZJREFUeNrsmktvHMcVhW9Vd8+L IkVKsiMrsbIzbCQGvIyBLIQgykJLrfMX8wsCBEYcIBsDseWFEsGQFVkC9GJIkRpyZrq7Kt+9t4d6 WOJwRMrwgjNo9fSruk6dc8+9VZTI6ef0c/p5l5/wU7wkL/eeHH7OwPLJtP3WIMNPAIZz19n96ggt /IXtfj4JgOHdAfoz586JfP59kNURxz0//WxbpG2e3z9ay1J11+R/Ijfey/Jg7XUglwIXThiUs3Md duqdINNWJD3hXC2yf5Hf/QAokdmYB9sg1TBLOeCp/4oMUpai4PdGlsC5r2OWRys0+Tde8e+lwYUT BfR57ezEPZjZAsSHQSYA2RgH6/SM6xkWU+V9DHQ+bIpUE5EdQM64p7+VZSUA+IIDvLECg18sDS4c HxSSu9bnzAyGYGd/R2RvI8hZ5JYT586wV+klkZUhALm3aRUoHY8iTze7FgFW7GUZNFl2+R12ngP8 9vzS4Mrjs1QGSY+cofE6gFZhDTDa+f5ulEAf6sDxCIDs230FTJzxuIIrzmTJU1ij+R5bM0rSn2YZ ryTZIdZWd0U+VfBXRB7IHByIDwdXvj2oayK/Wwmy8jDIUwUEwALJTaooowiYWZQ8BJjey7VBG23f cE1PqQw19gb0sekliW2WqUqT602VZEh87Ycku+uA234duOMz9iNQ1wAVzrv0tnowtIcEAVTGCBMA qAHEcQJck6NUBSwBrJmpBIGqDAIoAaSg+dxCYcM2TGAPEom16YxDYmw/Ae6ZyC8wl/JDXl8H79Lh rIUlgTmo84C6ezfIDrIbACKXdH4QZVhEmU6UpYKRLyQBMgd6yfnQRpOiRHEWlR1YyjHRRUCxpdAi R6yUrc6tTAEV95LsF0lWAD4kNXwzwi3/SZduHRpr5VuD2iZmRmdhhTiKuaAlfreA0eEGjH4VYEyl AQzRZZk0uBhsbTXBQkwthy3ekgz4lCuQLBU39Nk3GIka0QxVVMRkT1lbPZ4UX5YgRhGIqSdPnCkF tTaNstexkxsVFQBL2sy6ATCWyE7fEf1YZRa9uZhVToABlH6hD2btlL26AYTMsoySmgtOyTt3GJRP fgDsZhdrb5bjEc0DS1f3mz70mBpMnCkFFWEnaKcBRITAjrZZoT5AcdwoSOtxcdAHOJE2qVEgQ2m4 Ams4SbLO+hax/aBxWCaZMaAtptI+Y0ABW/6+q2RuLc/YyxIk96ilq/upUUw0pgqVIPJKDirnCuMo pQiVZeBWfJ9hU1nT+yzGTIMOKkctTfR6bWBpxJhsib22UEOJClFm/LsG0GbDjFTk/YVyPAJj1z35 thOcr69kRBlook0aAHRWO0w7CipGzUQVDPboqIKqjDVjVJlVVNp/NQ6kF4ivHGofx5ydSSwe75cC s6lx0AYXZBykVJyVJ31ndXlgL8WW1n1735OALwRZV1vPmnyfG0VVwRYGUQIktQqk5zpRcMpedknm ELvGg5mFx1xzAErNI3BPDO6eCqBi62P5maQuMzUe+kKdmdaOm8eILS1m52xVmpAVdK3A3MqD2bkb hsox5MqBhXktUbn9i+evbKYRzdI9pkpnT+MPaeesBhM9idudPEeybjpitYi2suVw1hYAo2CdbjFK FLXrKgucqQZQQPspekJOCiyrgXTgkF80hhykxpcNgHq4asGAAcpcv7UYVqascLSTofO6zmhUtlN7 ZJlPXBResqGxfhlZ4IqrU+TAVgKy19PRZasLy2PJnK/0LXT7XHSgCjOB3Fl+nnu+xZzHkMrR8puW WF4zW2qY2njAfZk1TK3UisN8PGA66d3TiSHKmiEBBdWvXFKaaIM+H7Vj0SSp8WFySr5Jd+y6cvlY as65q9E9l2nlobJM7LUaiZRYavcz7muw92dukNZEWXXZ47i1YqNZn4DtrZtLUykAVAMZQIUmXDra hiDzDBWDG4TGSphXbR0jObgbWnnflU7WIMdROdIUwPUCZ2wpp/SFhTJYEakwNtzGf6t8XCkyDRl3 NqnB2vPKfN6svTfN61E5SLwmKxVe8IteB2oC7oDwm66CUyWgx40dB3XIoLGXzFb0uZLfvR4yRHpb qpqZZQKRLw5NzosZa18JWJ0/zYM4dOqytNIB9PzkiHNI1lkzi7kRyJwpzke1uZlYylWrM+tviavW WEuNN9owUgNtg3lbnyL4NkG/9R2XduUwZywPLRVHo5fLsPCsKwF1usGWgifVwiLE672sLCggmcvP nc9rwNbAOWNUG5GaIigNNaVX4+VVoyC5mcojVL5/RpwVT5EiMtwC4HiQF01PDmes7C7rokvYQt/0 cQygVuWRXG65aA1Ugt6oHbeR70BZkRss5vRek6V4fWi1YQu4Ysa1mpiqDWDOLlepWu5iGjPBRJiq xL0s50g/xT8WynAxMF0B+y30lw+wd5ja7TtLBaBSoZ3G5NXJdOpBN1LsHM/Ko2S2HrrC1/wwuhRD akyOBeDarKAaqxdVktngMB+rWynHVPWwFGFrja5+s57lyX5eJMPFwO6z/YagHdJRXUUi95ocijZZ x1PderKxiePcGT3ONK9pnotdCIbQgRWPIYspjS01kVgf/FZQfRgrpknGFIixTnKW2L73AfFFbI3v HmmWXC5emf1MbOkso+0Rs9lpqxaP/XYmEWLjuFI3HbHOF5a/YvQEbKWEjnDyAjdboQsIwFS5Y1tc egoqDFvZB1SDvZ9H9pcvZbn5NbF180hsHQHY/Sw3rop8iu2rHEe6orTCKO4yN0vBclI5NbJ8uqGs 0HEtt/QbklfzZfRrFnOty1d6yA451zbBaQ2wyk+ZUlCzXdwQ0xiuZPnqKyT4HxoaH4BatKYR3lDd v3Dtl0H+9BmhTmXfXGSSOWN+RL0YKamme4XVi2X0Sj8wR0spWu3nRa0DC4WvbxhwlTHGUEBxw+8p UusPEKBa/I6uVHGeOFoH9Bnc784dkdu3YWucl1kwjYvRKmvv+cJlwShua8mDpFpGvZ86ByuQUusx E1p1NpyOrdCNXJV0q2Y0zL6nx7VMJjyTVYIIkOd7AMxnWkvK53tJHv86y7949yugjloEH57H5hgf /FXkyh+z1w67SXYHkYI4WQ3ZTyYw++rSmtaNtprWhuf90FKs8nJoQuItKI+mXWUyYID2aiwd5yth dh2W7l3O8h0xNTmQ35EluLCk+hFrfwfciHxycRXdT5DQKMmoD1QAzmBupm436ZKzGkqJ01W+haKW yHGpeUtZqhoZBjeL/bVkcXWGZLzRT3LvIizxvsnNF2NqKVAL782v3nPpUpCrV3V+FuTxvi+u7DP5 3Bjgh1oYcy6RRJvgy9gHutBcRNXSp2IvYGRTJ9mPvWpZOcdAIe87FyiVMIktzGh8j1c/lbcBdOQF 09eCKwD3sa7zMaK6JDbg+lQXWnRpG0DndEr/Qjg8KcQT+0NYZz8jAvrBS6Q7H7wA6O7czo8FapmV 4Ffu/Ujk4h+YcI5EPvlBl8QANaRjMFbTwVEOL02ZmIBbAat/LmI+baWRVRFfypsAHfePd0d+9sfg PmZjNntxM9g635X3RVb3dUYQZHNT53F+2zrzuH6fioXK5UvQPmLKcRYwd5Hk7i05aUBv1UZ+43Mw +NGqL/VtbwebN3WTZDl7VmTQVeOG4yUwJw7ouH/4O24b+V3/14WT+Bv0z/c/mJx+Tj+nn9PPsp// CzAAqYTVOPWO2yYAAAAASUVORK5CYII="
          width={54}
          height={50}
          opacity={0.2}
          overflow="visible"
          transform="matrix(.98 0 0 .98 1024.282 776.242)"
        />
        <linearGradient
          id="bC"
          x1={2506.385}
          x2={2519.25}
          y1={-330.325}
          y2={-322.19}
          gradientTransform="matrix(-1 0 0 1 3563.187 1126.798)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#fff",
            }}
          />
          <stop
            offset={0.839}
            style={{
              stopColor: "#fff",
              stopOpacity: 0,
            }}
          />
        </linearGradient>
        <path fill="url(#bC)" d="m1042.8 802.4 2.6 4.2 13-8.2-2.6-4.2z" />
      </g>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-800ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-1000ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-1200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-1400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-1600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-1800ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-2000ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-2200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-2400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-2600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-2800ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-3000ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-3200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-3400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-3600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-3800ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-4000ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-4200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-4400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-4600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-4800ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-5000ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-5200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-5400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-5600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-5800ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-6000ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-6200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-6400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-6600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-6800ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-7000ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-7200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-7400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-7600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-7800ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-8000ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-8200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-8400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-8600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-8800ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-9000ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-9200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-9400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-9600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-9800ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-10000ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.2"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-10200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-10400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-10600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-10800ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-11000ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-11200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-11400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-11600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-11800ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-12000ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-12200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-12400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-12600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-12800ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-13000ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-13200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-13400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-13600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-13800ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-14000ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-14200ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-14400ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <circle
        r={1}
        opacity="0.4gf"
        style={{
          fill: "#fff",
        }}
      >
        <animateMotion begin="-14600ms" dur="10s" repeatCount="indefinite">
          <mpath xlinkHref="#bD" />
        </animateMotion>
      </circle>
      <g opacity={0.7}>
        <g opacity={0.25}>
          <linearGradient
            id="bE"
            x1={1652.497}
            x2={1679.39}
            y1={450.19}
            y2={464.583}
            gradientTransform="matrix(1 0 0 -1 0 1200)"
            gradientUnits="userSpaceOnUse"
          >
            <stop
              offset={0}
              style={{
                stopColor: "#494949",
              }}
            />
            <stop
              offset={0.226}
              style={{
                stopColor: "#525252",
              }}
            />
            <stop
              offset={0.593}
              style={{
                stopColor: "#6a6a6a",
              }}
            />
            <stop
              offset={1}
              style={{
                stopColor: "#8c8c8c",
              }}
            />
          </linearGradient>
          <path fill="url(#bE)" d="M1653 717.7v33.1l27 16.2v-31.3z" />
          <linearGradient
            id="bF"
            x1={1683.934}
            x2={1705.967}
            y1={468.25}
            y2={446.217}
            gradientTransform="matrix(1 0 0 -1 0 1200)"
            gradientUnits="userSpaceOnUse"
          >
            <stop
              offset={0}
              style={{
                stopColor: "#bfbfbf",
              }}
            />
            <stop
              offset={0.219}
              style={{
                stopColor: "#a2a2a2",
              }}
            />
            <stop
              offset={0.561}
              style={{
                stopColor: "#7b7b7b",
              }}
            />
            <stop
              offset={0.833}
              style={{
                stopColor: "#636363",
              }}
            />
            <stop
              offset={1}
              style={{
                stopColor: "#5a5a5a",
              }}
            />
          </linearGradient>
          <path fill="url(#bF)" d="M1680 735.7V767l29-16.2v-33.1z" />
          <linearGradient
            id="bG"
            x1={1673.077}
            x2={1690.518}
            y1={469.563}
            y2={496.866}
            gradientTransform="matrix(1 0 0 -1 0 1200)"
            gradientUnits="userSpaceOnUse"
          >
            <stop
              offset={0}
              style={{
                stopColor: "#a6a6a6",
              }}
            />
            <stop
              offset={0.034}
              style={{
                stopColor: "#a2a2a2",
              }}
            />
            <stop
              offset={0.457}
              style={{
                stopColor: "#7b7b7b",
              }}
            />
            <stop
              offset={0.793}
              style={{
                stopColor: "#636363",
              }}
            />
            <stop
              offset={1}
              style={{
                stopColor: "#5a5a5a",
              }}
            />
          </linearGradient>
          <path fill="url(#bG)" d="m1653.6 717.7 26.5 18 28.8-18-26.9-17.4z" />
        </g>
        <linearGradient
          id="bH"
          x1={1709.906}
          x2={1652.837}
          y1={447.788}
          y2={483.866}
          gradientTransform="matrix(1 0 0 -1 0 1200)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#3a3c4d",
            }}
          />
          <stop
            offset={0.42}
            style={{
              stopColor: "#3d3f4e",
            }}
          />
          <stop
            offset={0.706}
            style={{
              stopColor: "#464752",
            }}
          />
          <stop
            offset={0.951}
            style={{
              stopColor: "#555557",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#595959",
            }}
          />
        </linearGradient>
        <path
          fill="url(#bH)"
          d="m1675.6 703.4-.3-.6 1-.6.3.6-1 .6zm2.7-1.7-.3-.6-1 .6.3.6 1-.6zm1.7-1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.2-.3-.6-1 .6.3.6 1-.6zm1.8-1-.3-.6-1 .6.3.6 1-.6zm1.7-1.1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.3-.3-.6-1 .6.3.6 1-.6zm1.8-1.1-.3-.6-1 .6.3.6 1-.6zm1.7-1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.3-.3-.6-1 .6.3.6 1-.6zm1.7-1.1-.3-.6-1 .6.3.6 1-.6zm1.8-1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.2-.3-.6-1 .6.3.6 1-.6zm1.7-1-.3-.6-1 .6.3.6 1-.6zm1.8-1.1-.3-.6-1 .6.3.6 1-.6zm-5.9 10.1h-.9v1.3h.9v-1.3zm0-1.7h-.9v.9h.9v-.9zm0-2.2h-.9v.9h.9v-.9zm0 10.3h-.9v.9h.9v-.9zm0-2.2h-.9v1.3h.9v-1.3zm0-2.1h-.9v.9h.9v-.9zm0 10.3h-.9v.9h.9v-.9zm0-2.2h-.9v1.3h.9v-1.3zm0-1.7h-.9v.9h.9v-.9zm0 9.9h-.9v1.3h.9v-1.3zm0-2.2h-.9v1.3h.9v-1.3zm0-2.1h-.9v1.3h.9v-1.3zm0 10.3h-.9v1.3h.9v-1.3zm0-1.7h-.9v.9h.9v-.9zm0-2.2h-.9v1.3h.9v-1.3zm4.6 10.6-.4.6.9.6.4-.6-.9-.6zm-1.6-1.1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5.1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5-.4.6.9.6.4-.6-.9-.6zm-1.7-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm11.5.6.3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5 .3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5.1.3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm6.4-10h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10.1h.7v-1.1h-.7v1.1zm0 2.1h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10.1h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10.1h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm-5.1-10.6.4-.6-.9-.6-.4.6.9.6zm1.7 1.1.4-.6-.9-.6-.4.6.9.6zm1.6 1 .4-.6-.9-.6-.4.6.9.6zm-8.2-5.3.4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm1.7 1 .4-.6-.9-.6-.4.6.9.6zm-8.3-5.3.4-.6-.9-.6-.4.6.9.6zm1.7 1 .4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm-8.2-5.3.4-.6-.9-.6-.4.6.9.6zm1.7 1 .4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm-8.2-5.4.4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm1.7 1.1.4-.6-.9-.6-.4.6.9.6zm-35 12.8.3.6.5-.3-.3-.6-.5.3zm-.9.6V717.8h.9v-.6h-.2l.2-.1-.4-.6-.5.4c0-.1 0-.1 0 0zm0 1.7h.7v-.6h-.7v.6zm-.1 31.9h.7v-.6h-.7v.6zm.1.7s0 .2.1.2c0 0 .1.2.2.2.1.1.3.2.3.2l.4-.6s-.1 0-.1-.1c0 0-.1 0-.1-.1v-.4h-.6l-.2.6zm1.4 1.3.4-.6-.5-.3-.4.6.5.3zm25.4 15.7.4-.6-.6-.3-.4.6.6.3zm1 .6s.1 0 0 0c.1-.1.2-.1.3-.1.1-.1.3-.2.3-.2l-.3-.6s-.1 0-.1.1h-.1l-.2-.1-.4.6.5.3zm1.6-.9-.3-.6-.6.3.3.6.6-.3zm27.5-15.7-.3-.6-.6.3.3.6.6-.3zm.9-.6c0-.1 0-.1 0 0v-.3c0-.2-.1-.3-.1-.3h-.8v.2h.2l-.2.1.4.6.5-.3c.1 0 0 0 0 0zm.1-1.8h-.7v.6h.7v-.6zm0-31.5h-.7v.6h.7v-.6zm-.1-.9s0-.1 0 0l-.2-.2c-.1-.1-.3-.2-.3-.2l-.4.5s.1 0 .1.1h.1v.2h-.2v.1l.8.1.1-.6c.1 0 .1 0 0 0zm-1.4-1.1-.4.6.5.3.4-.6-.5-.3zm-25.6-16.8-.4.6.6.4.4-.6-.6-.4zm-1.1-.6s-.1 0 0 0c-.1.1-.2.1-.3.2-.2.1-.3.2-.3.2l.3.6s.1 0 .1-.1h.1l.2.2.4-.6-.5-.5c0-.1 0-.1 0 0zm-1.7 1 .3.6.6-.4-.3-.6-.6.4z"
        />
        <g opacity={0.25}>
          <linearGradient
            id="bI"
            x1={1495.502}
            x2={1522.384}
            y1={354.178}
            y2={368.592}
            gradientTransform="matrix(1 0 0 -1 0 1200)"
            gradientUnits="userSpaceOnUse"
          >
            <stop
              offset={0}
              style={{
                stopColor: "#494949",
              }}
            />
            <stop
              offset={0.226}
              style={{
                stopColor: "#525252",
              }}
            />
            <stop
              offset={0.593}
              style={{
                stopColor: "#6a6a6a",
              }}
            />
            <stop
              offset={1}
              style={{
                stopColor: "#8c8c8c",
              }}
            />
          </linearGradient>
          <path fill="url(#bI)" d="M1496 813.7v33.1l27 16.2v-31.3z" />
          <linearGradient
            id="bJ"
            x1={1526.934}
            x2={1548.967}
            y1={372.25}
            y2={350.217}
            gradientTransform="matrix(1 0 0 -1 0 1200)"
            gradientUnits="userSpaceOnUse"
          >
            <stop
              offset={0}
              style={{
                stopColor: "#bfbfbf",
              }}
            />
            <stop
              offset={0.219}
              style={{
                stopColor: "#a2a2a2",
              }}
            />
            <stop
              offset={0.561}
              style={{
                stopColor: "#7b7b7b",
              }}
            />
            <stop
              offset={0.833}
              style={{
                stopColor: "#636363",
              }}
            />
            <stop
              offset={1}
              style={{
                stopColor: "#5a5a5a",
              }}
            />
          </linearGradient>
          <path fill="url(#bJ)" d="M1523 831.7V863l29-16.2v-33.1z" />
          <linearGradient
            id="bK"
            x1={1516.077}
            x2={1533.518}
            y1={373.563}
            y2={400.866}
            gradientTransform="matrix(1 0 0 -1 0 1200)"
            gradientUnits="userSpaceOnUse"
          >
            <stop
              offset={0}
              style={{
                stopColor: "#a6a6a6",
              }}
            />
            <stop
              offset={0.034}
              style={{
                stopColor: "#a2a2a2",
              }}
            />
            <stop
              offset={0.457}
              style={{
                stopColor: "#7b7b7b",
              }}
            />
            <stop
              offset={0.793}
              style={{
                stopColor: "#636363",
              }}
            />
            <stop
              offset={1}
              style={{
                stopColor: "#5a5a5a",
              }}
            />
          </linearGradient>
          <path fill="url(#bK)" d="m1496.6 813.7 26.5 18 28.8-18-26.9-17.4z" />
        </g>
        <linearGradient
          id="bL"
          x1={1552.906}
          x2={1495.837}
          y1={351.788}
          y2={387.866}
          gradientTransform="matrix(1 0 0 -1 0 1200)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#3a3c4d",
            }}
          />
          <stop
            offset={0.42}
            style={{
              stopColor: "#3d3f4e",
            }}
          />
          <stop
            offset={0.706}
            style={{
              stopColor: "#464752",
            }}
          />
          <stop
            offset={0.951}
            style={{
              stopColor: "#555557",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#595959",
            }}
          />
        </linearGradient>
        <path
          fill="url(#bL)"
          d="m1518.6 799.4-.3-.6 1-.6.3.6-1 .6zm2.7-1.7-.3-.6-1 .6.3.6 1-.6zm1.7-1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.2-.3-.6-1 .6.3.6 1-.6zm1.8-1-.3-.6-1 .6.3.6 1-.6zm1.7-1.1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.3-.3-.6-1 .6.3.6 1-.6zm1.8-1.1-.3-.6-1 .6.3.6 1-.6zm1.7-1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.3-.3-.6-1 .6.3.6 1-.6zm1.7-1.1-.3-.6-1 .6.3.6 1-.6zm1.8-1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.2-.3-.6-1 .6.3.6 1-.6zm1.7-1-.3-.6-1 .6.3.6 1-.6zm1.8-1.1-.3-.6-1 .6.3.6 1-.6zm-5.9 10.1h-.9v1.3h.9v-1.3zm0-1.7h-.9v.9h.9v-.9zm0-2.2h-.9v.9h.9v-.9zm0 10.3h-.9v.9h.9v-.9zm0-2.2h-.9v1.3h.9v-1.3zm0-2.1h-.9v.9h.9v-.9zm0 10.3h-.9v.9h.9v-.9zm0-2.2h-.9v1.3h.9v-1.3zm0-1.7h-.9v.9h.9v-.9zm0 9.9h-.9v1.3h.9v-1.3zm0-2.2h-.9v1.3h.9v-1.3zm0-2.1h-.9v1.3h.9v-1.3zm0 10.3h-.9v1.3h.9v-1.3zm0-1.7h-.9v.9h.9v-.9zm0-2.2h-.9v1.3h.9v-1.3zm4.6 10.6-.4.6.9.6.4-.6-.9-.6zm-1.6-1.1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5.1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5-.4.6.9.6.4-.6-.9-.6zm-1.7-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm11.5.6.3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5 .3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5.1.3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm6.4-10h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10.1h.7v-1.1h-.7v1.1zm0 2.1h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10.1h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10.1h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm-5.1-10.6.4-.6-.9-.6-.4.6.9.6zm1.7 1.1.4-.6-.9-.6-.4.6.9.6zm1.6 1 .4-.6-.9-.6-.4.6.9.6zm-8.2-5.3.4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm1.7 1 .4-.6-.9-.6-.4.6.9.6zm-8.3-5.3.4-.6-.9-.6-.4.6.9.6zm1.7 1 .4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm-8.2-5.3.4-.6-.9-.6-.4.6.9.6zm1.7 1 .4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm-8.2-5.4.4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm1.7 1.1.4-.6-.9-.6-.4.6.9.6zm-35 12.8.3.6.5-.3-.3-.6-.5.3zm-.9.6V813.8h.9v-.6h-.2l.2-.1-.4-.6-.5.4c0-.1 0-.1 0 0zm0 1.7h.7v-.6h-.7v.6zm-.1 31.9h.7v-.6h-.7v.6zm.1.7s0 .2.1.2c0 0 .1.2.2.2.1.1.3.2.3.2l.4-.6s-.1 0-.1-.1c0 0-.1 0-.1-.1v-.4h-.6l-.2.6zm1.4 1.3.4-.6-.5-.3-.4.6.5.3zm25.4 15.7.4-.6-.6-.3-.4.6.6.3zm1 .6s.1 0 0 0c.1-.1.2-.1.3-.1.1-.1.3-.2.3-.2l-.3-.6s-.1 0-.1.1h-.1l-.2-.1-.4.6.5.3zm1.6-.9-.3-.6-.6.3.3.6.6-.3zm27.5-15.7-.3-.6-.6.3.3.6.6-.3zm.9-.6c0-.1 0-.1 0 0v-.3c0-.2-.1-.3-.1-.3h-.8v.2h.2l-.2.1.4.6.5-.3c.1 0 0 0 0 0zm.1-1.8h-.7v.6h.7v-.6zm0-31.5h-.7v.6h.7v-.6zm-.1-.9s0-.1 0 0l-.2-.2c-.1-.1-.3-.2-.3-.2l-.4.5s.1 0 .1.1h.1v.2h-.2v.1l.8.1.1-.6c.1 0 .1 0 0 0zm-1.4-1.1-.4.6.5.3.4-.6-.5-.3zm-25.6-16.8-.4.6.6.4.4-.6-.6-.4zm-1.1-.6s-.1 0 0 0c-.1.1-.2.1-.3.2-.2.1-.3.2-.3.2l.3.6s.1 0 .1-.1h.1l.2.2.4-.6-.5-.5c0-.1 0-.1 0 0zm-1.7 1 .3.6.6-.4-.3-.6-.6.4z"
        />
        <linearGradient
          id="bM"
          x1={1553.097}
          x2={1662.411}
          y1={420.805}
          y2={420.805}
          gradientTransform="matrix(1 0 0 -1 0 1200)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.112}
            style={{
              stopColor: "#aaaaae",
            }}
          />
          <stop
            offset={0.406}
            style={{
              stopColor: "#767884",
            }}
          />
          <stop
            offset={0.663}
            style={{
              stopColor: "#515366",
            }}
          />
          <stop
            offset={0.87}
            style={{
              stopColor: "#3a3d54",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#32354d",
            }}
          />
        </linearGradient>
        <path
          fill="url(#bM)"
          d="m1591.9 790.2-.8-1.2 2.8-1.8.8 1.2-2.8 1.8zm-1.4.8-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm25.3-16-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-21.1 13.4-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.1 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm67.1-43-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm25.3-16-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm21.4-13.3-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-34.1 21.4-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.1 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-46.2 29.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.5 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8z"
        />
        <g opacity={0.15}>
          <linearGradient
            id="bN"
            x1={1336.497}
            x2={1363.39}
            y1={257.19}
            y2={271.583}
            gradientTransform="matrix(1 0 0 -1 0 1200)"
            gradientUnits="userSpaceOnUse"
          >
            <stop
              offset={0}
              style={{
                stopColor: "#494949",
              }}
            />
            <stop
              offset={0.226}
              style={{
                stopColor: "#525252",
              }}
            />
            <stop
              offset={0.593}
              style={{
                stopColor: "#6a6a6a",
              }}
            />
            <stop
              offset={1}
              style={{
                stopColor: "#8c8c8c",
              }}
            />
          </linearGradient>
          <path fill="url(#bN)" d="M1337 910.7v33.1l27 16.2v-31.3z" />
          <linearGradient
            id="bO"
            x1={1367.934}
            x2={1389.967}
            y1={275.25}
            y2={253.217}
            gradientTransform="matrix(1 0 0 -1 0 1200)"
            gradientUnits="userSpaceOnUse"
          >
            <stop
              offset={0}
              style={{
                stopColor: "#bfbfbf",
              }}
            />
            <stop
              offset={0.219}
              style={{
                stopColor: "#a2a2a2",
              }}
            />
            <stop
              offset={0.561}
              style={{
                stopColor: "#7b7b7b",
              }}
            />
            <stop
              offset={0.833}
              style={{
                stopColor: "#636363",
              }}
            />
            <stop
              offset={1}
              style={{
                stopColor: "#5a5a5a",
              }}
            />
          </linearGradient>
          <path fill="url(#bO)" d="M1364 928.7V960l29-16.2v-33.1z" />
          <linearGradient
            id="bP"
            x1={1357.077}
            x2={1374.518}
            y1={276.563}
            y2={303.866}
            gradientTransform="matrix(1 0 0 -1 0 1200)"
            gradientUnits="userSpaceOnUse"
          >
            <stop
              offset={0}
              style={{
                stopColor: "#a6a6a6",
              }}
            />
            <stop
              offset={0.034}
              style={{
                stopColor: "#a2a2a2",
              }}
            />
            <stop
              offset={0.457}
              style={{
                stopColor: "#7b7b7b",
              }}
            />
            <stop
              offset={0.793}
              style={{
                stopColor: "#636363",
              }}
            />
            <stop
              offset={1}
              style={{
                stopColor: "#5a5a5a",
              }}
            />
          </linearGradient>
          <path fill="url(#bP)" d="m1337.6 910.7 26.5 18 28.8-18-26.9-17.4z" />
        </g>
        <linearGradient
          id="bQ"
          x1={1393.906}
          x2={1336.837}
          y1={254.788}
          y2={290.866}
          gradientTransform="matrix(1 0 0 -1 0 1200)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#3a3c4d",
            }}
          />
          <stop
            offset={0.42}
            style={{
              stopColor: "#3d3f4e",
            }}
          />
          <stop
            offset={0.706}
            style={{
              stopColor: "#464752",
            }}
          />
          <stop
            offset={0.951}
            style={{
              stopColor: "#555557",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#595959",
            }}
          />
        </linearGradient>
        <path
          fill="url(#bQ)"
          d="m1359.6 896.4-.3-.6 1-.6.3.6-1 .6zm2.7-1.7-.3-.6-1 .6.3.6 1-.6zm1.7-1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.2-.3-.6-1 .6.3.6 1-.6zm1.8-1-.3-.6-1 .6.3.6 1-.6zm1.7-1.1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.3-.3-.6-1 .6.3.6 1-.6zm1.8-1.1-.3-.6-1 .6.3.6 1-.6zm1.7-1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.3-.3-.6-1 .6.3.6 1-.6zm1.7-1.1-.3-.6-1 .6.3.6 1-.6zm1.8-1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.2-.3-.6-1 .6.3.6 1-.6zm1.7-1-.3-.6-1 .6.3.6 1-.6zm1.8-1.1-.3-.6-1 .6.3.6 1-.6zm-5.9 10.1h-.9v1.3h.9v-1.3zm0-1.7h-.9v.9h.9v-.9zm0-2.2h-.9v.9h.9v-.9zm0 10.3h-.9v.9h.9v-.9zm0-2.2h-.9v1.3h.9v-1.3zm0-2.1h-.9v.9h.9v-.9zm0 10.3h-.9v.9h.9v-.9zm0-2.2h-.9v1.3h.9v-1.3zm0-1.7h-.9v.9h.9v-.9zm0 9.9h-.9v1.3h.9v-1.3zm0-2.2h-.9v1.3h.9v-1.3zm0-2.1h-.9v1.3h.9v-1.3zm0 10.3h-.9v1.3h.9v-1.3zm0-1.7h-.9v.9h.9v-.9zm0-2.2h-.9v1.3h.9v-1.3zm4.6 10.6-.4.6.9.6.4-.6-.9-.6zm-1.6-1.1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5.1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5-.4.6.9.6.4-.6-.9-.6zm-1.7-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm11.5.6.3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5 .3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5.1.3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm6.4-10h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10.1h.7v-1.1h-.7v1.1zm0 2.1h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10.1h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10.1h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm-5.1-10.6.4-.6-.9-.6-.4.6.9.6zm1.7 1.1.4-.6-.9-.6-.4.6.9.6zm1.6 1 .4-.6-.9-.6-.4.6.9.6zm-8.2-5.3.4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm1.7 1 .4-.6-.9-.6-.4.6.9.6zm-8.3-5.3.4-.6-.9-.6-.4.6.9.6zm1.7 1 .4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm-8.2-5.3.4-.6-.9-.6-.4.6.9.6zm1.7 1 .4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm-8.2-5.4.4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm1.7 1.1.4-.6-.9-.6-.4.6.9.6zm-35 12.8.3.6.5-.3-.3-.6-.5.3zm-.9.6V910.8h.9v-.6h-.2l.2-.1-.4-.6-.5.4c0-.1 0-.1 0 0zm0 1.7h.7v-.6h-.7v.6zm-.1 31.9h.7v-.6h-.7v.6zm.1.7s0 .2.1.2c0 0 .1.2.2.2.1.1.3.2.3.2l.4-.6s-.1 0-.1-.1c0 0-.1 0-.1-.1v-.4h-.6l-.2.6zm1.4 1.3.4-.6-.5-.3-.4.6.5.3zm25.4 15.7.4-.6-.6-.3-.4.6.6.3zm1 .6s.1 0 0 0c.1-.1.2-.1.3-.1.1-.1.3-.2.3-.2l-.3-.6s-.1 0-.1.1h-.1l-.2-.1-.4.6.5.3zm1.6-.9-.3-.6-.6.3.3.6.6-.3zm27.5-15.7-.3-.6-.6.3.3.6.6-.3zm.9-.6c0-.1 0-.1 0 0v-.3c0-.2-.1-.3-.1-.3h-.8v.2h.2l-.2.1.4.6.5-.3c.1 0 0 0 0 0zm.1-1.8h-.7v.6h.7v-.6zm0-31.5h-.7v.6h.7v-.6zm-.1-.9s0-.1 0 0l-.2-.2c-.1-.1-.3-.2-.3-.2l-.4.5s.1 0 .1.1h.1v.2h-.2v.1l.8.1.1-.6c.1 0 .1 0 0 0zm-1.4-1.1-.4.6.5.3.4-.6-.5-.3zm-25.6-16.8-.4.6.6.4.4-.6-.6-.4zm-1.1-.6s-.1 0 0 0c-.1.1-.2.1-.3.2-.2.1-.3.2-.3.2l.3.6s.1 0 .1-.1h.1l.2.2.4-.6-.5-.5c0-.1 0-.1 0 0zm-1.7 1 .3.6.6-.4-.3-.6-.6.4z"
        />
        <linearGradient
          id="bR"
          x1={1394.097}
          x2={1503.411}
          y1={323.805}
          y2={323.805}
          gradientTransform="matrix(1 0 0 -1 0 1200)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.112}
            style={{
              stopColor: "#aaaaae",
            }}
          />
          <stop
            offset={0.406}
            style={{
              stopColor: "#767884",
            }}
          />
          <stop
            offset={0.663}
            style={{
              stopColor: "#515366",
            }}
          />
          <stop
            offset={0.87}
            style={{
              stopColor: "#3a3d54",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#32354d",
            }}
          />
        </linearGradient>
        <path
          fill="url(#bR)"
          d="m1432.9 887.2-.8-1.2 2.8-1.8.8 1.2-2.8 1.8zm-1.4.8-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm25.3-16-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-21.1 13.4-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.1 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm67.1-43-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm25.3-16-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm21.4-13.3-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-34.1 21.4-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.1 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-46.2 29.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.5 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8z"
        />
        <g opacity={0.25}>
          <linearGradient
            id="bS"
            x1={1177.757}
            x2={1204.431}
            y1={159.506}
            y2={173.782}
            gradientTransform="matrix(1 0 0 -1 0 1200)"
            gradientUnits="userSpaceOnUse"
          >
            <stop
              offset={0}
              style={{
                stopColor: "#494949",
              }}
            />
            <stop
              offset={0.226}
              style={{
                stopColor: "#525252",
              }}
            />
            <stop
              offset={0.593}
              style={{
                stopColor: "#6a6a6a",
              }}
            />
            <stop
              offset={1}
              style={{
                stopColor: "#8c8c8c",
              }}
            />
          </linearGradient>
          <path fill="url(#bS)" d="m1178.5 1008.3-.1 33.4 26.6 16v-31.3z" />
          <linearGradient
            id="bT"
            x1={1208.934}
            x2={1230.967}
            y1={177.522}
            y2={155.488}
            gradientTransform="matrix(1 0 0 -1 0 1200)"
            gradientUnits="userSpaceOnUse"
          >
            <stop
              offset={0}
              style={{
                stopColor: "#bfbfbf",
              }}
            />
            <stop
              offset={0.219}
              style={{
                stopColor: "#a2a2a2",
              }}
            />
            <stop
              offset={0.561}
              style={{
                stopColor: "#7b7b7b",
              }}
            />
            <stop
              offset={0.833}
              style={{
                stopColor: "#636363",
              }}
            />
            <stop
              offset={1}
              style={{
                stopColor: "#5a5a5a",
              }}
            />
          </linearGradient>
          <path fill="url(#bT)" d="M1205 1026.4v31.3l29-16.2v-33.1z" />
          <linearGradient
            id="bU"
            x1={1197.969}
            x2={1215.41}
            y1={178.834}
            y2={206.137}
            gradientTransform="matrix(1 0 0 -1 0 1200)"
            gradientUnits="userSpaceOnUse"
          >
            <stop
              offset={0}
              style={{
                stopColor: "#a6a6a6",
              }}
            />
            <stop
              offset={0.034}
              style={{
                stopColor: "#a2a2a2",
              }}
            />
            <stop
              offset={0.457}
              style={{
                stopColor: "#7b7b7b",
              }}
            />
            <stop
              offset={0.793}
              style={{
                stopColor: "#636363",
              }}
            />
            <stop
              offset={1}
              style={{
                stopColor: "#5a5a5a",
              }}
            />
          </linearGradient>
          <path fill="url(#bU)" d="m1178.5 1008.4 26.5 18 28.8-18-26.9-17.4z" />
        </g>
        <linearGradient
          id="bV"
          x1={1234.419}
          x2={1178.051}
          y1={157.3}
          y2={192.934}
          gradientTransform="matrix(1 0 0 -1 0 1200)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#595959",
            }}
          />
          <stop
            offset={0.049}
            style={{
              stopColor: "#555557",
            }}
          />
          <stop
            offset={0.294}
            style={{
              stopColor: "#464752",
            }}
          />
          <stop
            offset={0.58}
            style={{
              stopColor: "#3d3f4e",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#3a3c4d",
            }}
          />
        </linearGradient>
        <path
          fill="url(#bV)"
          d="m1200.5 994.1-.3-.6 1-.6.3.6-1 .6zm2.7-1.6-.3-.6-1 .6.3.6 1-.6zm1.7-1.1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.3-.3-.6-1 .6.3.6 1-.6zm1.8-1.1-.3-.6-1 .6.3.6 1-.6zm1.7-1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.2-.3-.6-1 .6.3.6 1-.6zm1.7-1-.3-.6-1 .6.3.6 1-.6zm1.8-1.1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.3-.3-.6-1 .6.3.6 1-.6zm1.7-1.1-.3-.6-1 .6.3.6 1-.6zm1.8-1-.3-.6-1 .6.3.6 1-.6zm-8.7 5.2-.3-.6-1 .6.3.6 1-.6zm1.7-1-.3-.6-1 .6.3.6 1-.6zm1.8-1.1-.3-.6-1 .6.3.6 1-.6zm-5.9 10.1h-.9v1.3h.9v-1.3zm0-1.7h-.9v.9h.9v-.9zm0-2.2h-.9v.9h.9v-.9zm0 10.3h-.9v.9h.9v-.9zm0-2.1h-.9v1.3h.9v-1.3zm0-2.2h-.9v.9h.9v-.9zm0 10.3h-.9v.9h.9v-.9zm0-2.1h-.9v1.3h.9v-1.3zm0-1.7h-.9v.9h.9v-.9zm0 9.8h-.9v1.3h.9v-1.3zm0-2.1h-.9v1.3h.9v-1.3zm0-2.2h-.9v1.3h.9v-1.3zm0 10.3h-.9v1.3h.9v-1.3zm0-1.7h-.9v.9h.9v-.9zm0-2.2h-.9v1.3h.9v-1.3zm4.6 10.6-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5-.4.6.9.6.4-.6-.9-.6zm-1.7-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm8.1 5-.4.6.9.6.4-.6-.9-.6zm-1.6-1-.4.6.9.6.4-.6-.9-.6zm-1.7-1-.4.6.9.6.4-.6-.9-.6zm11.6.6.3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5 .3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5.1.3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm8.8-5 .3.6 1-.6-.3-.6-1 .6zm-1.8 1 .3.6 1-.6-.3-.6-1 .6zm-1.7 1 .3.6 1-.6-.3-.6-1 .6zm6.4-10h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2.1h.7v-1.1h-.7v1.1zm0-10.1h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10.1h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10.1h.7v-1.1h-.7v1.1zm0 2.1h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0-10.1h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm0 2h.7v-1.1h-.7v1.1zm-5.1-10.6.4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm1.7 1.1.4-.6-.9-.6-.4.6.9.6zm-8.2-5.4.4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm-8.2-5.4.4-.6-.9-.6-.4.6.9.6zm1.7 1.1.4-.6-.9-.6-.4.6.9.6zm1.6 1 .4-.6-.9-.6-.4.6.9.6zm-8.2-5.3.4-.6-.9-.6-.4.6.9.6zm1.6 1.1.4-.6-.9-.6-.4.6.9.6zm1.7 1 .4-.6-.9-.6-.4.6.9.6zm-8.2-5.3.4-.6-.9-.6-.4.6.9.6zm1.6 1 .4-.6-.9-.6-.4.6.9.6zm1.7 1.1.4-.6-.9-.6-.4.6.9.6zm-35 12.8.3.6.5-.3-.3-.6-.5.3zm-.9.6V1008.5h.9v-.6h-.2l.2-.1-.4-.6-.5.4zm0 1.7h.7v-.6h-.7v.6zm-.1 31.9h.7v-.6h-.7v.6zm.1.7s0 .2.1.2c0 0 .1.2.2.2.1.1.3.2.3.2l.4-.6s-.1 0-.1-.1c0 0-.1 0-.1-.1v-.4h-.6l-.2.6s-.1 0 0 0zm1.4 1.3.4-.6-.5-.3-.4.6.5.3zm25.4 15.7.4-.6-.6-.3-.4.6.6.3zm1 .6c.1-.1.2-.1.3-.2.1-.1.3-.2.3-.2l-.3-.6s-.1 0-.1.1h-.1l-.2-.1-.4.6.5.4c0 .1 0 0 0 0zm1.6-.9-.3-.6-.6.3.3.6.6-.3zm27.5-15.7-.3-.6-.6.3.3.6.6-.3zm.9-.6v-.3c0-.2-.1-.3-.1-.3h-.8v.2h.2l-.2.1.4.6.5-.3c.1 0 0 0 0 0zm.1-1.8h-.7v.6h.7v-.6zm0-31.4h-.7v.6h.7v-.6zm-.1-1s0-.1 0 0l-.2-.2c-.1-.1-.3-.2-.3-.2l-.4.5s.1 0 .1.1h.1v.2h-.2v.1l.8.1.1-.6c.1 0 .1 0 0 0zm-1.4-1-.4.6.5.3.4-.6-.5-.3zm-25.6-16.8-.4.6.6.4.4-.6-.6-.4zm-1.1-.7s-.1 0 0 0c-.1.1-.2.1-.3.2-.2.1-.3.2-.3.2l.3.6s.1 0 .1-.1h.1l.2.2.4-.6-.5-.5zm-1.7 1 .3.6.6-.4-.3-.6-.6.4z"
        />
        <linearGradient
          id="bW"
          x1={1234.615}
          x2={1343.239}
          y1={190.746}
          y2={260.481}
          gradientTransform="matrix(1 0 0 -1 0 1200)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
          <stop
            offset={0.112}
            style={{
              stopColor: "#aaaaae",
            }}
          />
          <stop
            offset={0.406}
            style={{
              stopColor: "#767884",
            }}
          />
          <stop
            offset={0.663}
            style={{
              stopColor: "#515366",
            }}
          />
          <stop
            offset={0.87}
            style={{
              stopColor: "#3a3d54",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#32354d",
            }}
          />
        </linearGradient>
        <path
          fill="url(#bW)"
          d="m1273.8 984.9-.8-1.2 2.8-1.8.8 1.2-2.8 1.8zm-1.4.9-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm25.3-16-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-21.1 13.4-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.1 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm67.1-43.1-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm25.3-16-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm21.4-13.3-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-34.1 21.4-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.1 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-46.2 29.6-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.2 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8zm-4.5 2.7-.8-1.2-2.8 1.8.8 1.2 2.8-1.8z"
        />
        <linearGradient
          id="bX"
          x1={1179.3}
          x2={969.973}
          y1={193.976}
          y2={325.796}
          gradientTransform="matrix(1 0 0 -1 0 1200)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#32354d",
            }}
          />
          <stop
            offset={0.131}
            style={{
              stopColor: "#3a3d54",
            }}
          />
          <stop
            offset={0.337}
            style={{
              stopColor: "#515366",
            }}
          />
          <stop
            offset={0.594}
            style={{
              stopColor: "#767884",
            }}
          />
          <stop
            offset={0.888}
            style={{
              stopColor: "#aaaaae",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
        </linearGradient>
        <path
          fill="url(#bX)"
          d="m1112.1 964.9.8-1.2 2.8 1.8-.8 1.2-2.8-1.8zm-1.4-.9.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.1-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm25.1 16 .8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-20.9-13.3.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm67.1 42.3.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.1-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm25.1 16 .8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm16.9 10.9.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-33.6-21.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.1-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-46.2-29.1.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.3-2.9.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-67.3-42.8.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.1-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm25.1 16 .8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-20.9-13.3.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm67.1 42.3.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.1-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm25.1 15.9.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm16.9 10.9.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-33.6-21.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.1-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-46.2-29.1.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.3-2.9.8-1.2-2.8-1.8-.8 1.2 2.8 1.8z"
        />
        <linearGradient
          id="bY"
          x1={1653.741}
          x2={1444.414}
          y1={481.328}
          y2={613.148}
          gradientTransform="matrix(1 0 0 -1 0 1200)"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset={0}
            style={{
              stopColor: "#32354d",
            }}
          />
          <stop
            offset={0.131}
            style={{
              stopColor: "#3a3d54",
            }}
          />
          <stop
            offset={0.337}
            style={{
              stopColor: "#515366",
            }}
          />
          <stop
            offset={0.594}
            style={{
              stopColor: "#767884",
            }}
          />
          <stop
            offset={0.888}
            style={{
              stopColor: "#aaaaae",
            }}
          />
          <stop
            offset={1}
            style={{
              stopColor: "#bfbfbf",
            }}
          />
        </linearGradient>
        <path
          fill="url(#bY)"
          d="m1587.1 677.9.8-1.2 2.8 1.8-.8 1.2-2.8-1.8zm-1.4-.9.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.1-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm25.1 16 .8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-20.9-13.3.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm67.1 42.3.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.1-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm25.1 16 .8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm16.9 10.9.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-33.6-21.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.1-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-46.2-29.1.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.3-2.9.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-67.3-42.8.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.1-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm25.1 16 .8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-20.9-13.3.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm67.1 42.3.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.1-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm25.1 15.9.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm16.9 10.9.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-33.6-21.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.1-2.7.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-46.2-29.1.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.2-2.6.8-1.2-2.8-1.8-.8 1.2 2.8 1.8zm-4.3-2.9.8-1.2-2.8-1.8-.8 1.2 2.8 1.8z"
        />
        <path
          id="bD"
          d="m969.4 873.9 241.4 151.8 291.8-184.6 177.5-105.4-241.9-153.4"
          style={{
            fill: "none",
            strokeMiterlimit: 10,
            stroke: "transparent",
          }}
        />
      </g>
      <linearGradient
        id="bZ"
        x1={605.925}
        x2={606.089}
        y1={953.613}
        y2={817.645}
        gradientTransform="matrix(.3592 -.3065 -1.2019 -.6834 1880.408 1551.138)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
      </linearGradient>
      <path
        fill="url(#bZ)"
        d="m1139.7 789.3-65.4 36.7-188.2-114.5 68.9-38.7z"
        className="particlespoly"
        opacity={0.25}
      />
      <linearGradient
        id="ca"
        x1={763.471}
        x2={725.636}
        y1={1027.766}
        y2={878.734}
        gradientTransform="matrix(.3592 -.3065 -1.2019 -.6834 1880.408 1551.138)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.387}
          style={{
            stopColor: "#FE8989",
            stopOpacity: 0.5873,
          }}
        />
        <stop
          offset={0.939}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
      </linearGradient>
      <path
        fill="url(#ca)"
        d="m1123.5 708.5-66 40.5-170.3-110.1 67.6-42.1z"
        className="particlespoly"
        opacity={0.75}
      />
      <g fill="#FFF">
        <circle cx={942.6} cy={693.5} r={1.6} />
        <circle cx={931.6} cy={675.3} r={1.6} />
        <circle cx={1000.1} cy={737.7} r={1.6} />
        <circle cx={1118.8} cy={719.1} r={1.6} />
        <circle cx={936.4} cy={658.5} r={1.6} />
        <circle cx={1017.4} cy={786.5} r={1.6} />
        <circle cx={1090} cy={769.7} r={1.6} />
        <circle cx={1004.9} cy={672.1} r={1.6} />
        <circle cx={957.9} cy={615.2} r={1.6} />
        <circle cx={948} cy={651.1} r={1.6} />
        <circle cx={1063.7} cy={686.7} r={1.6} />
        <circle cx={904.7} cy={691.9} r={3.2} />
      </g>
      <linearGradient
        id="cb"
        x1={608.255}
        x2={591.352}
        y1={1006.76}
        y2={855.332}
        gradientTransform="matrix(.3592 -.3065 -1.2019 -.6834 1880.408 1551.138)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.199}
          style={{
            stopColor: "#c5c6d1",
            stopOpacity: 0.8007,
          }}
        />
        <stop
          offset={0.392}
          style={{
            stopColor: "#9497ab",
            stopOpacity: 0.6081,
          }}
        />
        <stop
          offset={0.573}
          style={{
            stopColor: "#6e728d",
            stopOpacity: 0.4269,
          }}
        />
        <stop
          offset={0.74}
          style={{
            stopColor: "#535878",
            stopOpacity: 0.2601,
          }}
        />
        <stop
          offset={0.887}
          style={{
            stopColor: "#43486b",
            stopOpacity: 0.1126,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#3d4266",
            stopOpacity: 0,
          }}
        />
      </linearGradient>
      <path
        fill="url(#cb)"
        d="M1066 755.4v66.7L886.1 711.5l.6-72.6z"
        className="particlespoly"
        opacity={0.6}
      />
      <linearGradient
        id="cc"
        x1={606.56}
        x2={594.13}
        y1={980.964}
        y2={869.611}
        gradientTransform="matrix(.3592 -.3065 -1.2019 -.6834 1880.408 1551.138)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
      </linearGradient>
      <path
        fill="url(#cc)"
        d="M1066 778.8V798L886.1 681.3l.6-19.1z"
        className="particlespoly"
        opacity={0.35}
      />
      <linearGradient
        id="cd"
        x1={826.091}
        x2={792.899}
        y1={1015.935}
        y2={858.202}
        gradientTransform="matrix(.3592 -.3065 -1.2019 -.6834 1880.408 1551.138)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
      </linearGradient>
      <path
        fill="url(#cd)"
        d="m1133.1 714.6-.9 12.6L955 609.6v-13.2z"
        className="particlespoly"
        opacity={0.2}
      />
      <g fill="#FE8989" opacity={0.75}>
        <path d="m973.8 636.1-3.1 19.9-26-1.1-5.2 3.2 34.7 1.2 3.9-26.1z" />
        <path d="m981.8 644.1-3.1 19.9-26-1.1-5.1 3.2 34.6 1.2 4-26z" />
      </g>
      <g fill="#FE8989" opacity={0.75}>
        <path d="m1012.5 770.8-2.1-20.1 25.5-5.5 4.1-4.4-33.8 7.6 2.8 26.2z" />
        <path d="m1002.6 765-2-20 25.4-5.6 4.2-4.4-33.8 7.6 2.7 26.2z" />
      </g>
      <linearGradient
        id="ce"
        x1={1330}
        x2={1330.164}
        y1={905.428}
        y2={769.46}
        gradientTransform="matrix(.3592 -.3065 -1.2019 -.6834 1880.408 1551.138)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
      </linearGradient>
      <path
        fill="url(#ce)"
        d="m1457.7 600.3-65.4 36.7-188.2-114.5 68.9-38.7z"
        className="particlespoly"
        opacity={0.25}
      />
      <linearGradient
        id="cf"
        x1={1487.542}
        x2={1449.706}
        y1={979.565}
        y2={830.533}
        gradientTransform="matrix(.3592 -.3065 -1.2019 -.6834 1880.408 1551.138)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#FE8989",
          }}
        />
        <stop
          offset={0.387}
          style={{
            stopColor: "#FE8989",
            stopOpacity: 0.5873,
          }}
        />
        <stop
          offset={0.939}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
      </linearGradient>
      <path
        fill="url(#cf)"
        d="m1441.5 519.5-66 40.5-170.3-110.1 67.6-42.1z"
        className="particlespoly"
        opacity={0.75}
      />
      <g fill="#FFF">
        <circle cx={1260.6} cy={504.5} r={1.6} />
        <circle cx={1249.6} cy={486.3} r={1.6} />
        <circle cx={1318.1} cy={548.7} r={1.6} />
        <circle cx={1436.8} cy={530.1} r={1.6} />
        <circle cx={1254.4} cy={469.5} r={1.6} />
        <circle cx={1335.4} cy={597.5} r={1.6} />
        <circle cx={1408} cy={580.7} r={1.6} />
        <circle cx={1322.9} cy={483.1} r={1.6} />
        <circle cx={1275.9} cy={426.2} r={1.6} />
        <circle cx={1266} cy={462.1} r={1.6} />
        <circle cx={1381.7} cy={497.7} r={1.6} />
        <circle cx={1222.7} cy={502.9} r={3.2} />
      </g>
      <linearGradient
        id="cg"
        x1={1332.328}
        x2={1315.426}
        y1={958.568}
        y2={807.14}
        gradientTransform="matrix(.3592 -.3065 -1.2019 -.6834 1880.408 1551.138)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={0.083}
          style={{
            stopColor: "#e2e2e5",
            stopOpacity: 0.9173,
          }}
        />
        <stop
          offset={0.266}
          style={{
            stopColor: "#a8a9af",
            stopOpacity: 0.7344,
          }}
        />
        <stop
          offset={0.442}
          style={{
            stopColor: "#777983",
            stopOpacity: 0.5577,
          }}
        />
        <stop
          offset={0.608}
          style={{
            stopColor: "#515460",
            stopOpacity: 0.3916,
          }}
        />
        <stop
          offset={0.761}
          style={{
            stopColor: "#363947",
            stopOpacity: 0.2386,
          }}
        />
        <stop
          offset={0.897}
          style={{
            stopColor: "#262938",
            stopOpacity: 0.1033,
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#202333",
            stopOpacity: 0,
          }}
        />
      </linearGradient>
      <path
        fill="url(#cg)"
        d="M1384 566.4v66.7l-179.9-110.6.6-72.6z"
        className="particlespoly"
        opacity={0.6}
      />
      <linearGradient
        id="ch"
        x1={1330.634}
        x2={1318.204}
        y1={932.772}
        y2={821.419}
        gradientTransform="matrix(.3592 -.3065 -1.2019 -.6834 1880.408 1551.138)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
      </linearGradient>
      <path
        fill="url(#ch)"
        d="M1384 589.8V609l-179.9-116.7.6-19.1z"
        className="particlespoly"
        opacity={0.35}
      />
      <linearGradient
        id="ci"
        x1={1550.163}
        x2={1516.97}
        y1={967.737}
        y2={810.004}
        gradientTransform="matrix(.3592 -.3065 -1.2019 -.6834 1880.408 1551.138)"
        gradientUnits="userSpaceOnUse"
      >
        <stop
          offset={0}
          style={{
            stopColor: "#fff",
          }}
        />
        <stop
          offset={1}
          style={{
            stopColor: "#fff",
            stopOpacity: 0,
          }}
        />
      </linearGradient>
      <path
        fill="url(#ci)"
        d="m1451.1 525.6-.9 12.6L1273 420.6v-13.2z"
        className="particlespoly"
        opacity={0.2}
      />
      <g fill="#FE8989" opacity={0.75}>
        <path d="m1311.9 461.6-3.1 19.9-26.1-1.1-5.1 3.2 34.6 1.2 4-26z" />
        <path d="m1319.9 469.6-3.1 19.9-26-1-5.1 3.1 34.6 1.3 4-26.1z" />
      </g>
    </svg>
  );
}
