import type { SVGProps } from "react";

export const OfficeEmployeeIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <path
        fill="#FFDDA1"
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M10 6.667a2.916 2.916 0 1 0 0-5.832 2.916 2.916 0 0 0 0 5.832Z"
      />
      <path fill="#66E1FF" d="M13.954 10.834a4.139 4.139 0 0 0-7.908 0" />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M13.954 10.834a4.139 4.139 0 0 0-7.908 0"
      />
      <path
        fill="#E3E3E3"
        d="M15.416 19.167H4.583l-1.407-5.63a.834.834 0 0 1 .808-1.037h12.031a.835.835 0 0 1 .808 1.035l-1.407 5.632Z"
      />
      <path
        fill="#fff"
        d="M3.985 12.5a.834.834 0 0 0-.808 1.035l1.407 5.632h1.852l6.667-6.667H3.985Z"
      />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15.416 19.167H4.583l-1.407-5.63a.834.834 0 0 1 .808-1.037h12.031a.835.835 0 0 1 .808 1.035l-1.407 5.632ZM.834 19.167h18.332M10 10v1.25"
      />
      <path
        fill="#C77F67"
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M10.972 3.751c.655 0 1.303-.132 1.905-.39A2.899 2.899 0 0 0 7.495 2.28a4.842 4.842 0 0 0 3.477 1.472Z"
      />
      <path
        fill="gray"
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M10 16.667a1.042 1.042 0 1 0 0-2.084 1.042 1.042 0 0 0 0 2.084Z"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h20v20H0z" />
      </clipPath>
    </defs>
  </svg>
)
