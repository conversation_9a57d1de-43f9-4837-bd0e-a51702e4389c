import type { SVGProps } from "react";
export const UploadIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <path
      fill="#66E1FF"
      d="M20.381 12a1.571 1.571 0 0 0-1.571-1.571H5.19A1.573 1.573 0 0 0 3.62 12v6.81H20.38V12Z"
    />
    <path
      fill="#C2F3FF"
      d="M16.19 10.429h-11A1.571 1.571 0 0 0 3.62 12v6.81h4.19l8.38-8.381Z"
    />
    <path
      fill="#fff"
      d="M20.381 19.334a1.571 1.571 0 0 1-1.571 1.571H5.19a1.57 1.57 0 0 1-1.57-1.571v-.524H20.38v.524Z"
    />
    <path
      stroke="#fff"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M8.857 23h6.286M12 20.904V23M3.62 18.81H20.38M12 1v12.571M16.19 5.19 12 1 7.81 5.19M15.143 10.429h3.667A1.571 1.571 0 0 1 20.38 12v7.334a1.571 1.571 0 0 1-1.571 1.57H5.19a1.57 1.57 0 0 1-1.57-1.57V12a1.571 1.571 0 0 1 1.57-1.571h3.667"
    />
  </svg>
)
    