import React, {
  use<PERSON><PERSON><PERSON>,
  useEffect, use<PERSON>emo, useState,
} from "react";
import {
  Eip1193Provider, ethers, JsonRpcSigner,
} from "ethers";
import {
  usePrivy, useWallets,
} from "@privy-io/react-auth";
import {
  ChainType,
  Token,
  ChainData,
  OnChainExecutionData,
} from "@0xsquid/squid-types";

import {
  PublicKey,
  SystemProgram,
  TransactionMessage,
  VersionedTransaction,
} from "@solana/web3.js";
import { tokenList } from "@/common/components/molecules/doogly/constants";
import HttpAdapter from "@/common/components/molecules/doogly/adapter/HttpAdapter";
import {
  PhantomSigner, SolanaSigner,
} from "@/common/components/molecules/doogly/types";
import {
  CircularSpinner,
} from "@/common/components/atoms";
import { DefaultModal } from "@/common/components/organisms";
import { SolanaHandler } from "@/common/components/molecules/doogly/handlers/solana";
import toast from "react-hot-toast";
import { ChainSelector } from "./ChainSelector";
import { TokenSelector } from "./TokenSelector";

declare global {
  interface Window {
    ethereum?: any;
    solana?: {
      isPhantom?: boolean;
      connect(): Promise<{ publicKey: { toString(): string } }>;
    };
    phantom?: {
      bitcoin: {
        connect(): Promise<{ publicKey: { toString(): string } }>;
      };
    };
  }
}

const erc20ContractABI = [
  {
    constant: false,
    inputs: [
      {
        name: "_spender",
        type: "address",
      },
      {
        name: "_value",
        type: "uint256",
      },
    ],
    name: "approve",
    outputs: [
      {
        name: "",
        type: "bool",
      },
    ],
    payable: false,
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    constant: true,
    inputs: [
      {
        name: "_owner",
        type: "address",
      },
    ],
    name: "balanceOf",
    outputs: [
      {
        name: "balance",
        type: "uint256",
      },
    ],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  {
    constant: true,
    inputs: [
      {
        name: "_owner",
        type: "address",
      },
      {
        name: "_spender",
        type: "address",
      },
    ],
    name: "allowance",
    outputs: [
      {
        name: "",
        type: "uint256",
      },
    ],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  {
    constant: false,
    inputs: [
      {
        name: "_to",
        type: "address",
      },
      {
        name: "_value",
        type: "uint256",
      },
    ],
    name: "transfer",
    outputs: [
      {
        name: "",
        type: "bool",
      },
    ],
    payable: false,
    stateMutability: "nonpayable",
    type: "function",
  },
];

const permit2Abi = [
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "address",
        name: "spender",
        type: "address",
      },
      {
        internalType: "uint160",
        name: "amount",
        type: "uint160",
      },
      {
        internalType: "uint48",
        name: "expiration",
        type: "uint48",
      },
    ],
    name: "approve",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
];

const permit2Address = "******************************************";

interface Web3Config {
  provider?: ethers.JsonRpcProvider | ethers.BrowserProvider;
}

interface transactionCallback {
  transactionId: string;
  requestId: string;
  fromChainId: string;
  toChainId: string;
  status: string; // ["success", "partial_success", "needs_gas", "not_found"]
}

interface DooglyProps {
  buttonClassName?: string;
  web3Config?: Web3Config;
  config?: {
    destinationChain?: string;
    destinationAddress?: string;
    destinationOutputTokenAddress?: string;
    initialAmount?: string;
    initialChainId?: string;
    initialToken?: string;
  };
  projectId?: string;
  provider?: Eip1193Provider;
  signer?: ethers.JsonRpcSigner;
  postSwapHook?: Array<{
    target: `0x${string}`;
    callData: `0x${string}`;
    callType?: number;
    tokenAddress?: `0x${string}`;
    inputPos?: number;
  }>;
  apiUrl: string;
  webhookUrl?: string;
  webHookData?: string;
  callback?: (transactionCallback: transactionCallback) => void;
  privyAppId?: string;
}

const _tokens = tokenList;

const DooglyInterface: React.FC<Omit<DooglyProps, "web3Config" | "privyAppId">> = ({
  config: initialConfig,
  postSwapHook,
  apiUrl,
  callback,
  provider: injectedProvider,
}) => {
  const [provider, setProvider] = useState<
    ethers.BrowserProvider | ethers.Provider | SolanaSigner | null
  >(null);
  const [signer, setSigner] = useState<ethers.JsonRpcSigner | string | null>(
    null,
  );
  const [, setNetwork] = useState<ethers.Network>();
  const [initialized, setInitialized] = useState(false);
  const [config] = useState(initialConfig);
  const [currentToken, setCurrentToken] = useState<Token>(
    config?.initialToken
      ? config.initialChainId
        ? _tokens.find(
          (v: Token) =>
            config && v.address.toLowerCase() == config.initialToken?.toLowerCase() &&
              v.chainId == config.initialChainId,
        )
        : _tokens.find((v: Token) => v.address == config.initialToken)
      : undefined,
  );
  const [currentChainId, setCurrentChainId] = useState<bigint | string>(
    config?.initialChainId ?? "1",
  );

  const [donationAmount, setDonationAmount] = useState(
    config?.initialAmount ?? "0",
  );
  const [submitButtonDisabled, setSubmitButtonDisabled] = useState(false);
  const [allTokens, setAllTokens] = useState<Token[]>();
  const [tokens, setTokens] = useState<Token[]>(
    config?.initialChainId
      ? _tokens.filter((i: Token) => i.chainId == config.initialChainId)
      : _tokens.filter((i: Token) => i.chainId == "1"),
  );
  const [chains, setChains] = useState<ChainData[]>([]);
  const [connected, setConnected] = useState(false);
  const [quoteData, setQuoteData] = useState<any>(null);
  const [showQuote, setShowQuote] = useState(false);

  const httpInstance = useMemo(() => new HttpAdapter({
    baseUrl: apiUrl,
  }), [apiUrl]);

  const {
    login, authenticated,
  } = usePrivy();
  const { wallets } = useWallets();

  const initializeProvider = async (provider: Eip1193Provider) => {
    try {
      const web3Provider = new ethers.BrowserProvider(provider);
      const signer = await web3Provider.getSigner();
      const net = await web3Provider.getNetwork();
      setNetwork(net);
      setSigner(signer);
      setProvider(web3Provider);
      setConnected(true);
    } catch (e) {
      console.error("Failed to initialize provider:", e);
    }
  };

  useEffect(() => {
    const initialize = async () => {
      const response = await httpInstance.get("info");

      if (response.status != 200) {
        throw new Error("Initialization failed");
      }

      const filteredChains = response.data.chains.filter(
        (i: ChainData) =>
          i.chainType != ChainType.COSMOS && i.chainType != ChainType.BTC,
      );

      // Rearranging the filteredChains to move the last element to the second position
      const rearrangedChains = [
        filteredChains[0], // First element remains the same
        filteredChains[filteredChains.length - 1], // Last element moved to second position
        ...filteredChains.slice(1, filteredChains.length - 1), // All elements except the first and last
      ];

      setAllTokens(response.data.tokens);
      setChains(rearrangedChains);
    };

    if (!initialized) {
      initialize();
      setInitialized(true);
    }
  }, [initialized, injectedProvider, httpInstance]);

  const connectWallet = useCallback(async () => {
    try {
      if (currentChainId === "solana-mainnet-beta") {
        // Try Phantom first
        const { solana } = window as any;
        if (solana?.isPhantom) {
          const response = await solana.connect();
          const provider = response.publicKey.toString();
          setSigner(provider);
          setProvider(solana);
          setConnected(true);
          return;
        }

        // If no Phantom, try Privy
        if (!authenticated) {
          await login();
          return;
        }

        window.open("https://phantom.app/", "_blank");
        return;
      }

      // For EVM chains
      if (injectedProvider) {
        await initializeProvider(injectedProvider);
      } else if (window.ethereum) {
        await window.ethereum.request({ method: "eth_requestAccounts" });
        await initializeProvider(window.ethereum);
      } else {
        // Use Privy as fallback
        if (!authenticated) {
          await login();
          return;
        }

        const wallet = await wallets[0]?.getEthereumProvider();
        if (wallet) {
          window.ethereum = wallet;
          await initializeProvider(wallet);
        } else {
          toast.error("Please install wallet or use a Web3 enabled browser!");
        }
      }
    } catch (error) {
      console.error("Error connecting wallet:", error);
      toast.error("Failed to connect wallet. Please try again.");
    }
  }, [authenticated, login, wallets, currentChainId, injectedProvider]);
  const getChainData = (
    chains: ChainData[],
    chainId: number | string,
  ): ChainData | undefined => chains?.find((chain) => chain.chainId == chainId);

  async function switchChainEVM (chainId: number) {
    try {
      const activeProvider = injectedProvider || window.ethereum;
      if (!activeProvider) {
        throw new Error("No Web3 provider available");
      }

      await activeProvider.request({
        method: "wallet_switchEthereumChain",
        params: [{ chainId: `0x${chainId.toString(16)}` }],
      });

      await initializeProvider(activeProvider);
    } catch (switchError: any) {
      // This error code indicates that the chain has not been added to the wallet
      if (switchError.code === 4902) {
        try {
          const chainData = getChainData(chains, chainId);
          const activeProvider = injectedProvider || window.ethereum;

          if (!activeProvider) {
            toast.error("No Web3 provider available");
            throw new Error("No Web3 provider available");
          }
          if (chainData) {
            await activeProvider.request({
              method: "wallet_addEthereumChain",
              params: [
                {
                  chainId: `0x${chainId.toString(16)}`,
                  chainName: chainData.networkName,
                  rpcUrls: [chainData.rpc],
                  iconUrls: [chainData.chainIconURI],
                  nativeCurrency: chainData.nativeCurrency,
                  blockExplorerUrls: chainData.blockExplorerUrls,
                },
              ],
            });
          }
        } catch (addError) {
          toast.error("Failed to switch network");
          throw new Error("Failed to add the network to wallet");
        }
      } else {
        toast.error("Failed to switch network");
        throw new Error("Failed to switch network");
      }
    }
  }

  const fetchTokensAndSwitchChain = async (chain: string) => {
    const fromToken = allTokens?.filter(
      (t) => t.chainId === chain.toString(),
    );
    setTokens(fromToken ?? []);
    if (fromToken?.length) {
      setCurrentToken(fromToken?.[0]);
    }
    const currentChainData = getChainData(chains, chain ?? "1");

    switch (currentChainData?.chainType) {
    case ChainType.EVM:
      await switchChainEVM(parseInt(chain));
      break;
    case ChainType.SOLANA:
      setConnected(false);
      break;
    case ChainType.BTC:
      setConnected(false);
      break;
    }
  };

  const fetchTokenBalance = useCallback(async (requestedAmount: string) => {
    try {
      if (!signer || !currentToken) {
        return true;
      }
      if (typeof signer === "string") {
        // For non-EVM chains like Solana, we don't have a simple way to get balance
        // This would need a custom implementation
        return true;
      }

      // For EVM chains
      const erc20Contract = new ethers.Contract(
        currentToken.address,
        erc20ContractABI,
        signer as JsonRpcSigner,
      );
      // If it's the native token (ETH, etc.)
      if (currentToken.address === "******************************************") {
        const balance = await (signer as JsonRpcSigner).provider.getBalance((signer as JsonRpcSigner).address);
        const formattedBalance = ethers.formatUnits(balance, currentToken.decimals);
        return parseFloat(formattedBalance) < parseFloat(requestedAmount);
      }

      // For ERC20 tokens
      const balance = await erc20Contract.balanceOf((signer as JsonRpcSigner).address);
      const formattedBalance = ethers.formatUnits(balance, currentToken.decimals);
      return parseFloat(formattedBalance) < parseFloat(requestedAmount);
    } catch (error) {
      console.error("Error fetching token balance:", error);
      return true
    }
  }, [signer, currentToken]);

  const getRoute = useCallback(async (params: any) => {
    try {
      const result = await httpInstance.post(
        "route",

        params,
        {
          headers: {
            "Content-Type": "application/json",
          },
        },
      );
      const requestId = result.data["x-request-id"]; // Retrieve request ID from response headers
      return {
        data: result.data,
        requestId: requestId,
      };
    } catch (error: any) {
      if (error.response) {
        console.error("API error:", error.response.data.error);
      }
      throw new Error(error.response.data.error);
    }
  }, [httpInstance]);

  const getQuoteForDonation = useCallback(async () => {
    const amount = donationAmount;
    if (!amount) {
      return;
    }

    if (injectedProvider) {
      await initializeProvider(injectedProvider);
    } else if (window.ethereum) {
      try {
        await initializeProvider(window.ethereum);
      } catch (e) {
        toast.error("Failed to initialize provider");
      }
    } else {
      await connectWallet();
      // toast.error("Please install wallet or use a Web3 enabled browser!");
    }

    if (!signer || !config?.destinationChain) {
      return;
    }
    setSubmitButtonDisabled(true);
    try {
      // Fetch the current token balance
      const lowLiquidity = await fetchTokenBalance(amount);
      if (lowLiquidity) {
        toast.error(`Insufficient ${currentToken.symbol} balance. Please add more tokens to your wallet.`);
        return;
      }

      const donationAmount = ethers.parseUnits(
        String(amount),
        currentToken.decimals,
      );

      let params: any = {
        fromAddress:
          typeof signer === "string"
            ? signer
            : (signer as JsonRpcSigner).address,
        fromChain: currentChainId?.toString(),
        fromToken: currentToken?.address as string,
        fromAmount: donationAmount.toString(),
        toChain: config.destinationChain,
        toToken: config.destinationOutputTokenAddress,
        toAddress: config.destinationAddress,
        enableBoost: true,
        quoteOnly: true, // Add this to get quote only
      };

      if (postSwapHook) {
        params = {
          ...params,
          postHook: {
            chainType: ChainType.EVM,
            calls: postSwapHook.map(
              (i: {
                target: `0x${string}`;
                callData: `0x${string}`;
                callType?: number;
                tokenAddress?: `0x${string}`;
                inputPos?: number;
              }) => {
                if (
                  i.callData.includes("deadbeef1234567890abcdef1234567890abcdef")
                ) {
                  i.callData = i.callData.replace(
                    "deadbeef1234567890abcdef1234567890abcdef",
                    ((signer as JsonRpcSigner).address as string)
                      .substring(2)
                      .toLowerCase(),
                  ) as `0x${string}`;
                }
                return {
                  chainType: "evm",
                  callType: i.callType ?? 0,
                  target: i.target,
                  value: "0",
                  callData: i.callData,
                  payload: {
                    tokenAddress: i.tokenAddress ?? "",
                    inputPos: i.inputPos ?? 0,
                  },
                  estimatedGas: "50000",
                };
              },
            ),
            provider: "DreamStartr", //This should be the name of your product or application that is triggering the hook
            description: "Cross chain contract call",
            logoURI: "",
          },
        };
      }

      const routeResult = await getRoute(params);
      setQuoteData(routeResult.data.route.estimate);
      await connectWallet();
      setShowQuote(true);
    } catch (error) {
      console.error("Failed to get quote:", error);
      toast.error(`Failed to get quote. Please try again.`);
    } finally {
      setSubmitButtonDisabled(false);
    }
  }, [config, currentChainId, currentToken, donationAmount, postSwapHook, getRoute, injectedProvider, signer, connectWallet, fetchTokenBalance]);

  const getDepositAddress = async (transactionRequest: any) => {
    try {
      const result = await httpInstance.post("deposit", transactionRequest, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      return result.data;
    } catch (error: any) {
      if (error.response) {
        console.error("API error:", error.response.data);
      }
      console.error("Error getting deposit address:", error);
      throw new Error(error.response.data);
    }
  };

  const getStatus = async (params: any) => {
    try {
      const result = await httpInstance.get("status", {
        params: {
          transactionId: params.transactionId,
          fromChainId: params.fromChainId,
          toChainId: params.toChainId,
          requestId: params.requestId,
          bridgeType: params.bridgeType,
        },
      });
      return result.data;
    } catch (error: any) {
      if (error.response) {
        console.error("API error:", error.response.data.error);
      }
      console.error("Error with parameters:", params);
      throw error;
    }
  };

  const updateTransactionStatusAndExecuteCallback = async (
    transactionId: string,
    requestId: string,
    fromChainId: string,
    toChainId: string,
    bridgeType?: string,
  ) => {

    const getStatusParams = bridgeType
      ? {
        transactionId,
        fromChainId,
        toChainId,
        bridgeType,
        requestId,
      }
      : {
        transactionId,
        requestId,
        fromChainId,
        toChainId,
      };

    let status;
    const completedStatuses = [
      "success",
      "partial_success",
      "needs_gas",
      "not_found",
    ];
    const maxRetries = 10;
    let retryCount = 0;

    // Set initial estimated duration and time remaining

    do {
      try {
        status = await getStatus(getStatusParams);

        if (callback) {
          callback({
            transactionId,
            fromChainId,
            toChainId,
            requestId,
            status: status?.squidTransactionStatus,
          });
        }

      } catch (error: any) {
        if (error.response && error.response.status === 404) {
          retryCount++;
          if (retryCount >= maxRetries) {
            console.error("Max retries reached. Transaction not found.");
            toast.error(`Transaction failed. Please try again.`);
            break;
          }
          await new Promise((resolve) => setTimeout(resolve, 5000));
          continue;
        } else {
          throw error.message;
        }
      }

      if (!completedStatuses.includes(status?.squidTransactionStatus)) {
        await new Promise((resolve) => setTimeout(resolve, 5000));
      }
    } while (!completedStatuses.includes(status?.squidTransactionStatus));
    toast.success(`Transaction successful!`);
    setShowQuote(false);
    const donationElement = document.getElementById("donationAmount") as HTMLInputElement
    if (donationElement) {
      donationElement.value = "";
    }
  };

  async function submitDonation () {
    const amount = donationAmount;
    if (!amount) {
      return;
    }
    const inputTokenAddress = currentToken.address;

    setSubmitButtonDisabled(true);

    try {
      if (
        getChainData(chains, currentChainId.toString())?.chainType ==
        ChainType.EVM
      ) {
        if (!config) {
          return
        }
        // For ERC20 token transactions
        const donationAmount = ethers.parseUnits(
          String(amount),
          currentToken.decimals,
        );

        const erc20Contract = new ethers.Contract(
          inputTokenAddress,
          erc20ContractABI,
          signer as JsonRpcSigner,
        );

        const permit2Contract = new ethers.Contract(
          permit2Address,
          permit2Abi,
          signer as JsonRpcSigner,
        );

        let params: any = {
          fromAddress: (signer as JsonRpcSigner).address as string,
          fromChain: currentChainId?.toString(),
          fromToken: currentToken?.address as string,
          fromAmount: donationAmount.toString(),
          toChain: config.destinationChain,
          toToken: config.destinationOutputTokenAddress,
          toAddress: config.destinationAddress,
          enableBoost: true,
        };

        if (postSwapHook) {
          params = {
            ...params,
            postHook: {
              chainType: ChainType.EVM,
              calls: postSwapHook.map(
                (i: {
                  target: `0x${string}`;
                  callData: `0x${string}`;
                  callType?: number;
                  tokenAddress?: `0x${string}`;
                  inputPos?: number;
                }) => {
                  if (
                    i.callData.includes(
                      "deadbeef1234567890abcdef1234567890abcdef",
                    )
                  ) {
                    i.callData = i.callData.replace(
                      "deadbeef1234567890abcdef1234567890abcdef",
                      ((signer as JsonRpcSigner).address as string)
                        .substring(2)
                        .toLowerCase(),
                    ) as `0x${string}`;
                  }
                  return {
                    chainType: "evm",
                    callType: i.callType ?? 0,
                    target: i.target,
                    value: "0",
                    callData: i.callData,
                    payload: {
                      tokenAddress: i.tokenAddress ?? "",
                      inputPos: i.inputPos ?? 0,
                    },
                    estimatedGas: "50000",
                  };
                },
              ),
              provider: "DreamStartr", //This should be the name of your product or application that is triggering the hook
              description: "Cross chain contract call",
              logoURI: "",
            },
          }
        }

        // Get the swap route using Squid API
        const routeResult = await getRoute(params);
        const route = routeResult.data.route;
        // const requestId = routeResult.requestId;

        const transactionRequest = route.transactionRequest;

        if (
          currentToken?.address != "******************************************"
        ) {

          // Check current allowance
          const currentAllowance = await erc20Contract.allowance(
            (signer as JsonRpcSigner)?.address,
            permit2Address,
          );
          // If current allowance is less than donation amount, request approval
          if (Number(currentAllowance) < donationAmount) {
            const approveTx = await erc20Contract.approve(
              permit2Address,
              ethers.MaxUint256,
              {
                gasLimit: 500000,
              },
            );
            await approveTx.wait();
          }

          // Call approve on Permit2 to target contract for donation amount with expiry
          const expiry = Math.floor(Date.now() / 1000) + 3000; // Current time + 5 minutes
          const permitTx = await permit2Contract.approve(
            inputTokenAddress,
            (transactionRequest as OnChainExecutionData).target,
            donationAmount,
            expiry,
            {
              gasLimit: 500000,
            },
          );
          await permitTx.wait();
        }

        const tx = await (signer as JsonRpcSigner).sendTransaction({
          to: transactionRequest.target,
          data: transactionRequest.data,
          value:
            (BigInt(transactionRequest.value) * BigInt("110")) / BigInt(100),
          gasLimit:
            (BigInt(transactionRequest.gasLimit) * BigInt("110")) / BigInt(100),
        });
        if (config?.destinationChain) {
          await updateTransactionStatusAndExecuteCallback(
            tx.hash,
            routeResult.requestId,
            currentChainId?.toString(),
            config.destinationChain,
          );
        }
      } else {
        if (
          getChainData(chains, currentChainId.toString())?.chainType ==
          ChainType.SOLANA
        ) {
          if (!config?.destinationChain || !signer) {
            return
          }
          const solana = new SolanaHandler();
          const donationAmount = ethers.parseUnits(
            String(amount),
            currentToken.decimals,
          );

          if (currentToken.symbol != "SOL" && currentToken.symbol != "USDC") {

            const params = {
              fromAddress: signer,
              fromChain: "solana-mainnet-beta",
              fromToken: currentToken.address,
              fromAmount: donationAmount.toString(),
              toChain: "solana-mainnet-beta",
              toToken: "******************************************",
              toAddress: signer,
              quoteOnly: false,
            };

            const routeResult = await getRoute(params);
            const route = routeResult.data.route;
            // const requestId = routeResult.requestId;

            await solana.executeRoute({
              data: {
                route: routeResult.data.route,
                signer: provider as SolanaSigner,
              },
            });

            // Transfer SOL to EVM address
            const params2 = {
              fromAddress: signer,
              fromChain: "solana-mainnet-beta",
              fromToken: "******************************************",
              fromAmount:
                route.estimate.actions[route.estimate.actions.length - 1]
                  .toAmount,
              toChain: config.destinationChain,
              toToken: config.destinationOutputTokenAddress,
              toAddress: config.destinationAddress,
              quoteOnly: false,
            };

            const routeResult2 = await getRoute(params2);
            const route2 = routeResult2.data.route;
            const requestId2 = routeResult2.requestId;

            // Get deposit address using transaction request
            const depositAddressResult = await getDepositAddress(
              route2.transactionRequest,
            );

            // create array of instructions
            const instructions = [
              SystemProgram.transfer({
                fromPubkey: new PublicKey(signer),
                toPubkey: new PublicKey(depositAddressResult.depositAddress),
                lamports: parseInt(depositAddressResult.amount),
              }),
            ];

            const resp = await httpInstance.get("/blockhash");
            const latestBlockHash = await resp.data;

            // create v0 compatible message
            const messageV0 = new TransactionMessage({
              payerKey: new PublicKey(signer),
              recentBlockhash: latestBlockHash.blockhash,
              instructions,
            }).compileToV0Message();

            // make a versioned transaction
            const transactionV0 = new VersionedTransaction(messageV0);

            // phantom wallet signer
            await (provider as PhantomSigner).signAndSendTransaction(
              transactionV0,
            );

            // Monitor using chainflipStatusTrackingId with determined bridge type
            await updateTransactionStatusAndExecuteCallback(
              depositAddressResult.chainflipStatusTrackingId,
              requestId2,
              "solana-mainnet-beta",
              config.destinationChain,
              depositAddressResult.chainflipStatusTrackingId === "42161"
                ? "chainflip"
                : "chainflipmultihop",
            );

            return;
          }

          // Transfer SOL to EVM address
          const params = {
            fromAddress: signer,
            fromChain: "solana-mainnet-beta",
            fromToken: currentToken.address,
            fromAmount: donationAmount.toString(),
            toChain: config.destinationChain,
            toToken: config.destinationOutputTokenAddress,
            toAddress: config.destinationAddress,
            quoteOnly: false,
          };

          const routeResult = await getRoute(params);
          const route = routeResult.data.route;
          const requestId = routeResult.requestId;

          // Get deposit address using transaction request
          const depositAddressResult = await getDepositAddress(
            route.transactionRequest,
          );

          // create array of instructions
          const instructions = [
            SystemProgram.transfer({
              fromPubkey: new PublicKey(signer),
              toPubkey: new PublicKey(depositAddressResult.depositAddress),
              lamports: parseInt(depositAddressResult.amount),
            }),
          ];

          const resp = await httpInstance.get("/blockhash");
          const latestBlockHash = await resp.data;

          // create v0 compatible message
          const messageV0 = new TransactionMessage({
            payerKey: new PublicKey(signer),
            recentBlockhash: latestBlockHash.blockhash,
            instructions,
          }).compileToV0Message();

          // make a versioned transaction
          const transactionV0 = new VersionedTransaction(messageV0);

          // phantom wallet signer
          await (
            provider as PhantomSigner
          ).signAndSendTransaction(transactionV0);

          // Monitor using chainflipStatusTrackingId with determined bridge type
          await updateTransactionStatusAndExecuteCallback(
            depositAddressResult.chainflipStatusTrackingId,
            requestId,
            "solana-mainnet-beta",
            config.destinationChain,
            depositAddressResult.chainflipStatusTrackingId === "42161"
              ? "chainflip"
              : "chainflipmultihop",
          );

          toast.success(`Transaction successful!`);
          setShowQuote(false)
          const donationElement = document.getElementById("donationAmount") as HTMLInputElement
          if (donationElement) {
            donationElement.value = "";
          }
        } else {
          throw new Error("Chain not supported");
        }
      }
    } catch (error) {
      console.error("Transaction failed:", error);
      toast.error(`Transaction failed. Please try again.`);
    } finally {
      setSubmitButtonDisabled(false);
    }
  }

  const formatTokenAmount = (amount: string | undefined, decimals: number = 18): string => {
    if (!amount) {
      return "0";
    }
    const formattedValue = ethers.formatUnits(amount, decimals);
    const numValue = parseFloat(formattedValue);

    if (numValue > 0 && numValue < 0.001) {
      return formattedValue;
    }

    const fixed = numValue.toFixed(2);
    return fixed.replace(/\.?0+$/, '');
  };

  const QuoteDisplay = () => {
    const footerContent = (
      <div className="flex justify-end w-full">
        <button
          onClick={submitDonation}
          disabled={submitButtonDisabled}
          className="w-full whitespace-nowrap block text-center py-2 px-6 bg-light-yellow disabled:bg-light-yellow/20 disabled:cursor-not-allowed disabled:text-gray-200 hover:bg-yellow-200 text-black font-medium rounded-xl transition-all duration-300"
        >
          {submitButtonDisabled ? "Processing..." : "Confirm Transfer"}
        </button>
      </div>
    );

    return (
      <DefaultModal
        isOpen={showQuote}
        onClose={() => {
          setShowQuote(false);
          setQuoteData(null);
          setSubmitButtonDisabled(false);
        }}
        maxWidth="max-w-[420px]"
        footerContent={footerContent}
      >
        <div className="space-y-4 px-6">
          <h3 className="font-semibold text-xl text-white mb-6">
            Transaction Quote
          </h3>
          <div className="space-y-3 text-white">
            <p className="flex justify-between gap-2">
              <span>You will send:</span>
              <span className="font-semibold text-right text-light-yellow">
                {quoteData?.fromAmount ? ethers.formatUnits(quoteData?.fromAmount, currentToken?.decimals) : ''}{" "}
                {currentToken?.symbol}
              </span>
            </p>
            <p className="flex justify-between gap-2">
              <span>Recipient will receive:</span>
              <span className="font-semibold text-right text-light-yellow">
                {quoteData?.toAmount ? ethers.formatUnits(quoteData?.toAmount, quoteData?.toToken?.decimals) : ''}{" "}
                {quoteData?.toToken?.symbol}
              </span>
            </p>
            <p className="flex justify-between gap-2">
              <span>Estimated Gas Fee:</span>
              <span className="font-semibold text-right text-light-yellow">
                {formatTokenAmount(quoteData?.gasCosts?.[0]?.amount, 18)}{" "}
                {quoteData?.fromToken?.symbol}
              </span>
            </p>
          </div>
        </div>
      </DefaultModal>
    );
  };

  return (
    <>
      <QuoteDisplay />
      <div className="space-y-4">
        <div className="mb-4 relative">
          <div className="text-gray-200 text-xs mb-1">Select Chain</div>
          <ChainSelector
            chains={chains}
            currentChainId={currentChainId.toString()}
            fetchTokensAndSwitchChain={fetchTokensAndSwitchChain}
            setCurrentChainId={(chainId) => setCurrentChainId(chainId)}
          />
        </div>

        <div className="mb-4 relative">
          <TokenSelector
            tokens={tokens}
            currentToken={currentToken}
            setCurrentToken={setCurrentToken}
          />
        </div>
        <div className="mb-4">
          <input
            type="number"
            min={1}
            id="donationAmount"
            placeholder="Enter Amount"
            onChange={(e) => setDonationAmount(e.target.value)}
            className={`rounded-xl w-full border border-yellow-500/10 bg-violets-are-blue/5 backdrop-blur-sm py-3 px-4 text-white text-sm focus:outline-none focus:border-yellow-500/50`}
          />
        </div>

        <div className="flex justify-between mt-4">
          <button
            onClick={getQuoteForDonation}
            className="w-full whitespace-nowrap block text-center py-2 px-6 bg-light-yellow disabled:bg-light-yellow/20 disabled:cursor-not-allowed disabled:text-gray-200 hover:bg-yellow-200 text-black font-medium rounded-xl transition-all duration-300"
            disabled={submitButtonDisabled}
          >
            {submitButtonDisabled ? <div className="flex justify-center"><CircularSpinner /></div> : (connected ? "Send Funds" : "Connect Wallet")}
          </button>
        </div>
      </div>
    </>
  );
};

export type {
  DooglyProps,
  Web3Config,
};

export { DooglyInterface };

export const createCustomProvider = (url: string, chainId: number) => {
  return new ethers.JsonRpcProvider(url, chainId);
};

export const createBrowserProvider = (ethereum: any) => {
  return new ethers.BrowserProvider(ethereum);
};
