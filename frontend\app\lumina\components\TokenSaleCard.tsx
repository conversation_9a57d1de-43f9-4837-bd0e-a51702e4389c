import {
  useState, useEffect, useCallback,
} from "react";
import { secondaryFont } from "@/common/utils/localFont";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { ethers } from "ethers";
import { DooglyInterface } from "./DooglyInterface";
import { RecentTransactionsCard } from "./RecentTransactionsCard";
import { useAddressTransactions } from "../hooks/useAddressTransactions";
// import { useEthPrice } from "../hooks/useEthPrice";
dayjs.extend(relativeTime);

// Utility function to format numbers with appropriate suffixes
const formatNumberWithSuffix = (num: number): string => {
  if (num >= 1000000000) {
    const billions = num / 1000000000;
    return `${billions.toFixed(2)}B`;
  } else if (num >= 1000000) {
    const millions = num / 1000000;
    return `${millions.toFixed(2)}M`;
  } else if (num >= 1000) {
    const thousands = num / 1000;
    return `${thousands.toFixed(2)}K`;
  } else {
    return num.toFixed(2);
  }
};

const TokenSaleCard = () => {
  const [raisedUSD, setRaisedUSD] = useState(0);
  const [progressPercent, setProgressPercent] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  // const {
  //   price: ethPrice, isLoading: isPriceLoading,
  // } = useEthPrice(300000);

  const {
    transactions, isLoading: isTransactionsLoading,
  } = useAddressTransactions(3);
  const isDataLoading = isLoading;

  const goal = 5500000; // Goal in USD (5.5M)
  const tokenPrice = 0.022;
  const ethPrice = 2561.72;

  const fetchDestinationBalance = useCallback(async () => {
    setIsLoading(true);
    try {
      const destinationAddress = process.env.NEXT_PUBLIC_LUMINACASA_DESTINATION_ADDRESS ??
        "******************************************";

      const provider = new ethers.JsonRpcProvider("https://mainnet.base.org");

      const balance = await provider.getBalance(destinationAddress);
      const formattedBalance = parseFloat(ethers.formatEther(balance));
      const usdValue = formattedBalance * ethPrice;
      setRaisedUSD(usdValue);

      const calculatedProgress = Math.min(Math.round((usdValue / goal) * 100), 100);
      setProgressPercent(calculatedProgress);

    } catch (error) {
      console.error("Error fetching destination balance:", error);
      setRaisedUSD(0);
      setProgressPercent(0);
    } finally {
      setIsLoading(false);
    }
  }, [goal, ethPrice]);

  useEffect(() => {
    fetchDestinationBalance();
  }, [fetchDestinationBalance]);

  useEffect(() => {
    if (ethPrice > 0) {
      fetchDestinationBalance();
    }
  }, [ethPrice, fetchDestinationBalance]);

  return (
    <div className="w-full max-w-md mx-auto bg-gradient-to-br from-yellow-200/5 via-yellow-100/20 to-yellow-200/5 rounded-3xl shadow-2xl p-6 relative border border-yellow-500/10">
      <h2
        className={`text-xl font-semibold text-white mb-1 `}
      >
        <span className={`${secondaryFont.className} text-[22px] text-light-yellow text-shadow-glow`}>$CASA</span> Token Sale
      </h2>
      <p className="text-xs text-gray-300 mb-4">Backed by Real World Assets</p>
      <div className="mb-4">
        <div className="flex justify-between items-center mb-1">
          <span className="text-xs text-gray-300">Progress</span>
          <span className="text-xs text-white font-semibold">
            {progressPercent}%
          </span>
        </div>
        <div className="w-full h-2 rounded-full bg-white/10 overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-yellow-300 to-light-yellow transition-all duration-500"
            style={{ width: `${progressPercent}%` }}
          />
        </div>
      </div>
      <div className="flex justify-between gap-2 mb-4">
        <div className="flex-1 bg-white/5 rounded-2xl p-3 text-center">
          <div className="text-lg font-semibold text-white">
            {isDataLoading ? (
              <span className="inline-block w-16 h-6 bg-white/10 animate-pulse rounded"></span>
            ) : (
              <span className={`${secondaryFont.className}`}>{`$${formatNumberWithSuffix(raisedUSD)}/$${formatNumberWithSuffix(goal)}`}</span>
            )}
          </div>
          <div className="text-xs text-gray-300 mt-1">
            Raised
          </div>
        </div>
        <div className="flex-1 bg-white/5 rounded-2xl p-3 text-center">
          <div className="text-lg font-semibold text-white">
            $<span className={`${secondaryFont.className}`}>{tokenPrice}</span>{" "}
            <span className="text-xs text-gray-300">USD</span>
          </div>
          <div className="text-xs text-gray-300 mt-1">Token Price</div>
        </div>
      </div>

      <div className="mt-2">
        <DooglyInterface
          apiUrl="https://api.doogly.org"
          config={{
            destinationChain: "8453",
            initialChainId: "8453",
            initialToken: "0xd07379a755a8f11b57610154861d694b2a0f615a",
            destinationAddress:
              process.env.NEXT_PUBLIC_LUMINACASA_DESTINATION_ADDRESS ??
              "******************************************",
            destinationOutputTokenAddress:
              "0xeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee",
          }}
        />
      </div>

      <RecentTransactionsCard
        transactions={transactions}
        isLoading={isTransactionsLoading}
      />
    </div>
  );
};

export default TokenSaleCard;
