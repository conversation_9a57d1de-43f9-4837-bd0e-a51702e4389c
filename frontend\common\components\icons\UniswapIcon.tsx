import type { SVGProps } from "react";
export const UniswapIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={32}
    height={32}
    fill="none"
    {...props}
  >
    <path
      fill="white"
      d="M11.21 6.161c-.292-.045-.305-.05-.168-.071.264-.041.885.014 1.313.116.999.238 1.908.847 2.879 1.93l.258.287.368-.06c1.554-.25 3.135-.05 4.457.561.364.169.937.504 1.01.59.022.027.064.204.092.392.098.652.05 1.152-.15 1.525-.108.203-.114.268-.041.442.058.138.22.241.38.24.329 0 .683-.532.846-1.272l.066-.294.129.146c.707.803 1.262 1.898 1.358 2.677l.025.203-.12-.185a2.276 2.276 0 0 0-.673-.708c-.474-.315-.976-.422-2.305-.492-1.2-.064-1.88-.166-2.553-.386-1.146-.374-1.723-.873-3.085-2.661-.604-.795-.978-1.234-1.35-1.588-.844-.805-1.674-1.227-2.737-1.392Z"
    />
    <path
      fill="white"
      d="M21.597 7.935c.03-.532.102-.883.247-1.204.057-.127.111-.23.12-.23.008 0-.017.093-.056.207-.105.312-.123.737-.05 1.233.092.628.144.719.808 1.397.31.319.672.72.803.893l.239.313-.238-.224c-.292-.274-.963-.808-1.111-.884-.1-.052-.114-.05-.175.01-.057.057-.069.141-.076.541-.013.623-.098 1.023-.302 1.423-.11.216-.128.17-.028-.074.075-.182.082-.262.082-.866-.002-1.212-.145-1.503-.988-2.002a9.227 9.227 0 0 0-.781-.406c-.217-.096-.389-.18-.382-.186.024-.024.845.216 1.176.343.492.19.573.215.633.192.04-.016.06-.132.079-.476ZM11.776 10.009c-.592-.817-.959-2.069-.88-3.004l.025-.29.135.025c.253.046.69.209.894.333.56.341.803.79 1.05 1.943.073.338.168.72.211.85.07.208.336.695.552 1.01.156.228.053.336-.291.305-.526-.047-1.237-.54-1.696-1.172ZM20.878 16.085c-2.767-1.116-3.741-2.084-3.741-3.719 0-.24.008-.437.018-.437.01 0 .117.08.238.177.561.45 1.19.643 2.929.897 1.024.15 1.6.27 2.131.447 1.689.561 2.734 1.7 2.983 3.25.073.45.03 1.296-.087 1.741-.093.352-.376.986-.45 1.01-.022.007-.042-.073-.047-.182-.029-.582-.323-1.149-.816-1.573-.561-.483-1.315-.868-3.158-1.61ZM18.936 16.549a4.939 4.939 0 0 0-.134-.586l-.07-.211.13.147c.182.204.326.465.447.813.093.265.104.344.103.776 0 .423-.012.512-.098.75a2.374 2.374 0 0 1-.584.931c-.506.515-1.155.8-2.093.919-.163.02-.639.055-1.056.077-1.053.055-1.746.168-2.368.387a.498.498 0 0 1-.178.043c-.025-.026.399-.279.749-.448.494-.238.985-.368 2.086-.551.544-.091 1.106-.201 1.248-.245 1.347-.414 2.04-1.48 1.818-2.802Z"
    />
    <path
      fill="white"
      d="M20.204 18.805c-.367-.791-.452-1.556-.25-2.268.021-.077.056-.139.077-.139.02 0 .107.047.193.105.169.114.509.306 1.414.8 1.13.617 1.774 1.095 2.212 1.64.383.479.62 1.023.735 1.687.065.376.027 1.28-.07 1.659-.303 1.194-1.01 2.131-2.016 2.678-.148.08-.28.146-.294.147-.015 0 .04-.137.12-.305.338-.709.376-1.398.12-2.166-.157-.47-.476-1.044-1.122-2.013-.75-1.127-.934-1.427-1.119-1.825ZM9.81 23.076c1.027-.868 2.305-1.485 3.469-1.674.502-.082 1.337-.05 1.802.07.744.19 1.41.618 1.756 1.127.339.498.484.931.635 1.896.06.381.125.763.145.85.113.5.334.9.608 1.1.435.32 1.184.339 1.92.051.125-.049.234-.082.242-.075.026.027-.345.276-.606.406a2.057 2.057 0 0 1-1.005.245c-.674 0-1.235-.344-1.702-1.045-.092-.138-.299-.551-.46-.918-.493-1.128-.737-1.471-1.31-1.847-.498-.327-1.141-.386-1.625-.148-.636.312-.813 1.126-.358 1.641.181.205.518.382.794.416a.848.848 0 0 0 .96-.85c0-.34-.13-.533-.457-.68-.448-.203-.928.033-.926.454.001.18.08.292.259.373.115.052.118.057.024.037-.41-.085-.507-.58-.177-.91.396-.394 1.215-.22 1.496.319.118.226.132.677.029.95-.23.608-.903.929-1.584.754-.465-.118-.654-.247-1.213-.824-.973-1.003-1.35-1.198-2.752-1.417l-.27-.042.307-.259Z"
    />
    <path
      fill="white"
      fillRule="evenodd"
      d="M2.477.6c3.248 3.948 8.255 10.095 8.504 10.44.205.283.128.539-.224.739-.195.111-.598.224-.799.224-.227 0-.484-.11-.67-.287-.133-.125-.665-.922-1.895-2.834a208.162 208.162 0 0 0-1.75-2.696c-.05-.047-.05-.045 1.654 3.007 1.069 1.916 1.43 2.594 1.43 2.684 0 .184-.05.281-.277.535-.379.423-.548.898-.67 1.881-.137 1.102-.522 1.88-1.588 3.213-.625.78-.727.923-.885 1.238-.198.395-.253.617-.275 1.117-.023.529.022.87.184 1.375.14.443.288.735.665 1.32.325.503.512.878.512 1.025 0 .116.022.117.526.003 1.205-.273 2.184-.753 2.735-1.34.34-.364.42-.565.423-1.063.002-.326-.01-.394-.098-.582-.143-.305-.404-.559-.979-.952-.752-.516-1.074-.931-1.163-1.502-.073-.468.012-.799.428-1.673.431-.905.538-1.29.61-2.203.047-.59.112-.822.281-1.008.176-.195.335-.261.771-.32.712-.098 1.165-.282 1.537-.626.323-.298.459-.586.48-1.018l.015-.328-.18-.21C11.125 9.994 2.039 0 1.999 0c-.009 0 .207.27.478.6ZM6.76 20.46a.58.58 0 0 0-.178-.764c-.234-.155-.597-.082-.597.12 0 .***************.**************.142.037.296-.104.155-.095.292.023.385.191.15.461.067.603-.184ZM12.408 13.112c-.334.103-.658.457-.759.83-.061.226-.026.624.065.747.149.198.292.25.68.248.761-.005 1.423-.332 1.5-.74.063-.336-.228-.8-.628-1.003-.206-.105-.645-.147-.858-.082Zm.89.697c.117-.167.065-.348-.134-.47-.38-.232-.954-.04-.954.32 0 .179.3.374.574.374.182 0 .433-.11.513-.224Z"
      clipRule="evenodd"
    />
  </svg>
);
