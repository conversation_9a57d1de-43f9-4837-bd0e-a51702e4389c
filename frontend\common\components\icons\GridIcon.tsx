import type { SVGProps } from "react";

export const GridIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <path
        fill="#FF808C"
        d="M8.09 13.438H1.98a.764.764 0 0 0-.765.763v3.82c0 .422.342.764.764.764H8.09a.764.764 0 0 0 .764-.764V14.2a.764.764 0 0 0-.764-.764Z"
      />
      <path
        fill="#FFBFC5"
        d="M5.035 13.438H1.979a.764.764 0 0 0-.764.763v3.82a.764.764 0 0 0 .764.764h3.056v-5.348Z"
      />
      <path
        fill="#78EB7B"
        d="M11.91 18.785h6.111a.764.764 0 0 0 .764-.764V9.618a.764.764 0 0 0-.764-.764H11.91a.764.764 0 0 0-.764.764v8.403c0 .422.342.764.764.764Z"
      />
      <path
        fill="#C9F7CA"
        d="M14.965 8.854H11.91a.764.764 0 0 0-.764.764v8.403a.764.764 0 0 0 .764.764h3.055v-9.93Z"
      />
      <path
        fill="#FFEF5E"
        d="M11.91 6.563h6.111a.764.764 0 0 0 .764-.764v-3.82a.764.764 0 0 0-.764-.764H11.91a.764.764 0 0 0-.764.764V5.8c0 .421.342.763.764.763Z"
      />
      <path
        fill="#FFF7AE"
        d="M14.965 1.215H11.91a.764.764 0 0 0-.764.764V5.8a.764.764 0 0 0 .764.763h3.055V1.215Z"
      />
      <path
        fill="#78EB7B"
        d="M8.09 1.215H1.98a.764.764 0 0 0-.765.764v8.403c0 .422.342.764.764.764H8.09a.764.764 0 0 0 .764-.764V1.979a.764.764 0 0 0-.764-.764Z"
      />
      <path
        fill="#C9F7CA"
        d="M5.035 1.215H1.979a.764.764 0 0 0-.764.764v8.403a.764.764 0 0 0 .764.764h3.056v-9.93Z"
      />
      <path
        stroke="#fff"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M8.09 13.438H1.98a.764.764 0 0 0-.765.763v3.82c0 .422.342.764.764.764H8.09a.764.764 0 0 0 .764-.764V14.2a.764.764 0 0 0-.764-.764ZM11.91 18.785h6.111a.764.764 0 0 0 .764-.764V9.618a.764.764 0 0 0-.764-.764H11.91a.764.764 0 0 0-.764.764v8.403c0 .422.342.764.764.764ZM11.91 6.563h6.111a.764.764 0 0 0 .764-.764v-3.82a.764.764 0 0 0-.764-.764H11.91a.764.764 0 0 0-.764.764V5.8c0 .421.342.763.764.763ZM8.09 1.215H1.98a.764.764 0 0 0-.765.764v8.403c0 .422.342.764.764.764H8.09a.764.764 0 0 0 .764-.764V1.979a.764.764 0 0 0-.764-.764Z"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h20v20H0z" />
      </clipPath>
    </defs>
  </svg>
)
