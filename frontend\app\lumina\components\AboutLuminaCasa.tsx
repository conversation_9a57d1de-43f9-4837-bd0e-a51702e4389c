'use client'

import {
  motion, useInView,
} from 'framer-motion';
import { useRef } from 'react';
import {
  Building2,
  Dna,
  Globe,
  Cog,
  Users,
  Sparkles,
  Flower,
  Gem,
  ShieldPlus,
  School,
} from 'lucide-react';

const AboutLuminaCasa = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, {
    once: true,
    amount: 0.2,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  const fundPillars = [
    {
      icon: <Building2 className="w-5 h-5 lg:w-8 lg:h-8 text-light-yellow" />,
      title: "Excellence Centers",
      description: "Regenerative sanctuaries for embodiment, wellness, innovation, and awakening (MVP live in Thailand)",
    },
    {
      icon: <Dna className="w-5 h-5 lg:w-8 lg:h-8 text-light-yellow" />,
      title: "Quantum Longevity Protocol",
      description: "Integrating cutting-edge science, ancient wisdom, and frequency-based medicine to enhance human vitality, joy, and lifespan",
    },
    {
      icon: <School className="w-5 h-5 lg:w-8 lg:h-8 text-light-yellow" />,
      title: "Light Portals & Mystery School Systems",
      description: "Sacred knowledge and multidimensional education accessed online and on-land",
    },
    {
      icon: <Cog className="w-5 h-5 lg:w-8 lg:h-8 text-light-yellow" />,
      title: "DAO & Ecosystem Build-Out",
      description: "Lotus Grid expansion, regenerative housing, decentralized governance & collective intelligence tools",
    },
    {
      icon: <Sparkles className="w-5 h-5 lg:w-8 lg:h-8 text-light-yellow" />,
      title: "Investment in Emergent Technologies",
      description: "Including consciousness-aligned AI, geometric computing, zero-point energy systems, and advanced resonance tools",
    },
    {
      icon: <Users className="w-5 h-5 lg:w-8 lg:h-8 text-light-yellow" />,
      title: "Organizational Backbone",
      description: "Legal integrity, team scaling, infrastructure, and soul-aligned operational protocols",
    },
  ];

  return (
    <section
      id="about"
      ref={sectionRef}
      className="py-4 lg:py-16 relative w-full container px-2 md:px-8 mx-auto !max-w-[1200px]"
    >
      <div className="">
        <div className="mb-8 lg:mb-12 flex flex-col">
          <h2 className={`text-xl mb-2 md:text-2xl lg:text-3xl font-semibold text-white`}>
            What is Lumina CASA?
          </h2>
          <div className="rounded-full h-1 w-20 bg-gradient-to-tr from-light-yellow to-yellow-600 mb-6"></div>
          <p className="text-neutral-300 mb-4 text-sm md:text-base max-w-3xl">
            Lumina CASA is the quantum value container of the Lumina ecosystem — a multi-dimensional architecture uniting:
          </p>
          <motion.ul
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.15,
                },
              },
            }}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            className="text-neutral-300 text-sm md:text-base max-w-3xl space-y-3 mb-6"
          >
            <motion.li
              variants={{
                hidden: {
                  opacity: 0,
                  x: -10,
                },
                visible: {
                  opacity: 1,
                  x: 0,
                  transition: {
                    duration: 0.5,
                  },
                },
              }}
              className="flex items-center gap-3 group"
            >
              <div className="p-1.5 rounded-full bg-yellow-500/10 text-light-yellow group-hover:bg-yellow-500/20 transition-all duration-300">
                <ShieldPlus className="w-4 h-4" />
              </div>
              <span className="group-hover:text-white transition-colors duration-300">Healing technologies & bio-resonance systems</span>
            </motion.li>
            <motion.li
              variants={{
                hidden: {
                  opacity: 0,
                  x: -10,
                },
                visible: {
                  opacity: 1,
                  x: 0,
                  transition: {
                    duration: 0.5,
                  },
                },
              }}
              className="flex items-center gap-3 group"
            >
              <div className="p-1.5 rounded-full bg-yellow-500/10 text-light-yellow group-hover:bg-yellow-500/20 transition-all duration-300">
                <Gem className="w-4 h-4" />
              </div>
              <span className="group-hover:text-white transition-colors duration-300">Sacred geometry & energetic coherence</span>
            </motion.li>
            <motion.li
              variants={{
                hidden: {
                  opacity: 0,
                  x: -10,
                },
                visible: {
                  opacity: 1,
                  x: 0,
                  transition: {
                    duration: 0.5,
                  },
                },
              }}
              className="flex items-center gap-3 group"
            >
              <div className="p-1.5 rounded-full bg-yellow-500/10 text-light-yellow group-hover:bg-yellow-500/20 transition-all duration-300">
                <Flower className="w-4 h-4" />
              </div>
              <span className="group-hover:text-white transition-colors duration-300">Regenerative retreats & intentional sanctuaries</span>
            </motion.li>
            <motion.li
              variants={{
                hidden: {
                  opacity: 0,
                  x: -10,
                },
                visible: {
                  opacity: 1,
                  x: 0,
                  transition: {
                    duration: 0.5,
                  },
                },
              }}
              className="flex items-center gap-3 group"
            >
              <div className="p-1.5 rounded-full bg-yellow-500/10 text-light-yellow group-hover:bg-yellow-500/20 transition-all duration-300">
                <School className="w-4 h-4" />
              </div>
              <span className="group-hover:text-white transition-colors duration-300">Conscious learning & digital mystery schools</span>
            </motion.li>
            <motion.li
              variants={{
                hidden: {
                  opacity: 0,
                  x: -10,
                },
                visible: {
                  opacity: 1,
                  x: 0,
                  transition: {
                    duration: 0.5,
                  },
                },
              }}
              className="flex items-center gap-3 group"
            >
              <div className="p-1.5 rounded-full bg-yellow-500/10 text-light-yellow group-hover:bg-yellow-500/20 transition-all duration-300">
                <Globe className="w-4 h-4" />
              </div>
              <span className="group-hover:text-white transition-colors duration-300">Community-based business & decentralized governance</span>
            </motion.li>
          </motion.ul>
          <p className="text-neutral-300 text-sm md:text-base max-w-3xl">
            Each token is a vessel for vibrational integrity — carrying <b className='text-white'>peace, power, prosperity, and planetary</b> service. CASA is a unit of sacred transaction and a living light node within the global frequency grid.
          </p>
        </div>

        <div className="text-center mb-8 lg:mb-12 flex flex-col items-center">
          <h2 className={`text-xl text-center md:text-2xl lg:text-3xl font-semibold text-white my-2`}>
            Use of Funds
          </h2>
          <div className="rounded-full h-1 w-20 bg-gradient-to-tr from-light-yellow to-yellow-600 mx-auto mb-6"></div>
          <p className="text-neutral-300 text-center text-sm md:text-base max-w-3xl">
            Your investment supports the sacred architecture of Lumina CASA across six key pillars:
          </p>
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6"
        >
          {fundPillars.map((pillar, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-violets-are-blue/5 border border-violets-are-blue/15 rounded-3xl p-4 lg:p-6 transition-all duration-300 flex flex-col"
            >
              <div className="mb-2 lg:mb-4 flex justify-center">
                <div className="p-3 lg:p-4 rounded-full bg-yellow-500/5 inline-block">
                  {pillar.icon}
                </div>
              </div>
              <h3 className={`text-lg lg:text-xl font-semibold text-white mb-1 lg:mb-4 text-center`}>
                {pillar.title}
              </h3>
              <p className="text-xs lg:text-sm text-gray-300 text-center flex-grow">
                {pillar.description}
              </p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default AboutLuminaCasa;
