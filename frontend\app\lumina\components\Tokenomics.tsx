'use client'

import {
  motion, useInView,
} from 'framer-motion';
import {
  useRef, useState,
} from 'react';
import { secondaryFont } from '@/common/utils/localFont';
import Link from 'next/link';

const Tokenomics = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, {
    once: true,
    amount: 0.2,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  const tokenDistribution = [
    {
      name: "Public Sale",
      percentage: 25,
      color: "bg-light-yellow",
      hexColor: "#ffadad",
    },
    {
      name: "Sacred Infrastructure",
      percentage: 25,
      color: "bg-amber-600",
      hexColor: "#ffd6a5",
    },
    {
      name: "Team & Advisors",
      percentage: 15,
      color: "bg-yellow-300",
      hexColor: "#fdffb6",
    },
    {
      name: "Liquidity Pool",
      percentage: 11,
      color: "bg-amber-200",
      hexColor: "#caffbf",
    },
    {
      name: "Community Rewards",
      percentage: 10,
      color: "bg-yellow-600",
      hexColor: "#9bf6ff",
    },
    {
      name: "Reserve Fund",
      percentage: 9,
      color: "bg-yellow-500",
      hexColor: "#a0c4ff",
    },
    {
      name: "Ecosystem & Partnerships",
      percentage: 5,
      color: "bg-amber-700",
      hexColor: "#bdb2ff",
    },
  ];

  const calculateCoordinatesForPercent = (percent: number, innerRadius: number, outerRadius: number) => {
    const x = Math.cos(2 * Math.PI * percent);
    const y = Math.sin(2 * Math.PI * percent);
    return {
      inner: [x * innerRadius, y * innerRadius],
      outer: [x * outerRadius, y * outerRadius],
    };
  };

  const PieChart = () => {
    const [hoveredSegment, setHoveredSegment] = useState<string | null>(null);

    const innerRadius = 0.55;
    const outerRadius = 0.9;

    let cumulativePercent = 0;
    const segments = tokenDistribution.map((item, i) => {
      const startPercent = cumulativePercent;
      const endPercent = startPercent + (item.percentage / 100);

      const startCoords = calculateCoordinatesForPercent(startPercent, innerRadius, outerRadius);
      const endCoords = calculateCoordinatesForPercent(endPercent, innerRadius, outerRadius);

      const largeArcFlag = item.percentage > 50 ? 1 : 0;

      const pathData = [
        `M ${startCoords.outer[0]} ${startCoords.outer[1]}`,
        `A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${endCoords.outer[0]} ${endCoords.outer[1]}`,
        `L ${endCoords.inner[0]} ${endCoords.inner[1]}`,
        `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${startCoords.inner[0]} ${startCoords.inner[1]}`,
        `Z`,
      ].join(' ');

      cumulativePercent = endPercent;

      const isHovered = hoveredSegment === item.name;

      return (
        <path
          key={i}
          d={pathData}
          fill={item.hexColor}
          stroke="#0e1111"
          strokeWidth="0.005"
          style={{
            transformBox: 'fill-box',
            transformOrigin: 'center',
            transform: isHovered ? 'scale(1.02)' : 'scale(1)',
            transition: 'transform 0.2s ease-in-out, opacity 0.2s ease-in-out',
            cursor: 'pointer',
            zIndex: isHovered ? 100 : tokenDistribution.length - i,
            opacity: hoveredSegment ? (isHovered ? 1 : 0.7) : 0.9,
            position: 'relative', 
          }}
          onMouseEnter={() => setHoveredSegment(item.name)}
          onMouseLeave={() => setHoveredSegment(null)}
        />
      );
    });

    return (
      <div className="relative w-full h-full">
        <svg
          viewBox="-1.05 -1.05 2.1 2.1"
          style={{ transform: 'rotate(-90deg)' }}
          className="w-full h-full"
          preserveAspectRatio="xMidYMid meet"
        >
          <g>{segments}</g>
        </svg>

        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="text-center">
            {hoveredSegment ? (
              <>
                <p className={`text-light-yellow text-2xl lg:text-3xl font-semibold mb-1 ${secondaryFont.className}`}>
                  {tokenDistribution.find(item => item.name === hoveredSegment)?.percentage}%
                </p>
                <p className="text-gray-300 text-sm max-w-[80px] sm:max-w-[150px]">
                  {tokenDistribution.find(item => item.name === hoveredSegment)?.name}
                </p>
              </>
            ) : (
              <>
                <p className={`text-light-yellow text-2xl lg:text-3xl font-semibold mb-1 ${secondaryFont.className}`}>
                  1B
                </p>
                <p className="text-gray-300 text-sm">
                  Total Supply
                </p>
              </>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <section
      id="token"
      ref={sectionRef}
      className="py-4 lg:py-16 relative w-full container px-2 md:px-8 mx-auto !max-w-[1200px]"
    >
      <div className="">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className=""
        >
          <div className="mb-8">
            <div className="w-min whitespace-nowrap bg-violets-are-blue/20 rounded-full px-4 py-1 mb-2 lg:mb-4 text-xs lg:text-sm text-light-yellow font-medium">
              Tokenomics
            </div>
            <h2 className={`text-xl md:text-2xl lg:text-3xl font-semibold text-white mb-4`}>
              <span className={`${secondaryFont.className} text-shadow-glow font-semibold text-light-yellow`}>$CASA </span>
              Token
            </h2>
            <p className="text-gray-300 max-w-3xl">
              CASA Token offers onboarding to our LUMOS ecosystem with the potential for future governance rights
            </p>
            <Link
              href="https://dreamstartr.com/LUMINA_CASA_Quantum_Longevity_Scientific_White_Paper.pdf"
              rel="noopener noreferrer"
              className={`w-full mt-2 sm:w-min whitespace-nowrap block text-center py-2 px-6 bg-violets-are-blue/5 border border-white/20 hover:bg-yellow-200/80 hover:text-black text-white font-medium rounded-xl transition-all duration-300`}
            >
              View Protocol
            </Link>
          </div>

          <div className="mb-4 lg:mb-12 w-full">
            <motion.div
              variants={itemVariants}
              className="flex-1"
            >
              <div className="bg-violets-are-blue/5 relative border border-violets-are-blue/15 rounded-3xl px-6 py-4">
                <h3 className="text-xl font-semibold text-white mb-6">
                  Token Allocation
                </h3>
                <div className="absolute top-4 right-6 text-right">
                  <h3 className="text-light-yellow text-2xl lg:text-3xl font-bold mb-1">$0.022</h3>
                  <p className="text-gray-400 text-sm">Initial Token Price</p>
                </div>
                <div className="flex sm:flex-row flex-col gap-8 mb-4 sm:mb-12 w-full sm:justify-center items-center">
                  <div
                    className="w-[220px] sm:w-[320px] h-[220px] sm:h-[320px] relative p-4"
                  >
                    <PieChart />
                  </div>
                  <div className="mt-4 flex flex-col gap-2">
                    {tokenDistribution.map((item, index) => (
                      <div key={index} className="flex items-center">
                        <span className="inline-block w-3 h-3 rounded-full mr-2" style={{ backgroundColor: item.hexColor }}></span>
                        <span className="text-sm text-gray-300">{item.name}: <span className="text-white font-medium">{item.percentage}%</span></span>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="mt-10">
                  <h3 className="text-lg font-semibold text-white mb-4">
                    Vesting Schedule
                  </h3>
                  <ul className="space-y-3">
                    <li className="flex items-center gap-2">
                      <span className="inline-block w-3 h-3 rounded-full" style={{ backgroundColor: "#ffadad" }}></span>
                      <span className="text-sm text-gray-300">Public sale: 25% at TGE, 75% linear over 6 months</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="inline-block w-3 h-3 rounded-full" style={{ backgroundColor: "#fdffb6" }}></span>
                      <span className="text-sm text-gray-300">Team & Advisors: 12-month cliff, linear over 36 months</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="inline-block w-3 h-3 rounded-full" style={{ backgroundColor: "#a0c4ff" }}></span>
                      <span className="text-sm text-gray-300">Reserve Fund: 10% at TGE, linear over 48 months</span>
                    </li>
                  </ul>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Tokenomics;
