'use client'
import {
  AnimatedText, Link,
} from '@/common/components/atoms';
import { routes } from '@/common/routes';
import { 
  AnalyticsBoardIcon,
  CheckSquareIcon,
  ComputerChipFlashIcon, HRTeamIcon, LogoIcon,
} from '@/common/components/icons';


const AboutClient = () => {

  const features = [
    {
      icon: <ComputerChipFlashIcon />,
      title: "AI-Powered Creation",
      description: "Our AI assistant helps refine concepts, generate content, and create professional project assets to bring your dreams to life.",
    },
    {
      icon: <AnalyticsBoardIcon />,
      title: "Bonding Curves",
      description: "Fair and transparent token pricing through automated market makers, rewarding early supporters while maintaining sustainable growth.",
    },
    {
      icon: <HRTeamIcon />,
      title: "AI Community Growth",
      description: "Dedicated AI agents help build and engage your community through automated updates, responses, and content sharing.",
    },
    {
      icon: <CheckSquareIcon />,
      title: "Community Validation",
      description: "Projects grow through transparent community feedback, governance, and milestone tracking.",
    },
  ];

  return (
    <div className="min-h-screen pt-20 md:pt-32 overflow-hidden">
      <div className="absolute -z-10 inset-0 w-full h-full bg-gradient-to-br from-eerie-black via-space-cadet/30 to-eerie-black opacity-80"></div>
      <div className="absolute -z-10 top-0 right-0 w-2/3 h-2/3 bg-gradient-to-bl from-han-purple/10 via-transparent to-transparent opacity-50"></div>
      <div className="absolute -z-10 bottom-0 left-0 w-2/3 h-2/3 bg-gradient-to-tr from-tulip/10 via-transparent to-transparent opacity-50"></div>
      <div className="container mx-auto px-2 md:px-8 mb-10">
        <div className="max-w-4xl mx-auto">
          <section className="text-center mt-8 md:mt-0 mb-8 flex flex-col items-center">
            <LogoIcon />
            <h1 className="text-xl md:text-2xl lg:text-3xl font-semibold text-white mt-4">
              About DreamStartr
            </h1>
            <div className='rounded-full h-1 w-20 bg-gradient-to-tr from-han-purple to-tulip my-2'></div>
            <p className="text-gray-300 text-sm md:text-base">
              DreamStartr is revolutionizing how ideas become reality by combining the power of AI,
              community validation, and decentralized finance. Our platform empowers you to transform
              your dreams into successful projects with the support of an engaged community.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-lg font-semibold text-white mb-2">How DreamStartr Works</h2>
            <div className="grid md:grid-cols-2 gap-4">
              {features.map((feature, index) => (
                <div key={index} className="bg-violets-are-blue/5 border-white/5 border rounded-3xl p-4">
                  <div className="mb-4 flex gap-2 items-center">
                    {feature.icon}
                    <h3 className="font-semibold text-white">
                      {feature.title}
                    </h3>
                  </div>
                  <p className="text-sm text-gray-300">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </section>

          <section className="mb-8">
            <h2 className="text-lg font-semibold text-white mb-2">Understanding Bonding Curves</h2>
            <div className="bg-violets-are-blue/5 border-white/5 border rounded-3xl p-4 text-sm">
              <p className="text-gray-300 mb-4">
                We use a unique bonding curve mechanism that makes investing fair and transparent:
              </p>
              <ul className="text-gray-300 space-y-2 mb-4">
                <li className="flex items-start">
                  <span className="font-bold mr-2">•</span>
                  <span>The token price increases gradually as more tokens are purchased</span>
                </li>
                <li className="flex items-start">
                  <span className="font-bold mr-2">•</span>
                  <span>Early supporters get better prices for believing in projects early</span>
                </li>
                <li className="flex items-start">
                  <span className="font-bold mr-2">•</span>
                  <span>You can buy or sell tokens at any time through our automated market maker</span>
                </li>
                <li className="flex items-start">
                  <span className="font-bold mr-2">•</span>
                  <span>The bonding curve ensures there's always liquidity for trading</span>
                </li>
              </ul>
              <p className="text-gray-300">
                For example: If a project's initial token price is $0.10, it might increase to $0.15 after 1,000 tokens
                are purchased, and $0.22 after 2,000 tokens. This rewards early supporters while maintaining a
                sustainable price discovery mechanism.
              </p>
            </div>
          </section>

          <section className="mb-8">
            <h2 className="text-lg font-semibold text-white mb-2">Platform Benefits</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-violets-are-blue/5 border-white/5 border rounded-3xl p-4">
                <h3 className="font-semibold text-white mb-3">
                  <AnimatedText text="For Creators" />
                </h3>
                <ul className="text-gray-300 space-y-1">
                  <li className="flex items-start text-sm">
                    <span className="font-bold mr-2">•</span>
                    <span>Transform your ideas into reality with AI assistance</span>
                  </li>
                  <li className="flex items-start text-sm">
                    <span className="font-bold mr-2">•</span>
                    <span>Get instant access to a supportive community</span>
                  </li>
                  <li className="flex items-start text-sm">
                    <span className="font-bold mr-2">•</span>
                    <span>Secure funding through fair token distribution</span>
                  </li>
                  <li className="flex items-start text-sm">
                    <span className="font-bold mr-2">•</span>
                    <span>Maintain creative control of your project</span>
                  </li>
                  <li className="flex items-start text-sm">
                    <span className="font-bold mr-2">•</span>
                    <span>Access powerful tools for community building</span>
                  </li>
                </ul>
              </div>

              <div className="bg-violets-are-blue/5 border-white/5 border rounded-3xl p-4">
                <h3 className="font-semibold text-white mb-3">
                  <AnimatedText text="For Supporters" />
                </h3>
                <ul className="text-gray-300 space-y-1">
                  <li className="flex items-start text-sm">
                    <span className="font-bold mr-2">•</span>
                    <span>Discover innovative projects early</span>
                  </li>
                  <li className="flex items-start text-sm">
                    <span className="font-bold mr-2">•</span>
                    <span>Support ideas you believe in</span>
                  </li>
                  <li className="flex items-start text-sm">
                    <span className="font-bold mr-2">•</span>
                    <span>Earn potential returns through bonding curves</span>
                  </li>
                  <li className="flex items-start text-sm">
                    <span className="font-bold mr-2">•</span>
                    <span>Participate in project governance</span>
                  </li>
                  <li className="flex items-start text-sm">
                    <span className="font-bold mr-2">•</span>
                    <span>Help shape the future of exciting projects</span>
                  </li>
                </ul>
              </div>
            </div>
          </section>

          <section className="mb-8">
            <h2 className="text-lg font-semibold text-white mb-2">Getting Started</h2>
            <div className="bg-violets-are-blue/5 border-white/5 border rounded-3xl p-4">
              <h3 className="font-semibold text-white mb-4">Three Simple Steps:</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="text-sm font-semibold text-white mb-2">1. Connect Your Wallet/Create your Wallet</h4>
                  <p className="text-gray-300 text-xs sm:text-sm">
                    Sign in by connecting your Web3 wallet or create a new one using your email.
                    You'll have to also load this wallet with a small amount of POL tokens after creating a wallet.
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-white mb-2">2. Create or Support</h4>
                  <p className="text-gray-300 text-xs sm:text-sm">
                    Choose to either launch your own dream or browse existing projects to support.
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-white mb-2">3. Join the Community</h4>
                  <p className="text-gray-300 text-xs sm:text-sm">
                    Engage with creators, supporters, and AI agents to help projects succeed.
                  </p>
                </div>
              </div>
            </div>
          </section>

          <section className="mb-8">
            <h2 className="text-lg font-semibold text-white mb-2">Our Vision</h2>
            <div className="bg-violets-are-blue/5 border-white/5 border rounded-3xl p-4 text-sm">
              <p className="text-gray-300 mb-4">
                DreamStartr believes that great ideas can come from anywhere. By combining AI with
                community-driven development and decentralized finance, we're creating an ecosystem
                where innovation thrives and dreams become reality.
              </p>
              <p className="text-gray-300 mb-2">We're committed to:</p>
              <ul className="text-gray-300 space-y-2">
                <li className="flex items-start">
                  <span className="font-bold mr-2">•</span>
                  <span>Making dream-building accessible to everyone</span>
                </li>
                <li className="flex items-start">
                  <span className="font-bold mr-2">•</span>
                  <span>Fostering transparent and fair project development</span>
                </li>
                <li className="flex items-start">
                  <span className="font-bold mr-2">•</span>
                  <span>Building a supportive community of creators and supporters</span>
                </li>
                <li className="flex items-start">
                  <span className="font-bold mr-2">•</span>
                  <span>Using AI to reduce barriers to entry</span>
                </li>
                <li className="flex items-start">
                  <span className="font-bold mr-2">•</span>
                  <span>Empowering the next generation of innovators</span>
                </li>
              </ul>
            </div>
          </section>

          <section className="text-center mb-16">
            <h2 className="text-xl sm:text-2xl font-semibold text-white mb-6">
              Join us in building the future, one dream at a time.
            </h2>
            <div className="flex gap-4 flex-col justify-center md:flex-row">
              <Link
                href={routes.createProjectPath}
                variant='gradient'
                size='lg'
                prefetch={true}
              >
                Generate Dream
              </Link>
              <Link
                href={routes.viewProjectsPath}
                variant='outline'
                size='lg'
                prefetch={true}
         
              >
                Dreams Gallery
              </Link>
            </div>
          </section>

          <section className="text-center mb-8">
            <p className="text-gray-300 text-sm sm:text-base">
              DreamStartr is democratizing dream-building through AI-assisted development and community validation.
            </p>
          </section>
        </div>
      </div>
    </div>
  );
};

export default AboutClient;
