/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@coinbase";
exports.ids = ["vendor-chunks/@coinbase"];
exports.modules = {

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/node_modules/clsx/dist/clsx.m.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/node_modules/clsx/dist/clsx.m.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clsx: () => (/* binding */ clsx),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f);else for(t in e)e[t]&&(n&&(n+=\" \"),n+=t);return n}function clsx(){for(var e,t,f=0,n=\"\";f<arguments.length;)(e=arguments[f++])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clsx);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvbm9kZV9tb2R1bGVzL2Nsc3gvZGlzdC9jbHN4Lm0uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxjQUFjLGFBQWEsK0NBQStDLHVEQUF1RCxXQUFXLDBDQUEwQyx5Q0FBeUMsU0FBZ0IsZ0JBQWdCLHFCQUFxQixtQkFBbUIsa0RBQWtELFNBQVMsaUVBQWUsSUFBSSIsInNvdXJjZXMiOlsid2VicGFjazovL2RyZWFtc3RhcnRyLy4vbm9kZV9tb2R1bGVzL0Bjb2luYmFzZS93YWxsZXQtc2RrL25vZGVfbW9kdWxlcy9jbHN4L2Rpc3QvY2xzeC5tLmpzPzdlNzQiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gcihlKXt2YXIgdCxmLG49XCJcIjtpZihcInN0cmluZ1wiPT10eXBlb2YgZXx8XCJudW1iZXJcIj09dHlwZW9mIGUpbis9ZTtlbHNlIGlmKFwib2JqZWN0XCI9PXR5cGVvZiBlKWlmKEFycmF5LmlzQXJyYXkoZSkpZm9yKHQ9MDt0PGUubGVuZ3RoO3QrKyllW3RdJiYoZj1yKGVbdF0pKSYmKG4mJihuKz1cIiBcIiksbis9Zik7ZWxzZSBmb3IodCBpbiBlKWVbdF0mJihuJiYobis9XCIgXCIpLG4rPXQpO3JldHVybiBufWV4cG9ydCBmdW5jdGlvbiBjbHN4KCl7Zm9yKHZhciBlLHQsZj0wLG49XCJcIjtmPGFyZ3VtZW50cy5sZW5ndGg7KShlPWFyZ3VtZW50c1tmKytdKSYmKHQ9cihlKSkmJihuJiYobis9XCIgXCIpLG4rPXQpO3JldHVybiBufWV4cG9ydCBkZWZhdWx0IGNsc3g7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/node_modules/clsx/dist/clsx.m.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/abi.cjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/abi.cjs ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Extracted from https://github.com/ethereumjs/ethereumjs-abi and stripped out irrelevant code\n// Original code licensed under the MIT License - Copyright (c) 2015 Alex Beregszaszi\n\n/* eslint-disable */\n//prettier-ignore\nconst util = __webpack_require__(/*! ./util.cjs */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/util.cjs\")\n\n// Convert from short to canonical names\n// FIXME: optimise or make this nicer?\nfunction elementaryName (name) {\n  if (name.startsWith('int[')) {\n    return 'int256' + name.slice(3)\n  } else if (name === 'int') {\n    return 'int256'\n  } else if (name.startsWith('uint[')) {\n    return 'uint256' + name.slice(4)\n  } else if (name === 'uint') {\n    return 'uint256'\n  } else if (name.startsWith('fixed[')) {\n    return 'fixed128x128' + name.slice(5)\n  } else if (name === 'fixed') {\n    return 'fixed128x128'\n  } else if (name.startsWith('ufixed[')) {\n    return 'ufixed128x128' + name.slice(6)\n  } else if (name === 'ufixed') {\n    return 'ufixed128x128'\n  }\n  return name\n}\n\n// Parse N from type<N>\nfunction parseTypeN (type) {\n  return Number.parseInt(/^\\D+(\\d+)$/.exec(type)[1], 10)\n}\n\n// Parse N,M from type<N>x<M>\nfunction parseTypeNxM (type) {\n  var tmp = /^\\D+(\\d+)x(\\d+)$/.exec(type)\n  return [ Number.parseInt(tmp[1], 10), Number.parseInt(tmp[2], 10) ]\n}\n\n// Parse N in type[<N>] where \"type\" can itself be an array type.\nfunction parseTypeArray (type) {\n  var tmp = type.match(/(.*)\\[(.*?)\\]$/)\n  if (tmp) {\n    return tmp[2] === '' ? 'dynamic' : Number.parseInt(tmp[2], 10)\n  }\n  return null\n}\n\nfunction parseNumber (arg) {\n  var type = typeof arg\n  if (type === 'string' || type === 'number') {\n    return BigInt(arg)\n  } else if (type === 'bigint') {\n    return arg\n  } else {\n    throw new Error('Argument is not a number')\n  }\n}\n\n// Encodes a single item (can be dynamic array)\n// @returns: Buffer\nfunction encodeSingle (type, arg) {\n  var size, num, ret, i\n\n  if (type === 'address') {\n    return encodeSingle('uint160', parseNumber(arg))\n  } else if (type === 'bool') {\n    return encodeSingle('uint8', arg ? 1 : 0)\n  } else if (type === 'string') {\n    return encodeSingle('bytes', new Buffer(arg, 'utf8'))\n  } else if (isArray(type)) {\n    // this part handles fixed-length ([2]) and variable length ([]) arrays\n    // NOTE: we catch here all calls to arrays, that simplifies the rest\n    if (typeof arg.length === 'undefined') {\n      throw new Error('Not an array?')\n    }\n    size = parseTypeArray(type)\n    if (size !== 'dynamic' && size !== 0 && arg.length > size) {\n      throw new Error('Elements exceed array size: ' + size)\n    }\n    ret = []\n    type = type.slice(0, type.lastIndexOf('['))\n    if (typeof arg === 'string') {\n      arg = JSON.parse(arg)\n    }\n    for (i in arg) {\n      ret.push(encodeSingle(type, arg[i]))\n    }\n    if (size === 'dynamic') {\n      var length = encodeSingle('uint256', arg.length)\n      ret.unshift(length)\n    }\n    return Buffer.concat(ret)\n  } else if (type === 'bytes') {\n    arg = new Buffer(arg)\n\n    ret = Buffer.concat([ encodeSingle('uint256', arg.length), arg ])\n\n    if ((arg.length % 32) !== 0) {\n      ret = Buffer.concat([ ret, util.zeros(32 - (arg.length % 32)) ])\n    }\n\n    return ret\n  } else if (type.startsWith('bytes')) {\n    size = parseTypeN(type)\n    if (size < 1 || size > 32) {\n      throw new Error('Invalid bytes<N> width: ' + size)\n    }\n\n    return util.setLengthRight(arg, 32)\n  } else if (type.startsWith('uint')) {\n    size = parseTypeN(type)\n    if ((size % 8) || (size < 8) || (size > 256)) {\n      throw new Error('Invalid uint<N> width: ' + size)\n    }\n\n    num = parseNumber(arg)\n    const bitLength = util.bitLengthFromBigInt(num)\n    if (bitLength > size) {\n      throw new Error('Supplied uint exceeds width: ' + size + ' vs ' + bitLength)\n    }\n\n    if (num < 0) {\n      throw new Error('Supplied uint is negative')\n    }\n\n    return util.bufferBEFromBigInt(num, 32);\n  } else if (type.startsWith('int')) {\n    size = parseTypeN(type)\n    if ((size % 8) || (size < 8) || (size > 256)) {\n      throw new Error('Invalid int<N> width: ' + size)\n    }\n\n    num = parseNumber(arg)\n    const bitLength = util.bitLengthFromBigInt(num)\n    if (bitLength > size) {\n      throw new Error('Supplied int exceeds width: ' + size + ' vs ' + bitLength)\n    }\n\n    const twos = util.twosFromBigInt(num, 256);\n\n    return util.bufferBEFromBigInt(twos, 32);\n  } else if (type.startsWith('ufixed')) {\n    size = parseTypeNxM(type)\n\n    num = parseNumber(arg)\n\n    if (num < 0) {\n      throw new Error('Supplied ufixed is negative')\n    }\n\n    return encodeSingle('uint256', num * BigInt(2) ** BigInt(size[1]))\n  } else if (type.startsWith('fixed')) {\n    size = parseTypeNxM(type)\n\n    return encodeSingle('int256', parseNumber(arg) * BigInt(2) ** BigInt(size[1]))\n  }\n\n  throw new Error('Unsupported or invalid type: ' + type)\n}\n\n// Is a type dynamic?\nfunction isDynamic (type) {\n  // FIXME: handle all types? I don't think anything is missing now\n  return (type === 'string') || (type === 'bytes') || (parseTypeArray(type) === 'dynamic')\n}\n\n// Is a type an array?\nfunction isArray (type) {\n  return type.lastIndexOf(']') === type.length - 1\n}\n\n// Encode a method/event with arguments\n// @types an array of string type names\n// @args  an array of the appropriate values\nfunction rawEncode (types, values) {\n  var output = []\n  var data = []\n\n  var headLength = 32 * types.length\n\n  for (var i in types) {\n    var type = elementaryName(types[i])\n    var value = values[i]\n    var cur = encodeSingle(type, value)\n\n    // Use the head/tail method for storing dynamic data\n    if (isDynamic(type)) {\n      output.push(encodeSingle('uint256', headLength))\n      data.push(cur)\n      headLength += cur.length\n    } else {\n      output.push(cur)\n    }\n  }\n\n  return Buffer.concat(output.concat(data))\n}\n\nfunction solidityPack (types, values) {\n  if (types.length !== values.length) {\n    throw new Error('Number of types are not matching the values')\n  }\n\n  var size, num\n  var ret = []\n\n  for (var i = 0; i < types.length; i++) {\n    var type = elementaryName(types[i])\n    var value = values[i]\n\n    if (type === 'bytes') {\n      ret.push(value)\n    } else if (type === 'string') {\n      ret.push(new Buffer(value, 'utf8'))\n    } else if (type === 'bool') {\n      ret.push(new Buffer(value ? '01' : '00', 'hex'))\n    } else if (type === 'address') {\n      ret.push(util.setLength(value, 20))\n    } else if (type.startsWith('bytes')) {\n      size = parseTypeN(type)\n      if (size < 1 || size > 32) {\n        throw new Error('Invalid bytes<N> width: ' + size)\n      }\n\n      ret.push(util.setLengthRight(value, size))\n    } else if (type.startsWith('uint')) {\n      size = parseTypeN(type)\n      if ((size % 8) || (size < 8) || (size > 256)) {\n        throw new Error('Invalid uint<N> width: ' + size)\n      }\n\n      num = parseNumber(value)\n      const bitLength = util.bitLengthFromBigInt(num)\n      if (bitLength > size) {\n        throw new Error('Supplied uint exceeds width: ' + size + ' vs ' + bitLength)\n      }\n\n      ret.push(util.bufferBEFromBigInt(num, size / 8))\n    } else if (type.startsWith('int')) {\n      size = parseTypeN(type)\n      if ((size % 8) || (size < 8) || (size > 256)) {\n        throw new Error('Invalid int<N> width: ' + size)\n      }\n\n      num = parseNumber(value)\n      const bitLength = util.bitLengthFromBigInt(num)\n      if (bitLength > size) {\n        throw new Error('Supplied int exceeds width: ' + size + ' vs ' + bitLength)\n      }\n\n      const twos = util.twosFromBigInt(num, size);\n      ret.push(util.bufferBEFromBigInt(twos, size / 8))\n    } else {\n      // FIXME: support all other types\n      throw new Error('Unsupported or invalid type: ' + type)\n    }\n  }\n\n  return Buffer.concat(ret)\n}\n\nfunction soliditySHA3 (types, values) {\n  return util.keccak(solidityPack(types, values))\n}\n\nmodule.exports = {\n  rawEncode,\n  solidityPack,\n  soliditySHA3\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/abi.cjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/index.cjs":
/*!************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/index.cjs ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint-disable */\n//prettier-ignore\n\nconst util = __webpack_require__(/*! ./util.cjs */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/util.cjs\")\nconst abi = __webpack_require__(/*! ./abi.cjs */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/abi.cjs\")\n\nconst TYPED_MESSAGE_SCHEMA = {\n  type: 'object',\n  properties: {\n    types: {\n      type: 'object',\n      additionalProperties: {\n        type: 'array',\n        items: {\n          type: 'object',\n          properties: {\n            name: {type: 'string'},\n            type: {type: 'string'},\n          },\n          required: ['name', 'type'],\n        },\n      },\n    },\n    primaryType: {type: 'string'},\n    domain: {type: 'object'},\n    message: {type: 'object'},\n  },\n  required: ['types', 'primaryType', 'domain', 'message'],\n}\n\n/**\n * A collection of utility functions used for signing typed data\n */\nconst TypedDataUtils = {\n  /**\n   * Encodes an object by encoding and concatenating each of its members\n   *\n   * @param {string} primaryType - Root type\n   * @param {Object} data - Object to encode\n   * @param {Object} types - Type definitions\n   * @returns {string} - Encoded representation of an object\n   */\n  encodeData (primaryType, data, types, useV4 = true) {\n    const encodedTypes = ['bytes32']\n    const encodedValues = [this.hashType(primaryType, types)]\n\n    if(useV4) {\n      const encodeField = (name, type, value) => {\n        if (types[type] !== undefined) {\n          return ['bytes32', value == null ?\n            '0x0000000000000000000000000000000000000000000000000000000000000000' :\n            util.keccak(this.encodeData(type, value, types, useV4))]\n        }\n\n        if(value === undefined)\n          throw new Error(`missing value for field ${name} of type ${type}`)\n\n        if (type === 'bytes') {\n          return ['bytes32', util.keccak(value)]\n        }\n\n        if (type === 'string') {\n          // convert string to buffer - prevents ethUtil from interpreting strings like '0xabcd' as hex\n          if (typeof value === 'string') {\n            value = Buffer.from(value, 'utf8')\n          }\n          return ['bytes32', util.keccak(value)]\n        }\n\n        if (type.lastIndexOf(']') === type.length - 1) {\n          const parsedType = type.slice(0, type.lastIndexOf('['))\n          const typeValuePairs = value.map(item =>\n            encodeField(name, parsedType, item))\n          return ['bytes32', util.keccak(abi.rawEncode(\n            typeValuePairs.map(([type]) => type),\n            typeValuePairs.map(([, value]) => value),\n          ))]\n        }\n\n        return [type, value]\n      }\n\n      for (const field of types[primaryType]) {\n        const [type, value] = encodeField(field.name, field.type, data[field.name])\n        encodedTypes.push(type)\n        encodedValues.push(value)\n      }\n    } else {\n      for (const field of types[primaryType]) {\n        let value = data[field.name]\n        if (value !== undefined) {\n          if (field.type === 'bytes') {\n            encodedTypes.push('bytes32')\n            value = util.keccak(value)\n            encodedValues.push(value)\n          } else if (field.type === 'string') {\n            encodedTypes.push('bytes32')\n            // convert string to buffer - prevents ethUtil from interpreting strings like '0xabcd' as hex\n            if (typeof value === 'string') {\n              value = Buffer.from(value, 'utf8')\n            }\n            value = util.keccak(value)\n            encodedValues.push(value)\n          } else if (types[field.type] !== undefined) {\n            encodedTypes.push('bytes32')\n            value = util.keccak(this.encodeData(field.type, value, types, useV4))\n            encodedValues.push(value)\n          } else if (field.type.lastIndexOf(']') === field.type.length - 1) {\n            throw new Error('Arrays currently unimplemented in encodeData')\n          } else {\n            encodedTypes.push(field.type)\n            encodedValues.push(value)\n          }\n        }\n      }\n    }\n\n    return abi.rawEncode(encodedTypes, encodedValues)\n  },\n\n  /**\n   * Encodes the type of an object by encoding a comma delimited list of its members\n   *\n   * @param {string} primaryType - Root type to encode\n   * @param {Object} types - Type definitions\n   * @returns {string} - Encoded representation of the type of an object\n   */\n  encodeType (primaryType, types) {\n    let result = ''\n    let deps = this.findTypeDependencies(primaryType, types).filter(dep => dep !== primaryType)\n    deps = [primaryType].concat(deps.sort())\n    for (const type of deps) {\n      const children = types[type]\n      if (!children) {\n        throw new Error('No type definition specified: ' + type)\n      }\n      result += type + '(' + types[type].map(({ name, type }) => type + ' ' + name).join(',') + ')'\n    }\n    return result\n  },\n\n  /**\n   * Finds all types within a type definition object\n   *\n   * @param {string} primaryType - Root type\n   * @param {Object} types - Type definitions\n   * @param {Array} results - current set of accumulated types\n   * @returns {Array} - Set of all types found in the type definition\n   */\n  findTypeDependencies (primaryType, types, results = []) {\n    primaryType = primaryType.match(/^\\w*/)[0]\n    if (results.includes(primaryType) || types[primaryType] === undefined) { return results }\n    results.push(primaryType)\n    for (const field of types[primaryType]) {\n      for (const dep of this.findTypeDependencies(field.type, types, results)) {\n        !results.includes(dep) && results.push(dep)\n      }\n    }\n    return results\n  },\n\n  /**\n   * Hashes an object\n   *\n   * @param {string} primaryType - Root type\n   * @param {Object} data - Object to hash\n   * @param {Object} types - Type definitions\n   * @returns {Buffer} - Hash of an object\n   */\n  hashStruct (primaryType, data, types, useV4 = true) {\n    return util.keccak(this.encodeData(primaryType, data, types, useV4))\n  },\n\n  /**\n   * Hashes the type of an object\n   *\n   * @param {string} primaryType - Root type to hash\n   * @param {Object} types - Type definitions\n   * @returns {string} - Hash of an object\n   */\n  hashType (primaryType, types) {\n    return util.keccak(this.encodeType(primaryType, types))\n  },\n\n  /**\n   * Removes properties from a message object that are not defined per EIP-712\n   *\n   * @param {Object} data - typed message object\n   * @returns {Object} - typed message object with only allowed fields\n   */\n  sanitizeData (data) {\n    const sanitizedData = {}\n    for (const key in TYPED_MESSAGE_SCHEMA.properties) {\n      data[key] && (sanitizedData[key] = data[key])\n    }\n    if (sanitizedData.types) {\n      sanitizedData.types = Object.assign({ EIP712Domain: [] }, sanitizedData.types)\n    }\n    return sanitizedData\n  },\n\n  /**\n   * Returns the hash of a typed message as per EIP-712 for signing\n   *\n   * @param {Object} typedData - Types message data to sign\n   * @returns {string} - sha3 hash for signing\n   */\n  hash (typedData, useV4 = true) {\n    const sanitizedData = this.sanitizeData(typedData)\n    const parts = [Buffer.from('1901', 'hex')]\n    parts.push(this.hashStruct('EIP712Domain', sanitizedData.domain, sanitizedData.types, useV4))\n    if (sanitizedData.primaryType !== 'EIP712Domain') {\n      parts.push(this.hashStruct(sanitizedData.primaryType, sanitizedData.message, sanitizedData.types, useV4))\n    }\n    return util.keccak(Buffer.concat(parts))\n  },\n}\n\nmodule.exports = {\n  TYPED_MESSAGE_SCHEMA,\n  TypedDataUtils,\n\n  hashForSignTypedDataLegacy: function (msgParams) {\n    return typedSignatureHashLegacy(msgParams.data)\n  },\n\n  hashForSignTypedData_v3: function (msgParams) {\n    return TypedDataUtils.hash(msgParams.data, false)\n  },\n\n  hashForSignTypedData_v4: function (msgParams) {\n    return TypedDataUtils.hash(msgParams.data)\n  },\n}\n\n/**\n * @param typedData - Array of data along with types, as per EIP712.\n * @returns Buffer\n */\nfunction typedSignatureHashLegacy(typedData) {\n  const error = new Error('Expect argument to be non-empty array')\n  if (typeof typedData !== 'object' || !typedData.length) throw error\n\n  const data = typedData.map(function (e) {\n    return e.type === 'bytes' ? util.toBuffer(e.value) : e.value\n  })\n  const types = typedData.map(function (e) { return e.type })\n  const schema = typedData.map(function (e) {\n    if (!e.name) throw error\n    return e.type + ' ' + e.name\n  })\n\n  return abi.soliditySHA3(\n    ['bytes32', 'bytes32'],\n    [\n      abi.soliditySHA3(new Array(typedData.length).fill('string'), schema),\n      abi.soliditySHA3(types, data)\n    ]\n  )\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/index.cjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/util.cjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/util.cjs ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Extracted from https://github.com/ethereumjs/ethereumjs-util and stripped out irrelevant code\n// Original code licensed under the Mozilla Public License Version 2.0\n\n/* eslint-disable */\n//prettier-ignore\nconst { keccak_256 } = __webpack_require__(/*! @noble/hashes/sha3 */ \"(ssr)/./node_modules/@noble/hashes/sha3.js\")\n\n/**\n * Returns a buffer filled with 0s\n * @method zeros\n * @param {Number} bytes  the number of bytes the buffer should be\n * @return {Buffer}\n */\nfunction zeros (bytes) {\n  return Buffer.allocUnsafe(bytes).fill(0)\n}\n\nfunction bitLengthFromBigInt (num) {\n  return num.toString(2).length\n}\n\nfunction bufferBEFromBigInt(num, length) {\n  let hex = num.toString(16);\n  // Ensure the hex string length is even\n  if (hex.length % 2 !== 0) hex = '0' + hex;\n  // Convert hex string to a byte array\n  const byteArray = hex.match(/.{1,2}/g).map(byte => parseInt(byte, 16));\n  // Ensure the byte array is of the specified length\n  while (byteArray.length < length) {\n    byteArray.unshift(0); // Prepend with zeroes if shorter than required length\n  }\n\n  return Buffer.from(byteArray);\n}\n\nfunction twosFromBigInt(value, width) {\n  const isNegative = value < 0n;\n  let result;\n  if (isNegative) {\n    // Prepare a mask for the specified width to perform NOT operation\n    const mask = (1n << BigInt(width)) - 1n;\n    // Invert bits (using NOT) and add one\n    result = (~value & mask) + 1n;\n  } else {\n    result = value;\n  }\n  // Ensure the result fits in the specified width\n  result &= (1n << BigInt(width)) - 1n;\n\n  return result;\n}\n\n/**\n * Left Pads an `Array` or `Buffer` with leading zeros till it has `length` bytes.\n * Or it truncates the beginning if it exceeds.\n * @method setLength\n * @param {Buffer|Array} msg the value to pad\n * @param {Number} length the number of bytes the output should be\n * @param {Boolean} [right=false] whether to start padding form the left or right\n * @return {Buffer|Array}\n */\nfunction setLength (msg, length, right) {\n  const buf = zeros(length)\n  msg = toBuffer(msg)\n  if (right) {\n    if (msg.length < length) {\n      msg.copy(buf)\n      return buf\n    }\n    return msg.slice(0, length)\n  } else {\n    if (msg.length < length) {\n      msg.copy(buf, length - msg.length)\n      return buf\n    }\n    return msg.slice(-length)\n  }\n}\n\n/**\n * Right Pads an `Array` or `Buffer` with leading zeros till it has `length` bytes.\n * Or it truncates the beginning if it exceeds.\n * @param {Buffer|Array} msg the value to pad\n * @param {Number} length the number of bytes the output should be\n * @return {Buffer|Array}\n */\nfunction setLengthRight (msg, length) {\n  return setLength(msg, length, true)\n}\n\n/**\n * Attempts to turn a value into a `Buffer`. As input it supports `Buffer`, `String`, `Number`, null/undefined, `BIgInt` and other objects with a `toArray()` method.\n * @param {*} v the value\n */\nfunction toBuffer (v) {\n  if (!Buffer.isBuffer(v)) {\n    if (Array.isArray(v)) {\n      v = Buffer.from(v)\n    } else if (typeof v === 'string') {\n      if (isHexString(v)) {\n        v = Buffer.from(padToEven(stripHexPrefix(v)), 'hex')\n      } else {\n        v = Buffer.from(v)\n      }\n    } else if (typeof v === 'number') {\n      v = intToBuffer(v)\n    } else if (v === null || v === undefined) {\n      v = Buffer.allocUnsafe(0)\n    } else if (typeof v === 'bigint') {\n      v = bufferBEFromBigInt(v)\n    } else if (v.toArray) {\n      // TODO: bigint should be handled above, may remove this duplicate\n      // converts a BigInt to a Buffer\n      v = Buffer.from(v.toArray())\n    } else {\n      throw new Error('invalid type')\n    }\n  }\n  return v\n}\n\n/**\n * Converts a `Buffer` into a hex `String`\n * @param {Buffer} buf\n * @return {String}\n */\nfunction bufferToHex (buf) {\n  buf = toBuffer(buf)\n  return '0x' + buf.toString('hex')\n}\n\n/**\n * Creates Keccak hash of the input\n * @param {Buffer|Array|String|Number} a the input data\n * @param {Number} [bits=256] the Keccak width\n * @return {Buffer}\n */\nfunction keccak (a, bits) {\n  a = toBuffer(a)\n  if (!bits) bits = 256\n  if (bits !== 256) {\n    throw new Error('unsupported')\n  }\n  return Buffer.from(keccak_256(new Uint8Array(a)))\n}\n\nfunction padToEven (str) {\n  return str.length % 2 ? '0' + str : str\n}\n\nfunction isHexString (str) {\n  return typeof str === 'string' && str.match(/^0x[0-9A-Fa-f]*$/)\n}\n\nfunction stripHexPrefix (str) {\n  if (typeof str === 'string' && str.startsWith('0x')) {\n    return str.slice(2)\n  }\n  return str\n}\n\nmodule.exports = {\n  zeros,\n  setLength,\n  setLengthRight,\n  isHexString,\n  stripHexPrefix,\n  toBuffer,\n  bufferToHex,\n  keccak,\n  bitLengthFromBigInt,\n  bufferBEFromBigInt,\n  twosFromBigInt\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/util.cjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/CoinbaseWalletProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/CoinbaseWalletProvider.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoinbaseWalletProvider: () => (/* binding */ CoinbaseWalletProvider)\n/* harmony export */ });\n/* harmony import */ var _sign_util_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sign/util.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/util.js\");\n/* harmony import */ var _core_communicator_Communicator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./core/communicator/Communicator.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/communicator/Communicator.js\");\n/* harmony import */ var _core_constants_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./core/constants.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/constants.js\");\n/* harmony import */ var _core_error_constants_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./core/error/constants.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/constants.js\");\n/* harmony import */ var _core_error_errors_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./core/error/errors.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js\");\n/* harmony import */ var _core_error_serialize_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./core/error/serialize.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/serialize.js\");\n/* harmony import */ var _core_provider_interface_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core/provider/interface.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/provider/interface.js\");\n/* harmony import */ var _core_storage_ScopedLocalStorage_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./core/storage/ScopedLocalStorage.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/storage/ScopedLocalStorage.js\");\n/* harmony import */ var _core_type_util_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./core/type/util.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\n/* harmony import */ var _util_provider_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./util/provider.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/provider.js\");\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n\n\n\n\n\n\n\n\n\n\nclass CoinbaseWalletProvider extends _core_provider_interface_js__WEBPACK_IMPORTED_MODULE_0__.ProviderEventEmitter {\n    constructor(_a) {\n        var { metadata } = _a, _b = _a.preference, { keysUrl } = _b, preference = __rest(_b, [\"keysUrl\"]);\n        super();\n        this.signer = null;\n        this.isCoinbaseWallet = true;\n        this.metadata = metadata;\n        this.preference = preference;\n        this.communicator = new _core_communicator_Communicator_js__WEBPACK_IMPORTED_MODULE_1__.Communicator({\n            url: keysUrl,\n            metadata,\n            preference,\n        });\n        const signerType = (0,_sign_util_js__WEBPACK_IMPORTED_MODULE_2__.loadSignerType)();\n        if (signerType) {\n            this.signer = this.initSigner(signerType);\n        }\n    }\n    async request(args) {\n        try {\n            (0,_util_provider_js__WEBPACK_IMPORTED_MODULE_3__.checkErrorForInvalidRequestArgs)(args);\n            if (!this.signer) {\n                switch (args.method) {\n                    case 'eth_requestAccounts': {\n                        const signerType = await this.requestSignerSelection(args);\n                        const signer = this.initSigner(signerType);\n                        await signer.handshake(args);\n                        this.signer = signer;\n                        (0,_sign_util_js__WEBPACK_IMPORTED_MODULE_2__.storeSignerType)(signerType);\n                        break;\n                    }\n                    case 'wallet_sendCalls': {\n                        const ephemeralSigner = this.initSigner('scw');\n                        await ephemeralSigner.handshake({ method: 'handshake' }); // exchange session keys\n                        const result = await ephemeralSigner.request(args); // send diffie-hellman encrypted request\n                        await ephemeralSigner.cleanup(); // clean up (rotate) the ephemeral session keys\n                        return result;\n                    }\n                    case 'wallet_getCallsStatus':\n                        return (0,_util_provider_js__WEBPACK_IMPORTED_MODULE_3__.fetchRPCRequest)(args, _core_constants_js__WEBPACK_IMPORTED_MODULE_4__.CB_WALLET_RPC_URL);\n                    case 'net_version':\n                        return 1; // default value\n                    case 'eth_chainId':\n                        return (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_5__.hexStringFromNumber)(1); // default value\n                    default: {\n                        throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_6__.standardErrors.provider.unauthorized(\"Must call 'eth_requestAccounts' before other methods\");\n                    }\n                }\n            }\n            return await this.signer.request(args);\n        }\n        catch (error) {\n            const { code } = error;\n            if (code === _core_error_constants_js__WEBPACK_IMPORTED_MODULE_7__.standardErrorCodes.provider.unauthorized)\n                this.disconnect();\n            return Promise.reject((0,_core_error_serialize_js__WEBPACK_IMPORTED_MODULE_8__.serializeError)(error));\n        }\n    }\n    /** @deprecated Use `.request({ method: 'eth_requestAccounts' })` instead. */\n    async enable() {\n        console.warn(`.enable() has been deprecated. Please use .request({ method: \"eth_requestAccounts\" }) instead.`);\n        return await this.request({\n            method: 'eth_requestAccounts',\n        });\n    }\n    async disconnect() {\n        var _a;\n        await ((_a = this.signer) === null || _a === void 0 ? void 0 : _a.cleanup());\n        this.signer = null;\n        _core_storage_ScopedLocalStorage_js__WEBPACK_IMPORTED_MODULE_9__.ScopedLocalStorage.clearAll();\n        this.emit('disconnect', _core_error_errors_js__WEBPACK_IMPORTED_MODULE_6__.standardErrors.provider.disconnected('User initiated disconnection'));\n    }\n    requestSignerSelection(handshakeRequest) {\n        return (0,_sign_util_js__WEBPACK_IMPORTED_MODULE_2__.fetchSignerType)({\n            communicator: this.communicator,\n            preference: this.preference,\n            metadata: this.metadata,\n            handshakeRequest,\n            callback: this.emit.bind(this),\n        });\n    }\n    initSigner(signerType) {\n        return (0,_sign_util_js__WEBPACK_IMPORTED_MODULE_2__.createSigner)({\n            signerType,\n            metadata: this.metadata,\n            communicator: this.communicator,\n            callback: this.emit.bind(this),\n        });\n    }\n}\n//# sourceMappingURL=CoinbaseWalletProvider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/CoinbaseWalletProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/communicator/Communicator.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/communicator/Communicator.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Communicator: () => (/* binding */ Communicator)\n/* harmony export */ });\n/* harmony import */ var _sdk_info_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../sdk-info.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sdk-info.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../constants.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/constants.js\");\n/* harmony import */ var _error_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../error/errors.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js\");\n/* harmony import */ var _util_web_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/web.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/web.js\");\n\n\n\n\n/**\n * Communicates with a popup window for Coinbase keys.coinbase.com (or another url)\n * to send and receive messages.\n *\n * This class is responsible for opening a popup window, posting messages to it,\n * and listening for responses.\n *\n * It also handles cleanup of event listeners and the popup window itself when necessary.\n */\nclass Communicator {\n    constructor({ url = _constants_js__WEBPACK_IMPORTED_MODULE_0__.CB_KEYS_URL, metadata, preference }) {\n        this.popup = null;\n        this.listeners = new Map();\n        /**\n         * Posts a message to the popup window\n         */\n        this.postMessage = async (message) => {\n            const popup = await this.waitForPopupLoaded();\n            popup.postMessage(message, this.url.origin);\n        };\n        /**\n         * Posts a request to the popup window and waits for a response\n         */\n        this.postRequestAndWaitForResponse = async (request) => {\n            const responsePromise = this.onMessage(({ requestId }) => requestId === request.id);\n            this.postMessage(request);\n            return await responsePromise;\n        };\n        /**\n         * Listens for messages from the popup window that match a given predicate.\n         */\n        this.onMessage = async (predicate) => {\n            return new Promise((resolve, reject) => {\n                const listener = (event) => {\n                    if (event.origin !== this.url.origin)\n                        return; // origin validation\n                    const message = event.data;\n                    if (predicate(message)) {\n                        resolve(message);\n                        window.removeEventListener('message', listener);\n                        this.listeners.delete(listener);\n                    }\n                };\n                window.addEventListener('message', listener);\n                this.listeners.set(listener, { reject });\n            });\n        };\n        /**\n         * Closes the popup, rejects all requests and clears the listeners\n         */\n        this.disconnect = () => {\n            // Note: keys popup handles closing itself. this is a fallback.\n            (0,_util_web_js__WEBPACK_IMPORTED_MODULE_1__.closePopup)(this.popup);\n            this.popup = null;\n            this.listeners.forEach(({ reject }, listener) => {\n                reject(_error_errors_js__WEBPACK_IMPORTED_MODULE_2__.standardErrors.provider.userRejectedRequest('Request rejected'));\n                window.removeEventListener('message', listener);\n            });\n            this.listeners.clear();\n        };\n        /**\n         * Waits for the popup window to fully load and then sends a version message.\n         */\n        this.waitForPopupLoaded = async () => {\n            if (this.popup && !this.popup.closed) {\n                // In case the user un-focused the popup between requests, focus it again\n                this.popup.focus();\n                return this.popup;\n            }\n            this.popup = await (0,_util_web_js__WEBPACK_IMPORTED_MODULE_1__.openPopup)(this.url);\n            this.onMessage(({ event }) => event === 'PopupUnload')\n                .then(this.disconnect)\n                .catch(() => { });\n            return this.onMessage(({ event }) => event === 'PopupLoaded')\n                .then((message) => {\n                this.postMessage({\n                    requestId: message.id,\n                    data: {\n                        version: _sdk_info_js__WEBPACK_IMPORTED_MODULE_3__.VERSION,\n                        metadata: this.metadata,\n                        preference: this.preference,\n                        location: window.location.toString(),\n                    },\n                });\n            })\n                .then(() => {\n                if (!this.popup)\n                    throw _error_errors_js__WEBPACK_IMPORTED_MODULE_2__.standardErrors.rpc.internal();\n                return this.popup;\n            });\n        };\n        this.url = new URL(url);\n        this.metadata = metadata;\n        this.preference = preference;\n    }\n}\n//# sourceMappingURL=Communicator.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9jb3JlL2NvbW11bmljYXRvci9Db21tdW5pY2F0b3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEM7QUFDRTtBQUNNO0FBQ007QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxrQkFBa0IsTUFBTSxzREFBVyx3QkFBd0I7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzREFBc0QsV0FBVztBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0NBQStDLFFBQVE7QUFDdkQsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksd0RBQVU7QUFDdEI7QUFDQSxzQ0FBc0MsUUFBUTtBQUM5Qyx1QkFBdUIsNERBQWM7QUFDckM7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQix1REFBUztBQUN4Qyw4QkFBOEIsT0FBTztBQUNyQztBQUNBLGdDQUFnQztBQUNoQyxxQ0FBcUMsT0FBTztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxpREFBTztBQUN4QztBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckIsaUJBQWlCO0FBQ2pCLGFBQWE7QUFDYjtBQUNBO0FBQ0EsMEJBQTBCLDREQUFjO0FBQ3hDO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJlYW1zdGFydHIvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9jb3JlL2NvbW11bmljYXRvci9Db21tdW5pY2F0b3IuanM/ZGViNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBWRVJTSU9OIH0gZnJvbSAnLi4vLi4vc2RrLWluZm8uanMnO1xuaW1wb3J0IHsgQ0JfS0VZU19VUkwgfSBmcm9tICcuLi9jb25zdGFudHMuanMnO1xuaW1wb3J0IHsgc3RhbmRhcmRFcnJvcnMgfSBmcm9tICcuLi9lcnJvci9lcnJvcnMuanMnO1xuaW1wb3J0IHsgY2xvc2VQb3B1cCwgb3BlblBvcHVwIH0gZnJvbSAnLi4vLi4vdXRpbC93ZWIuanMnO1xuLyoqXG4gKiBDb21tdW5pY2F0ZXMgd2l0aCBhIHBvcHVwIHdpbmRvdyBmb3IgQ29pbmJhc2Uga2V5cy5jb2luYmFzZS5jb20gKG9yIGFub3RoZXIgdXJsKVxuICogdG8gc2VuZCBhbmQgcmVjZWl2ZSBtZXNzYWdlcy5cbiAqXG4gKiBUaGlzIGNsYXNzIGlzIHJlc3BvbnNpYmxlIGZvciBvcGVuaW5nIGEgcG9wdXAgd2luZG93LCBwb3N0aW5nIG1lc3NhZ2VzIHRvIGl0LFxuICogYW5kIGxpc3RlbmluZyBmb3IgcmVzcG9uc2VzLlxuICpcbiAqIEl0IGFsc28gaGFuZGxlcyBjbGVhbnVwIG9mIGV2ZW50IGxpc3RlbmVycyBhbmQgdGhlIHBvcHVwIHdpbmRvdyBpdHNlbGYgd2hlbiBuZWNlc3NhcnkuXG4gKi9cbmV4cG9ydCBjbGFzcyBDb21tdW5pY2F0b3Ige1xuICAgIGNvbnN0cnVjdG9yKHsgdXJsID0gQ0JfS0VZU19VUkwsIG1ldGFkYXRhLCBwcmVmZXJlbmNlIH0pIHtcbiAgICAgICAgdGhpcy5wb3B1cCA9IG51bGw7XG4gICAgICAgIHRoaXMubGlzdGVuZXJzID0gbmV3IE1hcCgpO1xuICAgICAgICAvKipcbiAgICAgICAgICogUG9zdHMgYSBtZXNzYWdlIHRvIHRoZSBwb3B1cCB3aW5kb3dcbiAgICAgICAgICovXG4gICAgICAgIHRoaXMucG9zdE1lc3NhZ2UgPSBhc3luYyAobWVzc2FnZSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgcG9wdXAgPSBhd2FpdCB0aGlzLndhaXRGb3JQb3B1cExvYWRlZCgpO1xuICAgICAgICAgICAgcG9wdXAucG9zdE1lc3NhZ2UobWVzc2FnZSwgdGhpcy51cmwub3JpZ2luKTtcbiAgICAgICAgfTtcbiAgICAgICAgLyoqXG4gICAgICAgICAqIFBvc3RzIGEgcmVxdWVzdCB0byB0aGUgcG9wdXAgd2luZG93IGFuZCB3YWl0cyBmb3IgYSByZXNwb25zZVxuICAgICAgICAgKi9cbiAgICAgICAgdGhpcy5wb3N0UmVxdWVzdEFuZFdhaXRGb3JSZXNwb25zZSA9IGFzeW5jIChyZXF1ZXN0KSA9PiB7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZVByb21pc2UgPSB0aGlzLm9uTWVzc2FnZSgoeyByZXF1ZXN0SWQgfSkgPT4gcmVxdWVzdElkID09PSByZXF1ZXN0LmlkKTtcbiAgICAgICAgICAgIHRoaXMucG9zdE1lc3NhZ2UocmVxdWVzdCk7XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2VQcm9taXNlO1xuICAgICAgICB9O1xuICAgICAgICAvKipcbiAgICAgICAgICogTGlzdGVucyBmb3IgbWVzc2FnZXMgZnJvbSB0aGUgcG9wdXAgd2luZG93IHRoYXQgbWF0Y2ggYSBnaXZlbiBwcmVkaWNhdGUuXG4gICAgICAgICAqL1xuICAgICAgICB0aGlzLm9uTWVzc2FnZSA9IGFzeW5jIChwcmVkaWNhdGUpID0+IHtcbiAgICAgICAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgbGlzdGVuZXIgPSAoZXZlbnQpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGV2ZW50Lm9yaWdpbiAhPT0gdGhpcy51cmwub3JpZ2luKVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuOyAvLyBvcmlnaW4gdmFsaWRhdGlvblxuICAgICAgICAgICAgICAgICAgICBjb25zdCBtZXNzYWdlID0gZXZlbnQuZGF0YTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHByZWRpY2F0ZShtZXNzYWdlKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVzb2x2ZShtZXNzYWdlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdtZXNzYWdlJywgbGlzdGVuZXIpO1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5saXN0ZW5lcnMuZGVsZXRlKGxpc3RlbmVyKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ21lc3NhZ2UnLCBsaXN0ZW5lcik7XG4gICAgICAgICAgICAgICAgdGhpcy5saXN0ZW5lcnMuc2V0KGxpc3RlbmVyLCB7IHJlamVjdCB9KTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgICAgICAvKipcbiAgICAgICAgICogQ2xvc2VzIHRoZSBwb3B1cCwgcmVqZWN0cyBhbGwgcmVxdWVzdHMgYW5kIGNsZWFycyB0aGUgbGlzdGVuZXJzXG4gICAgICAgICAqL1xuICAgICAgICB0aGlzLmRpc2Nvbm5lY3QgPSAoKSA9PiB7XG4gICAgICAgICAgICAvLyBOb3RlOiBrZXlzIHBvcHVwIGhhbmRsZXMgY2xvc2luZyBpdHNlbGYuIHRoaXMgaXMgYSBmYWxsYmFjay5cbiAgICAgICAgICAgIGNsb3NlUG9wdXAodGhpcy5wb3B1cCk7XG4gICAgICAgICAgICB0aGlzLnBvcHVwID0gbnVsbDtcbiAgICAgICAgICAgIHRoaXMubGlzdGVuZXJzLmZvckVhY2goKHsgcmVqZWN0IH0sIGxpc3RlbmVyKSA9PiB7XG4gICAgICAgICAgICAgICAgcmVqZWN0KHN0YW5kYXJkRXJyb3JzLnByb3ZpZGVyLnVzZXJSZWplY3RlZFJlcXVlc3QoJ1JlcXVlc3QgcmVqZWN0ZWQnKSk7XG4gICAgICAgICAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21lc3NhZ2UnLCBsaXN0ZW5lcik7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRoaXMubGlzdGVuZXJzLmNsZWFyKCk7XG4gICAgICAgIH07XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBXYWl0cyBmb3IgdGhlIHBvcHVwIHdpbmRvdyB0byBmdWxseSBsb2FkIGFuZCB0aGVuIHNlbmRzIGEgdmVyc2lvbiBtZXNzYWdlLlxuICAgICAgICAgKi9cbiAgICAgICAgdGhpcy53YWl0Rm9yUG9wdXBMb2FkZWQgPSBhc3luYyAoKSA9PiB7XG4gICAgICAgICAgICBpZiAodGhpcy5wb3B1cCAmJiAhdGhpcy5wb3B1cC5jbG9zZWQpIHtcbiAgICAgICAgICAgICAgICAvLyBJbiBjYXNlIHRoZSB1c2VyIHVuLWZvY3VzZWQgdGhlIHBvcHVwIGJldHdlZW4gcmVxdWVzdHMsIGZvY3VzIGl0IGFnYWluXG4gICAgICAgICAgICAgICAgdGhpcy5wb3B1cC5mb2N1cygpO1xuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLnBvcHVwO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5wb3B1cCA9IGF3YWl0IG9wZW5Qb3B1cCh0aGlzLnVybCk7XG4gICAgICAgICAgICB0aGlzLm9uTWVzc2FnZSgoeyBldmVudCB9KSA9PiBldmVudCA9PT0gJ1BvcHVwVW5sb2FkJylcbiAgICAgICAgICAgICAgICAudGhlbih0aGlzLmRpc2Nvbm5lY3QpXG4gICAgICAgICAgICAgICAgLmNhdGNoKCgpID0+IHsgfSk7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5vbk1lc3NhZ2UoKHsgZXZlbnQgfSkgPT4gZXZlbnQgPT09ICdQb3B1cExvYWRlZCcpXG4gICAgICAgICAgICAgICAgLnRoZW4oKG1lc3NhZ2UpID0+IHtcbiAgICAgICAgICAgICAgICB0aGlzLnBvc3RNZXNzYWdlKHtcbiAgICAgICAgICAgICAgICAgICAgcmVxdWVzdElkOiBtZXNzYWdlLmlkLFxuICAgICAgICAgICAgICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2ZXJzaW9uOiBWRVJTSU9OLFxuICAgICAgICAgICAgICAgICAgICAgICAgbWV0YWRhdGE6IHRoaXMubWV0YWRhdGEsXG4gICAgICAgICAgICAgICAgICAgICAgICBwcmVmZXJlbmNlOiB0aGlzLnByZWZlcmVuY2UsXG4gICAgICAgICAgICAgICAgICAgICAgICBsb2NhdGlvbjogd2luZG93LmxvY2F0aW9uLnRvU3RyaW5nKCksXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgIC50aGVuKCgpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoIXRoaXMucG9wdXApXG4gICAgICAgICAgICAgICAgICAgIHRocm93IHN0YW5kYXJkRXJyb3JzLnJwYy5pbnRlcm5hbCgpO1xuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLnBvcHVwO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMudXJsID0gbmV3IFVSTCh1cmwpO1xuICAgICAgICB0aGlzLm1ldGFkYXRhID0gbWV0YWRhdGE7XG4gICAgICAgIHRoaXMucHJlZmVyZW5jZSA9IHByZWZlcmVuY2U7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Q29tbXVuaWNhdG9yLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/communicator/Communicator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/constants.js":
/*!******************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/constants.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CBW_MOBILE_DEEPLINK_URL: () => (/* binding */ CBW_MOBILE_DEEPLINK_URL),\n/* harmony export */   CB_KEYS_URL: () => (/* binding */ CB_KEYS_URL),\n/* harmony export */   CB_WALLET_RPC_URL: () => (/* binding */ CB_WALLET_RPC_URL),\n/* harmony export */   WALLETLINK_URL: () => (/* binding */ WALLETLINK_URL)\n/* harmony export */ });\nconst CB_KEYS_URL = 'https://keys.coinbase.com/connect';\nconst CB_WALLET_RPC_URL = 'https://rpc.wallet.coinbase.com';\nconst WALLETLINK_URL = 'https://www.walletlink.org';\nconst CBW_MOBILE_DEEPLINK_URL = 'https://go.cb-w.com/walletlink';\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9jb3JlL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDUCIsInNvdXJjZXMiOlsid2VicGFjazovL2RyZWFtc3RhcnRyLy4vbm9kZV9tb2R1bGVzL0Bjb2luYmFzZS93YWxsZXQtc2RrL2Rpc3QvY29yZS9jb25zdGFudHMuanM/Mjc1MCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgQ0JfS0VZU19VUkwgPSAnaHR0cHM6Ly9rZXlzLmNvaW5iYXNlLmNvbS9jb25uZWN0JztcbmV4cG9ydCBjb25zdCBDQl9XQUxMRVRfUlBDX1VSTCA9ICdodHRwczovL3JwYy53YWxsZXQuY29pbmJhc2UuY29tJztcbmV4cG9ydCBjb25zdCBXQUxMRVRMSU5LX1VSTCA9ICdodHRwczovL3d3dy53YWxsZXRsaW5rLm9yZyc7XG5leHBvcnQgY29uc3QgQ0JXX01PQklMRV9ERUVQTElOS19VUkwgPSAnaHR0cHM6Ly9nby5jYi13LmNvbS93YWxsZXRsaW5rJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnN0YW50cy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/constants.js":
/*!************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/error/constants.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   errorValues: () => (/* binding */ errorValues),\n/* harmony export */   standardErrorCodes: () => (/* binding */ standardErrorCodes)\n/* harmony export */ });\nconst standardErrorCodes = {\n    rpc: {\n        invalidInput: -32000,\n        resourceNotFound: -32001,\n        resourceUnavailable: -32002,\n        transactionRejected: -32003,\n        methodNotSupported: -32004,\n        limitExceeded: -32005,\n        parse: -32700,\n        invalidRequest: -32600,\n        methodNotFound: -32601,\n        invalidParams: -32602,\n        internal: -32603,\n    },\n    provider: {\n        userRejectedRequest: 4001,\n        unauthorized: 4100,\n        unsupportedMethod: 4200,\n        disconnected: 4900,\n        chainDisconnected: 4901,\n        unsupportedChain: 4902,\n    },\n};\nconst errorValues = {\n    '-32700': {\n        standard: 'JSON RPC 2.0',\n        message: 'Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text.',\n    },\n    '-32600': {\n        standard: 'JSON RPC 2.0',\n        message: 'The JSON sent is not a valid Request object.',\n    },\n    '-32601': {\n        standard: 'JSON RPC 2.0',\n        message: 'The method does not exist / is not available.',\n    },\n    '-32602': {\n        standard: 'JSON RPC 2.0',\n        message: 'Invalid method parameter(s).',\n    },\n    '-32603': {\n        standard: 'JSON RPC 2.0',\n        message: 'Internal JSON-RPC error.',\n    },\n    '-32000': {\n        standard: 'EIP-1474',\n        message: 'Invalid input.',\n    },\n    '-32001': {\n        standard: 'EIP-1474',\n        message: 'Resource not found.',\n    },\n    '-32002': {\n        standard: 'EIP-1474',\n        message: 'Resource unavailable.',\n    },\n    '-32003': {\n        standard: 'EIP-1474',\n        message: 'Transaction rejected.',\n    },\n    '-32004': {\n        standard: 'EIP-1474',\n        message: 'Method not supported.',\n    },\n    '-32005': {\n        standard: 'EIP-1474',\n        message: 'Request limit exceeded.',\n    },\n    '4001': {\n        standard: 'EIP-1193',\n        message: 'User rejected the request.',\n    },\n    '4100': {\n        standard: 'EIP-1193',\n        message: 'The requested account and/or method has not been authorized by the user.',\n    },\n    '4200': {\n        standard: 'EIP-1193',\n        message: 'The requested method is not supported by this Ethereum provider.',\n    },\n    '4900': {\n        standard: 'EIP-1193',\n        message: 'The provider is disconnected from all chains.',\n    },\n    '4901': {\n        standard: 'EIP-1193',\n        message: 'The provider is disconnected from the specified chain.',\n    },\n    '4902': {\n        standard: 'EIP-3085',\n        message: 'Unrecognized chain ID.',\n    },\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   standardErrors: () => (/* binding */ standardErrors)\n/* harmony export */ });\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/constants.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/utils.js\");\n\n\nconst standardErrors = {\n    rpc: {\n        parse: (arg) => getEthJsonRpcError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.rpc.parse, arg),\n        invalidRequest: (arg) => getEthJsonRpcError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.rpc.invalidRequest, arg),\n        invalidParams: (arg) => getEthJsonRpcError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.rpc.invalidParams, arg),\n        methodNotFound: (arg) => getEthJsonRpcError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.rpc.methodNotFound, arg),\n        internal: (arg) => getEthJsonRpcError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.rpc.internal, arg),\n        server: (opts) => {\n            if (!opts || typeof opts !== 'object' || Array.isArray(opts)) {\n                throw new Error('Ethereum RPC Server errors must provide single object argument.');\n            }\n            const { code } = opts;\n            if (!Number.isInteger(code) || code > -32005 || code < -32099) {\n                throw new Error('\"code\" must be an integer such that: -32099 <= code <= -32005');\n            }\n            return getEthJsonRpcError(code, opts);\n        },\n        invalidInput: (arg) => getEthJsonRpcError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.rpc.invalidInput, arg),\n        resourceNotFound: (arg) => getEthJsonRpcError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.rpc.resourceNotFound, arg),\n        resourceUnavailable: (arg) => getEthJsonRpcError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.rpc.resourceUnavailable, arg),\n        transactionRejected: (arg) => getEthJsonRpcError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.rpc.transactionRejected, arg),\n        methodNotSupported: (arg) => getEthJsonRpcError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.rpc.methodNotSupported, arg),\n        limitExceeded: (arg) => getEthJsonRpcError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.rpc.limitExceeded, arg),\n    },\n    provider: {\n        userRejectedRequest: (arg) => {\n            return getEthProviderError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.provider.userRejectedRequest, arg);\n        },\n        unauthorized: (arg) => {\n            return getEthProviderError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.provider.unauthorized, arg);\n        },\n        unsupportedMethod: (arg) => {\n            return getEthProviderError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.provider.unsupportedMethod, arg);\n        },\n        disconnected: (arg) => {\n            return getEthProviderError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.provider.disconnected, arg);\n        },\n        chainDisconnected: (arg) => {\n            return getEthProviderError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.provider.chainDisconnected, arg);\n        },\n        unsupportedChain: (arg) => {\n            return getEthProviderError(_constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.provider.unsupportedChain, arg);\n        },\n        custom: (opts) => {\n            if (!opts || typeof opts !== 'object' || Array.isArray(opts)) {\n                throw new Error('Ethereum Provider custom errors must provide single object argument.');\n            }\n            const { code, message, data } = opts;\n            if (!message || typeof message !== 'string') {\n                throw new Error('\"message\" must be a nonempty string');\n            }\n            return new EthereumProviderError(code, message, data);\n        },\n    },\n};\n// Internal\nfunction getEthJsonRpcError(code, arg) {\n    const [message, data] = parseOpts(arg);\n    return new EthereumRpcError(code, message || (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getMessageFromCode)(code), data);\n}\nfunction getEthProviderError(code, arg) {\n    const [message, data] = parseOpts(arg);\n    return new EthereumProviderError(code, message || (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getMessageFromCode)(code), data);\n}\nfunction parseOpts(arg) {\n    if (arg) {\n        if (typeof arg === 'string') {\n            return [arg];\n        }\n        else if (typeof arg === 'object' && !Array.isArray(arg)) {\n            const { message, data } = arg;\n            if (message && typeof message !== 'string') {\n                throw new Error('Must specify string message.');\n            }\n            return [message || undefined, data];\n        }\n    }\n    return [];\n}\nclass EthereumRpcError extends Error {\n    constructor(code, message, data) {\n        if (!Number.isInteger(code)) {\n            throw new Error('\"code\" must be an integer.');\n        }\n        if (!message || typeof message !== 'string') {\n            throw new Error('\"message\" must be a nonempty string.');\n        }\n        super(message);\n        this.code = code;\n        if (data !== undefined) {\n            this.data = data;\n        }\n    }\n}\nclass EthereumProviderError extends EthereumRpcError {\n    /**\n     * Create an Ethereum Provider JSON-RPC error.\n     * `code` must be an integer in the 1000 <= 4999 range.\n     */\n    constructor(code, message, data) {\n        if (!isValidEthProviderCode(code)) {\n            throw new Error('\"code\" must be an integer such that: 1000 <= code <= 4999');\n        }\n        super(code, message, data);\n    }\n}\nfunction isValidEthProviderCode(code) {\n    return Number.isInteger(code) && code >= 1000 && code <= 4999;\n}\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/serialize.js":
/*!************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/error/serialize.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serializeError: () => (/* binding */ serializeError)\n/* harmony export */ });\n/* harmony import */ var _sdk_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../sdk-info.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sdk-info.js\");\n/* harmony import */ var _sign_walletlink_relay_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../sign/walletlink/relay/type/Web3Response.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/Web3Response.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/constants.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/utils.js\");\n// TODO: error should not depend on walletlink. revisit this.\n\n\n\n\n/**\n * Serializes an error to a format that is compatible with the Ethereum JSON RPC error format.\n * See https://docs.cloud.coinbase.com/wallet-sdk/docs/errors\n * for more information.\n */\nfunction serializeError(error) {\n    const serialized = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.serialize)(getErrorObject(error), {\n        shouldIncludeStack: true,\n    });\n    const docUrl = new URL('https://docs.cloud.coinbase.com/wallet-sdk/docs/errors');\n    docUrl.searchParams.set('version', _sdk_info_js__WEBPACK_IMPORTED_MODULE_1__.VERSION);\n    docUrl.searchParams.set('code', serialized.code.toString());\n    docUrl.searchParams.set('message', serialized.message);\n    return Object.assign(Object.assign({}, serialized), { docUrl: docUrl.href });\n}\n/**\n * Converts an error to a serializable object.\n */\nfunction getErrorObject(error) {\n    var _a;\n    if (typeof error === 'string') {\n        return {\n            message: error,\n            code: _constants_js__WEBPACK_IMPORTED_MODULE_2__.standardErrorCodes.rpc.internal,\n        };\n    }\n    else if ((0,_sign_walletlink_relay_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_3__.isErrorResponse)(error)) {\n        const message = error.errorMessage;\n        const code = (_a = error.errorCode) !== null && _a !== void 0 ? _a : (message.match(/(denied|rejected)/i)\n            ? _constants_js__WEBPACK_IMPORTED_MODULE_2__.standardErrorCodes.provider.userRejectedRequest\n            : undefined);\n        return Object.assign(Object.assign({}, error), { message,\n            code, data: { method: error.method } });\n    }\n    return error;\n}\n//# sourceMappingURL=serialize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/serialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/error/utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JSON_RPC_SERVER_ERROR_MESSAGE: () => (/* binding */ JSON_RPC_SERVER_ERROR_MESSAGE),\n/* harmony export */   getErrorCode: () => (/* binding */ getErrorCode),\n/* harmony export */   getMessageFromCode: () => (/* binding */ getMessageFromCode),\n/* harmony export */   isValidCode: () => (/* binding */ isValidCode),\n/* harmony export */   serialize: () => (/* binding */ serialize)\n/* harmony export */ });\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/constants.js\");\n\nconst FALLBACK_MESSAGE = 'Unspecified error message.';\nconst JSON_RPC_SERVER_ERROR_MESSAGE = 'Unspecified server error.';\n/**\n * Gets the message for a given code, or a fallback message if the code has\n * no corresponding message.\n */\nfunction getMessageFromCode(code, fallbackMessage = FALLBACK_MESSAGE) {\n    if (code && Number.isInteger(code)) {\n        const codeString = code.toString();\n        if (hasKey(_constants_js__WEBPACK_IMPORTED_MODULE_0__.errorValues, codeString)) {\n            return _constants_js__WEBPACK_IMPORTED_MODULE_0__.errorValues[codeString].message;\n        }\n        if (isJsonRpcServerError(code)) {\n            return JSON_RPC_SERVER_ERROR_MESSAGE;\n        }\n    }\n    return fallbackMessage;\n}\n/**\n * Returns whether the given code is valid.\n * A code is only valid if it has a message.\n */\nfunction isValidCode(code) {\n    if (!Number.isInteger(code)) {\n        return false;\n    }\n    const codeString = code.toString();\n    if (_constants_js__WEBPACK_IMPORTED_MODULE_0__.errorValues[codeString]) {\n        return true;\n    }\n    if (isJsonRpcServerError(code)) {\n        return true;\n    }\n    return false;\n}\n/**\n * Returns the error code from an error object.\n */\nfunction getErrorCode(error) {\n    var _a;\n    if (typeof error === 'number') {\n        return error;\n    }\n    else if (isErrorWithCode(error)) {\n        return (_a = error.code) !== null && _a !== void 0 ? _a : error.errorCode;\n    }\n    return undefined;\n}\nfunction isErrorWithCode(error) {\n    return (typeof error === 'object' &&\n        error !== null &&\n        (typeof error.code === 'number' ||\n            typeof error.errorCode === 'number'));\n}\nfunction serialize(error, { shouldIncludeStack = false } = {}) {\n    const serialized = {};\n    if (error &&\n        typeof error === 'object' &&\n        !Array.isArray(error) &&\n        hasKey(error, 'code') &&\n        isValidCode(error.code)) {\n        const _error = error;\n        serialized.code = _error.code;\n        if (_error.message && typeof _error.message === 'string') {\n            serialized.message = _error.message;\n            if (hasKey(_error, 'data')) {\n                serialized.data = _error.data;\n            }\n        }\n        else {\n            serialized.message = getMessageFromCode(serialized.code);\n            serialized.data = { originalError: assignOriginalError(error) };\n        }\n    }\n    else {\n        serialized.code = _constants_js__WEBPACK_IMPORTED_MODULE_0__.standardErrorCodes.rpc.internal;\n        serialized.message = hasStringProperty(error, 'message') ? error.message : FALLBACK_MESSAGE;\n        serialized.data = { originalError: assignOriginalError(error) };\n    }\n    if (shouldIncludeStack) {\n        serialized.stack = hasStringProperty(error, 'stack') ? error.stack : undefined;\n    }\n    return serialized;\n}\n// Internal\nfunction isJsonRpcServerError(code) {\n    return code >= -32099 && code <= -32000;\n}\nfunction assignOriginalError(error) {\n    if (error && typeof error === 'object' && !Array.isArray(error)) {\n        return Object.assign({}, error);\n    }\n    return error;\n}\nfunction hasKey(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\nfunction hasStringProperty(obj, prop) {\n    return (typeof obj === 'object' && obj !== null && prop in obj && typeof obj[prop] === 'string');\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/provider/interface.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/provider/interface.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProviderEventEmitter: () => (/* binding */ ProviderEventEmitter)\n/* harmony export */ });\n/* harmony import */ var eventemitter3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! eventemitter3 */ \"(ssr)/./node_modules/eventemitter3/index.mjs\");\n\nclass ProviderEventEmitter extends eventemitter3__WEBPACK_IMPORTED_MODULE_0__.EventEmitter {\n}\n//# sourceMappingURL=interface.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9jb3JlL3Byb3ZpZGVyL2ludGVyZmFjZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE2QztBQUN0QyxtQ0FBbUMsdURBQVk7QUFDdEQ7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2RyZWFtc3RhcnRyLy4vbm9kZV9tb2R1bGVzL0Bjb2luYmFzZS93YWxsZXQtc2RrL2Rpc3QvY29yZS9wcm92aWRlci9pbnRlcmZhY2UuanM/MjM5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFdmVudEVtaXR0ZXIgfSBmcm9tICdldmVudGVtaXR0ZXIzJztcbmV4cG9ydCBjbGFzcyBQcm92aWRlckV2ZW50RW1pdHRlciBleHRlbmRzIEV2ZW50RW1pdHRlciB7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbnRlcmZhY2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/provider/interface.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/storage/ScopedLocalStorage.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/storage/ScopedLocalStorage.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScopedLocalStorage: () => (/* binding */ ScopedLocalStorage)\n/* harmony export */ });\n// Copyright (c) 2018-2024 Coinbase, Inc. <https://www.coinbase.com/>\n// TODO: clean up, or possibly deprecate Storage class\nclass ScopedLocalStorage {\n    constructor(scope, module) {\n        this.scope = scope;\n        this.module = module;\n    }\n    storeObject(key, item) {\n        this.setItem(key, JSON.stringify(item));\n    }\n    loadObject(key) {\n        const item = this.getItem(key);\n        return item ? JSON.parse(item) : undefined;\n    }\n    setItem(key, value) {\n        localStorage.setItem(this.scopedKey(key), value);\n    }\n    getItem(key) {\n        return localStorage.getItem(this.scopedKey(key));\n    }\n    removeItem(key) {\n        localStorage.removeItem(this.scopedKey(key));\n    }\n    clear() {\n        const prefix = this.scopedKey('');\n        const keysToRemove = [];\n        for (let i = 0; i < localStorage.length; i++) {\n            const key = localStorage.key(i);\n            if (typeof key === 'string' && key.startsWith(prefix)) {\n                keysToRemove.push(key);\n            }\n        }\n        keysToRemove.forEach((key) => localStorage.removeItem(key));\n    }\n    scopedKey(key) {\n        return `-${this.scope}${this.module ? `:${this.module}` : ''}:${key}`;\n    }\n    static clearAll() {\n        new ScopedLocalStorage('CBWSDK').clear();\n        new ScopedLocalStorage('walletlink').clear();\n    }\n}\n//# sourceMappingURL=ScopedLocalStorage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/storage/ScopedLocalStorage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/type/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/type/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddressString: () => (/* binding */ AddressString),\n/* harmony export */   BigIntString: () => (/* binding */ BigIntString),\n/* harmony export */   HexString: () => (/* binding */ HexString),\n/* harmony export */   IntNumber: () => (/* binding */ IntNumber),\n/* harmony export */   OpaqueType: () => (/* binding */ OpaqueType),\n/* harmony export */   RegExpString: () => (/* binding */ RegExpString)\n/* harmony export */ });\nfunction OpaqueType() {\n    return (value) => value;\n}\nconst HexString = OpaqueType();\nconst AddressString = OpaqueType();\nconst BigIntString = OpaqueType();\nfunction IntNumber(num) {\n    return Math.floor(num);\n}\nconst RegExpString = OpaqueType();\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9jb3JlL3R5cGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ087QUFDQTtBQUNBO0FBQ0E7QUFDUDtBQUNBO0FBQ087QUFDUCIsInNvdXJjZXMiOlsid2VicGFjazovL2RyZWFtc3RhcnRyLy4vbm9kZV9tb2R1bGVzL0Bjb2luYmFzZS93YWxsZXQtc2RrL2Rpc3QvY29yZS90eXBlL2luZGV4LmpzP2VlMGYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIE9wYXF1ZVR5cGUoKSB7XG4gICAgcmV0dXJuICh2YWx1ZSkgPT4gdmFsdWU7XG59XG5leHBvcnQgY29uc3QgSGV4U3RyaW5nID0gT3BhcXVlVHlwZSgpO1xuZXhwb3J0IGNvbnN0IEFkZHJlc3NTdHJpbmcgPSBPcGFxdWVUeXBlKCk7XG5leHBvcnQgY29uc3QgQmlnSW50U3RyaW5nID0gT3BhcXVlVHlwZSgpO1xuZXhwb3J0IGZ1bmN0aW9uIEludE51bWJlcihudW0pIHtcbiAgICByZXR1cm4gTWF0aC5mbG9vcihudW0pO1xufVxuZXhwb3J0IGNvbnN0IFJlZ0V4cFN0cmluZyA9IE9wYXF1ZVR5cGUoKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/type/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js":
/*!******************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   areAddressArraysEqual: () => (/* binding */ areAddressArraysEqual),\n/* harmony export */   bigIntStringFromBigInt: () => (/* binding */ bigIntStringFromBigInt),\n/* harmony export */   encodeToHexString: () => (/* binding */ encodeToHexString),\n/* harmony export */   ensureAddressString: () => (/* binding */ ensureAddressString),\n/* harmony export */   ensureBigInt: () => (/* binding */ ensureBigInt),\n/* harmony export */   ensureBuffer: () => (/* binding */ ensureBuffer),\n/* harmony export */   ensureEvenLengthHexString: () => (/* binding */ ensureEvenLengthHexString),\n/* harmony export */   ensureHexString: () => (/* binding */ ensureHexString),\n/* harmony export */   ensureIntNumber: () => (/* binding */ ensureIntNumber),\n/* harmony export */   ensureParsedJSONObject: () => (/* binding */ ensureParsedJSONObject),\n/* harmony export */   ensureRegExpString: () => (/* binding */ ensureRegExpString),\n/* harmony export */   getFavicon: () => (/* binding */ getFavicon),\n/* harmony export */   has0xPrefix: () => (/* binding */ has0xPrefix),\n/* harmony export */   hexStringFromBuffer: () => (/* binding */ hexStringFromBuffer),\n/* harmony export */   hexStringFromNumber: () => (/* binding */ hexStringFromNumber),\n/* harmony export */   hexStringToUint8Array: () => (/* binding */ hexStringToUint8Array),\n/* harmony export */   intNumberFromHexString: () => (/* binding */ intNumberFromHexString),\n/* harmony export */   isBigNumber: () => (/* binding */ isBigNumber),\n/* harmony export */   isHexString: () => (/* binding */ isHexString),\n/* harmony export */   prepend0x: () => (/* binding */ prepend0x),\n/* harmony export */   randomBytesHex: () => (/* binding */ randomBytesHex),\n/* harmony export */   range: () => (/* binding */ range),\n/* harmony export */   strip0x: () => (/* binding */ strip0x),\n/* harmony export */   uint8ArrayToHex: () => (/* binding */ uint8ArrayToHex)\n/* harmony export */ });\n/* harmony import */ var _error_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../error/errors.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/type/index.js\");\n/* eslint-disable @typescript-eslint/no-explicit-any */\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\n\n\nconst INT_STRING_REGEX = /^[0-9]*$/;\nconst HEXADECIMAL_STRING_REGEX = /^[a-f0-9]*$/;\n/**\n * @param length number of bytes\n */\nfunction randomBytesHex(length) {\n    return uint8ArrayToHex(crypto.getRandomValues(new Uint8Array(length)));\n}\nfunction uint8ArrayToHex(value) {\n    return [...value].map((b) => b.toString(16).padStart(2, '0')).join('');\n}\nfunction hexStringToUint8Array(hexString) {\n    return new Uint8Array(hexString.match(/.{1,2}/g).map((byte) => Number.parseInt(byte, 16)));\n}\nfunction hexStringFromBuffer(buf, includePrefix = false) {\n    const hex = buf.toString('hex');\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.HexString)(includePrefix ? `0x${hex}` : hex);\n}\nfunction encodeToHexString(str) {\n    return hexStringFromBuffer(ensureBuffer(str), true);\n}\nfunction bigIntStringFromBigInt(bi) {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.BigIntString)(bi.toString(10));\n}\nfunction intNumberFromHexString(hex) {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.IntNumber)(Number(BigInt(ensureEvenLengthHexString(hex, true))));\n}\nfunction hexStringFromNumber(num) {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.HexString)(`0x${BigInt(num).toString(16)}`);\n}\nfunction has0xPrefix(str) {\n    return str.startsWith('0x') || str.startsWith('0X');\n}\nfunction strip0x(hex) {\n    if (has0xPrefix(hex)) {\n        return hex.slice(2);\n    }\n    return hex;\n}\nfunction prepend0x(hex) {\n    if (has0xPrefix(hex)) {\n        return `0x${hex.slice(2)}`;\n    }\n    return `0x${hex}`;\n}\nfunction isHexString(hex) {\n    if (typeof hex !== 'string') {\n        return false;\n    }\n    const s = strip0x(hex).toLowerCase();\n    return HEXADECIMAL_STRING_REGEX.test(s);\n}\nfunction ensureHexString(hex, includePrefix = false) {\n    if (typeof hex === 'string') {\n        const s = strip0x(hex).toLowerCase();\n        if (HEXADECIMAL_STRING_REGEX.test(s)) {\n            return (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.HexString)(includePrefix ? `0x${s}` : s);\n        }\n    }\n    throw _error_errors_js__WEBPACK_IMPORTED_MODULE_1__.standardErrors.rpc.invalidParams(`\"${String(hex)}\" is not a hexadecimal string`);\n}\nfunction ensureEvenLengthHexString(hex, includePrefix = false) {\n    let h = ensureHexString(hex, false);\n    if (h.length % 2 === 1) {\n        h = (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.HexString)(`0${h}`);\n    }\n    return includePrefix ? (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.HexString)(`0x${h}`) : h;\n}\nfunction ensureAddressString(str) {\n    if (typeof str === 'string') {\n        const s = strip0x(str).toLowerCase();\n        if (isHexString(s) && s.length === 40) {\n            return (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.AddressString)(prepend0x(s));\n        }\n    }\n    throw _error_errors_js__WEBPACK_IMPORTED_MODULE_1__.standardErrors.rpc.invalidParams(`Invalid Ethereum address: ${String(str)}`);\n}\nfunction ensureBuffer(str) {\n    if (Buffer.isBuffer(str)) {\n        return str;\n    }\n    if (typeof str === 'string') {\n        if (isHexString(str)) {\n            const s = ensureEvenLengthHexString(str, false);\n            return Buffer.from(s, 'hex');\n        }\n        return Buffer.from(str, 'utf8');\n    }\n    throw _error_errors_js__WEBPACK_IMPORTED_MODULE_1__.standardErrors.rpc.invalidParams(`Not binary data: ${String(str)}`);\n}\nfunction ensureIntNumber(num) {\n    if (typeof num === 'number' && Number.isInteger(num)) {\n        return (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.IntNumber)(num);\n    }\n    if (typeof num === 'string') {\n        if (INT_STRING_REGEX.test(num)) {\n            return (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.IntNumber)(Number(num));\n        }\n        if (isHexString(num)) {\n            return (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.IntNumber)(Number(BigInt(ensureEvenLengthHexString(num, true))));\n        }\n    }\n    throw _error_errors_js__WEBPACK_IMPORTED_MODULE_1__.standardErrors.rpc.invalidParams(`Not an integer: ${String(num)}`);\n}\nfunction ensureRegExpString(regExp) {\n    if (regExp instanceof RegExp) {\n        return (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.RegExpString)(regExp.toString());\n    }\n    throw _error_errors_js__WEBPACK_IMPORTED_MODULE_1__.standardErrors.rpc.invalidParams(`Not a RegExp: ${String(regExp)}`);\n}\nfunction ensureBigInt(val) {\n    if (val !== null && (typeof val === 'bigint' || isBigNumber(val))) {\n        return BigInt(val.toString(10));\n    }\n    if (typeof val === 'number') {\n        return BigInt(ensureIntNumber(val));\n    }\n    if (typeof val === 'string') {\n        if (INT_STRING_REGEX.test(val)) {\n            return BigInt(val);\n        }\n        if (isHexString(val)) {\n            return BigInt(ensureEvenLengthHexString(val, true));\n        }\n    }\n    throw _error_errors_js__WEBPACK_IMPORTED_MODULE_1__.standardErrors.rpc.invalidParams(`Not an integer: ${String(val)}`);\n}\nfunction ensureParsedJSONObject(val) {\n    if (typeof val === 'string') {\n        return JSON.parse(val);\n    }\n    if (typeof val === 'object') {\n        return val;\n    }\n    throw _error_errors_js__WEBPACK_IMPORTED_MODULE_1__.standardErrors.rpc.invalidParams(`Not a JSON string or an object: ${String(val)}`);\n}\nfunction isBigNumber(val) {\n    if (val == null || typeof val.constructor !== 'function') {\n        return false;\n    }\n    const { constructor } = val;\n    return typeof constructor.config === 'function' && typeof constructor.EUCLID === 'number';\n}\nfunction range(start, stop) {\n    return Array.from({ length: stop - start }, (_, i) => start + i);\n}\nfunction getFavicon() {\n    const el = document.querySelector('link[sizes=\"192x192\"]') ||\n        document.querySelector('link[sizes=\"180x180\"]') ||\n        document.querySelector('link[rel=\"icon\"]') ||\n        document.querySelector('link[rel=\"shortcut icon\"]');\n    const { protocol, host } = document.location;\n    const href = el ? el.getAttribute('href') : null;\n    if (!href || href.startsWith('javascript:') || href.startsWith('vbscript:')) {\n        return `${protocol}//${host}/favicon.ico`; // fallback\n    }\n    if (href.startsWith('http://') || href.startsWith('https://') || href.startsWith('data:')) {\n        return href;\n    }\n    if (href.startsWith('//')) {\n        return protocol + href;\n    }\n    return `${protocol}//${host}${href}`;\n}\nfunction areAddressArraysEqual(arr1, arr2) {\n    return arr1.length === arr2.length && arr1.every((value, index) => value === arr2[index]);\n}\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/createCoinbaseWalletProvider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/createCoinbaseWalletProvider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCoinbaseWalletProvider: () => (/* binding */ createCoinbaseWalletProvider)\n/* harmony export */ });\n/* harmony import */ var _CoinbaseWalletProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CoinbaseWalletProvider.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/CoinbaseWalletProvider.js\");\n/* harmony import */ var _util_provider_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/provider.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/provider.js\");\n\n\nfunction createCoinbaseWalletProvider(options) {\n    var _a;\n    const params = {\n        metadata: options.metadata,\n        preference: options.preference,\n    };\n    return (_a = (0,_util_provider_js__WEBPACK_IMPORTED_MODULE_0__.getCoinbaseInjectedProvider)(params)) !== null && _a !== void 0 ? _a : new _CoinbaseWalletProvider_js__WEBPACK_IMPORTED_MODULE_1__.CoinbaseWalletProvider(params);\n}\n//# sourceMappingURL=createCoinbaseWalletProvider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9jcmVhdGVDb2luYmFzZVdhbGxldFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRTtBQUNKO0FBQzFEO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQiw4RUFBMkIsK0NBQStDLDhFQUFzQjtBQUNqSDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJlYW1zdGFydHIvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9jcmVhdGVDb2luYmFzZVdhbGxldFByb3ZpZGVyLmpzPzlhMjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ29pbmJhc2VXYWxsZXRQcm92aWRlciB9IGZyb20gJy4vQ29pbmJhc2VXYWxsZXRQcm92aWRlci5qcyc7XG5pbXBvcnQgeyBnZXRDb2luYmFzZUluamVjdGVkUHJvdmlkZXIgfSBmcm9tICcuL3V0aWwvcHJvdmlkZXIuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUNvaW5iYXNlV2FsbGV0UHJvdmlkZXIob3B0aW9ucykge1xuICAgIHZhciBfYTtcbiAgICBjb25zdCBwYXJhbXMgPSB7XG4gICAgICAgIG1ldGFkYXRhOiBvcHRpb25zLm1ldGFkYXRhLFxuICAgICAgICBwcmVmZXJlbmNlOiBvcHRpb25zLnByZWZlcmVuY2UsXG4gICAgfTtcbiAgICByZXR1cm4gKF9hID0gZ2V0Q29pbmJhc2VJbmplY3RlZFByb3ZpZGVyKHBhcmFtcykpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IG5ldyBDb2luYmFzZVdhbGxldFByb3ZpZGVyKHBhcmFtcyk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jcmVhdGVDb2luYmFzZVdhbGxldFByb3ZpZGVyLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/createCoinbaseWalletProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/createCoinbaseWalletSDK.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/createCoinbaseWalletSDK.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCoinbaseWalletSDK: () => (/* binding */ createCoinbaseWalletSDK)\n/* harmony export */ });\n/* harmony import */ var _createCoinbaseWalletProvider_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./createCoinbaseWalletProvider.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/createCoinbaseWalletProvider.js\");\n/* harmony import */ var _sdk_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sdk-info.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sdk-info.js\");\n/* harmony import */ var _core_storage_ScopedLocalStorage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core/storage/ScopedLocalStorage.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/storage/ScopedLocalStorage.js\");\n/* harmony import */ var _util_checkCrossOriginOpenerPolicy_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/checkCrossOriginOpenerPolicy.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/checkCrossOriginOpenerPolicy.js\");\n/* harmony import */ var _util_validatePreferences_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./util/validatePreferences.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/validatePreferences.js\");\n\n\n\n\n\nconst DEFAULT_PREFERENCE = {\n    options: 'all',\n};\n/**\n * Create a Coinbase Wallet SDK instance.\n * @param params - Options to create a Coinbase Wallet SDK instance.\n * @returns A Coinbase Wallet SDK object.\n */\nfunction createCoinbaseWalletSDK(params) {\n    var _a;\n    const versionStorage = new _core_storage_ScopedLocalStorage_js__WEBPACK_IMPORTED_MODULE_0__.ScopedLocalStorage('CBWSDK');\n    versionStorage.setItem('VERSION', _sdk_info_js__WEBPACK_IMPORTED_MODULE_1__.VERSION);\n    void (0,_util_checkCrossOriginOpenerPolicy_js__WEBPACK_IMPORTED_MODULE_2__.checkCrossOriginOpenerPolicy)();\n    const options = {\n        metadata: {\n            appName: params.appName || 'Dapp',\n            appLogoUrl: params.appLogoUrl || '',\n            appChainIds: params.appChainIds || [],\n        },\n        preference: Object.assign(DEFAULT_PREFERENCE, (_a = params.preference) !== null && _a !== void 0 ? _a : {}),\n    };\n    /**\n     * Validate user supplied preferences. Throws if key/values are not valid.\n     */\n    (0,_util_validatePreferences_js__WEBPACK_IMPORTED_MODULE_3__.validatePreferences)(options.preference);\n    let provider = null;\n    return {\n        getProvider: () => {\n            if (!provider) {\n                provider = (0,_createCoinbaseWalletProvider_js__WEBPACK_IMPORTED_MODULE_4__.createCoinbaseWalletProvider)(options);\n            }\n            return provider;\n        },\n    };\n}\n//# sourceMappingURL=createCoinbaseWalletSDK.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/createCoinbaseWalletSDK.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sdk-info.js":
/*!************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sdk-info.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NAME: () => (/* binding */ NAME),\n/* harmony export */   VERSION: () => (/* binding */ VERSION)\n/* harmony export */ });\nconst VERSION = '4.3.2';\nconst NAME = '@coinbase/wallet-sdk';\n//# sourceMappingURL=sdk-info.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9zZGstaW5mby5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ0E7QUFDUCIsInNvdXJjZXMiOlsid2VicGFjazovL2RyZWFtc3RhcnRyLy4vbm9kZV9tb2R1bGVzL0Bjb2luYmFzZS93YWxsZXQtc2RrL2Rpc3Qvc2RrLWluZm8uanM/YjdiOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgVkVSU0lPTiA9ICc0LjMuMic7XG5leHBvcnQgY29uc3QgTkFNRSA9ICdAY29pbmJhc2Uvd2FsbGV0LXNkayc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zZGstaW5mby5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sdk-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWKeyManager.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWKeyManager.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SCWKeyManager: () => (/* binding */ SCWKeyManager)\n/* harmony export */ });\n/* harmony import */ var _core_storage_ScopedLocalStorage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../core/storage/ScopedLocalStorage.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/storage/ScopedLocalStorage.js\");\n/* harmony import */ var _util_cipher_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/cipher.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/cipher.js\");\n\n\nconst OWN_PRIVATE_KEY = {\n    storageKey: 'ownPrivateKey',\n    keyType: 'private',\n};\nconst OWN_PUBLIC_KEY = {\n    storageKey: 'ownPublicKey',\n    keyType: 'public',\n};\nconst PEER_PUBLIC_KEY = {\n    storageKey: 'peerPublicKey',\n    keyType: 'public',\n};\nclass SCWKeyManager {\n    constructor() {\n        this.storage = new _core_storage_ScopedLocalStorage_js__WEBPACK_IMPORTED_MODULE_0__.ScopedLocalStorage('CBWSDK', 'SCWKeyManager');\n        this.ownPrivateKey = null;\n        this.ownPublicKey = null;\n        this.peerPublicKey = null;\n        this.sharedSecret = null;\n    }\n    async getOwnPublicKey() {\n        await this.loadKeysIfNeeded();\n        return this.ownPublicKey;\n    }\n    // returns null if the shared secret is not yet derived\n    async getSharedSecret() {\n        await this.loadKeysIfNeeded();\n        return this.sharedSecret;\n    }\n    async setPeerPublicKey(key) {\n        this.sharedSecret = null;\n        this.peerPublicKey = key;\n        await this.storeKey(PEER_PUBLIC_KEY, key);\n        await this.loadKeysIfNeeded();\n    }\n    async clear() {\n        this.ownPrivateKey = null;\n        this.ownPublicKey = null;\n        this.peerPublicKey = null;\n        this.sharedSecret = null;\n        this.storage.removeItem(OWN_PUBLIC_KEY.storageKey);\n        this.storage.removeItem(OWN_PRIVATE_KEY.storageKey);\n        this.storage.removeItem(PEER_PUBLIC_KEY.storageKey);\n    }\n    async generateKeyPair() {\n        const newKeyPair = await (0,_util_cipher_js__WEBPACK_IMPORTED_MODULE_1__.generateKeyPair)();\n        this.ownPrivateKey = newKeyPair.privateKey;\n        this.ownPublicKey = newKeyPair.publicKey;\n        await this.storeKey(OWN_PRIVATE_KEY, newKeyPair.privateKey);\n        await this.storeKey(OWN_PUBLIC_KEY, newKeyPair.publicKey);\n    }\n    async loadKeysIfNeeded() {\n        if (this.ownPrivateKey === null) {\n            this.ownPrivateKey = await this.loadKey(OWN_PRIVATE_KEY);\n        }\n        if (this.ownPublicKey === null) {\n            this.ownPublicKey = await this.loadKey(OWN_PUBLIC_KEY);\n        }\n        if (this.ownPrivateKey === null || this.ownPublicKey === null) {\n            await this.generateKeyPair();\n        }\n        if (this.peerPublicKey === null) {\n            this.peerPublicKey = await this.loadKey(PEER_PUBLIC_KEY);\n        }\n        if (this.sharedSecret === null) {\n            if (this.ownPrivateKey === null || this.peerPublicKey === null)\n                return;\n            this.sharedSecret = await (0,_util_cipher_js__WEBPACK_IMPORTED_MODULE_1__.deriveSharedSecret)(this.ownPrivateKey, this.peerPublicKey);\n        }\n    }\n    // storage methods\n    async loadKey(item) {\n        const key = this.storage.getItem(item.storageKey);\n        if (!key)\n            return null;\n        return (0,_util_cipher_js__WEBPACK_IMPORTED_MODULE_1__.importKeyFromHexString)(item.keyType, key);\n    }\n    async storeKey(item, key) {\n        const hexString = await (0,_util_cipher_js__WEBPACK_IMPORTED_MODULE_1__.exportKeyToHexString)(item.keyType, key);\n        this.storage.setItem(item.storageKey, hexString);\n    }\n}\n//# sourceMappingURL=SCWKeyManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9zaWduL3Njdy9TQ1dLZXlNYW5hZ2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4RTtBQUM0QztBQUMxSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EsMkJBQTJCLG1GQUFrQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsZ0VBQWU7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLG1FQUFrQjtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsdUVBQXNCO0FBQ3JDO0FBQ0E7QUFDQSxnQ0FBZ0MscUVBQW9CO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJlYW1zdGFydHIvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9zaWduL3Njdy9TQ1dLZXlNYW5hZ2VyLmpzPzE0YjUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU2NvcGVkTG9jYWxTdG9yYWdlIH0gZnJvbSAnLi4vLi4vY29yZS9zdG9yYWdlL1Njb3BlZExvY2FsU3RvcmFnZS5qcyc7XG5pbXBvcnQgeyBkZXJpdmVTaGFyZWRTZWNyZXQsIGV4cG9ydEtleVRvSGV4U3RyaW5nLCBnZW5lcmF0ZUtleVBhaXIsIGltcG9ydEtleUZyb21IZXhTdHJpbmcsIH0gZnJvbSAnLi4vLi4vdXRpbC9jaXBoZXIuanMnO1xuY29uc3QgT1dOX1BSSVZBVEVfS0VZID0ge1xuICAgIHN0b3JhZ2VLZXk6ICdvd25Qcml2YXRlS2V5JyxcbiAgICBrZXlUeXBlOiAncHJpdmF0ZScsXG59O1xuY29uc3QgT1dOX1BVQkxJQ19LRVkgPSB7XG4gICAgc3RvcmFnZUtleTogJ293blB1YmxpY0tleScsXG4gICAga2V5VHlwZTogJ3B1YmxpYycsXG59O1xuY29uc3QgUEVFUl9QVUJMSUNfS0VZID0ge1xuICAgIHN0b3JhZ2VLZXk6ICdwZWVyUHVibGljS2V5JyxcbiAgICBrZXlUeXBlOiAncHVibGljJyxcbn07XG5leHBvcnQgY2xhc3MgU0NXS2V5TWFuYWdlciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMuc3RvcmFnZSA9IG5ldyBTY29wZWRMb2NhbFN0b3JhZ2UoJ0NCV1NESycsICdTQ1dLZXlNYW5hZ2VyJyk7XG4gICAgICAgIHRoaXMub3duUHJpdmF0ZUtleSA9IG51bGw7XG4gICAgICAgIHRoaXMub3duUHVibGljS2V5ID0gbnVsbDtcbiAgICAgICAgdGhpcy5wZWVyUHVibGljS2V5ID0gbnVsbDtcbiAgICAgICAgdGhpcy5zaGFyZWRTZWNyZXQgPSBudWxsO1xuICAgIH1cbiAgICBhc3luYyBnZXRPd25QdWJsaWNLZXkoKSB7XG4gICAgICAgIGF3YWl0IHRoaXMubG9hZEtleXNJZk5lZWRlZCgpO1xuICAgICAgICByZXR1cm4gdGhpcy5vd25QdWJsaWNLZXk7XG4gICAgfVxuICAgIC8vIHJldHVybnMgbnVsbCBpZiB0aGUgc2hhcmVkIHNlY3JldCBpcyBub3QgeWV0IGRlcml2ZWRcbiAgICBhc3luYyBnZXRTaGFyZWRTZWNyZXQoKSB7XG4gICAgICAgIGF3YWl0IHRoaXMubG9hZEtleXNJZk5lZWRlZCgpO1xuICAgICAgICByZXR1cm4gdGhpcy5zaGFyZWRTZWNyZXQ7XG4gICAgfVxuICAgIGFzeW5jIHNldFBlZXJQdWJsaWNLZXkoa2V5KSB7XG4gICAgICAgIHRoaXMuc2hhcmVkU2VjcmV0ID0gbnVsbDtcbiAgICAgICAgdGhpcy5wZWVyUHVibGljS2V5ID0ga2V5O1xuICAgICAgICBhd2FpdCB0aGlzLnN0b3JlS2V5KFBFRVJfUFVCTElDX0tFWSwga2V5KTtcbiAgICAgICAgYXdhaXQgdGhpcy5sb2FkS2V5c0lmTmVlZGVkKCk7XG4gICAgfVxuICAgIGFzeW5jIGNsZWFyKCkge1xuICAgICAgICB0aGlzLm93blByaXZhdGVLZXkgPSBudWxsO1xuICAgICAgICB0aGlzLm93blB1YmxpY0tleSA9IG51bGw7XG4gICAgICAgIHRoaXMucGVlclB1YmxpY0tleSA9IG51bGw7XG4gICAgICAgIHRoaXMuc2hhcmVkU2VjcmV0ID0gbnVsbDtcbiAgICAgICAgdGhpcy5zdG9yYWdlLnJlbW92ZUl0ZW0oT1dOX1BVQkxJQ19LRVkuc3RvcmFnZUtleSk7XG4gICAgICAgIHRoaXMuc3RvcmFnZS5yZW1vdmVJdGVtKE9XTl9QUklWQVRFX0tFWS5zdG9yYWdlS2V5KTtcbiAgICAgICAgdGhpcy5zdG9yYWdlLnJlbW92ZUl0ZW0oUEVFUl9QVUJMSUNfS0VZLnN0b3JhZ2VLZXkpO1xuICAgIH1cbiAgICBhc3luYyBnZW5lcmF0ZUtleVBhaXIoKSB7XG4gICAgICAgIGNvbnN0IG5ld0tleVBhaXIgPSBhd2FpdCBnZW5lcmF0ZUtleVBhaXIoKTtcbiAgICAgICAgdGhpcy5vd25Qcml2YXRlS2V5ID0gbmV3S2V5UGFpci5wcml2YXRlS2V5O1xuICAgICAgICB0aGlzLm93blB1YmxpY0tleSA9IG5ld0tleVBhaXIucHVibGljS2V5O1xuICAgICAgICBhd2FpdCB0aGlzLnN0b3JlS2V5KE9XTl9QUklWQVRFX0tFWSwgbmV3S2V5UGFpci5wcml2YXRlS2V5KTtcbiAgICAgICAgYXdhaXQgdGhpcy5zdG9yZUtleShPV05fUFVCTElDX0tFWSwgbmV3S2V5UGFpci5wdWJsaWNLZXkpO1xuICAgIH1cbiAgICBhc3luYyBsb2FkS2V5c0lmTmVlZGVkKCkge1xuICAgICAgICBpZiAodGhpcy5vd25Qcml2YXRlS2V5ID09PSBudWxsKSB7XG4gICAgICAgICAgICB0aGlzLm93blByaXZhdGVLZXkgPSBhd2FpdCB0aGlzLmxvYWRLZXkoT1dOX1BSSVZBVEVfS0VZKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5vd25QdWJsaWNLZXkgPT09IG51bGwpIHtcbiAgICAgICAgICAgIHRoaXMub3duUHVibGljS2V5ID0gYXdhaXQgdGhpcy5sb2FkS2V5KE9XTl9QVUJMSUNfS0VZKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5vd25Qcml2YXRlS2V5ID09PSBudWxsIHx8IHRoaXMub3duUHVibGljS2V5ID09PSBudWxsKSB7XG4gICAgICAgICAgICBhd2FpdCB0aGlzLmdlbmVyYXRlS2V5UGFpcigpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLnBlZXJQdWJsaWNLZXkgPT09IG51bGwpIHtcbiAgICAgICAgICAgIHRoaXMucGVlclB1YmxpY0tleSA9IGF3YWl0IHRoaXMubG9hZEtleShQRUVSX1BVQkxJQ19LRVkpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLnNoYXJlZFNlY3JldCA9PT0gbnVsbCkge1xuICAgICAgICAgICAgaWYgKHRoaXMub3duUHJpdmF0ZUtleSA9PT0gbnVsbCB8fCB0aGlzLnBlZXJQdWJsaWNLZXkgPT09IG51bGwpXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgdGhpcy5zaGFyZWRTZWNyZXQgPSBhd2FpdCBkZXJpdmVTaGFyZWRTZWNyZXQodGhpcy5vd25Qcml2YXRlS2V5LCB0aGlzLnBlZXJQdWJsaWNLZXkpO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8vIHN0b3JhZ2UgbWV0aG9kc1xuICAgIGFzeW5jIGxvYWRLZXkoaXRlbSkge1xuICAgICAgICBjb25zdCBrZXkgPSB0aGlzLnN0b3JhZ2UuZ2V0SXRlbShpdGVtLnN0b3JhZ2VLZXkpO1xuICAgICAgICBpZiAoIWtleSlcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICByZXR1cm4gaW1wb3J0S2V5RnJvbUhleFN0cmluZyhpdGVtLmtleVR5cGUsIGtleSk7XG4gICAgfVxuICAgIGFzeW5jIHN0b3JlS2V5KGl0ZW0sIGtleSkge1xuICAgICAgICBjb25zdCBoZXhTdHJpbmcgPSBhd2FpdCBleHBvcnRLZXlUb0hleFN0cmluZyhpdGVtLmtleVR5cGUsIGtleSk7XG4gICAgICAgIHRoaXMuc3RvcmFnZS5zZXRJdGVtKGl0ZW0uc3RvcmFnZUtleSwgaGV4U3RyaW5nKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1TQ1dLZXlNYW5hZ2VyLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWKeyManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWSigner.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWSigner.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SCWSigner: () => (/* binding */ SCWSigner)\n/* harmony export */ });\n/* harmony import */ var _SCWKeyManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SCWKeyManager.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWKeyManager.js\");\n/* harmony import */ var _core_error_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../core/error/errors.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js\");\n/* harmony import */ var _core_storage_ScopedLocalStorage_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../core/storage/ScopedLocalStorage.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/storage/ScopedLocalStorage.js\");\n/* harmony import */ var _core_type_util_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../core/type/util.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\n/* harmony import */ var _util_cipher_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../util/cipher.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/cipher.js\");\n/* harmony import */ var _util_provider_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../util/provider.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/provider.js\");\n\n\n\n\n\n\nconst ACCOUNTS_KEY = 'accounts';\nconst ACTIVE_CHAIN_STORAGE_KEY = 'activeChain';\nconst AVAILABLE_CHAINS_STORAGE_KEY = 'availableChains';\nconst WALLET_CAPABILITIES_STORAGE_KEY = 'walletCapabilities';\nclass SCWSigner {\n    constructor(params) {\n        var _a, _b, _c;\n        this.metadata = params.metadata;\n        this.communicator = params.communicator;\n        this.callback = params.callback;\n        this.keyManager = new _SCWKeyManager_js__WEBPACK_IMPORTED_MODULE_0__.SCWKeyManager();\n        this.storage = new _core_storage_ScopedLocalStorage_js__WEBPACK_IMPORTED_MODULE_1__.ScopedLocalStorage('CBWSDK', 'SCWStateManager');\n        this.accounts = (_a = this.storage.loadObject(ACCOUNTS_KEY)) !== null && _a !== void 0 ? _a : [];\n        this.chain = this.storage.loadObject(ACTIVE_CHAIN_STORAGE_KEY) || {\n            id: (_c = (_b = params.metadata.appChainIds) === null || _b === void 0 ? void 0 : _b[0]) !== null && _c !== void 0 ? _c : 1,\n        };\n        this.handshake = this.handshake.bind(this);\n        this.request = this.request.bind(this);\n        this.createRequestMessage = this.createRequestMessage.bind(this);\n        this.decryptResponseMessage = this.decryptResponseMessage.bind(this);\n    }\n    async handshake(args) {\n        var _a, _b, _c, _d;\n        // Open the popup before constructing the request message.\n        // This is to ensure that the popup is not blocked by some browsers (i.e. Safari)\n        await ((_b = (_a = this.communicator).waitForPopupLoaded) === null || _b === void 0 ? void 0 : _b.call(_a));\n        const handshakeMessage = await this.createRequestMessage({\n            handshake: {\n                method: args.method,\n                params: Object.assign({}, this.metadata, (_c = args.params) !== null && _c !== void 0 ? _c : {}),\n            },\n        });\n        const response = await this.communicator.postRequestAndWaitForResponse(handshakeMessage);\n        // store peer's public key\n        if ('failure' in response.content)\n            throw response.content.failure;\n        const peerPublicKey = await (0,_util_cipher_js__WEBPACK_IMPORTED_MODULE_2__.importKeyFromHexString)('public', response.sender);\n        await this.keyManager.setPeerPublicKey(peerPublicKey);\n        const decrypted = await this.decryptResponseMessage(response);\n        const result = decrypted.result;\n        if ('error' in result)\n            throw result.error;\n        switch (args.method) {\n            case 'eth_requestAccounts': {\n                const accounts = result.value;\n                this.accounts = accounts;\n                this.storage.storeObject(ACCOUNTS_KEY, accounts);\n                (_d = this.callback) === null || _d === void 0 ? void 0 : _d.call(this, 'accountsChanged', accounts);\n                break;\n            }\n        }\n    }\n    async request(request) {\n        var _a;\n        if (this.accounts.length === 0) {\n            switch (request.method) {\n                case 'wallet_sendCalls':\n                    return this.sendRequestToPopup(request);\n                default:\n                    throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_3__.standardErrors.provider.unauthorized();\n            }\n        }\n        switch (request.method) {\n            case 'eth_requestAccounts':\n                (_a = this.callback) === null || _a === void 0 ? void 0 : _a.call(this, 'connect', { chainId: (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_4__.hexStringFromNumber)(this.chain.id) });\n                return this.accounts;\n            case 'eth_accounts':\n                return this.accounts;\n            case 'eth_coinbase':\n                return this.accounts[0];\n            case 'net_version':\n                return this.chain.id;\n            case 'eth_chainId':\n                return (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_4__.hexStringFromNumber)(this.chain.id);\n            case 'wallet_getCapabilities':\n                return this.storage.loadObject(WALLET_CAPABILITIES_STORAGE_KEY);\n            case 'wallet_switchEthereumChain':\n                return this.handleSwitchChainRequest(request);\n            case 'eth_ecRecover':\n            case 'personal_sign':\n            case 'wallet_sign':\n            case 'personal_ecRecover':\n            case 'eth_signTransaction':\n            case 'eth_sendTransaction':\n            case 'eth_signTypedData_v1':\n            case 'eth_signTypedData_v3':\n            case 'eth_signTypedData_v4':\n            case 'eth_signTypedData':\n            case 'wallet_addEthereumChain':\n            case 'wallet_watchAsset':\n            case 'wallet_sendCalls':\n            case 'wallet_showCallsStatus':\n            case 'wallet_grantPermissions':\n                return this.sendRequestToPopup(request);\n            default:\n                if (!this.chain.rpcUrl)\n                    throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_3__.standardErrors.rpc.internal('No RPC URL set for chain');\n                return (0,_util_provider_js__WEBPACK_IMPORTED_MODULE_5__.fetchRPCRequest)(request, this.chain.rpcUrl);\n        }\n    }\n    async sendRequestToPopup(request) {\n        var _a, _b;\n        // Open the popup before constructing the request message.\n        // This is to ensure that the popup is not blocked by some browsers (i.e. Safari)\n        await ((_b = (_a = this.communicator).waitForPopupLoaded) === null || _b === void 0 ? void 0 : _b.call(_a));\n        const response = await this.sendEncryptedRequest(request);\n        const decrypted = await this.decryptResponseMessage(response);\n        const result = decrypted.result;\n        if ('error' in result)\n            throw result.error;\n        return result.value;\n    }\n    async cleanup() {\n        var _a, _b;\n        this.storage.clear();\n        await this.keyManager.clear();\n        this.accounts = [];\n        this.chain = {\n            id: (_b = (_a = this.metadata.appChainIds) === null || _a === void 0 ? void 0 : _a[0]) !== null && _b !== void 0 ? _b : 1,\n        };\n    }\n    /**\n     * @returns `null` if the request was successful.\n     * https://eips.ethereum.org/EIPS/eip-3326#wallet_switchethereumchain\n     */\n    async handleSwitchChainRequest(request) {\n        var _a;\n        const params = request.params;\n        if (!params || !((_a = params[0]) === null || _a === void 0 ? void 0 : _a.chainId)) {\n            throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_3__.standardErrors.rpc.invalidParams();\n        }\n        const chainId = (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_4__.ensureIntNumber)(params[0].chainId);\n        const localResult = this.updateChain(chainId);\n        if (localResult)\n            return null;\n        const popupResult = await this.sendRequestToPopup(request);\n        if (popupResult === null) {\n            this.updateChain(chainId);\n        }\n        return popupResult;\n    }\n    async sendEncryptedRequest(request) {\n        const sharedSecret = await this.keyManager.getSharedSecret();\n        if (!sharedSecret) {\n            throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_3__.standardErrors.provider.unauthorized('No valid session found, try requestAccounts before other methods');\n        }\n        const encrypted = await (0,_util_cipher_js__WEBPACK_IMPORTED_MODULE_2__.encryptContent)({\n            action: request,\n            chainId: this.chain.id,\n        }, sharedSecret);\n        const message = await this.createRequestMessage({ encrypted });\n        return this.communicator.postRequestAndWaitForResponse(message);\n    }\n    async createRequestMessage(content) {\n        const publicKey = await (0,_util_cipher_js__WEBPACK_IMPORTED_MODULE_2__.exportKeyToHexString)('public', await this.keyManager.getOwnPublicKey());\n        return {\n            id: crypto.randomUUID(),\n            sender: publicKey,\n            content,\n            timestamp: new Date(),\n        };\n    }\n    async decryptResponseMessage(message) {\n        var _a, _b;\n        const content = message.content;\n        // throw protocol level error\n        if ('failure' in content) {\n            throw content.failure;\n        }\n        const sharedSecret = await this.keyManager.getSharedSecret();\n        if (!sharedSecret) {\n            throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_3__.standardErrors.provider.unauthorized('Invalid session');\n        }\n        const response = await (0,_util_cipher_js__WEBPACK_IMPORTED_MODULE_2__.decryptContent)(content.encrypted, sharedSecret);\n        const availableChains = (_a = response.data) === null || _a === void 0 ? void 0 : _a.chains;\n        if (availableChains) {\n            const chains = Object.entries(availableChains).map(([id, rpcUrl]) => ({\n                id: Number(id),\n                rpcUrl,\n            }));\n            this.storage.storeObject(AVAILABLE_CHAINS_STORAGE_KEY, chains);\n            this.updateChain(this.chain.id, chains);\n        }\n        const walletCapabilities = (_b = response.data) === null || _b === void 0 ? void 0 : _b.capabilities;\n        if (walletCapabilities) {\n            this.storage.storeObject(WALLET_CAPABILITIES_STORAGE_KEY, walletCapabilities);\n        }\n        return response;\n    }\n    updateChain(chainId, newAvailableChains) {\n        var _a;\n        const chains = newAvailableChains !== null && newAvailableChains !== void 0 ? newAvailableChains : this.storage.loadObject(AVAILABLE_CHAINS_STORAGE_KEY);\n        const chain = chains === null || chains === void 0 ? void 0 : chains.find((chain) => chain.id === chainId);\n        if (!chain)\n            return false;\n        if (chain !== this.chain) {\n            this.chain = chain;\n            this.storage.storeObject(ACTIVE_CHAIN_STORAGE_KEY, chain);\n            (_a = this.callback) === null || _a === void 0 ? void 0 : _a.call(this, 'chainChanged', (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_4__.hexStringFromNumber)(chain.id));\n        }\n        return true;\n    }\n}\n//# sourceMappingURL=SCWSigner.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWSigner.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/util.js":
/*!*************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/util.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSigner: () => (/* binding */ createSigner),\n/* harmony export */   fetchSignerType: () => (/* binding */ fetchSignerType),\n/* harmony export */   loadSignerType: () => (/* binding */ loadSignerType),\n/* harmony export */   storeSignerType: () => (/* binding */ storeSignerType)\n/* harmony export */ });\n/* harmony import */ var _scw_SCWSigner_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./scw/SCWSigner.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/scw/SCWSigner.js\");\n/* harmony import */ var _walletlink_WalletLinkSigner_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./walletlink/WalletLinkSigner.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/WalletLinkSigner.js\");\n/* harmony import */ var _core_storage_ScopedLocalStorage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/storage/ScopedLocalStorage.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/storage/ScopedLocalStorage.js\");\n\n\n\nconst SIGNER_TYPE_KEY = 'SignerType';\nconst storage = new _core_storage_ScopedLocalStorage_js__WEBPACK_IMPORTED_MODULE_0__.ScopedLocalStorage('CBWSDK', 'SignerConfigurator');\nfunction loadSignerType() {\n    return storage.getItem(SIGNER_TYPE_KEY);\n}\nfunction storeSignerType(signerType) {\n    storage.setItem(SIGNER_TYPE_KEY, signerType);\n}\nasync function fetchSignerType(params) {\n    const { communicator, metadata, handshakeRequest, callback } = params;\n    listenForWalletLinkSessionRequest(communicator, metadata, callback).catch(() => { });\n    const request = {\n        id: crypto.randomUUID(),\n        event: 'selectSignerType',\n        data: Object.assign(Object.assign({}, params.preference), { handshakeRequest }),\n    };\n    const { data } = await communicator.postRequestAndWaitForResponse(request);\n    return data;\n}\nfunction createSigner(params) {\n    const { signerType, metadata, communicator, callback } = params;\n    switch (signerType) {\n        case 'scw': {\n            return new _scw_SCWSigner_js__WEBPACK_IMPORTED_MODULE_1__.SCWSigner({\n                metadata,\n                callback,\n                communicator,\n            });\n        }\n        case 'walletlink': {\n            return new _walletlink_WalletLinkSigner_js__WEBPACK_IMPORTED_MODULE_2__.WalletLinkSigner({\n                metadata,\n                callback,\n            });\n        }\n    }\n}\nasync function listenForWalletLinkSessionRequest(communicator, metadata, callback) {\n    await communicator.onMessage(({ event }) => event === 'WalletLinkSessionRequest');\n    // temporary walletlink signer instance to handle WalletLinkSessionRequest\n    // will revisit this when refactoring the walletlink signer\n    const walletlink = new _walletlink_WalletLinkSigner_js__WEBPACK_IMPORTED_MODULE_2__.WalletLinkSigner({\n        metadata,\n        callback,\n    });\n    // send wallet link session to popup\n    communicator.postMessage({\n        event: 'WalletLinkUpdate',\n        data: { session: walletlink.getSession() },\n    });\n    // wait for handshake to complete\n    await walletlink.handshake();\n    // send connected status to popup\n    communicator.postMessage({\n        event: 'WalletLinkUpdate',\n        data: { connected: true },\n    });\n}\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/WalletLinkSigner.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/WalletLinkSigner.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletLinkSigner: () => (/* binding */ WalletLinkSigner)\n/* harmony export */ });\n/* harmony import */ var _vendor_js_eth_eip712_util_index_cjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../vendor-js/eth-eip712-util/index.cjs */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/index.cjs\");\n/* harmony import */ var _relay_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./relay/constants.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/constants.js\");\n/* harmony import */ var _relay_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./relay/type/Web3Response.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/Web3Response.js\");\n/* harmony import */ var _relay_WalletLinkRelay_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./relay/WalletLinkRelay.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/WalletLinkRelay.js\");\n/* harmony import */ var _core_constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../core/constants.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/constants.js\");\n/* harmony import */ var _core_error_errors_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../core/error/errors.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js\");\n/* harmony import */ var _core_storage_ScopedLocalStorage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../core/storage/ScopedLocalStorage.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/storage/ScopedLocalStorage.js\");\n/* harmony import */ var _core_type_util_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../core/type/util.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\n/* harmony import */ var _util_provider_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../util/provider.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/provider.js\");\n// Copyright (c) 2018-2024 Coinbase, Inc. <https://www.coinbase.com/>\n// eslint-disable-next-line @typescript-eslint/ban-ts-comment\n// @ts-nocheck\n\n\n\n\n\n\n\n\n\nconst DEFAULT_CHAIN_ID_KEY = 'DefaultChainId';\nconst DEFAULT_JSON_RPC_URL = 'DefaultJsonRpcUrl';\n// original source: https://github.com/coinbase/coinbase-wallet-sdk/blob/v3.7.1/packages/wallet-sdk/src/provider/CoinbaseWalletProvider.ts\nclass WalletLinkSigner {\n    constructor(options) {\n        this._relay = null;\n        this._addresses = [];\n        this.metadata = options.metadata;\n        this._storage = new _core_storage_ScopedLocalStorage_js__WEBPACK_IMPORTED_MODULE_0__.ScopedLocalStorage('walletlink', _core_constants_js__WEBPACK_IMPORTED_MODULE_1__.WALLETLINK_URL);\n        this.callback = options.callback || null;\n        const cachedAddresses = this._storage.getItem(_relay_constants_js__WEBPACK_IMPORTED_MODULE_2__.LOCAL_STORAGE_ADDRESSES_KEY);\n        if (cachedAddresses) {\n            const addresses = cachedAddresses.split(' ');\n            if (addresses[0] !== '') {\n                this._addresses = addresses.map((address) => (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureAddressString)(address));\n            }\n        }\n        this.initializeRelay();\n    }\n    getSession() {\n        const relay = this.initializeRelay();\n        const { id, secret } = relay.getWalletLinkSession();\n        return { id, secret };\n    }\n    async handshake() {\n        await this._eth_requestAccounts();\n    }\n    get selectedAddress() {\n        return this._addresses[0] || undefined;\n    }\n    get jsonRpcUrl() {\n        var _a;\n        return (_a = this._storage.getItem(DEFAULT_JSON_RPC_URL)) !== null && _a !== void 0 ? _a : undefined;\n    }\n    set jsonRpcUrl(value) {\n        this._storage.setItem(DEFAULT_JSON_RPC_URL, value);\n    }\n    updateProviderInfo(jsonRpcUrl, chainId) {\n        var _a;\n        this.jsonRpcUrl = jsonRpcUrl;\n        // emit chainChanged event if necessary\n        const originalChainId = this.getChainId();\n        this._storage.setItem(DEFAULT_CHAIN_ID_KEY, chainId.toString(10));\n        const chainChanged = (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureIntNumber)(chainId) !== originalChainId;\n        if (chainChanged) {\n            (_a = this.callback) === null || _a === void 0 ? void 0 : _a.call(this, 'chainChanged', (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.hexStringFromNumber)(chainId));\n        }\n    }\n    async watchAsset(params) {\n        const request = (Array.isArray(params) ? params[0] : params);\n        if (!request.type) {\n            throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_4__.standardErrors.rpc.invalidParams('Type is required');\n        }\n        if ((request === null || request === void 0 ? void 0 : request.type) !== 'ERC20') {\n            throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_4__.standardErrors.rpc.invalidParams(`Asset of type '${request.type}' is not supported`);\n        }\n        if (!(request === null || request === void 0 ? void 0 : request.options)) {\n            throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_4__.standardErrors.rpc.invalidParams('Options are required');\n        }\n        if (!(request === null || request === void 0 ? void 0 : request.options.address)) {\n            throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_4__.standardErrors.rpc.invalidParams('Address is required');\n        }\n        const chainId = this.getChainId();\n        const { address, symbol, image, decimals } = request.options;\n        const relay = this.initializeRelay();\n        const result = await relay.watchAsset(request.type, address, symbol, decimals, image, chainId === null || chainId === void 0 ? void 0 : chainId.toString());\n        if ((0,_relay_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_5__.isErrorResponse)(result))\n            return false;\n        return !!result.result;\n    }\n    async addEthereumChain(params) {\n        var _a, _b;\n        const request = params[0];\n        if (((_a = request.rpcUrls) === null || _a === void 0 ? void 0 : _a.length) === 0) {\n            throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_4__.standardErrors.rpc.invalidParams('please pass in at least 1 rpcUrl');\n        }\n        if (!request.chainName || request.chainName.trim() === '') {\n            throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_4__.standardErrors.rpc.invalidParams('chainName is a required field');\n        }\n        if (!request.nativeCurrency) {\n            throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_4__.standardErrors.rpc.invalidParams('nativeCurrency is a required field');\n        }\n        const chainIdNumber = Number.parseInt(request.chainId, 16);\n        if (chainIdNumber === this.getChainId()) {\n            return false;\n        }\n        const relay = this.initializeRelay();\n        const { rpcUrls = [], blockExplorerUrls = [], chainName, iconUrls = [], nativeCurrency, } = request;\n        const res = await relay.addEthereumChain(chainIdNumber.toString(), rpcUrls, iconUrls, blockExplorerUrls, chainName, nativeCurrency);\n        if ((0,_relay_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_5__.isErrorResponse)(res))\n            return false;\n        if (((_b = res.result) === null || _b === void 0 ? void 0 : _b.isApproved) === true) {\n            this.updateProviderInfo(rpcUrls[0], chainIdNumber);\n            return null;\n        }\n        throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_4__.standardErrors.rpc.internal('unable to add ethereum chain');\n    }\n    async switchEthereumChain(params) {\n        const request = params[0];\n        const chainId = Number.parseInt(request.chainId, 16);\n        const relay = this.initializeRelay();\n        const res = await relay.switchEthereumChain(chainId.toString(10), this.selectedAddress || undefined);\n        if ((0,_relay_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_5__.isErrorResponse)(res))\n            throw res;\n        const switchResponse = res.result;\n        if (switchResponse.isApproved && switchResponse.rpcUrl.length > 0) {\n            this.updateProviderInfo(switchResponse.rpcUrl, chainId);\n        }\n        return null;\n    }\n    async cleanup() {\n        this.callback = null;\n        if (this._relay) {\n            this._relay.resetAndReload();\n        }\n        this._storage.clear();\n    }\n    _setAddresses(addresses, _) {\n        var _a;\n        if (!Array.isArray(addresses)) {\n            throw new Error('addresses is not an array');\n        }\n        const newAddresses = addresses.map((address) => (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureAddressString)(address));\n        if (JSON.stringify(newAddresses) === JSON.stringify(this._addresses)) {\n            return;\n        }\n        this._addresses = newAddresses;\n        (_a = this.callback) === null || _a === void 0 ? void 0 : _a.call(this, 'accountsChanged', newAddresses);\n        this._storage.setItem(_relay_constants_js__WEBPACK_IMPORTED_MODULE_2__.LOCAL_STORAGE_ADDRESSES_KEY, newAddresses.join(' '));\n    }\n    async request(request) {\n        const params = request.params || [];\n        switch (request.method) {\n            case 'eth_accounts':\n                return [...this._addresses];\n            case 'eth_coinbase':\n                return this.selectedAddress || null;\n            case 'net_version':\n                return this.getChainId().toString(10);\n            case 'eth_chainId':\n                return (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.hexStringFromNumber)(this.getChainId());\n            case 'eth_requestAccounts':\n                return this._eth_requestAccounts();\n            case 'eth_ecRecover':\n            case 'personal_ecRecover':\n                return this.ecRecover(request);\n            case 'personal_sign':\n                return this.personalSign(request);\n            case 'eth_signTransaction':\n                return this._eth_signTransaction(params);\n            case 'eth_sendRawTransaction':\n                return this._eth_sendRawTransaction(params);\n            case 'eth_sendTransaction':\n                return this._eth_sendTransaction(params);\n            case 'eth_signTypedData_v1':\n            case 'eth_signTypedData_v3':\n            case 'eth_signTypedData_v4':\n            case 'eth_signTypedData':\n                return this.signTypedData(request);\n            case 'wallet_addEthereumChain':\n                return this.addEthereumChain(params);\n            case 'wallet_switchEthereumChain':\n                return this.switchEthereumChain(params);\n            case 'wallet_watchAsset':\n                return this.watchAsset(params);\n            default:\n                if (!this.jsonRpcUrl)\n                    throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_4__.standardErrors.rpc.internal('No RPC URL set for chain');\n                return (0,_util_provider_js__WEBPACK_IMPORTED_MODULE_6__.fetchRPCRequest)(request, this.jsonRpcUrl);\n        }\n    }\n    _ensureKnownAddress(addressString) {\n        const addressStr = (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureAddressString)(addressString);\n        const lowercaseAddresses = this._addresses.map((address) => (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureAddressString)(address));\n        if (!lowercaseAddresses.includes(addressStr)) {\n            throw new Error('Unknown Ethereum address');\n        }\n    }\n    _prepareTransactionParams(tx) {\n        const fromAddress = tx.from ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureAddressString)(tx.from) : this.selectedAddress;\n        if (!fromAddress) {\n            throw new Error('Ethereum address is unavailable');\n        }\n        this._ensureKnownAddress(fromAddress);\n        const toAddress = tx.to ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureAddressString)(tx.to) : null;\n        const weiValue = tx.value != null ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureBigInt)(tx.value) : BigInt(0);\n        const data = tx.data ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureBuffer)(tx.data) : Buffer.alloc(0);\n        const nonce = tx.nonce != null ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureIntNumber)(tx.nonce) : null;\n        const gasPriceInWei = tx.gasPrice != null ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureBigInt)(tx.gasPrice) : null;\n        const maxFeePerGas = tx.maxFeePerGas != null ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureBigInt)(tx.maxFeePerGas) : null;\n        const maxPriorityFeePerGas = tx.maxPriorityFeePerGas != null ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureBigInt)(tx.maxPriorityFeePerGas) : null;\n        const gasLimit = tx.gas != null ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureBigInt)(tx.gas) : null;\n        const chainId = tx.chainId ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureIntNumber)(tx.chainId) : this.getChainId();\n        return {\n            fromAddress,\n            toAddress,\n            weiValue,\n            data,\n            nonce,\n            gasPriceInWei,\n            maxFeePerGas,\n            maxPriorityFeePerGas,\n            gasLimit,\n            chainId,\n        };\n    }\n    async ecRecover(request) {\n        const { method, params } = request;\n        if (!Array.isArray(params))\n            throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_4__.standardErrors.rpc.invalidParams();\n        const relay = this.initializeRelay();\n        const res = await relay.sendRequest({\n            method: 'ethereumAddressFromSignedMessage',\n            params: {\n                message: (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.encodeToHexString)(params[0]),\n                signature: (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.encodeToHexString)(params[1]),\n                addPrefix: method === 'personal_ecRecover',\n            },\n        });\n        if ((0,_relay_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_5__.isErrorResponse)(res))\n            throw res;\n        return res.result;\n    }\n    getChainId() {\n        var _a;\n        return Number.parseInt((_a = this._storage.getItem(DEFAULT_CHAIN_ID_KEY)) !== null && _a !== void 0 ? _a : '1', 10);\n    }\n    async _eth_requestAccounts() {\n        var _a, _b;\n        if (this._addresses.length > 0) {\n            (_a = this.callback) === null || _a === void 0 ? void 0 : _a.call(this, 'connect', { chainId: (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.hexStringFromNumber)(this.getChainId()) });\n            return this._addresses;\n        }\n        const relay = this.initializeRelay();\n        const res = await relay.requestEthereumAccounts();\n        if ((0,_relay_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_5__.isErrorResponse)(res))\n            throw res;\n        if (!res.result) {\n            throw new Error('accounts received is empty');\n        }\n        this._setAddresses(res.result);\n        (_b = this.callback) === null || _b === void 0 ? void 0 : _b.call(this, 'connect', { chainId: (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.hexStringFromNumber)(this.getChainId()) });\n        return this._addresses;\n    }\n    async personalSign({ params }) {\n        if (!Array.isArray(params))\n            throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_4__.standardErrors.rpc.invalidParams();\n        const address = params[1];\n        const rawData = params[0];\n        this._ensureKnownAddress(address);\n        const relay = this.initializeRelay();\n        const res = await relay.sendRequest({\n            method: 'signEthereumMessage',\n            params: {\n                address: (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureAddressString)(address),\n                message: (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.encodeToHexString)(rawData),\n                addPrefix: true,\n                typedDataJson: null,\n            },\n        });\n        if ((0,_relay_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_5__.isErrorResponse)(res))\n            throw res;\n        return res.result;\n    }\n    async _eth_signTransaction(params) {\n        const tx = this._prepareTransactionParams(params[0] || {});\n        const relay = this.initializeRelay();\n        const res = await relay.signEthereumTransaction(tx);\n        if ((0,_relay_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_5__.isErrorResponse)(res))\n            throw res;\n        return res.result;\n    }\n    async _eth_sendRawTransaction(params) {\n        const signedTransaction = (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureBuffer)(params[0]);\n        const relay = this.initializeRelay();\n        const res = await relay.submitEthereumTransaction(signedTransaction, this.getChainId());\n        if ((0,_relay_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_5__.isErrorResponse)(res))\n            throw res;\n        return res.result;\n    }\n    async _eth_sendTransaction(params) {\n        const tx = this._prepareTransactionParams(params[0] || {});\n        const relay = this.initializeRelay();\n        const res = await relay.signAndSubmitEthereumTransaction(tx);\n        if ((0,_relay_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_5__.isErrorResponse)(res))\n            throw res;\n        return res.result;\n    }\n    async signTypedData(request) {\n        const { method, params } = request;\n        if (!Array.isArray(params))\n            throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_4__.standardErrors.rpc.invalidParams();\n        const encode = (input) => {\n            const hashFuncMap = {\n                eth_signTypedData_v1: _vendor_js_eth_eip712_util_index_cjs__WEBPACK_IMPORTED_MODULE_7__.hashForSignTypedDataLegacy,\n                eth_signTypedData_v3: _vendor_js_eth_eip712_util_index_cjs__WEBPACK_IMPORTED_MODULE_7__.hashForSignTypedData_v3,\n                eth_signTypedData_v4: _vendor_js_eth_eip712_util_index_cjs__WEBPACK_IMPORTED_MODULE_7__.hashForSignTypedData_v4,\n                eth_signTypedData: _vendor_js_eth_eip712_util_index_cjs__WEBPACK_IMPORTED_MODULE_7__.hashForSignTypedData_v4,\n            };\n            return (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.hexStringFromBuffer)(hashFuncMap[method]({\n                data: (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureParsedJSONObject)(input),\n            }), true);\n        };\n        const address = params[method === 'eth_signTypedData_v1' ? 1 : 0];\n        const rawData = params[method === 'eth_signTypedData_v1' ? 0 : 1];\n        this._ensureKnownAddress(address);\n        const relay = this.initializeRelay();\n        const res = await relay.sendRequest({\n            method: 'signEthereumMessage',\n            params: {\n                address: (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_3__.ensureAddressString)(address),\n                message: encode(rawData),\n                typedDataJson: JSON.stringify(rawData, null, 2),\n                addPrefix: false,\n            },\n        });\n        if ((0,_relay_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_5__.isErrorResponse)(res))\n            throw res;\n        return res.result;\n    }\n    initializeRelay() {\n        if (!this._relay) {\n            this._relay = new _relay_WalletLinkRelay_js__WEBPACK_IMPORTED_MODULE_8__.WalletLinkRelay({\n                linkAPIUrl: _core_constants_js__WEBPACK_IMPORTED_MODULE_1__.WALLETLINK_URL,\n                storage: this._storage,\n                metadata: this.metadata,\n                accountsCallback: this._setAddresses.bind(this),\n                chainCallback: this.updateProviderInfo.bind(this),\n            });\n        }\n        return this._relay;\n    }\n}\n//# sourceMappingURL=WalletLinkSigner.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/WalletLinkSigner.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/RelayEventManager.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/RelayEventManager.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RelayEventManager: () => (/* binding */ RelayEventManager)\n/* harmony export */ });\n/* harmony import */ var _core_type_util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../core/type/util.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\n\nclass RelayEventManager {\n    constructor() {\n        this._nextRequestId = 0;\n        this.callbacks = new Map();\n    }\n    makeRequestId() {\n        // max nextId == max int32 for compatibility with mobile\n        this._nextRequestId = (this._nextRequestId + 1) % 0x7fffffff;\n        const id = this._nextRequestId;\n        const idStr = (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_0__.prepend0x)(id.toString(16));\n        // unlikely that this will ever be an issue, but just to be safe\n        const callback = this.callbacks.get(idStr);\n        if (callback) {\n            this.callbacks.delete(idStr);\n        }\n        return id;\n    }\n}\n//# sourceMappingURL=RelayEventManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9zaWduL3dhbGxldGxpbmsvcmVsYXkvUmVsYXlFdmVudE1hbmFnZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUQ7QUFDaEQ7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDZEQUFTO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2RyZWFtc3RhcnRyLy4vbm9kZV9tb2R1bGVzL0Bjb2luYmFzZS93YWxsZXQtc2RrL2Rpc3Qvc2lnbi93YWxsZXRsaW5rL3JlbGF5L1JlbGF5RXZlbnRNYW5hZ2VyLmpzPzlmMWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcHJlcGVuZDB4IH0gZnJvbSAnLi4vLi4vLi4vY29yZS90eXBlL3V0aWwuanMnO1xuZXhwb3J0IGNsYXNzIFJlbGF5RXZlbnRNYW5hZ2VyIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgdGhpcy5fbmV4dFJlcXVlc3RJZCA9IDA7XG4gICAgICAgIHRoaXMuY2FsbGJhY2tzID0gbmV3IE1hcCgpO1xuICAgIH1cbiAgICBtYWtlUmVxdWVzdElkKCkge1xuICAgICAgICAvLyBtYXggbmV4dElkID09IG1heCBpbnQzMiBmb3IgY29tcGF0aWJpbGl0eSB3aXRoIG1vYmlsZVxuICAgICAgICB0aGlzLl9uZXh0UmVxdWVzdElkID0gKHRoaXMuX25leHRSZXF1ZXN0SWQgKyAxKSAlIDB4N2ZmZmZmZmY7XG4gICAgICAgIGNvbnN0IGlkID0gdGhpcy5fbmV4dFJlcXVlc3RJZDtcbiAgICAgICAgY29uc3QgaWRTdHIgPSBwcmVwZW5kMHgoaWQudG9TdHJpbmcoMTYpKTtcbiAgICAgICAgLy8gdW5saWtlbHkgdGhhdCB0aGlzIHdpbGwgZXZlciBiZSBhbiBpc3N1ZSwgYnV0IGp1c3QgdG8gYmUgc2FmZVxuICAgICAgICBjb25zdCBjYWxsYmFjayA9IHRoaXMuY2FsbGJhY2tzLmdldChpZFN0cik7XG4gICAgICAgIGlmIChjYWxsYmFjaykge1xuICAgICAgICAgICAgdGhpcy5jYWxsYmFja3MuZGVsZXRlKGlkU3RyKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gaWQ7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9UmVsYXlFdmVudE1hbmFnZXIuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/RelayEventManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/WalletLinkRelay.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/WalletLinkRelay.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletLinkRelay: () => (/* binding */ WalletLinkRelay)\n/* harmony export */ });\n/* harmony import */ var _connection_WalletLinkConnection_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./connection/WalletLinkConnection.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkConnection.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/constants.js\");\n/* harmony import */ var _RelayEventManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RelayEventManager.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/RelayEventManager.js\");\n/* harmony import */ var _type_WalletLinkSession_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./type/WalletLinkSession.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/WalletLinkSession.js\");\n/* harmony import */ var _type_Web3Response_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./type/Web3Response.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/Web3Response.js\");\n/* harmony import */ var _ui_components_util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ui/components/util.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/util.js\");\n/* harmony import */ var _ui_WalletLinkRelayUI_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/WalletLinkRelayUI.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WalletLinkRelayUI.js\");\n/* harmony import */ var _ui_WLMobileRelayUI_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/WLMobileRelayUI.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WLMobileRelayUI.js\");\n/* harmony import */ var _core_error_errors_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../../core/error/errors.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js\");\n/* harmony import */ var _core_storage_ScopedLocalStorage_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../core/storage/ScopedLocalStorage.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/storage/ScopedLocalStorage.js\");\n/* harmony import */ var _core_type_util_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../core/type/util.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\n\n\n\n\n\n\n\n\n\n\n\nclass WalletLinkRelay {\n    constructor(options) {\n        this.chainCallbackParams = { chainId: '', jsonRpcUrl: '' }; // to implement distinctUntilChanged\n        this.isMobileWeb = (0,_ui_components_util_js__WEBPACK_IMPORTED_MODULE_0__.isMobileWeb)();\n        this.linkedUpdated = (linked) => {\n            this.isLinked = linked;\n            const cachedAddresses = this.storage.getItem(_constants_js__WEBPACK_IMPORTED_MODULE_1__.LOCAL_STORAGE_ADDRESSES_KEY);\n            if (linked) {\n                // Only set linked session variable one way\n                this._session.linked = linked;\n            }\n            this.isUnlinkedErrorState = false;\n            if (cachedAddresses) {\n                const addresses = cachedAddresses.split(' ');\n                const wasConnectedViaStandalone = this.storage.getItem('IsStandaloneSigning') === 'true';\n                if (addresses[0] !== '' && !linked && this._session.linked && !wasConnectedViaStandalone) {\n                    this.isUnlinkedErrorState = true;\n                }\n            }\n        };\n        this.metadataUpdated = (key, value) => {\n            this.storage.setItem(key, value);\n        };\n        this.chainUpdated = (chainId, jsonRpcUrl) => {\n            if (this.chainCallbackParams.chainId === chainId &&\n                this.chainCallbackParams.jsonRpcUrl === jsonRpcUrl) {\n                return;\n            }\n            this.chainCallbackParams = {\n                chainId,\n                jsonRpcUrl,\n            };\n            if (this.chainCallback) {\n                this.chainCallback(jsonRpcUrl, Number.parseInt(chainId, 10));\n            }\n        };\n        this.accountUpdated = (selectedAddress) => {\n            if (this.accountsCallback) {\n                this.accountsCallback([selectedAddress]);\n            }\n            if (WalletLinkRelay.accountRequestCallbackIds.size > 0) {\n                // We get the ethereum address from the metadata.  If for whatever\n                // reason we don't get a response via an explicit web3 message\n                // we can still fulfill the eip1102 request.\n                Array.from(WalletLinkRelay.accountRequestCallbackIds.values()).forEach((id) => {\n                    this.invokeCallback(id, {\n                        method: 'requestEthereumAccounts',\n                        result: [selectedAddress],\n                    });\n                });\n                WalletLinkRelay.accountRequestCallbackIds.clear();\n            }\n        };\n        this.resetAndReload = this.resetAndReload.bind(this);\n        this.linkAPIUrl = options.linkAPIUrl;\n        this.storage = options.storage;\n        this.metadata = options.metadata;\n        this.accountsCallback = options.accountsCallback;\n        this.chainCallback = options.chainCallback;\n        const { session, ui, connection } = this.subscribe();\n        this._session = session;\n        this.connection = connection;\n        this.relayEventManager = new _RelayEventManager_js__WEBPACK_IMPORTED_MODULE_2__.RelayEventManager();\n        this.ui = ui;\n        this.ui.attach();\n    }\n    subscribe() {\n        const session = _type_WalletLinkSession_js__WEBPACK_IMPORTED_MODULE_3__.WalletLinkSession.load(this.storage) || _type_WalletLinkSession_js__WEBPACK_IMPORTED_MODULE_3__.WalletLinkSession.create(this.storage);\n        const { linkAPIUrl } = this;\n        const connection = new _connection_WalletLinkConnection_js__WEBPACK_IMPORTED_MODULE_4__.WalletLinkConnection({\n            session,\n            linkAPIUrl,\n            listener: this,\n        });\n        const ui = this.isMobileWeb ? new _ui_WLMobileRelayUI_js__WEBPACK_IMPORTED_MODULE_5__.WLMobileRelayUI() : new _ui_WalletLinkRelayUI_js__WEBPACK_IMPORTED_MODULE_6__.WalletLinkRelayUI();\n        connection.connect();\n        return { session, ui, connection };\n    }\n    resetAndReload() {\n        this.connection\n            .destroy()\n            .then(() => {\n            /**\n             * Only clear storage if the session id we have in memory matches the one on disk\n             * Otherwise, in the case where we have 2 tabs, another tab might have cleared\n             * storage already.  In that case if we clear storage again, the user will be in\n             * a state where the first tab allows the user to connect but the session that\n             * was used isn't persisted.  This leaves the user in a state where they aren't\n             * connected to the mobile app.\n             */\n            const storedSession = _type_WalletLinkSession_js__WEBPACK_IMPORTED_MODULE_3__.WalletLinkSession.load(this.storage);\n            if ((storedSession === null || storedSession === void 0 ? void 0 : storedSession.id) === this._session.id) {\n                _core_storage_ScopedLocalStorage_js__WEBPACK_IMPORTED_MODULE_7__.ScopedLocalStorage.clearAll();\n            }\n            document.location.reload();\n        })\n            .catch((_) => { });\n    }\n    signEthereumTransaction(params) {\n        return this.sendRequest({\n            method: 'signEthereumTransaction',\n            params: {\n                fromAddress: params.fromAddress,\n                toAddress: params.toAddress,\n                weiValue: (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.bigIntStringFromBigInt)(params.weiValue),\n                data: (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.hexStringFromBuffer)(params.data, true),\n                nonce: params.nonce,\n                gasPriceInWei: params.gasPriceInWei ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.bigIntStringFromBigInt)(params.gasPriceInWei) : null,\n                maxFeePerGas: params.gasPriceInWei ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.bigIntStringFromBigInt)(params.gasPriceInWei) : null,\n                maxPriorityFeePerGas: params.gasPriceInWei\n                    ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.bigIntStringFromBigInt)(params.gasPriceInWei)\n                    : null,\n                gasLimit: params.gasLimit ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.bigIntStringFromBigInt)(params.gasLimit) : null,\n                chainId: params.chainId,\n                shouldSubmit: false,\n            },\n        });\n    }\n    signAndSubmitEthereumTransaction(params) {\n        return this.sendRequest({\n            method: 'signEthereumTransaction',\n            params: {\n                fromAddress: params.fromAddress,\n                toAddress: params.toAddress,\n                weiValue: (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.bigIntStringFromBigInt)(params.weiValue),\n                data: (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.hexStringFromBuffer)(params.data, true),\n                nonce: params.nonce,\n                gasPriceInWei: params.gasPriceInWei ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.bigIntStringFromBigInt)(params.gasPriceInWei) : null,\n                maxFeePerGas: params.maxFeePerGas ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.bigIntStringFromBigInt)(params.maxFeePerGas) : null,\n                maxPriorityFeePerGas: params.maxPriorityFeePerGas\n                    ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.bigIntStringFromBigInt)(params.maxPriorityFeePerGas)\n                    : null,\n                gasLimit: params.gasLimit ? (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.bigIntStringFromBigInt)(params.gasLimit) : null,\n                chainId: params.chainId,\n                shouldSubmit: true,\n            },\n        });\n    }\n    submitEthereumTransaction(signedTransaction, chainId) {\n        return this.sendRequest({\n            method: 'submitEthereumTransaction',\n            params: {\n                signedTransaction: (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.hexStringFromBuffer)(signedTransaction, true),\n                chainId,\n            },\n        });\n    }\n    getWalletLinkSession() {\n        return this._session;\n    }\n    sendRequest(request) {\n        let hideSnackbarItem = null;\n        const id = (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.randomBytesHex)(8);\n        const cancel = (error) => {\n            this.publishWeb3RequestCanceledEvent(id);\n            this.handleErrorResponse(id, request.method, error);\n            hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n        };\n        return new Promise((resolve, reject) => {\n            {\n                hideSnackbarItem = this.ui.showConnecting({\n                    isUnlinkedErrorState: this.isUnlinkedErrorState,\n                    onCancel: cancel,\n                    onResetConnection: this.resetAndReload, // eslint-disable-line @typescript-eslint/unbound-method\n                });\n            }\n            this.relayEventManager.callbacks.set(id, (response) => {\n                hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n                if ((0,_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_9__.isErrorResponse)(response)) {\n                    return reject(new Error(response.errorMessage));\n                }\n                resolve(response);\n            });\n            this.publishWeb3RequestEvent(id, request);\n        });\n    }\n    publishWeb3RequestEvent(id, request) {\n        const message = { type: 'WEB3_REQUEST', id, request };\n        this.publishEvent('Web3Request', message, true)\n            .then((_) => { })\n            .catch((err) => {\n            this.handleWeb3ResponseMessage(message.id, {\n                method: request.method,\n                errorMessage: err.message,\n            });\n        });\n        if (this.isMobileWeb) {\n            this.openCoinbaseWalletDeeplink(request.method);\n        }\n    }\n    // copied from MobileRelay\n    openCoinbaseWalletDeeplink(method) {\n        if (!(this.ui instanceof _ui_WLMobileRelayUI_js__WEBPACK_IMPORTED_MODULE_5__.WLMobileRelayUI))\n            return;\n        // For mobile relay requests, open the Coinbase Wallet app\n        switch (method) {\n            case 'requestEthereumAccounts': // requestEthereumAccounts is handled via popup\n            case 'switchEthereumChain': // switchEthereumChain doesn't need to open the app\n                return;\n            default:\n                window.addEventListener('blur', () => {\n                    window.addEventListener('focus', () => {\n                        this.connection.checkUnseenEvents();\n                    }, { once: true });\n                }, { once: true });\n                this.ui.openCoinbaseWalletDeeplink();\n                break;\n        }\n    }\n    publishWeb3RequestCanceledEvent(id) {\n        const message = {\n            type: 'WEB3_REQUEST_CANCELED',\n            id,\n        };\n        this.publishEvent('Web3RequestCanceled', message, false).then();\n    }\n    publishEvent(event, message, callWebhook) {\n        return this.connection.publishEvent(event, message, callWebhook);\n    }\n    handleWeb3ResponseMessage(id, response) {\n        if (response.method === 'requestEthereumAccounts') {\n            WalletLinkRelay.accountRequestCallbackIds.forEach((id) => this.invokeCallback(id, response));\n            WalletLinkRelay.accountRequestCallbackIds.clear();\n            return;\n        }\n        this.invokeCallback(id, response);\n    }\n    handleErrorResponse(id, method, error) {\n        var _a;\n        const errorMessage = (_a = error === null || error === void 0 ? void 0 : error.message) !== null && _a !== void 0 ? _a : 'Unspecified error message.';\n        this.handleWeb3ResponseMessage(id, {\n            method,\n            errorMessage,\n        });\n    }\n    invokeCallback(id, response) {\n        const callback = this.relayEventManager.callbacks.get(id);\n        if (callback) {\n            callback(response);\n            this.relayEventManager.callbacks.delete(id);\n        }\n    }\n    requestEthereumAccounts() {\n        const { appName, appLogoUrl } = this.metadata;\n        const request = {\n            method: 'requestEthereumAccounts',\n            params: {\n                appName,\n                appLogoUrl,\n            },\n        };\n        const hideSnackbarItem = null;\n        const id = (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.randomBytesHex)(8);\n        return new Promise((resolve, reject) => {\n            this.relayEventManager.callbacks.set(id, (response) => {\n                // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n                // @ts-ignore\n                hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n                if ((0,_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_9__.isErrorResponse)(response)) {\n                    return reject(new Error(response.errorMessage));\n                }\n                resolve(response);\n            });\n            WalletLinkRelay.accountRequestCallbackIds.add(id);\n            this.publishWeb3RequestEvent(id, request);\n        });\n    }\n    watchAsset(type, address, symbol, decimals, image, chainId) {\n        const request = {\n            method: 'watchAsset',\n            params: {\n                type,\n                options: {\n                    address,\n                    symbol,\n                    decimals,\n                    image,\n                },\n                chainId,\n            },\n        };\n        let hideSnackbarItem = null;\n        const id = (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.randomBytesHex)(8);\n        const cancel = (error) => {\n            this.publishWeb3RequestCanceledEvent(id);\n            this.handleErrorResponse(id, request.method, error);\n            hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n        };\n        {\n            hideSnackbarItem = this.ui.showConnecting({\n                isUnlinkedErrorState: this.isUnlinkedErrorState,\n                onCancel: cancel,\n                onResetConnection: this.resetAndReload, // eslint-disable-line @typescript-eslint/unbound-method\n            });\n        }\n        return new Promise((resolve, reject) => {\n            this.relayEventManager.callbacks.set(id, (response) => {\n                hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n                if ((0,_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_9__.isErrorResponse)(response)) {\n                    return reject(new Error(response.errorMessage));\n                }\n                resolve(response);\n            });\n            this.publishWeb3RequestEvent(id, request);\n        });\n    }\n    addEthereumChain(chainId, rpcUrls, iconUrls, blockExplorerUrls, chainName, nativeCurrency) {\n        const request = {\n            method: 'addEthereumChain',\n            params: {\n                chainId,\n                rpcUrls,\n                blockExplorerUrls,\n                chainName,\n                iconUrls,\n                nativeCurrency,\n            },\n        };\n        let hideSnackbarItem = null;\n        const id = (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.randomBytesHex)(8);\n        const cancel = (error) => {\n            this.publishWeb3RequestCanceledEvent(id);\n            this.handleErrorResponse(id, request.method, error);\n            hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n        };\n        {\n            hideSnackbarItem = this.ui.showConnecting({\n                isUnlinkedErrorState: this.isUnlinkedErrorState,\n                onCancel: cancel,\n                onResetConnection: this.resetAndReload, // eslint-disable-line @typescript-eslint/unbound-method\n            });\n        }\n        return new Promise((resolve, reject) => {\n            this.relayEventManager.callbacks.set(id, (response) => {\n                hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n                if ((0,_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_9__.isErrorResponse)(response)) {\n                    return reject(new Error(response.errorMessage));\n                }\n                resolve(response);\n            });\n            this.publishWeb3RequestEvent(id, request);\n        });\n    }\n    switchEthereumChain(chainId, address) {\n        const request = {\n            method: 'switchEthereumChain',\n            params: Object.assign({ chainId }, { address }),\n        };\n        let hideSnackbarItem = null;\n        const id = (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_8__.randomBytesHex)(8);\n        const cancel = (error) => {\n            this.publishWeb3RequestCanceledEvent(id);\n            this.handleErrorResponse(id, request.method, error);\n            hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n        };\n        {\n            hideSnackbarItem = this.ui.showConnecting({\n                isUnlinkedErrorState: this.isUnlinkedErrorState,\n                onCancel: cancel,\n                onResetConnection: this.resetAndReload, // eslint-disable-line @typescript-eslint/unbound-method\n            });\n        }\n        return new Promise((resolve, reject) => {\n            this.relayEventManager.callbacks.set(id, (response) => {\n                hideSnackbarItem === null || hideSnackbarItem === void 0 ? void 0 : hideSnackbarItem();\n                if ((0,_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_9__.isErrorResponse)(response) && response.errorCode) {\n                    return reject(_core_error_errors_js__WEBPACK_IMPORTED_MODULE_10__.standardErrors.provider.custom({\n                        code: response.errorCode,\n                        message: `Unrecognized chain ID. Try adding the chain using addEthereumChain first.`,\n                    }));\n                }\n                else if ((0,_type_Web3Response_js__WEBPACK_IMPORTED_MODULE_9__.isErrorResponse)(response)) {\n                    return reject(new Error(response.errorMessage));\n                }\n                resolve(response);\n            });\n            this.publishWeb3RequestEvent(id, request);\n        });\n    }\n}\nWalletLinkRelay.accountRequestCallbackIds = new Set();\n//# sourceMappingURL=WalletLinkRelay.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/WalletLinkRelay.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkCipher.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkCipher.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletLinkCipher: () => (/* binding */ WalletLinkCipher)\n/* harmony export */ });\n/* harmony import */ var _core_type_util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../core/type/util.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\n\nclass WalletLinkCipher {\n    // @param secret hex representation of 32-byte secret\n    constructor(secret) {\n        this.secret = secret;\n    }\n    /**\n     *\n     * @param plainText string to be encrypted\n     * returns hex string representation of bytes in the order: initialization vector (iv),\n     * auth tag, encrypted plaintext. IV is 12 bytes. Auth tag is 16 bytes. Remaining bytes are the\n     * encrypted plainText.\n     */\n    async encrypt(plainText) {\n        const secret = this.secret;\n        if (secret.length !== 64)\n            throw Error(`secret must be 256 bits`);\n        const ivBytes = crypto.getRandomValues(new Uint8Array(12));\n        const secretKey = await crypto.subtle.importKey('raw', (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_0__.hexStringToUint8Array)(secret), { name: 'aes-gcm' }, false, ['encrypt', 'decrypt']);\n        const enc = new TextEncoder();\n        // Will return encrypted plainText with auth tag (ie MAC or checksum) appended at the end\n        const encryptedResult = await window.crypto.subtle.encrypt({\n            name: 'AES-GCM',\n            iv: ivBytes,\n        }, secretKey, enc.encode(plainText));\n        const tagLength = 16;\n        const authTag = encryptedResult.slice(encryptedResult.byteLength - tagLength);\n        const encryptedPlaintext = encryptedResult.slice(0, encryptedResult.byteLength - tagLength);\n        const authTagBytes = new Uint8Array(authTag);\n        const encryptedPlaintextBytes = new Uint8Array(encryptedPlaintext);\n        const concatted = new Uint8Array([...ivBytes, ...authTagBytes, ...encryptedPlaintextBytes]);\n        return (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_0__.uint8ArrayToHex)(concatted);\n    }\n    /**\n     *\n     * @param cipherText hex string representation of bytes in the order: initialization vector (iv),\n     * auth tag, encrypted plaintext. IV is 12 bytes. Auth tag is 16 bytes.\n     */\n    async decrypt(cipherText) {\n        const secret = this.secret;\n        if (secret.length !== 64)\n            throw Error(`secret must be 256 bits`);\n        return new Promise((resolve, reject) => {\n            void (async function () {\n                const secretKey = await crypto.subtle.importKey('raw', (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_0__.hexStringToUint8Array)(secret), { name: 'aes-gcm' }, false, ['encrypt', 'decrypt']);\n                const encrypted = (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_0__.hexStringToUint8Array)(cipherText);\n                const ivBytes = encrypted.slice(0, 12);\n                const authTagBytes = encrypted.slice(12, 28);\n                const encryptedPlaintextBytes = encrypted.slice(28);\n                const concattedBytes = new Uint8Array([...encryptedPlaintextBytes, ...authTagBytes]);\n                const algo = {\n                    name: 'AES-GCM',\n                    iv: new Uint8Array(ivBytes),\n                };\n                try {\n                    const decrypted = await window.crypto.subtle.decrypt(algo, secretKey, concattedBytes);\n                    const decoder = new TextDecoder();\n                    resolve(decoder.decode(decrypted));\n                }\n                catch (err) {\n                    reject(err);\n                }\n            })();\n        });\n    }\n}\n//# sourceMappingURL=WalletLinkCipher.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkCipher.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkConnection.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkConnection.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletLinkConnection: () => (/* binding */ WalletLinkConnection)\n/* harmony export */ });\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../constants.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/constants.js\");\n/* harmony import */ var _WalletLinkCipher_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./WalletLinkCipher.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkCipher.js\");\n/* harmony import */ var _WalletLinkHTTP_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./WalletLinkHTTP.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkHTTP.js\");\n/* harmony import */ var _WalletLinkWebSocket_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./WalletLinkWebSocket.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkWebSocket.js\");\n/* harmony import */ var _core_type_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../core/type/index.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/type/index.js\");\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\n\n\n\n\n\nconst HEARTBEAT_INTERVAL = 10000;\nconst REQUEST_TIMEOUT = 60000;\n/**\n * Coinbase Wallet Connection\n */\nclass WalletLinkConnection {\n    /**\n     * Constructor\n     * @param session Session\n     * @param linkAPIUrl Coinbase Wallet link server URL\n     * @param listener WalletLinkConnectionUpdateListener\n     * @param [WebSocketClass] Custom WebSocket implementation\n     */\n    constructor({ session, linkAPIUrl, listener }) {\n        this.destroyed = false;\n        this.lastHeartbeatResponse = 0;\n        this.nextReqId = (0,_core_type_index_js__WEBPACK_IMPORTED_MODULE_0__.IntNumber)(1);\n        /**\n         * true if connected and authenticated, else false\n         * runs listener when connected status changes\n         */\n        this._connected = false;\n        /**\n         * true if linked (a guest has joined before)\n         * runs listener when linked status changes\n         */\n        this._linked = false;\n        this.shouldFetchUnseenEventsOnConnect = false;\n        this.requestResolutions = new Map();\n        this.handleSessionMetadataUpdated = (metadata) => {\n            if (!metadata)\n                return;\n            // Map of metadata key to handler function\n            const handlers = new Map([\n                ['__destroyed', this.handleDestroyed],\n                ['EthereumAddress', this.handleAccountUpdated],\n                ['WalletUsername', this.handleWalletUsernameUpdated],\n                ['AppVersion', this.handleAppVersionUpdated],\n                [\n                    'ChainId', // ChainId and JsonRpcUrl are always updated together\n                    (v) => metadata.JsonRpcUrl && this.handleChainUpdated(v, metadata.JsonRpcUrl),\n                ],\n            ]);\n            // call handler for each metadata key if value is defined\n            handlers.forEach((handler, key) => {\n                const value = metadata[key];\n                if (value === undefined)\n                    return;\n                handler(value);\n            });\n        };\n        this.handleDestroyed = (__destroyed) => {\n            var _a;\n            if (__destroyed !== '1')\n                return;\n            (_a = this.listener) === null || _a === void 0 ? void 0 : _a.resetAndReload();\n        };\n        this.handleAccountUpdated = async (encryptedEthereumAddress) => {\n            var _a;\n            const address = await this.cipher.decrypt(encryptedEthereumAddress);\n            (_a = this.listener) === null || _a === void 0 ? void 0 : _a.accountUpdated(address);\n        };\n        this.handleMetadataUpdated = async (key, encryptedMetadataValue) => {\n            var _a;\n            const decryptedValue = await this.cipher.decrypt(encryptedMetadataValue);\n            (_a = this.listener) === null || _a === void 0 ? void 0 : _a.metadataUpdated(key, decryptedValue);\n        };\n        this.handleWalletUsernameUpdated = async (walletUsername) => {\n            this.handleMetadataUpdated(_constants_js__WEBPACK_IMPORTED_MODULE_1__.WALLET_USER_NAME_KEY, walletUsername);\n        };\n        this.handleAppVersionUpdated = async (appVersion) => {\n            this.handleMetadataUpdated(_constants_js__WEBPACK_IMPORTED_MODULE_1__.APP_VERSION_KEY, appVersion);\n        };\n        this.handleChainUpdated = async (encryptedChainId, encryptedJsonRpcUrl) => {\n            var _a;\n            const chainId = await this.cipher.decrypt(encryptedChainId);\n            const jsonRpcUrl = await this.cipher.decrypt(encryptedJsonRpcUrl);\n            (_a = this.listener) === null || _a === void 0 ? void 0 : _a.chainUpdated(chainId, jsonRpcUrl);\n        };\n        this.session = session;\n        this.cipher = new _WalletLinkCipher_js__WEBPACK_IMPORTED_MODULE_2__.WalletLinkCipher(session.secret);\n        this.listener = listener;\n        const ws = new _WalletLinkWebSocket_js__WEBPACK_IMPORTED_MODULE_3__.WalletLinkWebSocket(`${linkAPIUrl}/rpc`, WebSocket);\n        ws.setConnectionStateListener(async (state) => {\n            // attempt to reconnect every 5 seconds when disconnected\n            let connected = false;\n            switch (state) {\n                case _WalletLinkWebSocket_js__WEBPACK_IMPORTED_MODULE_3__.ConnectionState.DISCONNECTED:\n                    // if DISCONNECTED and not destroyed\n                    if (!this.destroyed) {\n                        const connect = async () => {\n                            // wait 5 seconds\n                            await new Promise((resolve) => setTimeout(resolve, 5000));\n                            // check whether it's destroyed again\n                            if (!this.destroyed) {\n                                // reconnect\n                                ws.connect().catch(() => {\n                                    connect();\n                                });\n                            }\n                        };\n                        connect();\n                    }\n                    break;\n                case _WalletLinkWebSocket_js__WEBPACK_IMPORTED_MODULE_3__.ConnectionState.CONNECTED:\n                    // perform authentication upon connection\n                    // if CONNECTED, authenticate, and then check link status\n                    connected = await this.handleConnected();\n                    // send heartbeat every n seconds while connected\n                    // if CONNECTED, start the heartbeat timer\n                    // first timer event updates lastHeartbeat timestamp\n                    // subsequent calls send heartbeat message\n                    this.updateLastHeartbeat();\n                    setInterval(() => {\n                        this.heartbeat();\n                    }, HEARTBEAT_INTERVAL);\n                    // check for unseen events\n                    if (this.shouldFetchUnseenEventsOnConnect) {\n                        this.fetchUnseenEventsAPI();\n                    }\n                    break;\n                case _WalletLinkWebSocket_js__WEBPACK_IMPORTED_MODULE_3__.ConnectionState.CONNECTING:\n                    break;\n            }\n            // distinctUntilChanged\n            if (this.connected !== connected) {\n                this.connected = connected;\n            }\n        });\n        ws.setIncomingDataListener((m) => {\n            var _a;\n            switch (m.type) {\n                // handle server's heartbeat responses\n                case 'Heartbeat':\n                    this.updateLastHeartbeat();\n                    return;\n                // handle link status updates\n                case 'IsLinkedOK':\n                case 'Linked': {\n                    const linked = m.type === 'IsLinkedOK' ? m.linked : undefined;\n                    this.linked = linked || m.onlineGuests > 0;\n                    break;\n                }\n                // handle session config updates\n                case 'GetSessionConfigOK':\n                case 'SessionConfigUpdated': {\n                    this.handleSessionMetadataUpdated(m.metadata);\n                    break;\n                }\n                case 'Event': {\n                    this.handleIncomingEvent(m);\n                    break;\n                }\n            }\n            // resolve request promises\n            if (m.id !== undefined) {\n                (_a = this.requestResolutions.get(m.id)) === null || _a === void 0 ? void 0 : _a(m);\n            }\n        });\n        this.ws = ws;\n        this.http = new _WalletLinkHTTP_js__WEBPACK_IMPORTED_MODULE_4__.WalletLinkHTTP(linkAPIUrl, session.id, session.key);\n    }\n    /**\n     * Make a connection to the server\n     */\n    connect() {\n        if (this.destroyed) {\n            throw new Error('instance is destroyed');\n        }\n        this.ws.connect();\n    }\n    /**\n     * Terminate connection, and mark as destroyed. To reconnect, create a new\n     * instance of WalletSDKConnection\n     */\n    async destroy() {\n        if (this.destroyed)\n            return;\n        await this.makeRequest({\n            type: 'SetSessionConfig',\n            id: (0,_core_type_index_js__WEBPACK_IMPORTED_MODULE_0__.IntNumber)(this.nextReqId++),\n            sessionId: this.session.id,\n            metadata: { __destroyed: '1' },\n        }, { timeout: 1000 });\n        this.destroyed = true;\n        this.ws.disconnect();\n        this.listener = undefined;\n    }\n    get connected() {\n        return this._connected;\n    }\n    set connected(connected) {\n        this._connected = connected;\n    }\n    get linked() {\n        return this._linked;\n    }\n    set linked(linked) {\n        var _a, _b;\n        this._linked = linked;\n        if (linked)\n            (_a = this.onceLinked) === null || _a === void 0 ? void 0 : _a.call(this);\n        (_b = this.listener) === null || _b === void 0 ? void 0 : _b.linkedUpdated(linked);\n    }\n    setOnceLinked(callback) {\n        return new Promise((resolve) => {\n            if (this.linked) {\n                callback().then(resolve);\n            }\n            else {\n                this.onceLinked = () => {\n                    callback().then(resolve);\n                    this.onceLinked = undefined;\n                };\n            }\n        });\n    }\n    async handleIncomingEvent(m) {\n        var _a;\n        if (m.type !== 'Event' || m.event !== 'Web3Response') {\n            return;\n        }\n        const decryptedData = await this.cipher.decrypt(m.data);\n        const message = JSON.parse(decryptedData);\n        if (message.type !== 'WEB3_RESPONSE')\n            return;\n        const { id, response } = message;\n        (_a = this.listener) === null || _a === void 0 ? void 0 : _a.handleWeb3ResponseMessage(id, response);\n    }\n    async checkUnseenEvents() {\n        if (!this.connected) {\n            this.shouldFetchUnseenEventsOnConnect = true;\n            return;\n        }\n        await new Promise((resolve) => setTimeout(resolve, 250));\n        try {\n            await this.fetchUnseenEventsAPI();\n        }\n        catch (e) {\n            console.error('Unable to check for unseen events', e);\n        }\n    }\n    async fetchUnseenEventsAPI() {\n        this.shouldFetchUnseenEventsOnConnect = false;\n        const responseEvents = await this.http.fetchUnseenEvents();\n        responseEvents.forEach((e) => this.handleIncomingEvent(e));\n    }\n    /**\n     * Publish an event and emit event ID when successful\n     * @param event event name\n     * @param unencryptedData unencrypted event data\n     * @param callWebhook whether the webhook should be invoked\n     * @returns a Promise that emits event ID when successful\n     */\n    async publishEvent(event, unencryptedData, callWebhook = false) {\n        const data = await this.cipher.encrypt(JSON.stringify(Object.assign(Object.assign({}, unencryptedData), { origin: location.origin, location: location.href, relaySource: 'coinbaseWalletExtension' in window && window.coinbaseWalletExtension\n                ? 'injected_sdk'\n                : 'sdk' })));\n        const message = {\n            type: 'PublishEvent',\n            id: (0,_core_type_index_js__WEBPACK_IMPORTED_MODULE_0__.IntNumber)(this.nextReqId++),\n            sessionId: this.session.id,\n            event,\n            data,\n            callWebhook,\n        };\n        return this.setOnceLinked(async () => {\n            const res = await this.makeRequest(message);\n            if (res.type === 'Fail') {\n                throw new Error(res.error || 'failed to publish event');\n            }\n            return res.eventId;\n        });\n    }\n    sendData(message) {\n        this.ws.sendData(JSON.stringify(message));\n    }\n    updateLastHeartbeat() {\n        this.lastHeartbeatResponse = Date.now();\n    }\n    heartbeat() {\n        if (Date.now() - this.lastHeartbeatResponse > HEARTBEAT_INTERVAL * 2) {\n            this.ws.disconnect();\n            return;\n        }\n        try {\n            this.ws.sendData('h');\n        }\n        catch (_a) {\n            // noop\n        }\n    }\n    async makeRequest(message, options = { timeout: REQUEST_TIMEOUT }) {\n        const reqId = message.id;\n        this.sendData(message);\n        // await server message with corresponding id\n        let timeoutId;\n        return Promise.race([\n            new Promise((_, reject) => {\n                timeoutId = window.setTimeout(() => {\n                    reject(new Error(`request ${reqId} timed out`));\n                }, options.timeout);\n            }),\n            new Promise((resolve) => {\n                this.requestResolutions.set(reqId, (m) => {\n                    clearTimeout(timeoutId); // clear the timeout\n                    resolve(m);\n                    this.requestResolutions.delete(reqId);\n                });\n            }),\n        ]);\n    }\n    async handleConnected() {\n        const res = await this.makeRequest({\n            type: 'HostSession',\n            id: (0,_core_type_index_js__WEBPACK_IMPORTED_MODULE_0__.IntNumber)(this.nextReqId++),\n            sessionId: this.session.id,\n            sessionKey: this.session.key,\n        });\n        if (res.type === 'Fail')\n            return false;\n        this.sendData({\n            type: 'IsLinked',\n            id: (0,_core_type_index_js__WEBPACK_IMPORTED_MODULE_0__.IntNumber)(this.nextReqId++),\n            sessionId: this.session.id,\n        });\n        this.sendData({\n            type: 'GetSessionConfig',\n            id: (0,_core_type_index_js__WEBPACK_IMPORTED_MODULE_0__.IntNumber)(this.nextReqId++),\n            sessionId: this.session.id,\n        });\n        return true;\n    }\n}\n//# sourceMappingURL=WalletLinkConnection.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkConnection.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkHTTP.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkHTTP.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletLinkHTTP: () => (/* binding */ WalletLinkHTTP)\n/* harmony export */ });\nclass WalletLinkHTTP {\n    constructor(linkAPIUrl, sessionId, sessionKey) {\n        this.linkAPIUrl = linkAPIUrl;\n        this.sessionId = sessionId;\n        const credentials = `${sessionId}:${sessionKey}`;\n        this.auth = `Basic ${btoa(credentials)}`;\n    }\n    // mark unseen events as seen\n    async markUnseenEventsAsSeen(events) {\n        return Promise.all(events.map((e) => fetch(`${this.linkAPIUrl}/events/${e.eventId}/seen`, {\n            method: 'POST',\n            headers: {\n                Authorization: this.auth,\n            },\n        }))).catch((error) => console.error('Unabled to mark event as failed:', error));\n    }\n    async fetchUnseenEvents() {\n        var _a;\n        const response = await fetch(`${this.linkAPIUrl}/events?unseen=true`, {\n            headers: {\n                Authorization: this.auth,\n            },\n        });\n        if (response.ok) {\n            const { events, error } = (await response.json());\n            if (error) {\n                throw new Error(`Check unseen events failed: ${error}`);\n            }\n            const responseEvents = (_a = events === null || events === void 0 ? void 0 : events.filter((e) => e.event === 'Web3Response').map((e) => ({\n                type: 'Event',\n                sessionId: this.sessionId,\n                eventId: e.id,\n                event: e.event,\n                data: e.data,\n            }))) !== null && _a !== void 0 ? _a : [];\n            this.markUnseenEventsAsSeen(responseEvents);\n            return responseEvents;\n        }\n        throw new Error(`Check unseen events failed: ${response.status}`);\n    }\n}\n//# sourceMappingURL=WalletLinkHTTP.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkHTTP.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkWebSocket.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkWebSocket.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionState: () => (/* binding */ ConnectionState),\n/* harmony export */   WalletLinkWebSocket: () => (/* binding */ WalletLinkWebSocket)\n/* harmony export */ });\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\nvar ConnectionState;\n(function (ConnectionState) {\n    ConnectionState[ConnectionState[\"DISCONNECTED\"] = 0] = \"DISCONNECTED\";\n    ConnectionState[ConnectionState[\"CONNECTING\"] = 1] = \"CONNECTING\";\n    ConnectionState[ConnectionState[\"CONNECTED\"] = 2] = \"CONNECTED\";\n})(ConnectionState || (ConnectionState = {}));\nclass WalletLinkWebSocket {\n    setConnectionStateListener(listener) {\n        this.connectionStateListener = listener;\n    }\n    setIncomingDataListener(listener) {\n        this.incomingDataListener = listener;\n    }\n    /**\n     * Constructor\n     * @param url WebSocket server URL\n     * @param [WebSocketClass] Custom WebSocket implementation\n     */\n    constructor(url, WebSocketClass = WebSocket) {\n        this.WebSocketClass = WebSocketClass;\n        this.webSocket = null;\n        this.pendingData = [];\n        this.url = url.replace(/^http/, 'ws');\n    }\n    /**\n     * Make a websocket connection\n     * @returns a Promise that resolves when connected\n     */\n    async connect() {\n        if (this.webSocket) {\n            throw new Error('webSocket object is not null');\n        }\n        return new Promise((resolve, reject) => {\n            var _a;\n            let webSocket;\n            try {\n                this.webSocket = webSocket = new this.WebSocketClass(this.url);\n            }\n            catch (err) {\n                reject(err);\n                return;\n            }\n            (_a = this.connectionStateListener) === null || _a === void 0 ? void 0 : _a.call(this, ConnectionState.CONNECTING);\n            webSocket.onclose = (evt) => {\n                var _a;\n                this.clearWebSocket();\n                reject(new Error(`websocket error ${evt.code}: ${evt.reason}`));\n                (_a = this.connectionStateListener) === null || _a === void 0 ? void 0 : _a.call(this, ConnectionState.DISCONNECTED);\n            };\n            webSocket.onopen = (_) => {\n                var _a;\n                resolve();\n                (_a = this.connectionStateListener) === null || _a === void 0 ? void 0 : _a.call(this, ConnectionState.CONNECTED);\n                if (this.pendingData.length > 0) {\n                    const pending = [...this.pendingData];\n                    pending.forEach((data) => this.sendData(data));\n                    this.pendingData = [];\n                }\n            };\n            webSocket.onmessage = (evt) => {\n                var _a, _b;\n                if (evt.data === 'h') {\n                    (_a = this.incomingDataListener) === null || _a === void 0 ? void 0 : _a.call(this, {\n                        type: 'Heartbeat',\n                    });\n                }\n                else {\n                    try {\n                        const message = JSON.parse(evt.data);\n                        (_b = this.incomingDataListener) === null || _b === void 0 ? void 0 : _b.call(this, message);\n                    }\n                    catch (_c) {\n                        /* empty */\n                    }\n                }\n            };\n        });\n    }\n    /**\n     * Disconnect from server\n     */\n    disconnect() {\n        var _a;\n        const { webSocket } = this;\n        if (!webSocket) {\n            return;\n        }\n        this.clearWebSocket();\n        (_a = this.connectionStateListener) === null || _a === void 0 ? void 0 : _a.call(this, ConnectionState.DISCONNECTED);\n        this.connectionStateListener = undefined;\n        this.incomingDataListener = undefined;\n        try {\n            webSocket.close();\n        }\n        catch (_b) {\n            // noop\n        }\n    }\n    /**\n     * Send data to server\n     * @param data text to send\n     */\n    sendData(data) {\n        const { webSocket } = this;\n        if (!webSocket) {\n            this.pendingData.push(data);\n            this.connect();\n            return;\n        }\n        webSocket.send(data);\n    }\n    clearWebSocket() {\n        const { webSocket } = this;\n        if (!webSocket) {\n            return;\n        }\n        this.webSocket = null;\n        webSocket.onclose = null;\n        webSocket.onerror = null;\n        webSocket.onmessage = null;\n        webSocket.onopen = null;\n    }\n}\n//# sourceMappingURL=WalletLinkWebSocket.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/connection/WalletLinkWebSocket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/constants.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/constants.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_VERSION_KEY: () => (/* binding */ APP_VERSION_KEY),\n/* harmony export */   LOCAL_STORAGE_ADDRESSES_KEY: () => (/* binding */ LOCAL_STORAGE_ADDRESSES_KEY),\n/* harmony export */   WALLET_USER_NAME_KEY: () => (/* binding */ WALLET_USER_NAME_KEY)\n/* harmony export */ });\nconst WALLET_USER_NAME_KEY = 'walletUsername';\nconst LOCAL_STORAGE_ADDRESSES_KEY = 'Addresses';\nconst APP_VERSION_KEY = 'AppVersion';\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9zaWduL3dhbGxldGxpbmsvcmVsYXkvY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFPO0FBQ0E7QUFDQTtBQUNQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJlYW1zdGFydHIvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9zaWduL3dhbGxldGxpbmsvcmVsYXkvY29uc3RhbnRzLmpzPzU3OTUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFdBTExFVF9VU0VSX05BTUVfS0VZID0gJ3dhbGxldFVzZXJuYW1lJztcbmV4cG9ydCBjb25zdCBMT0NBTF9TVE9SQUdFX0FERFJFU1NFU19LRVkgPSAnQWRkcmVzc2VzJztcbmV4cG9ydCBjb25zdCBBUFBfVkVSU0lPTl9LRVkgPSAnQXBwVmVyc2lvbic7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25zdGFudHMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/WalletLinkSession.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/WalletLinkSession.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletLinkSession: () => (/* binding */ WalletLinkSession)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/sha256 */ \"(ssr)/./node_modules/@noble/hashes/esm/sha256.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/utils */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _core_type_util_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../core/type/util.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\n\n\n\nconst STORAGE_KEY_SESSION_ID = 'session:id';\nconst STORAGE_KEY_SESSION_SECRET = 'session:secret';\nconst STORAGE_KEY_SESSION_LINKED = 'session:linked';\nclass WalletLinkSession {\n    constructor(storage, id, secret, linked = false) {\n        this.storage = storage;\n        this.id = id;\n        this.secret = secret;\n        this.key = (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_0__.bytesToHex)((0,_noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_1__.sha256)(`${id}, ${secret} WalletLink`));\n        this._linked = !!linked;\n    }\n    static create(storage) {\n        const id = (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_2__.randomBytesHex)(16);\n        const secret = (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_2__.randomBytesHex)(32);\n        return new WalletLinkSession(storage, id, secret).save();\n    }\n    static load(storage) {\n        const id = storage.getItem(STORAGE_KEY_SESSION_ID);\n        const linked = storage.getItem(STORAGE_KEY_SESSION_LINKED);\n        const secret = storage.getItem(STORAGE_KEY_SESSION_SECRET);\n        if (id && secret) {\n            return new WalletLinkSession(storage, id, secret, linked === '1');\n        }\n        return null;\n    }\n    get linked() {\n        return this._linked;\n    }\n    set linked(val) {\n        this._linked = val;\n        this.persistLinked();\n    }\n    save() {\n        this.storage.setItem(STORAGE_KEY_SESSION_ID, this.id);\n        this.storage.setItem(STORAGE_KEY_SESSION_SECRET, this.secret);\n        this.persistLinked();\n        return this;\n    }\n    persistLinked() {\n        this.storage.setItem(STORAGE_KEY_SESSION_LINKED, this._linked ? '1' : '0');\n    }\n}\n//# sourceMappingURL=WalletLinkSession.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/WalletLinkSession.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/Web3Response.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/Web3Response.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isErrorResponse: () => (/* binding */ isErrorResponse)\n/* harmony export */ });\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\nfunction isErrorResponse(response) {\n    return response.errorMessage !== undefined;\n}\n//# sourceMappingURL=Web3Response.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9zaWduL3dhbGxldGxpbmsvcmVsYXkvdHlwZS9XZWIzUmVzcG9uc2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kcmVhbXN0YXJ0ci8uL25vZGVfbW9kdWxlcy9AY29pbmJhc2Uvd2FsbGV0LXNkay9kaXN0L3NpZ24vd2FsbGV0bGluay9yZWxheS90eXBlL1dlYjNSZXNwb25zZS5qcz80YzA3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgMjAxOC0yMDIzIENvaW5iYXNlLCBJbmMuIDxodHRwczovL3d3dy5jb2luYmFzZS5jb20vPlxuZXhwb3J0IGZ1bmN0aW9uIGlzRXJyb3JSZXNwb25zZShyZXNwb25zZSkge1xuICAgIHJldHVybiByZXNwb25zZS5lcnJvck1lc3NhZ2UgIT09IHVuZGVmaW5lZDtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPVdlYjNSZXNwb25zZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/type/Web3Response.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WLMobileRelayUI.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WLMobileRelayUI.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WLMobileRelayUI: () => (/* binding */ WLMobileRelayUI)\n/* harmony export */ });\n/* harmony import */ var _components_RedirectDialog_RedirectDialog_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components/RedirectDialog/RedirectDialog.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog.js\");\n/* harmony import */ var _components_util_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/util.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/util.js\");\n/* harmony import */ var _core_constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../core/constants.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/constants.js\");\n\n\n\nclass WLMobileRelayUI {\n    constructor() {\n        this.attached = false;\n        this.redirectDialog = new _components_RedirectDialog_RedirectDialog_js__WEBPACK_IMPORTED_MODULE_0__.RedirectDialog();\n    }\n    attach() {\n        if (this.attached) {\n            throw new Error('Coinbase Wallet SDK UI is already attached');\n        }\n        this.redirectDialog.attach();\n        this.attached = true;\n    }\n    redirectToCoinbaseWallet(walletLinkUrl) {\n        const url = new URL(_core_constants_js__WEBPACK_IMPORTED_MODULE_1__.CBW_MOBILE_DEEPLINK_URL);\n        url.searchParams.append('redirect_url', (0,_components_util_js__WEBPACK_IMPORTED_MODULE_2__.getLocation)().href);\n        if (walletLinkUrl) {\n            url.searchParams.append('wl_url', walletLinkUrl);\n        }\n        const anchorTag = document.createElement('a');\n        anchorTag.target = 'cbw-opener';\n        anchorTag.href = url.href;\n        anchorTag.rel = 'noreferrer noopener';\n        anchorTag.click();\n    }\n    openCoinbaseWalletDeeplink(walletLinkUrl) {\n        this.redirectDialog.present({\n            title: 'Redirecting to Coinbase Wallet...',\n            buttonText: 'Open',\n            onButtonClick: () => {\n                this.redirectToCoinbaseWallet(walletLinkUrl);\n            },\n        });\n        setTimeout(() => {\n            this.redirectToCoinbaseWallet(walletLinkUrl);\n        }, 99);\n    }\n    showConnecting(_options) {\n        // it uses the return callback to clear the dialog\n        return () => {\n            this.redirectDialog.clear();\n        };\n    }\n}\n//# sourceMappingURL=WLMobileRelayUI.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WLMobileRelayUI.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WalletLinkRelayUI.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WalletLinkRelayUI.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RETRY_SVG_PATH: () => (/* binding */ RETRY_SVG_PATH),\n/* harmony export */   WalletLinkRelayUI: () => (/* binding */ WalletLinkRelayUI)\n/* harmony export */ });\n/* harmony import */ var _components_cssReset_cssReset_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/cssReset/cssReset.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset.js\");\n/* harmony import */ var _components_Snackbar_Snackbar_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components/Snackbar/Snackbar.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar.js\");\n\n\nconst RETRY_SVG_PATH = 'M5.00008 0.96875C6.73133 0.96875 8.23758 1.94375 9.00008 3.375L10.0001 2.375V5.5H9.53133H7.96883H6.87508L7.80633 4.56875C7.41258 3.3875 6.31258 2.53125 5.00008 2.53125C3.76258 2.53125 2.70633 3.2875 2.25633 4.36875L0.812576 3.76875C1.50008 2.125 3.11258 0.96875 5.00008 0.96875ZM2.19375 6.43125C2.5875 7.6125 3.6875 8.46875 5 8.46875C6.2375 8.46875 7.29375 7.7125 7.74375 6.63125L9.1875 7.23125C8.5 8.875 6.8875 10.0312 5 10.0312C3.26875 10.0312 1.7625 9.05625 1 7.625L0 8.625V5.5H0.46875H2.03125H3.125L2.19375 6.43125Z';\nclass WalletLinkRelayUI {\n    constructor() {\n        this.attached = false;\n        this.snackbar = new _components_Snackbar_Snackbar_js__WEBPACK_IMPORTED_MODULE_0__.Snackbar();\n    }\n    attach() {\n        if (this.attached) {\n            throw new Error('Coinbase Wallet SDK UI is already attached');\n        }\n        const el = document.documentElement;\n        const container = document.createElement('div');\n        container.className = '-cbwsdk-css-reset';\n        el.appendChild(container);\n        this.snackbar.attach(container);\n        this.attached = true;\n        (0,_components_cssReset_cssReset_js__WEBPACK_IMPORTED_MODULE_1__.injectCssReset)();\n    }\n    showConnecting(options) {\n        let snackbarProps;\n        if (options.isUnlinkedErrorState) {\n            snackbarProps = {\n                autoExpand: true,\n                message: 'Connection lost',\n                menuItems: [\n                    {\n                        isRed: false,\n                        info: 'Reset connection',\n                        svgWidth: '10',\n                        svgHeight: '11',\n                        path: 'M5.00008 0.96875C6.73133 0.96875 8.23758 1.94375 9.00008 3.375L10.0001 2.375V5.5H9.53133H7.96883H6.87508L7.80633 4.56875C7.41258 3.3875 6.31258 2.53125 5.00008 2.53125C3.76258 2.53125 2.70633 3.2875 2.25633 4.36875L0.812576 3.76875C1.50008 2.125 3.11258 0.96875 5.00008 0.96875ZM2.19375 6.43125C2.5875 7.6125 3.6875 8.46875 5 8.46875C6.2375 8.46875 7.29375 7.7125 7.74375 6.63125L9.1875 7.23125C8.5 8.875 6.8875 10.0312 5 10.0312C3.26875 10.0312 1.7625 9.05625 1 7.625L0 8.625V5.5H0.46875H2.03125H3.125L2.19375 6.43125Z',\n                        defaultFillRule: 'evenodd',\n                        defaultClipRule: 'evenodd',\n                        onClick: options.onResetConnection,\n                    },\n                ],\n            };\n        }\n        else {\n            snackbarProps = {\n                message: 'Confirm on phone',\n                menuItems: [\n                    {\n                        isRed: true,\n                        info: 'Cancel transaction',\n                        svgWidth: '11',\n                        svgHeight: '11',\n                        path: 'M10.3711 1.52346L9.21775 0.370117L5.37109 4.21022L1.52444 0.370117L0.371094 1.52346L4.2112 5.37012L0.371094 9.21677L1.52444 10.3701L5.37109 6.53001L9.21775 10.3701L10.3711 9.21677L6.53099 5.37012L10.3711 1.52346Z',\n                        defaultFillRule: 'inherit',\n                        defaultClipRule: 'inherit',\n                        onClick: options.onCancel,\n                    },\n                    {\n                        isRed: false,\n                        info: 'Reset connection',\n                        svgWidth: '10',\n                        svgHeight: '11',\n                        path: RETRY_SVG_PATH,\n                        defaultFillRule: 'evenodd',\n                        defaultClipRule: 'evenodd',\n                        onClick: options.onResetConnection,\n                    },\n                ],\n            };\n        }\n        return this.snackbar.presentItem(snackbarProps);\n    }\n}\n//# sourceMappingURL=WalletLinkRelayUI.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WalletLinkRelayUI.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog-css.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog-css.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((() => `.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-backdrop{position:fixed;top:0;left:0;right:0;bottom:0;transition:opacity .25s;background-color:rgba(10,11,13,.5)}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-backdrop-hidden{opacity:0}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box{display:block;position:fixed;top:50%;left:50%;transform:translate(-50%, -50%);padding:20px;border-radius:8px;background-color:#fff;color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box p{display:block;font-weight:400;font-size:14px;line-height:20px;padding-bottom:12px;color:#5b636e}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box button{appearance:none;border:none;background:none;color:#0052ff;padding:0;text-decoration:none;display:block;font-weight:600;font-size:16px;line-height:24px}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.dark{background-color:#0a0b0d;color:#fff}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.dark button{color:#0052ff}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.light{background-color:#fff;color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.light button{color:#0052ff}`)());\n//# sourceMappingURL=RedirectDialog-css.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog-css.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RedirectDialog: () => (/* binding */ RedirectDialog)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/node_modules/clsx/dist/clsx.m.js\");\n/* harmony import */ var preact__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! preact */ \"(ssr)/./node_modules/preact/dist/preact.mjs\");\n/* harmony import */ var _cssReset_cssReset_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../cssReset/cssReset.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset.js\");\n/* harmony import */ var _Snackbar_Snackbar_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Snackbar/Snackbar.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/util.js\");\n/* harmony import */ var _RedirectDialog_css_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./RedirectDialog-css.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog-css.js\");\n\n\n\n\n\n\nclass RedirectDialog {\n    constructor() {\n        this.root = null;\n        this.darkMode = (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.isDarkMode)();\n    }\n    attach() {\n        const el = document.documentElement;\n        this.root = document.createElement('div');\n        this.root.className = '-cbwsdk-css-reset';\n        el.appendChild(this.root);\n        (0,_cssReset_cssReset_js__WEBPACK_IMPORTED_MODULE_3__.injectCssReset)();\n    }\n    present(props) {\n        this.render(props);\n    }\n    clear() {\n        this.render(null);\n    }\n    render(props) {\n        if (!this.root)\n            return;\n        (0,preact__WEBPACK_IMPORTED_MODULE_1__.render)(null, this.root);\n        if (!props)\n            return;\n        (0,preact__WEBPACK_IMPORTED_MODULE_1__.render)((0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(RedirectDialogContent, Object.assign({}, props, { onDismiss: () => {\n                this.clear();\n            }, darkMode: this.darkMode })), this.root);\n    }\n}\nconst RedirectDialogContent = ({ title, buttonText, darkMode, onButtonClick, onDismiss }) => {\n    const theme = darkMode ? 'dark' : 'light';\n    return ((0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(_Snackbar_Snackbar_js__WEBPACK_IMPORTED_MODULE_4__.SnackbarContainer, { darkMode: darkMode },\n        (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"div\", { class: \"-cbwsdk-redirect-dialog\" },\n            (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"style\", null, _RedirectDialog_css_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n            (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"div\", { class: \"-cbwsdk-redirect-dialog-backdrop\", onClick: onDismiss }),\n            (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"div\", { class: (0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)('-cbwsdk-redirect-dialog-box', theme) },\n                (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"p\", null, title),\n                (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"button\", { onClick: onButtonClick }, buttonText)))));\n};\n//# sourceMappingURL=RedirectDialog.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar-css.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar-css.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((() => `.-cbwsdk-css-reset .-gear-container{margin-left:16px !important;margin-right:9px !important;display:flex;align-items:center;justify-content:center;width:24px;height:24px;transition:opacity .25s}.-cbwsdk-css-reset .-gear-container *{user-select:none}.-cbwsdk-css-reset .-gear-container svg{opacity:0;position:absolute}.-cbwsdk-css-reset .-gear-icon{height:12px;width:12px;z-index:10000}.-cbwsdk-css-reset .-cbwsdk-snackbar{align-items:flex-end;display:flex;flex-direction:column;position:fixed;right:0;top:0;z-index:2147483647}.-cbwsdk-css-reset .-cbwsdk-snackbar *{user-select:none}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance{display:flex;flex-direction:column;margin:8px 16px 0 16px;overflow:visible;text-align:left;transform:translateX(0);transition:opacity .25s,transform .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header:hover .-gear-container svg{opacity:1}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header{display:flex;align-items:center;background:#fff;overflow:hidden;border:1px solid #e7ebee;box-sizing:border-box;border-radius:8px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header-cblogo{margin:8px 8px 8px 8px}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header *{cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header-message{color:#000;font-size:13px;line-height:1.5;user-select:none}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu{background:#fff;transition:opacity .25s ease-in-out,transform .25s linear,visibility 0s;visibility:hidden;border:1px solid #e7ebee;box-sizing:border-box;border-radius:8px;opacity:0;flex-direction:column;padding-left:8px;padding-right:8px}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:last-child{margin-bottom:8px !important}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover{background:#f5f7f8;border-radius:6px;transition:background .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover span{color:#050f19;transition:color .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover svg path{fill:#000;transition:fill .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item{visibility:inherit;height:35px;margin-top:8px;margin-bottom:0;display:flex;flex-direction:row;align-items:center;padding:8px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item *{visibility:inherit;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover{background:rgba(223,95,103,.2);transition:background .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover *{cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover svg path{fill:#df5f67;transition:fill .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover span{color:#df5f67;transition:color .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-info{color:#aaa;font-size:13px;margin:0 8px 0 32px;position:absolute}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-hidden{opacity:0;text-align:left;transform:translateX(25%);transition:opacity .5s linear}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-expanded .-cbwsdk-snackbar-instance-menu{opacity:1;display:flex;transform:translateY(8px);visibility:visible}`)());\n//# sourceMappingURL=Snackbar-css.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar-css.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Snackbar: () => (/* binding */ Snackbar),\n/* harmony export */   SnackbarContainer: () => (/* binding */ SnackbarContainer),\n/* harmony export */   SnackbarInstance: () => (/* binding */ SnackbarInstance)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/node_modules/clsx/dist/clsx.m.js\");\n/* harmony import */ var preact__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! preact */ \"(ssr)/./node_modules/preact/dist/preact.mjs\");\n/* harmony import */ var preact_hooks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! preact/hooks */ \"(ssr)/./node_modules/preact/hooks/dist/hooks.mjs\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/util.js\");\n/* harmony import */ var _Snackbar_css_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Snackbar-css.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar-css.js\");\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\n\n\n\n\n\nconst cblogo = `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEuNDkyIDEwLjQxOWE4LjkzIDguOTMgMCAwMTguOTMtOC45M2gxMS4xNjNhOC45MyA4LjkzIDAgMDE4LjkzIDguOTN2MTEuMTYzYTguOTMgOC45MyAwIDAxLTguOTMgOC45M0gxMC40MjJhOC45MyA4LjkzIDAgMDEtOC45My04LjkzVjEwLjQxOXoiIGZpbGw9IiMxNjUyRjAiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTEwLjQxOSAwSDIxLjU4QzI3LjMzNSAwIDMyIDQuNjY1IDMyIDEwLjQxOVYyMS41OEMzMiAyNy4zMzUgMjcuMzM1IDMyIDIxLjU4MSAzMkgxMC40MkM0LjY2NSAzMiAwIDI3LjMzNSAwIDIxLjU4MVYxMC40MkMwIDQuNjY1IDQuNjY1IDAgMTAuNDE5IDB6bTAgMS40ODhhOC45MyA4LjkzIDAgMDAtOC45MyA4LjkzdjExLjE2M2E4LjkzIDguOTMgMCAwMDguOTMgOC45M0gyMS41OGE4LjkzIDguOTMgMCAwMDguOTMtOC45M1YxMC40MmE4LjkzIDguOTMgMCAwMC04LjkzLTguOTNIMTAuNDJ6IiBmaWxsPSIjZmZmIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNS45OTggMjYuMDQ5Yy01LjU0OSAwLTEwLjA0Ny00LjQ5OC0xMC4wNDctMTAuMDQ3IDAtNS41NDggNC40OTgtMTAuMDQ2IDEwLjA0Ny0xMC4wNDYgNS41NDggMCAxMC4wNDYgNC40OTggMTAuMDQ2IDEwLjA0NiAwIDUuNTQ5LTQuNDk4IDEwLjA0Ny0xMC4wNDYgMTAuMDQ3eiIgZmlsbD0iI2ZmZiIvPjxwYXRoIGQ9Ik0xMi43NjIgMTQuMjU0YzAtLjgyMi42NjctMS40ODkgMS40ODktMS40ODloMy40OTdjLjgyMiAwIDEuNDg4LjY2NiAxLjQ4OCAxLjQ4OXYzLjQ5N2MwIC44MjItLjY2NiAxLjQ4OC0xLjQ4OCAxLjQ4OGgtMy40OTdhMS40ODggMS40ODggMCAwMS0xLjQ4OS0xLjQ4OHYtMy40OTh6IiBmaWxsPSIjMTY1MkYwIi8+PC9zdmc+`;\nconst gearIcon = `data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;\nclass Snackbar {\n    constructor() {\n        this.items = new Map();\n        this.nextItemKey = 0;\n        this.root = null;\n        this.darkMode = (0,_util_js__WEBPACK_IMPORTED_MODULE_3__.isDarkMode)();\n    }\n    attach(el) {\n        this.root = document.createElement('div');\n        this.root.className = '-cbwsdk-snackbar-root';\n        el.appendChild(this.root);\n        this.render();\n    }\n    presentItem(itemProps) {\n        const key = this.nextItemKey++;\n        this.items.set(key, itemProps);\n        this.render();\n        return () => {\n            this.items.delete(key);\n            this.render();\n        };\n    }\n    clear() {\n        this.items.clear();\n        this.render();\n    }\n    render() {\n        if (!this.root) {\n            return;\n        }\n        (0,preact__WEBPACK_IMPORTED_MODULE_1__.render)((0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"div\", null,\n            (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(SnackbarContainer, { darkMode: this.darkMode }, Array.from(this.items.entries()).map(([key, itemProps]) => ((0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(SnackbarInstance, Object.assign({}, itemProps, { key: key })))))), this.root);\n    }\n}\nconst SnackbarContainer = (props) => ((0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"div\", { class: (0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)('-cbwsdk-snackbar-container') },\n    (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"style\", null, _Snackbar_css_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n    (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"div\", { class: \"-cbwsdk-snackbar\" }, props.children)));\nconst SnackbarInstance = ({ autoExpand, message, menuItems, }) => {\n    const [hidden, setHidden] = (0,preact_hooks__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [expanded, setExpanded] = (0,preact_hooks__WEBPACK_IMPORTED_MODULE_2__.useState)(autoExpand !== null && autoExpand !== void 0 ? autoExpand : false);\n    (0,preact_hooks__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {\n        const timers = [\n            window.setTimeout(() => {\n                setHidden(false);\n            }, 1),\n            window.setTimeout(() => {\n                setExpanded(true);\n            }, 10000),\n        ];\n        return () => {\n            timers.forEach(window.clearTimeout);\n        };\n    });\n    const toggleExpanded = () => {\n        setExpanded(!expanded);\n    };\n    return ((0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"div\", { class: (0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)('-cbwsdk-snackbar-instance', hidden && '-cbwsdk-snackbar-instance-hidden', expanded && '-cbwsdk-snackbar-instance-expanded') },\n        (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"div\", { class: \"-cbwsdk-snackbar-instance-header\", onClick: toggleExpanded },\n            (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"img\", { src: cblogo, class: \"-cbwsdk-snackbar-instance-header-cblogo\" }),\n            ' ',\n            (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"div\", { class: \"-cbwsdk-snackbar-instance-header-message\" }, message),\n            (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"div\", { class: \"-gear-container\" },\n                !expanded && ((0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"svg\", { width: \"24\", height: \"24\", viewBox: \"0 0 24 24\", fill: \"none\", xmlns: \"http://www.w3.org/2000/svg\" },\n                    (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"circle\", { cx: \"12\", cy: \"12\", r: \"12\", fill: \"#F5F7F8\" }))),\n                (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"img\", { src: gearIcon, class: \"-gear-icon\", title: \"Expand\" }))),\n        menuItems && menuItems.length > 0 && ((0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"div\", { class: \"-cbwsdk-snackbar-instance-menu\" }, menuItems.map((action, i) => ((0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"div\", { class: (0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)('-cbwsdk-snackbar-instance-menu-item', action.isRed && '-cbwsdk-snackbar-instance-menu-item-is-red'), onClick: action.onClick, key: i },\n            (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"svg\", { width: action.svgWidth, height: action.svgHeight, viewBox: \"0 0 10 11\", fill: \"none\", xmlns: \"http://www.w3.org/2000/svg\" },\n                (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"path\", { \"fill-rule\": action.defaultFillRule, \"clip-rule\": action.defaultClipRule, d: action.path, fill: \"#AAAAAA\" })),\n            (0,preact__WEBPACK_IMPORTED_MODULE_1__.h)(\"span\", { class: (0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)('-cbwsdk-snackbar-instance-menu-item-info', action.isRed && '-cbwsdk-snackbar-instance-menu-item-info-is-red') }, action.info))))))));\n};\n//# sourceMappingURL=Snackbar.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset-css.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset-css.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((() => `@namespace svg \"http://www.w3.org/2000/svg\";.-cbwsdk-css-reset,.-cbwsdk-css-reset *{animation:none;animation-delay:0;animation-direction:normal;animation-duration:0;animation-fill-mode:none;animation-iteration-count:1;animation-name:none;animation-play-state:running;animation-timing-function:ease;backface-visibility:visible;background:0;background-attachment:scroll;background-clip:border-box;background-color:rgba(0,0,0,0);background-image:none;background-origin:padding-box;background-position:0 0;background-position-x:0;background-position-y:0;background-repeat:repeat;background-size:auto auto;border:0;border-style:none;border-width:medium;border-color:inherit;border-bottom:0;border-bottom-color:inherit;border-bottom-left-radius:0;border-bottom-right-radius:0;border-bottom-style:none;border-bottom-width:medium;border-collapse:separate;border-image:none;border-left:0;border-left-color:inherit;border-left-style:none;border-left-width:medium;border-radius:0;border-right:0;border-right-color:inherit;border-right-style:none;border-right-width:medium;border-spacing:0;border-top:0;border-top-color:inherit;border-top-left-radius:0;border-top-right-radius:0;border-top-style:none;border-top-width:medium;box-shadow:none;box-sizing:border-box;caption-side:top;clear:none;clip:auto;color:inherit;columns:auto;column-count:auto;column-fill:balance;column-gap:normal;column-rule:medium none currentColor;column-rule-color:currentColor;column-rule-style:none;column-rule-width:none;column-span:1;column-width:auto;counter-increment:none;counter-reset:none;direction:ltr;empty-cells:show;float:none;font:normal;font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Helvetica Neue\",Arial,sans-serif;font-size:medium;font-style:normal;font-variant:normal;font-weight:normal;height:auto;hyphens:none;letter-spacing:normal;line-height:normal;list-style:none;list-style-image:none;list-style-position:outside;list-style-type:disc;margin:0;margin-bottom:0;margin-left:0;margin-right:0;margin-top:0;opacity:1;orphans:0;outline:0;outline-color:invert;outline-style:none;outline-width:medium;overflow:visible;overflow-x:visible;overflow-y:visible;padding:0;padding-bottom:0;padding-left:0;padding-right:0;padding-top:0;page-break-after:auto;page-break-before:auto;page-break-inside:auto;perspective:none;perspective-origin:50% 50%;pointer-events:auto;position:static;quotes:\"\\\\201C\" \"\\\\201D\" \"\\\\2018\" \"\\\\2019\";tab-size:8;table-layout:auto;text-align:inherit;text-align-last:auto;text-decoration:none;text-decoration-color:inherit;text-decoration-line:none;text-decoration-style:solid;text-indent:0;text-shadow:none;text-transform:none;transform:none;transform-style:flat;transition:none;transition-delay:0s;transition-duration:0s;transition-property:none;transition-timing-function:ease;unicode-bidi:normal;vertical-align:baseline;visibility:visible;white-space:normal;widows:0;word-spacing:normal;z-index:auto}.-cbwsdk-css-reset strong{font-weight:bold}.-cbwsdk-css-reset *{box-sizing:border-box;font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Helvetica Neue\",Arial,sans-serif;line-height:1}.-cbwsdk-css-reset [class*=container]{margin:0;padding:0}.-cbwsdk-css-reset style{display:none}`)());\n//# sourceMappingURL=cssReset-css.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset-css.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   injectCssReset: () => (/* binding */ injectCssReset)\n/* harmony export */ });\n/* harmony import */ var _cssReset_css_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cssReset-css.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset-css.js\");\n// Copyright (c) 2018-2023 Coinbase, Inc. <https://www.coinbase.com/>\n\nfunction injectCssReset() {\n    const styleEl = document.createElement('style');\n    styleEl.type = 'text/css';\n    styleEl.appendChild(document.createTextNode(_cssReset_css_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]));\n    document.documentElement.appendChild(styleEl);\n}\n//# sourceMappingURL=cssReset.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC9zaWduL3dhbGxldGxpbmsvcmVsYXkvdWkvY29tcG9uZW50cy9jc3NSZXNldC9jc3NSZXNldC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ29DO0FBQzdCO0FBQ1A7QUFDQTtBQUNBLGdEQUFnRCx3REFBRztBQUNuRDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kcmVhbXN0YXJ0ci8uL25vZGVfbW9kdWxlcy9AY29pbmJhc2Uvd2FsbGV0LXNkay9kaXN0L3NpZ24vd2FsbGV0bGluay9yZWxheS91aS9jb21wb25lbnRzL2Nzc1Jlc2V0L2Nzc1Jlc2V0LmpzPzFjNzIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSAyMDE4LTIwMjMgQ29pbmJhc2UsIEluYy4gPGh0dHBzOi8vd3d3LmNvaW5iYXNlLmNvbS8+XG5pbXBvcnQgY3NzIGZyb20gJy4vY3NzUmVzZXQtY3NzLmpzJztcbmV4cG9ydCBmdW5jdGlvbiBpbmplY3RDc3NSZXNldCgpIHtcbiAgICBjb25zdCBzdHlsZUVsID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnc3R5bGUnKTtcbiAgICBzdHlsZUVsLnR5cGUgPSAndGV4dC9jc3MnO1xuICAgIHN0eWxlRWwuYXBwZW5kQ2hpbGQoZG9jdW1lbnQuY3JlYXRlVGV4dE5vZGUoY3NzKSk7XG4gICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmFwcGVuZENoaWxkKHN0eWxlRWwpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3NzUmVzZXQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/cssReset/cssReset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/util.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/util.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQrUrl: () => (/* binding */ createQrUrl),\n/* harmony export */   getLocation: () => (/* binding */ getLocation),\n/* harmony export */   isDarkMode: () => (/* binding */ isDarkMode),\n/* harmony export */   isMobileWeb: () => (/* binding */ isMobileWeb)\n/* harmony export */ });\nfunction createQrUrl(sessionId, sessionSecret, serverUrl, isParentConnection, version, chainId) {\n    const sessionIdKey = isParentConnection ? 'parent-id' : 'id';\n    const query = new URLSearchParams({\n        [sessionIdKey]: sessionId,\n        secret: sessionSecret,\n        server: serverUrl,\n        v: version,\n        chainId: chainId.toString(),\n    }).toString();\n    const qrUrl = `${serverUrl}/#/link?${query}`;\n    return qrUrl;\n}\nfunction isInIFrame() {\n    try {\n        return window.frameElement !== null;\n    }\n    catch (e) {\n        return false;\n    }\n}\nfunction getLocation() {\n    try {\n        if (isInIFrame() && window.top) {\n            return window.top.location;\n        }\n        return window.location;\n    }\n    catch (e) {\n        return window.location;\n    }\n}\nfunction isMobileWeb() {\n    var _a;\n    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test((_a = window === null || window === void 0 ? void 0 : window.navigator) === null || _a === void 0 ? void 0 : _a.userAgent);\n}\nfunction isDarkMode() {\n    var _a, _b;\n    return (_b = (_a = window === null || window === void 0 ? void 0 : window.matchMedia) === null || _a === void 0 ? void 0 : _a.call(window, '(prefers-color-scheme: dark)').matches) !== null && _b !== void 0 ? _b : false;\n}\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/checkCrossOriginOpenerPolicy.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/util/checkCrossOriginOpenerPolicy.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkCrossOriginOpenerPolicy: () => (/* binding */ checkCrossOriginOpenerPolicy),\n/* harmony export */   getCrossOriginOpenerPolicy: () => (/* binding */ getCrossOriginOpenerPolicy)\n/* harmony export */ });\nconst COOP_ERROR_MESSAGE = `Coinbase Wallet SDK requires the Cross-Origin-Opener-Policy header to not be set to 'same-origin'. This is to ensure that the SDK can communicate with the Coinbase Smart Wallet app.\n\nPlease see https://www.smartwallet.dev/guides/tips/popup-tips#cross-origin-opener-policy for more information.`;\n/**\n * Creates a checker for the Cross-Origin-Opener-Policy (COOP).\n *\n * @returns An object with methods to get and check the Cross-Origin-Opener-Policy.\n *\n * @method getCrossOriginOpenerPolicy\n * Retrieves current Cross-Origin-Opener-Policy.\n * @throws Will throw an error if the policy has not been checked yet.\n *\n * @method checkCrossOriginOpenerPolicy\n * Checks the Cross-Origin-Opener-Policy of the current environment.\n * If in a non-browser environment, sets the policy to 'non-browser-env'.\n * If in a browser environment, fetches the policy from the current origin.\n * Logs an error if the policy is 'same-origin'.\n */\nconst createCoopChecker = () => {\n    let crossOriginOpenerPolicy;\n    return {\n        getCrossOriginOpenerPolicy: () => {\n            if (crossOriginOpenerPolicy === undefined) {\n                return 'undefined';\n            }\n            return crossOriginOpenerPolicy;\n        },\n        checkCrossOriginOpenerPolicy: async () => {\n            if (typeof window === 'undefined') {\n                // Non-browser environment\n                crossOriginOpenerPolicy = 'non-browser-env';\n                return;\n            }\n            try {\n                const url = `${window.location.origin}${window.location.pathname}`;\n                const response = await fetch(url, {\n                    method: 'HEAD',\n                });\n                if (!response.ok) {\n                    throw new Error(`HTTP error! status: ${response.status}`);\n                }\n                const result = response.headers.get('Cross-Origin-Opener-Policy');\n                crossOriginOpenerPolicy = result !== null && result !== void 0 ? result : 'null';\n                if (crossOriginOpenerPolicy === 'same-origin') {\n                    console.error(COOP_ERROR_MESSAGE);\n                }\n            }\n            catch (error) {\n                console.error('Error checking Cross-Origin-Opener-Policy:', error.message);\n                crossOriginOpenerPolicy = 'error';\n            }\n        },\n    };\n};\nconst { checkCrossOriginOpenerPolicy, getCrossOriginOpenerPolicy } = createCoopChecker();\n//# sourceMappingURL=checkCrossOriginOpenerPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/checkCrossOriginOpenerPolicy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/cipher.js":
/*!***************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/util/cipher.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   decryptContent: () => (/* binding */ decryptContent),\n/* harmony export */   deriveSharedSecret: () => (/* binding */ deriveSharedSecret),\n/* harmony export */   encrypt: () => (/* binding */ encrypt),\n/* harmony export */   encryptContent: () => (/* binding */ encryptContent),\n/* harmony export */   exportKeyToHexString: () => (/* binding */ exportKeyToHexString),\n/* harmony export */   generateKeyPair: () => (/* binding */ generateKeyPair),\n/* harmony export */   importKeyFromHexString: () => (/* binding */ importKeyFromHexString)\n/* harmony export */ });\n/* harmony import */ var _core_type_util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/type/util.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/type/util.js\");\n\nasync function generateKeyPair() {\n    return crypto.subtle.generateKey({\n        name: 'ECDH',\n        namedCurve: 'P-256',\n    }, true, ['deriveKey']);\n}\nasync function deriveSharedSecret(ownPrivateKey, peerPublicKey) {\n    return crypto.subtle.deriveKey({\n        name: 'ECDH',\n        public: peerPublicKey,\n    }, ownPrivateKey, {\n        name: 'AES-GCM',\n        length: 256,\n    }, false, ['encrypt', 'decrypt']);\n}\nasync function encrypt(sharedSecret, plainText) {\n    const iv = crypto.getRandomValues(new Uint8Array(12));\n    const cipherText = await crypto.subtle.encrypt({\n        name: 'AES-GCM',\n        iv,\n    }, sharedSecret, new TextEncoder().encode(plainText));\n    return { iv, cipherText };\n}\nasync function decrypt(sharedSecret, { iv, cipherText }) {\n    const plainText = await crypto.subtle.decrypt({\n        name: 'AES-GCM',\n        iv,\n    }, sharedSecret, cipherText);\n    return new TextDecoder().decode(plainText);\n}\nfunction getFormat(keyType) {\n    switch (keyType) {\n        case 'public':\n            return 'spki';\n        case 'private':\n            return 'pkcs8';\n    }\n}\nasync function exportKeyToHexString(type, key) {\n    const format = getFormat(type);\n    const exported = await crypto.subtle.exportKey(format, key);\n    return (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_0__.uint8ArrayToHex)(new Uint8Array(exported));\n}\nasync function importKeyFromHexString(type, hexString) {\n    const format = getFormat(type);\n    const arrayBuffer = (0,_core_type_util_js__WEBPACK_IMPORTED_MODULE_0__.hexStringToUint8Array)(hexString).buffer;\n    return await crypto.subtle.importKey(format, new Uint8Array(arrayBuffer), {\n        name: 'ECDH',\n        namedCurve: 'P-256',\n    }, true, type === 'private' ? ['deriveKey'] : []);\n}\nasync function encryptContent(content, sharedSecret) {\n    const serialized = JSON.stringify(content, (_, value) => {\n        if (!(value instanceof Error))\n            return value;\n        const error = value;\n        return Object.assign(Object.assign({}, (error.code ? { code: error.code } : {})), { message: error.message });\n    });\n    return encrypt(sharedSecret, serialized);\n}\nasync function decryptContent(encryptedData, sharedSecret) {\n    return JSON.parse(await decrypt(sharedSecret, encryptedData));\n}\n//# sourceMappingURL=cipher.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/cipher.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/provider.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/util/provider.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkErrorForInvalidRequestArgs: () => (/* binding */ checkErrorForInvalidRequestArgs),\n/* harmony export */   fetchRPCRequest: () => (/* binding */ fetchRPCRequest),\n/* harmony export */   getCoinbaseInjectedProvider: () => (/* binding */ getCoinbaseInjectedProvider)\n/* harmony export */ });\n/* harmony import */ var _sdk_info_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../sdk-info.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sdk-info.js\");\n/* harmony import */ var _core_error_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/error/errors.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js\");\n\n\nasync function fetchRPCRequest(request, rpcUrl) {\n    const requestBody = Object.assign(Object.assign({}, request), { jsonrpc: '2.0', id: crypto.randomUUID() });\n    const res = await window.fetch(rpcUrl, {\n        method: 'POST',\n        body: JSON.stringify(requestBody),\n        mode: 'cors',\n        headers: {\n            'Content-Type': 'application/json',\n            'X-Cbw-Sdk-Version': _sdk_info_js__WEBPACK_IMPORTED_MODULE_0__.VERSION,\n            'X-Cbw-Sdk-Platform': _sdk_info_js__WEBPACK_IMPORTED_MODULE_0__.NAME,\n        },\n    });\n    const { result, error } = await res.json();\n    if (error)\n        throw error;\n    return result;\n}\nfunction getCoinbaseInjectedLegacyProvider() {\n    const window = globalThis;\n    return window.coinbaseWalletExtension;\n}\nfunction getInjectedEthereum() {\n    var _a, _b;\n    try {\n        const window = globalThis;\n        return (_a = window.ethereum) !== null && _a !== void 0 ? _a : (_b = window.top) === null || _b === void 0 ? void 0 : _b.ethereum;\n    }\n    catch (_c) {\n        return undefined;\n    }\n}\nfunction getCoinbaseInjectedProvider({ metadata, preference, }) {\n    var _a, _b;\n    const { appName, appLogoUrl, appChainIds } = metadata;\n    if (preference.options !== 'smartWalletOnly') {\n        const extension = getCoinbaseInjectedLegacyProvider();\n        if (extension) {\n            (_a = extension.setAppInfo) === null || _a === void 0 ? void 0 : _a.call(extension, appName, appLogoUrl, appChainIds, preference);\n            return extension;\n        }\n    }\n    const ethereum = getInjectedEthereum();\n    if (ethereum === null || ethereum === void 0 ? void 0 : ethereum.isCoinbaseBrowser) {\n        (_b = ethereum.setAppInfo) === null || _b === void 0 ? void 0 : _b.call(ethereum, appName, appLogoUrl, appChainIds, preference);\n        return ethereum;\n    }\n    return undefined;\n}\n/**\n * Validates the arguments for an invalid request and returns an error if any validation fails.\n * Valid request args are defined here: https://eips.ethereum.org/EIPS/eip-1193#request\n * @param args The request arguments to validate.\n * @returns An error object if the arguments are invalid, otherwise undefined.\n */\nfunction checkErrorForInvalidRequestArgs(args) {\n    if (!args || typeof args !== 'object' || Array.isArray(args)) {\n        throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_1__.standardErrors.rpc.invalidParams({\n            message: 'Expected a single, non-array, object argument.',\n            data: args,\n        });\n    }\n    const { method, params } = args;\n    if (typeof method !== 'string' || method.length === 0) {\n        throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_1__.standardErrors.rpc.invalidParams({\n            message: \"'args.method' must be a non-empty string.\",\n            data: args,\n        });\n    }\n    if (params !== undefined &&\n        !Array.isArray(params) &&\n        (typeof params !== 'object' || params === null)) {\n        throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_1__.standardErrors.rpc.invalidParams({\n            message: \"'args.params' must be an object or array if provided.\",\n            data: args,\n        });\n    }\n    switch (method) {\n        case 'eth_sign':\n        case 'eth_signTypedData_v2':\n        case 'eth_subscribe':\n        case 'eth_unsubscribe':\n            throw _core_error_errors_js__WEBPACK_IMPORTED_MODULE_1__.standardErrors.provider.unsupportedMethod();\n    }\n}\n//# sourceMappingURL=provider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/provider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/validatePreferences.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/util/validatePreferences.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validatePreferences: () => (/* binding */ validatePreferences)\n/* harmony export */ });\n/**\n * Validates user supplied preferences. Throws if keys are not valid.\n * @param preference\n */\nfunction validatePreferences(preference) {\n    if (!preference) {\n        return;\n    }\n    if (!['all', 'smartWalletOnly', 'eoaOnly'].includes(preference.options)) {\n        throw new Error(`Invalid options: ${preference.options}`);\n    }\n    if (preference.attribution) {\n        if (preference.attribution.auto !== undefined &&\n            preference.attribution.dataSuffix !== undefined) {\n            throw new Error(`Attribution cannot contain both auto and dataSuffix properties`);\n        }\n    }\n}\n//# sourceMappingURL=validatePreferences.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC91dGlsL3ZhbGlkYXRlUHJlZmVyZW5jZXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxtQkFBbUI7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJlYW1zdGFydHIvLi9ub2RlX21vZHVsZXMvQGNvaW5iYXNlL3dhbGxldC1zZGsvZGlzdC91dGlsL3ZhbGlkYXRlUHJlZmVyZW5jZXMuanM/MjM1OSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFZhbGlkYXRlcyB1c2VyIHN1cHBsaWVkIHByZWZlcmVuY2VzLiBUaHJvd3MgaWYga2V5cyBhcmUgbm90IHZhbGlkLlxuICogQHBhcmFtIHByZWZlcmVuY2VcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHZhbGlkYXRlUHJlZmVyZW5jZXMocHJlZmVyZW5jZSkge1xuICAgIGlmICghcHJlZmVyZW5jZSkge1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIGlmICghWydhbGwnLCAnc21hcnRXYWxsZXRPbmx5JywgJ2VvYU9ubHknXS5pbmNsdWRlcyhwcmVmZXJlbmNlLm9wdGlvbnMpKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgSW52YWxpZCBvcHRpb25zOiAke3ByZWZlcmVuY2Uub3B0aW9uc31gKTtcbiAgICB9XG4gICAgaWYgKHByZWZlcmVuY2UuYXR0cmlidXRpb24pIHtcbiAgICAgICAgaWYgKHByZWZlcmVuY2UuYXR0cmlidXRpb24uYXV0byAhPT0gdW5kZWZpbmVkICYmXG4gICAgICAgICAgICBwcmVmZXJlbmNlLmF0dHJpYnV0aW9uLmRhdGFTdWZmaXggIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBBdHRyaWJ1dGlvbiBjYW5ub3QgY29udGFpbiBib3RoIGF1dG8gYW5kIGRhdGFTdWZmaXggcHJvcGVydGllc2ApO1xuICAgICAgICB9XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmFsaWRhdGVQcmVmZXJlbmNlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/validatePreferences.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/web.js":
/*!************************************************************!*\
  !*** ./node_modules/@coinbase/wallet-sdk/dist/util/web.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closePopup: () => (/* binding */ closePopup),\n/* harmony export */   openPopup: () => (/* binding */ openPopup)\n/* harmony export */ });\n/* harmony import */ var _sdk_info_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../sdk-info.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sdk-info.js\");\n/* harmony import */ var _checkCrossOriginOpenerPolicy_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./checkCrossOriginOpenerPolicy.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/checkCrossOriginOpenerPolicy.js\");\n/* harmony import */ var _core_error_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/error/errors.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/core/error/errors.js\");\n/* harmony import */ var _sign_walletlink_relay_ui_components_Snackbar_Snackbar_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../sign/walletlink/relay/ui/components/Snackbar/Snackbar.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/components/Snackbar/Snackbar.js\");\n/* harmony import */ var _sign_walletlink_relay_ui_WalletLinkRelayUI_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../sign/walletlink/relay/ui/WalletLinkRelayUI.js */ \"(ssr)/./node_modules/@coinbase/wallet-sdk/dist/sign/walletlink/relay/ui/WalletLinkRelayUI.js\");\n\n\n\n\n\nconst POPUP_WIDTH = 420;\nconst POPUP_HEIGHT = 540;\nconst RETRY_BUTTON = {\n    isRed: false,\n    info: 'Retry',\n    svgWidth: '10',\n    svgHeight: '11',\n    path: _sign_walletlink_relay_ui_WalletLinkRelayUI_js__WEBPACK_IMPORTED_MODULE_0__.RETRY_SVG_PATH,\n    defaultFillRule: 'evenodd',\n    defaultClipRule: 'evenodd',\n};\nconst POPUP_BLOCKED_MESSAGE = 'Popup was blocked. Try again.';\nlet snackbar = null;\nfunction openPopup(url) {\n    const left = (window.innerWidth - POPUP_WIDTH) / 2 + window.screenX;\n    const top = (window.innerHeight - POPUP_HEIGHT) / 2 + window.screenY;\n    appendAppInfoQueryParams(url);\n    function tryOpenPopup() {\n        const popupId = `wallet_${crypto.randomUUID()}`;\n        const popup = window.open(url, popupId, `width=${POPUP_WIDTH}, height=${POPUP_HEIGHT}, left=${left}, top=${top}`);\n        popup === null || popup === void 0 ? void 0 : popup.focus();\n        if (!popup) {\n            return null;\n        }\n        return popup;\n    }\n    let popup = tryOpenPopup();\n    // If the popup was blocked, show a snackbar with a retry button\n    if (!popup) {\n        const sb = initSnackbar();\n        return new Promise((resolve, reject) => {\n            sb.presentItem({\n                autoExpand: true,\n                message: POPUP_BLOCKED_MESSAGE,\n                menuItems: [\n                    Object.assign(Object.assign({}, RETRY_BUTTON), { onClick: () => {\n                            popup = tryOpenPopup();\n                            if (popup) {\n                                resolve(popup);\n                            }\n                            else {\n                                reject(_core_error_errors_js__WEBPACK_IMPORTED_MODULE_1__.standardErrors.rpc.internal('Popup window was blocked'));\n                            }\n                            sb.clear();\n                        } }),\n                ],\n            });\n        });\n    }\n    return Promise.resolve(popup);\n}\nfunction closePopup(popup) {\n    if (popup && !popup.closed) {\n        popup.close();\n    }\n}\nfunction appendAppInfoQueryParams(url) {\n    const params = {\n        sdkName: _sdk_info_js__WEBPACK_IMPORTED_MODULE_2__.NAME,\n        sdkVersion: _sdk_info_js__WEBPACK_IMPORTED_MODULE_2__.VERSION,\n        origin: window.location.origin,\n        coop: (0,_checkCrossOriginOpenerPolicy_js__WEBPACK_IMPORTED_MODULE_3__.getCrossOriginOpenerPolicy)(),\n    };\n    for (const [key, value] of Object.entries(params)) {\n        url.searchParams.append(key, value.toString());\n    }\n}\nfunction initSnackbar() {\n    if (!snackbar) {\n        const root = document.createElement('div');\n        root.className = '-cbwsdk-css-reset';\n        document.body.appendChild(root);\n        snackbar = new _sign_walletlink_relay_ui_components_Snackbar_Snackbar_js__WEBPACK_IMPORTED_MODULE_4__.Snackbar();\n        snackbar.attach(root);\n    }\n    return snackbar;\n}\n//# sourceMappingURL=web.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@coinbase/wallet-sdk/dist/util/web.js\n");

/***/ })

};
;