'use client'

import {
  motion, useInView,
} from 'framer-motion';
import { useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';

const Join = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, {
    once: true,
    amount: 0.2,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  const statVariants = {
    hidden: {
      opacity: 0,
      scale: 0.8,
    },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const stats = [
    {
      value: "144+",
      description: "Founding Nodes",
    },
    {
      value: "55+",
      description: "Global Real World Sanctuaries",
    },
    {
      value: "12+",
      description: "Partner Institutions",
    },
    {
      value: "$4.8T",
      description: "Market Potential",
    },
  ];

  return (
    <section
      id="join"
      ref={sectionRef}
      className="py-4 lg:py-16 lg:pt-0 relative w-full container px-2 md:px-8 mx-auto !max-w-[1200px]"
    >
      <div className="">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-6xl mx-auto"
        >
          <div className='bg-eerie-black border border-light-yellow/5 rounded-3xl p-6 lg:p-8 overflow-hidden relative'>
            <div className="absolute pointer-events-none bottom-10 z-0 left-1/2 -translate-x-1/2 bg-light-yellow opacity-50 w-1/4 lg:w-1/2 h-20 blur-[120px]" />
            <div className="text-center mb-8 relative z-10">
              <h2 className={`text-xl text-center md:text-2xl lg:text-3xl font-semibold text-white mb-2`}>
                Join the Quantum Longevity Revolution
              </h2>
              <div className="rounded-full h-1 w-20 bg-gradient-to-tr from-light-yellow to-yellow-600 mb-4 mx-auto"></div>
              <p className="text-sm md:text-base text-gray-300 max-w-3xl mx-auto">
                Support groundbreaking research that combines quantum resonance, sacred geometry, and blockchain to extend the healthy human lifespan
              </p>
            </div>

            <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12 relative z-10">
              <motion.div variants={itemVariants}>
                <Link
                  href="#"
                  className={`w-full sm:w-min whitespace-nowrap block text-center py-2 px-6 bg-light-yellow hover:bg-yellow-200 text-black font-medium rounded-xl transition-all duration-300`}
                >
                  Join Fundraising
                </Link>
              </motion.div>
              <motion.div variants={itemVariants}>
                <Link
                  href="https://discord.gg/luminacasa"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`w-full sm:w-min whitespace-nowrap block text-center py-2 px-6 bg-violets-are-blue/5 border border-white/20 hover:bg-yellow-200/80 hover:text-black text-white font-medium rounded-xl transition-all duration-300`}
                >
                  Join Discord Community
                </Link>
              </motion.div>
            </div>

            {/* Stats Section */}
            <motion.div
              variants={containerVariants}
              className="grid grid-cols-2 md:grid-cols-4 gap-6 relative z-10"
            >
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  variants={statVariants}
                  className="text-center transition-all duration-300"
                >
                  <h3 className="text-3xl md:text-4xl font-bold text-light-yellow mb-2">
                    {stat.value}
                  </h3>
                  <p className="text-gray-300 text-sm">
                    {stat.description}
                  </p>
                </motion.div>
              ))}
            </motion.div>
          </div>

          {/* Stay Updated Section */}
          <motion.div
            variants={itemVariants}
            className="mt-12 lg:mt-20 text-center"
          >
            <h3 className={`text-xl text-center md:text-2xl lg:text-3xl font-semibold text-white mb-4`}>
              Stay Updated
            </h3>
            <div className="rounded-full h-1 w-20 bg-gradient-to-tr from-light-yellow to-yellow-600 mx-auto mb-6"></div>
            <p className="text-gray-300 mb-6 max-w-3xl mx-auto md:text-base text-sm">
              Join our community to receive the latest updates on quantum longevity research,
              token developments, and exclusive early access opportunities.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-2 max-w-md mx-auto mb-4">
              <input
                type="email"
                placeholder="Your email address"
                className="flex-1 rounded-xl border border-yellow-500/10 bg-violets-are-blue/5 backdrop-blur-sm py-3 px-4 text-white text-sm focus:outline-none focus:border-yellow-500/50"
              />
              <button className={`w-full sm:w-min whitespace-nowrap block text-center py-2 px-6 bg-light-yellow hover:bg-yellow-200 text-black font-medium rounded-xl transition-all duration-300`}>
                Subscribe
              </button>
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="mt-16 lg:mt-24 text-center"
          >
            <div className="flex justify-center mb-8">
              <Image
                src="/casa_logo.png"
                alt="Lumina Casa Logo"
                width={120}
                height={120}
                className="w-auto h-full max-h-16"
              />
            </div>

            <div className="mb-8">
              <h3 className={`text-xl text-center md:text-2xl lg:text-3xl font-semibold text-white mb-4`}>
                Who Is This For?
              </h3>
              <div className="rounded-full h-1 w-20 bg-gradient-to-tr from-light-yellow to-yellow-600 mx-auto mb-6"></div>
              <ul className="text-gray-300 space-y-2 max-w-3xl mx-auto md:text-base text-sm">
                <li className="flex items-center justify-center gap-2">
                  <span className="text-light-yellow">🌍</span> Grounded investors with conscious capital
                </li>
                <li className="flex items-center justify-center gap-2">
                  <span className="text-light-yellow">🧘‍♀️</span> Visionaries, creatives, and light-technologists
                </li>
                <li className="flex items-center justify-center gap-2">
                  <span className="text-light-yellow">💎</span> High-frequency asset holders
                </li>
                <li className="flex items-center justify-center gap-2">
                  <span className="text-light-yellow">🔭</span> Builders of bridges between dimensions and systems
                </li>
                <li className="flex items-center justify-center gap-2">
                  <span className="text-light-yellow">✨</span> You — if your cells light up when reading this
                </li>
              </ul>
            </div>

            <div className="border border-violets-are-blue/15 bg-eerie-black rounded-3xl p-6 lg:p-8 overflow-hidden relative">
              <div className="absolute pointer-events-none bottom-10 z-0 left-1/2 -translate-x-1/2 bg-light-yellow opacity-30 w-1/4 lg:w-1/2 h-20 blur-[120px]" />
              <h3 className={`text-xl text-center md:text-2xl lg:text-3xl font-semibold text-white mb-4`}>
                Final Words of Gratitude
              </h3>
              <div className="rounded-full h-1 w-20 bg-gradient-to-tr from-light-yellow to-yellow-600 mx-auto mb-6"></div>
              <p className="text-gray-300 mb-4 max-w-3xl mx-auto md:text-base text-sm">
                To the bold, the beautiful, the radiant investors of this New Earth:
              </p>
              <p className="text-light-yellow font-medium mb-2">Thank you for choosing love over fear.</p>
              <p className="text-light-yellow font-medium mb-2">Thank you for aligning your wealth with wisdom.</p>
              <p className="text-light-yellow font-medium mb-6">Thank you for showing up in this sacred moment of planetary transition.</p>
              <p className="text-gray-300 mb-6 max-w-3xl mx-auto md:text-base text-sm">
                Together we root the radiance. Together we activate the grid. Together we circulate the light of the new economy.
              </p>
              <p className="text-white font-medium mb-1">With infinite joy and devotion,</p>
              <p className="text-white font-medium mb-6">Team Lumina CASA</p>
              <p className="text-gray-400 text-sm italic">
                Prototype launched in Koh Phangan. Global rollout begins now.
              </p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Join;
