import type { SVGProps } from "react";

export const MultipleUsersIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <path
        fill="#C2F3FF"
        d="M15.182 5.995a5.199 5.199 0 0 1-5.196 5.2A5.254 5.254 0 0 1 4.82 5.902 5.066 5.066 0 0 1 9.78.838c.074-.005.148-.005.222-.005a5.151 5.151 0 0 1 5.18 5.162Z"
      />
      <path
        fill="#66E1FF"
        d="M9.986 8.314A5.21 5.21 0 0 1 5.024 4.47a5.14 5.14 0 0 0-.205 1.435 5.254 5.254 0 0 0 5.167 5.29 5.19 5.19 0 0 0 4.99-6.642 5.194 5.194 0 0 1-4.99 3.761Z"
      />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15.182 5.995a5.199 5.199 0 0 1-5.196 5.2A5.254 5.254 0 0 1 4.82 5.902 5.066 5.066 0 0 1 9.78.838c.074-.005.148-.005.222-.005a5.151 5.151 0 0 1 5.18 5.162Z"
      />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M9.777.838c-2.704 2.929-2.704 6.75 0 10.354M10.227.838c2.704 2.929 2.704 6.748 0 10.352M5.674 8.804h8.683M5.578 3.225h8.802M4.82 6.015h10.362"
      />
      <path
        fill="#FFDDA1"
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M10.068 16.935a1.873 1.873 0 1 0 0-3.746 1.873 1.873 0 0 0 0 3.746ZM4.176 16.935a1.873 1.873 0 1 0 0-3.746 1.873 1.873 0 0 0 0 3.746ZM15.829 16.935a1.873 1.873 0 1 0 0-3.746 1.873 1.873 0 0 0 0 3.746Z"
      />
      <path
        fill="#66E1FF"
        d="M1.232 19.167a3.734 3.734 0 0 1 5.813 0 3.841 3.841 0 0 1 5.91 0 3.732 3.732 0 0 1 5.813 0"
      />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M1.232 19.167a3.734 3.734 0 0 1 5.813 0 3.841 3.841 0 0 1 5.91 0 3.732 3.732 0 0 1 5.813 0"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h20v20H0z" />
      </clipPath>
    </defs>
  </svg>
)
