{"v": "5.8.1", "fr": 60, "ip": 0, "op": 125, "w": 430, "h": 430, "nm": "wired-outline-981-consultation", "ddd": 0, "assets": [{"id": "comp_1", "nm": "hover-conversation", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "bubble", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.161], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 56, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.427], "y": [0]}, "t": 67, "s": [-21]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 79.162, "s": [15]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 91.324, "s": [-9]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 103.488, "s": [3]}, {"t": 116.75390625, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [309, 202.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [29, 65, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-11.046, 0], [0, 0], [0, -11.046], [0, 0], [11.046, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 11.046]], "o": [[0, -11.046], [0, 0], [11.046, 0], [0, 0], [0, 11.046], [0, 0], [0, 0], [0, 0], [0, 0], [-11.046, 0], [0, 0]], "v": [[-95, -47.5], [-75, -67.5], [75, -67.5], [95, -47.5], [95, 7.5], [75, 27.5], [30, 27.5], [30, 67.5], [-30, 27.5], [-75, 27.5], [-95, 7.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-981-consultation').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-981-consultation').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "bubble", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.161], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 56, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.427], "y": [0]}, "t": 67, "s": [-21]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 79.162, "s": [15]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 91.324, "s": [-9]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 103.488, "s": [3]}, {"t": 116.75390625, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [309, 202.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [29, 65, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-11.046, 0], [0, 0], [0, -11.046], [0, 0], [11.046, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 11.046]], "o": [[0, -11.046], [0, 0], [11.046, 0], [0, 0], [0, 11.046], [0, 0], [0, 0], [0, 0], [0, 0], [-11.046, 0], [0, 0]], "v": [[-95, -47.5], [-75, -67.5], [75, -67.5], [95, -47.5], [95, 7.5], [75, 27.5], [30, 27.5], [30, 67.5], [-30, 27.5], [-75, 27.5], [-95, 7.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "bubble", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "bubble", "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.161], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.427], "y": [0]}, "t": 11, "s": [-21]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 23.162, "s": [15]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 35.324, "s": [-9]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 47.488, "s": [3]}, {"t": 60.75390625, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [121, 183, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-29, 68, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-11.046, 0], [0, 0], [0, -11.046], [0, 0], [11.046, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 11.046]], "o": [[0, -11.046], [0, 0], [11.046, 0], [0, 0], [0, 11.046], [0, 0], [0, 0], [0, 0], [0, 0], [-11.046, 0], [0, 0]], "v": [[-95, -50], [-75, -70], [75, -70], [95, -50], [95, 10], [75, 30], [30, 30], [-30, 70], [-30, 30], [-75, 30], [-95, 10]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-981-consultation').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-981-consultation').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "bubble", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "character-2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [300, 375, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 72.5, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 10.143, "s": [105, 95, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 18, "s": [98, 102, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 26, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 93, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 98.143, "s": [105, 95, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 106, "s": [98, 102, 100]}, {"t": 114, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[30, -42.5], [0, -12.5], [-30, -42.5], [0, -72.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[-5.204, -42.304], [-35.204, -12.304], [-65.204, -42.304], [-35.204, -72.304]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 61, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[-5.204, -42.304], [-35.204, -12.304], [-65.204, -42.304], [-35.204, -72.304]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 67, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[-5.204, -29.804], [-35.204, 0.196], [-65.204, -29.804], [-35.204, -59.804]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 73, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[-5.204, -42.304], [-35.204, -12.304], [-65.204, -42.304], [-35.204, -72.304]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 79, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[-5.204, -29.804], [-35.204, 0.196], [-65.204, -29.804], [-35.204, -59.804]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 85, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[-5.204, -42.304], [-35.204, -12.304], [-65.204, -42.304], [-35.204, -72.304]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 93, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[-5.204, -29.804], [-35.204, 0.196], [-65.204, -29.804], [-35.204, -59.804]], "c": true}]}, {"t": 106, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[30, -42.5], [0, -12.5], [-30, -42.5], [0, -72.5]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0, 0], [0, -24.853], [0, 0], [0, 0], [0, 0], [24.853, 0]], "o": [[-24.853, 0], [0, 0], [0, 0], [0, 0], [0, -24.853], [0, 0]], "v": [[-20, 12.5], [-65, 57.5], [-65, 72.5], [65, 72.5], [65, 57.5], [20, 12.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [{"i": [[0, 0], [0, -15.134], [0, 0], [0, 0], [0, 0], [32.235, 0]], "o": [[-15.134, 0], [0, 0], [0, 0], [0, 0], [0, -32.235], [0, 0]], "v": [[-37.598, 12.5], [-65, 39.902], [-65, 72.5], [65, 72.5], [65, 70.867], [6.633, 12.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 93, "s": [{"i": [[0, 0], [0, -15.134], [0, 0], [0, 0], [0, 0], [32.235, 0]], "o": [[-15.134, 0], [0, 0], [0, 0], [0, 0], [0, -32.235], [0, 0]], "v": [[-37.598, 12.5], [-65, 39.902], [-65, 72.5], [65, 72.5], [65, 70.867], [6.633, 12.5]], "c": true}]}, {"t": 106, "s": [{"i": [[0, 0], [0, -24.853], [0, 0], [0, 0], [0, 0], [24.853, 0]], "o": [[-24.853, 0], [0, 0], [0, 0], [0, 0], [0, -24.853], [0, 0]], "v": [[-20, 12.5], [-65, 57.5], [-65, 72.5], [65, 72.5], [65, 57.5], [20, 12.5]], "c": true}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-981-consultation').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-981-consultation').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "character-1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [130, 375, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 72.5, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5.143, "s": [105, 95, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 13, "s": [98, 102, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 21, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 104, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 109.143, "s": [105, 95, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 117, "s": [98, 102, 100]}, {"t": 125, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[30, -42.5], [0, -12.5], [-30, -42.5], [0, -72.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[72.347, -42.304], [42.347, -12.304], [12.347, -42.304], [42.347, -72.304]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[72.347, -42.304], [42.347, -12.304], [12.347, -42.304], [42.347, -72.304]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 32, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[75.847, -31.304], [45.847, -1.304], [15.847, -31.304], [45.847, -61.304]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[72.347, -42.304], [42.347, -12.304], [12.347, -42.304], [42.347, -72.304]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[75.847, -31.304], [45.847, -1.304], [15.847, -31.304], [45.847, -61.304]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 53, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[72.347, -42.304], [42.347, -12.304], [12.347, -42.304], [42.347, -72.304]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 59, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[75.847, -31.304], [45.847, -1.304], [15.847, -31.304], [45.847, -61.304]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[72.347, -42.304], [42.347, -12.304], [12.347, -42.304], [42.347, -72.304]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 72, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[75.847, -31.304], [45.847, -1.304], [15.847, -31.304], [45.847, -61.304]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 80, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[72.347, -42.304], [42.347, -12.304], [12.347, -42.304], [42.347, -72.304]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 104, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[72.347, -42.304], [42.347, -12.304], [12.347, -42.304], [42.347, -72.304]], "c": true}]}, {"t": 117, "s": [{"i": [[0, -16.569], [16.569, 0], [0, 16.569], [-16.569, 0]], "o": [[0, 16.569], [-16.569, 0], [0, -16.569], [16.569, 0]], "v": [[30, -42.5], [0, -12.5], [-30, -42.5], [0, -72.5]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, -24.853], [0, 0], [0, 0], [0, 0], [24.853, 0]], "o": [[-24.853, 0], [0, 0], [0, 0], [0, 0], [0, -24.853], [0, 0]], "v": [[-20, 12.5], [-65, 57.5], [-65, 72.5], [65, 72.5], [65, 57.5], [20, 12.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[0, 0], [0, -32.596], [0, 0], [0, 0], [0, 0], [14.457, 0]], "o": [[-32.596, 0], [0, 0], [0, 0], [0, 0], [0, -14.457], [0, 0]], "v": [[-5.98, 12.5], [-65, 71.52], [-65, 72.5], [65, 72.5], [65, 38.676], [38.824, 12.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 104, "s": [{"i": [[0, 0], [0, -32.596], [0, 0], [0, 0], [0, 0], [14.457, 0]], "o": [[-32.596, 0], [0, 0], [0, 0], [0, 0], [0, -14.457], [0, 0]], "v": [[-5.98, 12.5], [-65, 71.52], [-65, 72.5], [65, 72.5], [65, 38.676], [38.824, 12.5]], "c": true}]}, {"t": 117, "s": [{"i": [[0, 0], [0, -24.853], [0, 0], [0, 0], [0, 0], [24.853, 0]], "o": [[-24.853, 0], [0, 0], [0, 0], [0, 0], [0, -24.853], [0, 0]], "v": [[-20, 12.5], [-65, 57.5], [-65, 72.5], [65, 72.5], [65, 57.5], [20, 12.5]], "c": true}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-981-consultation').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-981-consultation').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "stroke", "np": 3, "mn": "Pseudo/@@CC1HaKWRSxasqbR7CXzomA", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "<PERSON><PERSON>", "mn": "Pseudo/@@CC1HaKWRSxasqbR7CXzomA-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [1, 1, 1], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [1, 1, 1], "ix": 1}}]}], "ip": 0, "op": 361, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-conversation", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 135, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-conversation", "dr": 125}]}