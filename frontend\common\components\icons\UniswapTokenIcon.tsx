import type { SVGProps } from "react";
export const UniswapTokenIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={18}
    height={20}
    fill="none"
    {...props}
  >
    <path
      fill="#fff"
      d="M6.804 4.552c-.215-.034-.224-.038-.123-.053.195-.03.653.01.97.086.738.176 1.41.626 2.127 1.425l.19.213.273-.044c1.148-.185 2.315-.038 3.292.414.269.124.693.372.746.436.017.02.047.15.068.29.073.481.037.85-.11 1.126-.08.15-.085.198-.031.326a.315.315 0 0 0 .281.178c.243 0 .504-.393.625-.94l.048-.217.096.108c.522.593.933 1.402 1.003 1.977l.018.15-.087-.136a1.683 1.683 0 0 0-.498-.524c-.35-.232-.721-.311-1.703-.363-.887-.047-1.388-.122-1.886-.285-.847-.276-1.273-.645-2.279-1.966-.447-.587-.723-.912-.997-1.173-.624-.595-1.238-.906-2.023-1.028Z"
    />
    <path
      fill="#fff"
      d="M14.479 5.862c.022-.393.075-.652.182-.89a.952.952 0 0 1 .088-.17c.006 0-.012.07-.04.154-.078.23-.091.544-.038.91.069.465.107.532.597 1.033.23.235.497.532.594.66l.176.23-.176-.165c-.215-.202-.71-.597-.82-.653-.074-.038-.085-.037-.13.008-.042.041-.05.104-.056.4-.01.46-.072.755-.223 1.05-.082.16-.095.126-.02-.054.055-.135.06-.194.06-.64-.001-.895-.107-1.11-.73-1.48a6.834 6.834 0 0 0-.577-.299 2.19 2.19 0 0 1-.282-.138c.017-.017.624.16.869.254.363.14.423.159.467.142.03-.012.044-.098.059-.352ZM7.223 7.394c-.437-.603-.708-1.528-.65-2.22l.019-.213.1.018c.186.034.509.155.66.246.414.252.593.584.776 1.436.053.25.123.532.156.628.052.154.248.513.407.746.115.169.04.248-.215.225-.388-.035-.914-.398-1.253-.866ZM13.948 11.883c-2.044-.824-2.764-1.54-2.764-2.747 0-.178.006-.323.013-.323.008 0 .087.059.176.13.415.333.879.476 2.164.663.756.11 1.182.2 1.574.33 1.248.415 2.02 1.256 2.204 2.402.053.333.022.957-.065 1.286-.068.26-.277.728-.332.746-.016.005-.03-.054-.035-.134-.02-.43-.238-.85-.602-1.163-.415-.357-.972-.64-2.333-1.19ZM12.513 12.226a3.63 3.63 0 0 0-.099-.433l-.052-.156.097.109c.134.15.24.344.33.6.068.197.076.255.075.573 0 .313-.009.379-.072.555-.1.279-.223.476-.431.688-.374.38-.854.591-1.547.679-.12.015-.471.04-.78.056-.777.04-1.29.125-1.75.286-.065.024-.124.038-.13.032-.019-.019.294-.206.553-.33.364-.176.728-.272 1.54-.408.403-.067.818-.149.923-.18.995-.306 1.507-1.095 1.343-2.071Z"
    />
    <path
      fill="#fff"
      d="M13.45 13.893c-.272-.585-.334-1.15-.185-1.676.016-.056.041-.102.057-.102a.55.55 0 0 1 .142.077c.125.084.376.226 1.045.591.835.456 1.31.809 1.634 1.212.283.353.459.755.543 1.246.048.277.02.946-.051 1.226-.225.882-.746 1.574-1.49 1.978-.109.06-.207.108-.217.108-.01 0 .029-.1.088-.224.25-.524.279-1.034.09-1.6-.116-.348-.352-.772-.83-1.488-.554-.833-.69-1.054-.826-1.348ZM5.771 17.048c.759-.641 1.703-1.097 2.563-1.237.37-.06.988-.036 1.33.052.55.14 1.043.456 1.299.833.25.367.357.688.469 1.4.044.282.092.564.106.628.084.37.247.665.45.813.32.236.874.25 1.418.038a.709.709 0 0 1 .178-.056c.02.02-.254.204-.447.3-.26.13-.467.181-.742.181-.498 0-.912-.254-1.258-.772a6.154 6.154 0 0 1-.339-.678c-.365-.833-.545-1.087-.968-1.364-.368-.242-.843-.285-1.2-.11-.47.23-.601.832-.265 1.212.133.152.383.282.587.308a.627.627 0 0 0 .708-.629c0-.25-.096-.393-.337-.502-.33-.15-.686.025-.684.336 0 .132.058.216.19.276.086.038.088.041.019.027-.304-.063-.375-.43-.13-.672.292-.292.897-.163 1.104.235.088.167.098.5.022.701-.17.45-.667.687-1.17.558-.344-.087-.483-.182-.897-.609-.718-.741-.997-.885-2.033-1.047l-.199-.03.226-.192Z"
    />
    <path
      fill="#fff"
      fillRule="evenodd"
      d="M.353.443c2.4 2.917 6.099 7.458 6.283 7.713.151.21.094.398-.166.546a1.573 1.573 0 0 1-.59.166.756.756 0 0 1-.495-.213c-.098-.092-.492-.68-1.4-2.093A153.814 153.814 0 0 0 2.692 4.57c-.037-.034-.036-.033 1.222 2.221.79 1.416 1.057 1.917 1.057 1.984 0 .136-.037.207-.205.395-.28.312-.405.663-.495 1.39-.101.814-.385 1.389-1.173 2.373-.462.576-.537.682-.654.914-.146.293-.187.457-.203.826-.017.39.016.643.136 1.016.104.327.213.543.49.974.241.373.38.65.38.758 0 .086.016.086.388.002.89-.201 1.614-.556 2.02-.99.252-.268.311-.417.313-.785.001-.24-.007-.291-.072-.43-.106-.225-.299-.413-.723-.704-.557-.38-.794-.687-.86-1.109-.054-.346.009-.59.317-1.236.318-.668.397-.954.45-1.627.035-.436.083-.608.208-.745.13-.144.247-.193.57-.237.525-.072.86-.208 1.135-.462a.988.988 0 0 0 .354-.752l.012-.242-.134-.156C6.742 7.384.03 0 0 0c-.006 0 .153.2.353.443Zm3.164 14.672a.429.429 0 0 0-.131-.565c-.173-.115-.44-.06-.44.09 0 .***************.***************.104.028.218-.077.115-.07.216.017.284.*************.445-.135ZM7.69 9.687c-.246.076-.486.338-.56.613-.046.167-.02.461.048.552.**************.502.183.562-.004 1.05-.245 1.108-.547.046-.247-.168-.59-.464-.74-.152-.078-.477-.11-.634-.061Zm.657.514c.087-.123.05-.256-.098-.346-.281-.172-.705-.03-.705.236 0 .132.22.276.424.276.135 0 .32-.08.38-.166Z"
      clipRule="evenodd"
    />
  </svg>
);
