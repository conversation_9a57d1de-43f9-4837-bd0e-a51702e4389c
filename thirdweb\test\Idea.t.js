const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("IdeaFactory", function () {
  let IdeaFactory, ideaFactory, Idea, idea, owner, addr1, addr2;

  const INIT_SUPPLY = ethers.utils.parseUnits("1000", 18);
  const IDEATOKEN_CREATION_FEE = ethers.utils.parseEther("0.1");
  const IDEACOIN_FUNDING_GOAL = ethers.utils.parseEther("10");
  const MAX_SUPPLY = ethers.utils.parseUnits("10000", 18);

  beforeEach(async function () {
    [owner, addr1, addr2] = await ethers.getSigners();
    Idea = await ethers.getContractFactory("Idea");
    IdeaFactory = await ethers.getContractFactory("IdeaFactory");

    ideaFactory = await IdeaFactory.deploy();
    await ideaFactory.deployed();
  });

  describe("createIdeaToken", function () {
    it("Should create an Idea token successfully", async function () {
      const createIdeaTokenParams = {
        name: "IdeaToken",
        symbol: "IDEA",
        description: "A great idea",
        imageUrl: "https://example.com/token.png",
        productUrl: "https://example.com",
        categories: ["Tech", "Innovation"],
        productScreenshotUrl: "https://example.com/screenshot.png",
        twitterUrl: "https://twitter.com/example",
        telegramUrl: "https://t.me/example",
      };

      await expect(
        ideaFactory
          .connect(addr1)
          .createIdeaToken(createIdeaTokenParams, {
            value: IDEATOKEN_CREATION_FEE,
          })
      ).to.emit(ideaFactory, "IdeaTokenCreated");

      const allTokens = await ideaFactory.getAllIdeaTokens();
      expect(allTokens.length).to.equal(1);
      expect(allTokens[0].name).to.equal("IdeaToken");
      expect(allTokens[0].symbol).to.equal("IDEA");
    });

    it("Should fail if insufficient creation fee is sent", async function () {
      const createIdeaTokenParams = {
        name: "IdeaToken",
        symbol: "IDEA",
        description: "A great idea",
        imageUrl: "https://example.com/token.png",
        productUrl: "https://example.com",
        categories: ["Tech", "Innovation"],
        productScreenshotUrl: "https://example.com/screenshot.png",
        twitterUrl: "https://twitter.com/example",
        telegramUrl: "https://t.me/example",
      };

      await expect(
        ideaFactory
          .connect(addr1)
          .createIdeaToken(createIdeaTokenParams, {
            value: ethers.utils.parseEther("0.05"),
          })
      ).to.be.revertedWith("InsufficientCreationFee");
    });
  });

  describe("buyIdeaToken", function () {
    it("Should allow buying Idea tokens successfully", async function () {
      const createIdeaTokenParams = {
        name: "IdeaToken",
        symbol: "IDEA",
        description: "A great idea",
        imageUrl: "https://example.com/token.png",
        productUrl: "https://example.com",
        categories: ["Tech", "Innovation"],
        productScreenshotUrl: "https://example.com/screenshot.png",
        twitterUrl: "https://twitter.com/example",
        telegramUrl: "https://t.me/example",
      };

      await ideaFactory
        .connect(addr1)
        .createIdeaToken(createIdeaTokenParams, {
          value: IDEATOKEN_CREATION_FEE,
        });

      const allTokens = await ideaFactory.getAllIdeaTokens();
      const ideaTokenAddress = allTokens[0].tokenAddress;

      await expect(
        ideaFactory
          .connect(addr2)
          .buyIdeaToken(ideaTokenAddress, ethers.utils.parseUnits("100", 18), {
            value: ethers.utils.parseEther("1"),
          })
      ).to.emit(ideaFactory, "IdeaTokenPurchased");
    });

    it("Should fail if insufficient ETH is sent", async function () {
      const createIdeaTokenParams = {
        name: "IdeaToken",
        symbol: "IDEA",
        description: "A great idea",
        imageUrl: "https://example.com/token.png",
        productUrl: "https://example.com",
        categories: ["Tech", "Innovation"],
        productScreenshotUrl: "https://example.com/screenshot.png",
        twitterUrl: "https://twitter.com/example",
        telegramUrl: "https://t.me/example",
      };

      await ideaFactory
        .connect(addr1)
        .createIdeaToken(createIdeaTokenParams, {
          value: IDEATOKEN_CREATION_FEE,
        });

      const allTokens = await ideaFactory.getAllIdeaTokens();
      const ideaTokenAddress = allTokens[0].tokenAddress;

      await expect(
        ideaFactory
          .connect(addr2)
          .buyIdeaToken(ideaTokenAddress, ethers.utils.parseUnits("100", 18), {
            value: ethers.utils.parseEther("0.1"),
          })
      ).to.be.revertedWith("InsufficientEthSent");
    });
  });

  describe("setCreationFeeInWei", function () {
    it("Should update the creation fee successfully", async function () {
      await ideaFactory
        .connect(owner)
        .setCreationFeeInWei(ethers.utils.parseEther("0.2"));
      const newFee = await ideaFactory.IDEATOKEN_CREATION_FEE();
      expect(newFee).to.equal(ethers.utils.parseEther("0.2"));
    });

    it("Should fail if called by non-owner", async function () {
      await expect(
        ideaFactory
          .connect(addr1)
          .setCreationFeeInWei(ethers.utils.parseEther("0.2"))
      ).to.be.revertedWith("Ownable: caller is not the owner");
    });
  });

  describe("withdraw", function () {
    it("Should allow the owner to withdraw contract funds", async function () {
      await ideaFactory.connect(addr1).createIdeaToken(
        {
          name: "IdeaToken",
          symbol: "IDEA",
          description: "A great idea",
          imageUrl: "https://example.com/token.png",
          productUrl: "https://example.com",
          categories: ["Tech", "Innovation"],
          productScreenshotUrl: "https://example.com/screenshot.png",
          twitterUrl: "https://twitter.com/example",
          telegramUrl: "https://t.me/example",
        },
        { value: IDEATOKEN_CREATION_FEE }
      );

      const initialBalance = await ethers.provider.getBalance(owner.address);

      await ideaFactory.connect(owner).withdraw();

      const finalBalance = await ethers.provider.getBalance(owner.address);
      expect(finalBalance).to.be.gt(initialBalance);
    });

    it("Should fail if called by non-owner", async function () {
      await expect(ideaFactory.connect(addr1).withdraw()).to.be.revertedWith(
        "Ownable: caller is not the owner"
      );
    });
  });

  describe("pause and unpause", function () {
    it("Should allow pausing and unpausing by the owner", async function () {
      await ideaFactory.connect(owner).pause();
      expect(await ideaFactory.paused()).to.be.true;

      await ideaFactory.connect(owner).unpause();
      expect(await ideaFactory.paused()).to.be.false;
    });

    it("Should fail to perform certain actions when paused", async function () {
      await ideaFactory.connect(owner).pause();

      await expect(
        ideaFactory.connect(addr1).createIdeaToken(
          {
            name: "IdeaToken",
            symbol: "IDEA",
            description: "A great idea",
            imageUrl: "https://example.com/token.png",
            productUrl: "https://example.com",
            categories: ["Tech", "Innovation"],
            productScreenshotUrl: "https://example.com/screenshot.png",
            twitterUrl: "https://twitter.com/example",
            telegramUrl: "https://t.me/example",
          },
          { value: IDEATOKEN_CREATION_FEE }
        )
      ).to.be.revertedWith("Pausable: paused");
    });
  });
});
