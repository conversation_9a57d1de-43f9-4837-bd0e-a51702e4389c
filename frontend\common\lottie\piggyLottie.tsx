'use client'
import <PERSON><PERSON> from "react-lottie";
import * as piggyData from '@/common/lottie/piggy-animation.json'

export const PiggyLottie = ({ 
  enablePiggyAnim, 
  width = 24, 
  height = 24,
} : { 
  enablePiggyAnim: boolean;
  width?: number;
  height?: number;
}) => {
  const piggyOptions = {
    loop: true,
    autoplay: false,
    animationData: piggyData,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  };
  return (
    <Lottie options={piggyOptions}
      isStopped={!enablePiggyAnim}
      style={{ 
        margin: '0',
        pointerEvents: 'none',
      }}
      height={width}
      width={height}
    />
  )
}
