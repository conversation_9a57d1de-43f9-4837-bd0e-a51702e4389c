'use client'
import Script from 'next/script';

export default function AboutStructuredData () {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "DreamStartr",
    "url": "https://dreamstartr.com/about",
    "logo": "https://dreamstartr.com/preview.png",
    "description": "DreamStartr combines AI technology, community validation, and bonding curves to help creators transform ideas into successful blockchain and AI projects.",
    "sameAs": [
      "https://twitter.com/dreamstarterxyz",
      "https://t.me/dreamstarterxyz",
    ],
    "foundingDate": "2024",
    "founders": [
      {
        "@type": "Person",
        "name": "DreamStartr Team",
      },
    ],
    "knowsAbout": [
      "Web3",
      "Blockchain",
      "AI",
      "Decentralized Finance",
      "Tokenization",
      "Bonding Curves",
      "Community Governance",
    ],
    "makesOffer": {
      "@type": "Offer",
      "itemOffered": {
        "@type": "Service",
        "name": "AI-Powered Project Development Platform",
        "description": "A platform that helps creators transform ideas into reality with AI assistance, community validation, and decentralized funding.",
      },
    },
    "potentialAction": {
      "@type": "CreateAction",
      "name": "Create a Project",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://dreamstartr.com",
      },
      "object": {
        "@type": "CreativeWork",
        "name": "Web3 Project",
      },
    },
  };

  // Add mainEntity structured data for the About page
  const aboutPageData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "About DreamStartr",
    "description": "Learn how DreamStartr combines AI assistance, community validation, and decentralized finance to help innovative ideas become reality.",
    "url": "https://dreamstartr.com/about",
    "isPartOf": {
      "@type": "WebSite",
      "name": "DreamStartr",
      "url": "https://dreamstartr.com",
    },
    "mainEntity": {
      "@type": "Organization",
      "name": "DreamStartr",
      "description": "DreamStartr is revolutionizing how ideas become reality by combining the power of AI, community validation, and decentralized finance.",
      "knowsAbout": [
        "AI-Powered Creation",
        "Bonding Curves",
        "AI Community Growth",
        "Community Validation",
      ],
    },
  };

  return (
    <>
      <Script id="organization-structured-data" type="application/ld+json" strategy="beforeInteractive">
        {JSON.stringify(jsonLd)}
      </Script>
      <Script id="webpage-structured-data" type="application/ld+json" strategy="beforeInteractive">
        {JSON.stringify(aboutPageData)}
      </Script>
    </>
  );
}