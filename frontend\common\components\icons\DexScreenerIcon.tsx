import type { SVGProps } from "react";
export const DexScreenerIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={14}
    height={16}
    fill="none"
    {...props}
  >
    <g fill="#fff" fillRule="evenodd" clipPath="url(#a)" clipRule="evenodd">
      <path d="M7.854 5.7a9.757 9.757 0 0 0 1.684-1.096c.127.273.141.511.076.704a.775.775 0 0 1-.25.347 1.12 1.12 0 0 1-.453.215c-.328.076-.71.032-1.057-.17Zm.083 2.5.628.374c-1.283.742-1.632 2.119-2.046 3.46-.415-1.341-.764-2.718-2.047-3.46l.629-.374a.31.31 0 0 0 .148-.122.325.325 0 0 0 .05-.19c-.057-1.255.272-1.81.715-2.16a.816.816 0 0 1 .505-.187c.17 0 .345.063.504.187.444.35.772.905.715 2.16a.325.325 0 0 0 .*********** 0 0 0 .149.122ZM6.519 0c.726.02 1.454.165 2.086.448a5.222 5.222 0 0 1 1.672 1.196c.409.014 1.006-.454 1.283-.891-.477 1.612-2.654 3.516-4.161 4.244h-.002a1.416 1.416 0 0 0-.878-.32c-.305 0-.609.106-.88.32C4.13 4.27 1.953 2.365 1.476.753c.277.437.874.905 1.283.89A5.222 5.222 0 0 1 4.432.449C5.064.165 5.792.02 6.519 0ZM5.183 5.7a9.756 9.756 0 0 1-1.684-1.096c-.127.273-.141.511-.076.704a.776.776 0 0 0 .25.347c.125.101.281.175.454.215.327.076.709.032 1.056-.17Z" />
      <path d="M10.2 4c.333-.346.627-.729.863-1.071l.12.232c.385.796.585 1.588.585 2.48v1.415l.007.734c.028 1.8.406 3.622 1.262 5.29l-1.791-1.49-1.268 2.121-1.332-1.292-2.127 3.56-2.128-3.56-1.332 1.292-1.268-2.12L0 13.08c.856-1.667 1.234-3.49 1.262-5.29l.007-.734V5.641c0-.892.2-1.684.586-2.48l.12-.232c.235.342.529.725.862 1.072l-.104.223c-.202.432-.27.916-.112 1.376.102.296.287.55.526.744.231.188.506.315.793.382.187.043.377.06.567.055a4.875 4.875 0 0 0-.065.799l-1.69 1.006 1.304.753c.*************.297.207 1.075.995 1.721 3.939 2.166 5.377.444-1.438 1.09-4.382 2.165-5.377.093-.077.193-.146.297-.207l1.304-.753-1.69-1.006a4.875 4.875 0 0 0-.065-.8c.19.007.38-.011.567-.054.287-.067.562-.194.794-.382.238-.194.423-.448.525-.744.157-.46.09-.944-.112-1.376L10.2 4Z" />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h13.037v16H0z" />
      </clipPath>
    </defs>
  </svg>
);
