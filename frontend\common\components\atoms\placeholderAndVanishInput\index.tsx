"use client";

import {
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  cn,
} from "@/common/utils/helpers";
import {
  AnimatePresence, motion,
} from "framer-motion";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/common/components/atoms';
import 'regenerator-runtime/runtime'
import SpeechRecognition, {
  useSpeechRecognition,
} from 'react-speech-recognition'
import {
  Mi<PERSON>,
  MicOff,
} from "lucide-react";
import { usePrivy } from "@privy-io/react-auth";

export function PlaceholderAndVanishInput ({
  placeholders,
  onChange,
  onSubmit,
  input,
}: {
  input: string;
  placeholders: string[];
  onChange: (value: string) => void;
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
}) {
  const [currentPlaceholder, setCurrentPlaceholder] = useState(0);

  const {
    authenticated, login,
  } = usePrivy()

  const {
    transcript,
    listening,
    browserSupportsSpeechRecognition,
  } = useSpeechRecognition();

  const handleSpeech = () => {
    if (listening) {
      SpeechRecognition.stopListening();
    } else {
      SpeechRecognition.startListening();
    }
  }

  useEffect(() => {
    if (listening && transcript) {
      setValue(transcript)
      onChange && onChange(transcript);
    }
  }, [transcript, listening, onChange])

  useEffect(() => {
    setValue(input)
  }, [input])

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const startAnimation = useCallback(() => {
    intervalRef.current = setInterval(() => {
      setCurrentPlaceholder((prev) => (prev + 1) % placeholders.length);
    }, 3000);
  }, [placeholders]);
  const handleVisibilityChange = useCallback(() => {
    if (document.visibilityState !== "visible" && intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    } else if (document.visibilityState === "visible") {
      startAnimation();
    }
  }, [startAnimation]);

  useEffect(() => {
    startAnimation();
    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [placeholders, handleVisibilityChange, startAnimation]);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const newDataRef = useRef<any[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const [value, setValue] = useState("");
  const [animating, setAnimating] = useState(false);

  const draw = useCallback(() => {
    if (!inputRef.current) {
      return;
    }
    const canvas = canvasRef.current;
    if (!canvas) {
      return;
    }
    const ctx = canvas.getContext("2d");
    if (!ctx) {
      return;
    }
    canvas.width = 800;
    canvas.height = 800;
    ctx.clearRect(0, 0, 800, 800);
    const computedStyles = getComputedStyle(inputRef.current);

    const fontSize = parseFloat(computedStyles.getPropertyValue("font-size"));
    ctx.font = `${fontSize * 2}px ${computedStyles.fontFamily}`;
    ctx.fillStyle = "#FFF";
    ctx.fillText(value, 16, 40);

    const imageData = ctx.getImageData(0, 0, 800, 800);
    const pixelData = imageData.data;
    const newData: any[] = [];

    for (let t = 0; t < 800; t++) {
      const i = 4 * t * 800;
      for (let n = 0; n < 800; n++) {
        const e = i + 4 * n;
        if (
          pixelData[e] !== 0 &&
          pixelData[e + 1] !== 0 &&
          pixelData[e + 2] !== 0
        ) {
          newData.push({
            x: n,
            y: t,
            color: [
              pixelData[e],
              pixelData[e + 1],
              pixelData[e + 2],
              pixelData[e + 3],
            ],
          });
        }
      }
    }

    newDataRef.current = newData.map(({
      x, y, color,
    }) => ({
      x,
      y,
      r: 1,
      color: `rgba(${color[0]}, ${color[1]}, ${color[2]}, ${color[3]})`,
    }));
  }, [value]);

  useEffect(() => {
    if (value) {
      draw();
    }
  }, [value, draw]);

  const animate = (start: number) => {
    const animateFrame = (pos: number = 0) => {
      requestAnimationFrame(() => {
        const newArr = [];
        for (let i = 0; i < newDataRef.current.length; i++) {
          const current = newDataRef.current[i];
          if (current.x < pos) {
            newArr.push(current);
          } else {
            if (current.r <= 0) {
              current.r = 0;
              continue;
            }
            current.x += Math.random() > 0.5 ? 1 : -1;
            current.y += Math.random() > 0.5 ? 1 : -1;
            current.r -= 0.05 * Math.random();
            newArr.push(current);
          }
        }
        newDataRef.current = newArr;
        const ctx = canvasRef.current?.getContext("2d");
        if (ctx) {
          ctx.clearRect(pos, 0, 800, 800);
          newDataRef.current.forEach((t) => {
            const {
              x: n, y: i, r: s, color: color,
            } = t;
            if (n > pos) {
              ctx.beginPath();
              ctx.rect(n, i, s, s);
              ctx.fillStyle = color;
              ctx.strokeStyle = color;
              ctx.stroke();
            }
          });
        }
        if (newDataRef.current.length > 0) {
          animateFrame(pos - 8);
        } else {
          setValue("");
          setAnimating(false);
        }
      });
    };
    animateFrame(start);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {

    if (e.key === "Enter" && !animating && inputRef.current?.value) {
      vanishAndSubmit();
    }
  };

  const vanishAndSubmit = () => {
    if (!authenticated) {
      return
    }
    setAnimating(true);
    draw();

    const value = inputRef.current?.value || "";
    if (value && inputRef.current) {
      const maxX = newDataRef.current.reduce(
        (prev, current) => (current.x > prev ? current.x : prev),
        0,
      );
      animate(maxX);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!authenticated) {
      login()
    } else {
      vanishAndSubmit();
      onSubmit && onSubmit(e);
    }
  };
  return (
    <form
      className={cn(
        "w-full relative max-w-xl mx-auto bg-white/5 backdrop-blur-sm h-10 md:h-12 rounded-xl sm:rounded-2xl border border-white/15 hover:border-violets-are-blue transition duration-200",
      )}
      onSubmit={handleSubmit}
    >
      <canvas
        className={cn(
          "absolute pointer-events-none  text-base transform scale-50 top-[11%] md:top-[18%] left-0 origin-top-left filter text-white/50 w-full",
          !animating ? "opacity-0" : "opacity-100",
        )}
        ref={canvasRef}
      />
      <input
        onChange={(e) => {
          if (!animating) {
            setValue(e.target.value);
            onChange && onChange(e.target.value);
          }
        }}
        onKeyDown={handleKeyDown}
        ref={inputRef}
        value={value}
        type="text"
        className={cn(
          "w-full relative text-sm sm:text-base z-50 border-none text-white bg-transparent h-full rounded-full focus:outline-none focus:ring-0 pl-4 pr-20 md:pr-24",
          animating && "text-transparent",
        )}
      />
      {browserSupportsSpeechRecognition && (
        <Tooltip delayDuration={200}>
          <TooltipTrigger
            type="button"
            onClick={handleSpeech}
            className="text-white bg-gradient-to-tr shadow-sm from-violets-are-blue to-han-purple hover:from-violets-are-blue/70 hover:to-han-purple/70 absolute right-10 md:right-12 hover:bg-opacity-70 top-1/2 z-50 h-6 w-6 md:h-8 md:w-8 rounded-full disabled:bg-zinc-600 -translate-y-1/2 transition duration-200 flex items-center justify-center"
          >
            {listening ? (
              <MicOff width={20} height={20} strokeWidth={2} className="w-4 h-4 md:w-5 md:h-5" />
            ) : (
              <Mic width={20} height={20} strokeWidth={2} className="w-4 h-4 md:w-5 md:h-5" />
            )}
          </TooltipTrigger>
          <TooltipContent className="isolate bg-eerie-black shadow-lg border-0 outline-none rounded-lg z-[100]">
            <p className="text-xs text-white">Dream Out Loud</p>
          </TooltipContent>
        </Tooltip>
      )}
      <button
        disabled={!value}
        type="submit"
        className="absolute right-2 top-1/2 z-50 shadow-sm -translate-y-1/2 h-6 w-6 md:h-8 md:w-8 rounded-full bg-gradient-to-tr from-violets-are-blue to-han-purple hover:from-violets-are-blue/70 hover:to-han-purple/70 disabled:from-violets-are-blue/50 disabled:to-han-purple/50 transition duration-200 flex items-center justify-center"
      >
        <motion.svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="text-white h-4 w-4"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none" />
          <motion.path
            d="M5 12l14 0"
            initial={{
              strokeDasharray: "50%",
              strokeDashoffset: "50%",
            }}
            animate={{
              strokeDashoffset: value ? 0 : "50%",
            }}
            transition={{
              duration: 0.3,
              ease: "linear",
            }}
          />
          <path d="M13 18l6 -6" />
          <path d="M13 6l6 6" />
        </motion.svg>
      </button>
      <div className="absolute inset-0 flex items-center rounded-full pointer-events-none">
        <AnimatePresence mode="wait">
          {!value && (
            <motion.p
              initial={{
                y: 5,
                opacity: 0,
              }}
              key={`current-placeholder-${currentPlaceholder}`}
              animate={{
                y: 0,
                opacity: 1,
              }}
              exit={{
                y: -15,
                opacity: 0,
              }}
              transition={{
                duration: 0.3,
                ease: "linear",
              }}
              className="text-zinc-500 text-sm sm:text-base font-normal pl-4 text-left w-[calc(100%-5rem)] truncate"
            >
              {placeholders[currentPlaceholder]}
            </motion.p>
          )}
        </AnimatePresence>
      </div>
    </form>
  );
}
