import lang from './lang'

const {
  generateIdea,
  homePage,
  manageIdea,
} = lang

export const mobileWidthLimit = 480;
export const tabletWidthLimit = 768;
export const lowResDeskLimit = 1024;
export const highResDeskLimit = 1280;

export const ContractFunctions = {
  getIdeas: 'getAllIdeaTokens',
  getIdea: 'getIdeaToken',
  createIdeaToken: 'createIdeaToken',
  updateIdeaToken: 'updateIdeaToken',
  buyToken: 'buyIdeaToken',
  calculateCost: 'calculateCost',
  tokenToPool: 'tokenToPool',
}

export const FILE_SIZE_FIVE_MB = 5000000;
export const acceptedImageMimeTypes = ['image/jpeg', 'image/png', 'image/svg+xml', 'image/webp', 'image/gif', 'image/apng', 'image/avif'];
export const promptPlaceholders = generateIdea.promptPlaceholders;
export const promptLoadingStates = generateIdea.promptLoadingStates
export const agentLoadingStates = manageIdea.promptLoadingStates

export const landingPageDescription = `
  Create a modern, responsive landing page SVG that effectively communicates our platform's value proposition.
  The design must follow these detailed specifications:

  Layout & Structure:
  - Viewport: 1280x720px
  - Grid: Implement a 12-column grid system for consistent spacing
  - Color Palette: Use the brand colors established in the logo, maintaining a consistent visual identity
  - Typography: Employ a clear hierarchy with H1 for main headline (32px), H2 for subheaders (24px), and body text (16px)

  Header Section:
  - Sticky navigation bar (height: 64px)
  - Left: Logo (48x48px) with 16px padding
  - Right: Navigation items ('About', 'Team', 'Services', 'Contact') with 24px spacing
  - Include a "Join Today" button with distinct styling

  Hero Section:
  - Left Column (50%):
    • Main headline (max 2 lines)
    • Description text (max 300 characters) with 1.5 line height
    • Two CTAs horizontally aligned:
      - Primary: "Launch App" (filled button)
      - Secondary: "Learn More" (outlined button)
    • Add social proof metrics below CTAs

  - Right Column (50%):
    • Feature dynamic 3D illustration showing:
      - visual representation of the platform
    • Include subtle animation indicators
    • Maintain adequate padding (32px) from edges

  Visual Elements:
  - Implement subtle background patterns
  - Add floating UI elements to enhance depth

  Performance Considerations:
  - Optimize SVG paths for rendering

  This design should convey professionalism and innovation while maintaining optimal usability and performance across all devices.
`
export const heroWords = homePage.heroWords;

export const DEFAULT_POOL_ADDRESS = "******************************************";
export const QUOTER_CONTRACT_ADDRESS = '******************************************'
export const SWAP_ROUTER_ADDRESS = '******************************************'
export const WETH9_ADDRESS = "******************************************"
export const POOL_FEE = 3000;

export const emailRegex = /^(?!.*\.{2})([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/;
export const usernameRegex = /^[A-Za-z0-9_]{4,15}$/;

export const getCurrency = () => {
  switch (process.env.NEXT_PUBLIC_CURRENT_CHAIN) {
  case 'POLYGON_MAIN':
    return 'POL'
  case 'SEPOLIA':
    return 'ETH'
  default:
    return 'POL'
  }
}

export const getSwapSymbol = () => {
  switch (process.env.NEXT_PUBLIC_CURRENT_CHAIN) {
  case 'POLYGON_MAIN':
    return 'POL'
  case 'SEPOLIA':
    return 'ETH'
  default:
    return 'POL'
  }
}

export const getChainAddressLink = (address: string) => {
  switch (process.env.NEXT_PUBLIC_CURRENT_CHAIN) {
  case 'POLYGON_MAIN':
    return `https://polygonscan.com/address/${address}`
  case 'SEPOLIA':
    return `https://sepolia.etherscan.io/address/${address}`
  default:
    return ''
  }
}

export const getChainTransactionLink = (address: string) => {
  switch (process.env.NEXT_PUBLIC_CURRENT_CHAIN) {
  case 'POLYGON_MAIN':
    return `https://polygonscan.com/tx/${address}`
  case 'SEPOLIA':
    return `https://sepolia.etherscan.io/tx/${address}`
  default:
    return ''
  }
}

export const getBaseChainTransactionLink = (address: string) => {
  return `https://basescan.org/tx/${address}`
}

export const getChainForMoralis = () => {
  switch (process.env.NEXT_PUBLIC_CURRENT_CHAIN) {
  case 'POLYGON_MAIN':
    return `polygon`
  case 'SEPOLIA':
    return `sepolia`
  default:
    return ''
  }
}

export const SupabaseTables = {
  Subdomains: process.env.NEXT_PUBLIC_TABLE_SUBDOMAINS || '',
  NewIdeas: process.env.NEXT_PUBLIC_TABLE_NEW_IDEAS || '',
  Sandboxes: process.env.NEXT_PUBLIC_TABLE_SANDBOXES || '',
  IdeaFeed: process.env.NEXT_PUBLIC_TABLE_IDEA_FEED || '',
  Agents: process.env.NEXT_PUBLIC_TABLE_AGENTS || '',
  DreamCharacter: process.env.NEXT_PUBLIC_TABLE_CHARACTER || '',
}

export const tones = [
  "Professional",
  "Gen-Z",
  "Casual",
  "Academic",
  "Mentor",
  "Creative",
]

export const mixpanelToken = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN;
export const isProd = process.env.NEXT_PUBLIC_ROOT_DOMAIN === 'dreamstartr.com';

export const adminAddress = "0xB739c297C20B0FFf91839F015F3aff2e65F6B4F5"
export const adminAddress2 = "0x9080196182F77B89BB5B0EeE3Ddb48cFA716c4c3"
export const metamaskDeepLink = "https://metamask.app.link/dapp/%dappURL%"

export const POLYGON_WHITELIST_ORDERED = [
  "0xfec68cbf04f7c069628cf9e9c609abc95f9a476b",
  "0xa76164b3d0a2f35b140ec57f1552f7d6987e06db",
  "0x560d5b84d081b3a91e1cea3a9713473303124730",
  "0xd7b0c417365b63f098c1c4ad2ced35982d648876",
  "0x45a04faf46aca702f1d1185988e97a1e1bd42673",
  "0x20fc765a33b81e248c5ce2fae2adaffe243db0e0",
  "0x1d9ccbe51cc6b4deeeca686d409561cf9f801fbc",
  "0xc1404a86d2c3a00a4946ee3289c6b238257be700",
  "0xd376cfc6baba795f8ce6e18b623ca0b6a227329d",
  "0xb6ab4a4b329435094907190b541f4bd92f14ebe6",
  "0x15987d862fbc435ac8445e809ebb377fb418ef8e",
  "0xa4c5ac734d05dbc97d5934f4b9c21f1859021511",
  "0x222c6b7abb32e7ef25baf599fb139001dd584885",
  "0xff056c576379f5ce7301623565b0921344e4b656",
  "0xe581b4854682f793c83a87e834b7d2a688d785c6",
  "0x2b62ab4f6537cc3e7a90cdc2284a8103f9bd5822",
  "0x2fc359fc903040ac5d34ff9d50802e1fe0ced8fe",
].map(addr => addr.toLowerCase());


export const ImageStyles = [
  { 
    option: "photorealistic", 
    label: "Photorealistic",
  },
  { 
    option: "watercolor", 
    label: "Watercolor",
  },
  { 
    option: "pixel_art", 
    label: "Pixel Art",
  },
  { 
    option: "art_deco", 
    label: "Art Deco",
  },
  { 
    option: "cyberpunk", 
    label: "Cyberpunk",
  },
  { 
    option: "impressionist", 
    label: "Impressionist",
  },
  { 
    option: "vaporwave", 
    label: "Vaporwave",
  },
  { 
    option: "isometric", 
    label: "Isometric",
  },
  { 
    option: "ukiyo_e", 
    label: "Ukiyo-e",
  },
  { 
    option: "low_poly", 
    label: "Low Poly",
  },
];

export const tonesSelection = [
  {
    title: "👨 Professional",
    value: "Professional",
  },
  {
    title: "🧑‍💻 Gen-Z",
    value: "Gen-Z",
  },
  {
    title: "🤙 Casual",
    value: "Casual",
  },
  {
    title: "👨‍🏫 Academic",
    value: "Academic",
  },
  {
    title: "🧑‍🏫 Mentor",
    value: "Mentor",
  },
  {
    title: "👨‍🎨 Creative",
    value: "Creative",
  },
]