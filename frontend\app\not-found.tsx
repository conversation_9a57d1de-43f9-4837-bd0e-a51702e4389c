
import React from 'react'
import { routes } from '@/common/routes'
import lang from '@/common/lang';
import { Link } from '@/common/components/atoms';

const {
  notFound: notFoundCopy,
} = lang

export default function NotFound () {
  return (
    <div className="min-h-screen w-full px-4 md:px-16 flex items-center justify-center py-8 pt-16 md:pt-48">
      <div className=" flex flex-col items-center justify-center px-4 md:px-8 lg:px-24 py-8 rounded-lg ">
        <video src="/404.webm" width={373} height={163} autoPlay loop muted className='mb-4'/>
        <p className="text-xl md:text-2xl font-semibold tracking-wider text-white mt-4 text-center text-text-dark">{notFoundCopy.heading}</p>
        <p className="text-xs md:text-base text-gray-500 mt-4 text-center">{notFoundCopy.subHeading1}</p>
        <p className="text-xs md:text-base text-gray-500 pb-4  text-center mb-4">{notFoundCopy.subHeading2}</p>
        <Link variant='outline' size='lg' href={routes.homePath}title="Return Home">
          {notFoundCopy.buttonTitle}
        </Link>
      </div>
    </div>
  )
}
