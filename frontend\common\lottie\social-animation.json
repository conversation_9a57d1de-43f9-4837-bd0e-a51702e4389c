{"v": "5.12.1", "fr": 60, "ip": 0, "op": 90, "w": 430, "h": 430, "nm": "wired-flat-2803-engagement-alt", "ddd": 0, "assets": [{"id": "comp_1", "nm": "hover-pinch", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "bubble 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.192], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.523], "y": [1]}, "o": {"x": [0.617], "y": [0]}, "t": 25, "s": [-15]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [7]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 61, "s": [-3]}, {"t": 73, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.192, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [180, 255, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.523, "y": 1}, "o": {"x": 0.617, "y": 0}, "t": 19, "s": [180, 285, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 36, "s": [180, 235, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 55, "s": [180, 265, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 67, "s": [180, 255, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 102.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 3, "nm": "Star", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.368], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.34], "y": [1]}, "o": {"x": [0.535], "y": [0]}, "t": 25, "s": [-16]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 48, "s": [27]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 63, "s": [-6]}, {"t": 75, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [0, -17.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "mask", "parent": 2, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, -4.124, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.462, "y": 1}, "o": {"x": 0.412, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [1.1, -2.23], [0, 0], [0, 0], [-1.781, -1.736], [0, 0], [0, 0], [-2.201, 1.157], [0, 0], [0, 0], [0.42, 2.451], [0, 0], [0, 0], [2.461, 0.358], [0, 0]], "o": [[-1.1, -2.23], [0, 0], [0, 0], [-2.461, 0.358], [0, 0], [0, 0], [-0.42, 2.451], [0, 0], [0, 0], [2.201, 1.157], [0, 0], [0, 0], [1.781, -1.736], [0, 0], [0, 0]], "v": [[2.69, -40.425], [-2.69, -40.425], [-14.695, -16.101], [-41.537, -12.201], [-43.2, -7.084], [-23.776, 11.849], [-28.362, 38.584], [-24.009, 41.746], [0, 29.124], [24.009, 41.746], [28.362, 38.584], [23.776, 11.849], [43.2, -7.084], [41.537, -12.201], [14.695, -16.101]], "c": true}]}, {"i": {"x": 0.38, "y": 1}, "o": {"x": 0.49, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [0.82, -1.662], [0, 0], [0, 0], [-1.327, -1.293], [0, 0], [0, 0], [-1.64, 0.862], [0, 0], [0, 0], [0.313, 1.826], [0, 0], [0, 0], [1.834, 0.266], [0, 0]], "o": [[-0.82, -1.662], [0, 0], [0, 0], [-1.834, 0.266], [0, 0], [0, 0], [-0.313, 1.826], [0, 0], [0, 0], [1.64, 0.862], [0, 0], [0, 0], [1.327, -1.293], [0, 0], [0, 0]], "v": [[2.005, -30.093], [-2.005, -30.093], [-10.95, -11.967], [-30.953, -9.06], [-32.192, -5.247], [-17.163, 7.742], [-20.58, 27.664], [-17.336, 30.02], [0.555, 20.614], [16.88, 29.802], [20.124, 27.445], [17.153, 7.187], [31.52, -5.58], [30.281, -9.394], [10.282, -11.182]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [{"i": [[0, 0], [1.41, -2.858], [0, 0], [0, 0], [-2.282, -2.225], [0, 0], [0, 0], [-2.821, 1.483], [0, 0], [0, 0], [0.539, 3.141], [0, 0], [0, 0], [3.154, 0.458], [0, 0]], "o": [[-1.41, -2.858], [0, 0], [0, 0], [-3.154, 0.458], [0, 0], [0, 0], [-0.539, 3.141], [0, 0], [0, 0], [2.821, 1.483], [0, 0], [0, 0], [2.282, -2.225], [0, 0], [0, 0]], "v": [[3.69, -51.445], [-3.206, -51.445], [-18.592, -20.269], [-52.997, -15.27], [-55.128, -8.711], [-29.277, 13.629], [-35.154, 47.895], [-29.575, 51.948], [1.197, 35.77], [29.276, 51.573], [34.855, 47.519], [29.745, 12.674], [54.456, -9.284], [52.325, -15.843], [17.927, -18.919]], "c": true}]}, {"t": 53, "s": [{"i": [[0, 0], [1.1, -2.23], [0, 0], [0, 0], [-1.781, -1.736], [0, 0], [0, 0], [-2.201, 1.157], [0, 0], [0, 0], [0.42, 2.451], [0, 0], [0, 0], [2.461, 0.358], [0, 0]], "o": [[-1.1, -2.23], [0, 0], [0, 0], [-2.461, 0.358], [0, 0], [0, 0], [-0.42, 2.451], [0, 0], [0, 0], [2.201, 1.157], [0, 0], [0, 0], [1.781, -1.736], [0, 0], [0, 0]], "v": [[2.69, -40.425], [-2.69, -40.425], [-14.695, -16.101], [-41.537, -12.201], [-43.2, -7.084], [-23.776, 11.849], [-28.362, 38.584], [-24.009, 41.746], [0, 29.124], [24.009, 41.746], [28.362, 38.584], [23.776, 11.849], [43.2, -7.084], [41.537, -12.201], [14.695, -16.101]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.780392169952, 0.219607844949, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Star", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Star-shadow", "parent": 2, "tt": 1, "tp": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-22.055, -4.124, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.632, 3.702], [0, 0], [0, 0], [-3.987, 0.571], [0, 0], [0, 0], [-1.735, 0], [0, 0]], "o": [[-3.59, 1.753], [0, 0], [0, 0], [-2.867, -2.638], [0, 0], [0, 0], [0.911, -1.661], [0, 0], [0, 0]], "v": [[-17.322, 63.372], [-24.472, 58.617], [-16.941, 17.838], [-48.828, -11.026], [-46.117, -18.785], [-2.046, -24.732], [17.673, -61.834], [22.055, -64.325], [22.054, 44.148]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2803-engagement-alt').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Star-shadow", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Star 2", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, -4.124, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.462, "y": 1}, "o": {"x": 0.412, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [1.1, -2.23], [0, 0], [0, 0], [-1.781, -1.736], [0, 0], [0, 0], [-2.201, 1.157], [0, 0], [0, 0], [0.42, 2.451], [0, 0], [0, 0], [2.461, 0.358], [0, 0]], "o": [[-1.1, -2.23], [0, 0], [0, 0], [-2.461, 0.358], [0, 0], [0, 0], [-0.42, 2.451], [0, 0], [0, 0], [2.201, 1.157], [0, 0], [0, 0], [1.781, -1.736], [0, 0], [0, 0]], "v": [[2.69, -40.425], [-2.69, -40.425], [-14.695, -16.101], [-41.537, -12.201], [-43.2, -7.084], [-23.776, 11.849], [-28.362, 38.584], [-24.009, 41.746], [0, 29.124], [24.009, 41.746], [28.362, 38.584], [23.776, 11.849], [43.2, -7.084], [41.537, -12.201], [14.695, -16.101]], "c": true}]}, {"i": {"x": 0.38, "y": 1}, "o": {"x": 0.49, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [0.82, -1.662], [0, 0], [0, 0], [-1.327, -1.293], [0, 0], [0, 0], [-1.64, 0.862], [0, 0], [0, 0], [0.313, 1.826], [0, 0], [0, 0], [1.834, 0.266], [0, 0]], "o": [[-0.82, -1.662], [0, 0], [0, 0], [-1.834, 0.266], [0, 0], [0, 0], [-0.313, 1.826], [0, 0], [0, 0], [1.64, 0.862], [0, 0], [0, 0], [1.327, -1.293], [0, 0], [0, 0]], "v": [[2.005, -30.093], [-2.005, -30.093], [-10.95, -11.967], [-30.953, -9.06], [-32.192, -5.247], [-17.163, 7.742], [-20.58, 27.664], [-17.336, 30.02], [0.555, 20.614], [16.88, 29.802], [20.124, 27.445], [17.153, 7.187], [31.52, -5.58], [30.281, -9.394], [10.282, -11.182]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [{"i": [[0, 0], [1.41, -2.858], [0, 0], [0, 0], [-2.282, -2.225], [0, 0], [0, 0], [-2.821, 1.483], [0, 0], [0, 0], [0.539, 3.141], [0, 0], [0, 0], [3.154, 0.458], [0, 0]], "o": [[-1.41, -2.858], [0, 0], [0, 0], [-3.154, 0.458], [0, 0], [0, 0], [-0.539, 3.141], [0, 0], [0, 0], [2.821, 1.483], [0, 0], [0, 0], [2.282, -2.225], [0, 0], [0, 0]], "v": [[3.69, -51.445], [-3.206, -51.445], [-18.592, -20.269], [-52.997, -15.27], [-55.128, -8.711], [-29.277, 13.629], [-35.154, 47.895], [-29.575, 51.948], [1.197, 35.77], [29.276, 51.573], [34.855, 47.519], [29.745, 12.674], [54.456, -9.284], [52.325, -15.843], [17.927, -18.919]], "c": true}]}, {"t": 53, "s": [{"i": [[0, 0], [1.1, -2.23], [0, 0], [0, 0], [-1.781, -1.736], [0, 0], [0, 0], [-2.201, 1.157], [0, 0], [0, 0], [0.42, 2.451], [0, 0], [0, 0], [2.461, 0.358], [0, 0]], "o": [[-1.1, -2.23], [0, 0], [0, 0], [-2.461, 0.358], [0, 0], [0, 0], [-0.42, 2.451], [0, 0], [0, 0], [2.201, 1.157], [0, 0], [0, 0], [1.781, -1.736], [0, 0], [0, 0]], "v": [[2.69, -40.425], [-2.69, -40.425], [-14.695, -16.101], [-41.537, -12.201], [-43.2, -7.084], [-23.776, 11.849], [-28.362, 38.584], [-24.009, 41.746], [0, 29.124], [24.009, 41.746], [28.362, 38.584], [23.776, 11.849], [43.2, -7.084], [41.537, -12.201], [14.695, -16.101]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2803-engagement-alt').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Star", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "shadow", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-102.5, -17.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -11.046], [0, 0], [-11.046, 0], [0, 0], [0, 11.046], [0, 0], [-11.046, 0]], "o": [[-11.046, 0], [0, 0], [0, 11.046], [0, 0], [-11.046, 0], [0, 0], [0, -11.046], [0, 0]], "v": [[-2.5, -85], [-22.5, -65], [-22.5, 65], [-2.5, 85], [22.5, 85], [2.5, 65], [2.5, -65], [22.5, -85]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.922, 0.902, 0.937, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2803-engagement-alt').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "shadow", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "bubble 3", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, -0.675, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-11.046, 0], [0, 0], [0, -11.046], [0, 0], [11.046, 0], [0, 0], [0, 0], [1.182, 1.241], [0, 0], [0, 0], [0, 11.046]], "o": [[0, -11.046], [0, 0], [11.046, 0], [0, 0], [0, 11.046], [0, 0], [0, 0], [-1.182, 1.241], [0, 0], [0, 0], [-11.046, 0], [0, 0]], "v": [[-125, -81.825], [-105, -101.825], [105, -101.825], [125, -81.825], [125, 48.175], [105, 68.175], [33.333, 68.175], [2.172, 100.894], [-2.172, 100.894], [-33.333, 68.175], [-105, 68.175], [-125, 48.175]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.922, 0.902, 0.937, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2803-engagement-alt').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "bubble", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Vector", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1, -7.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 21, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, 35], [-65, 35]], "c": false}]}, {"t": 42, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, 35], [0, 35]], "c": false}], "h": 1}, {"i": {"x": 0.16, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-64.999, 34.6], [-65, 35]], "c": false}]}, {"t": 80, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, 35], [-65, 35]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 14, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[65, 0], [-65, 0]], "c": false}]}, {"t": 35, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[65, 0], [65, 0]], "c": false}], "h": 1}, {"i": {"x": 0.16, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-65.488, 1.381], [-65, 0]], "c": false}]}, {"t": 73, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[65, 0], [-65, 0]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 9, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[65, -35], [-65, -35]], "c": false}]}, {"t": 30, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[65, -35], [65, -35]], "c": false}], "h": 1}, {"i": {"x": 0.16, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-65.488, -33.619], [-65, -35]], "c": false}]}, {"t": 68, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[65, -35], [-65, -35]], "c": false}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.227, 0.2, 0.278, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2803-engagement-alt').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "quaternary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 3, "nm": "bubble", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.192], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.523], "y": [1]}, "o": {"x": [0.617], "y": [0]}, "t": 26.889, "s": [15]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 49.24, "s": [-7]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 74.223, "s": [3]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.192, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [250, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.523, "y": 1}, "o": {"x": 0.617, "y": 0}, "t": 19, "s": [250, 420, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 41.352, "s": [250, 380, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66.334, "s": [250, 409, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 82.111328125, "s": [250, 400, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 102.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "shadow 2", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-102.5, -17.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -11.046], [0, 0], [-11.046, 0], [0, 0], [0, 11.046], [0, 0], [-11.046, 0]], "o": [[-11.046, 0], [0, 0], [0, 11.046], [0, 0], [-11.046, 0], [0, 0], [0, -11.046], [0, 0]], "v": [[-2.5, -85], [-22.5, -65], [-22.5, 65], [-2.5, 85], [22.5, 85], [2.5, 65], [2.5, -65], [22.5, -85]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.294, 0.702, 0.992, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2803-engagement-alt').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "shadow", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "bubble 4", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, -0.675, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-11.046, 0], [0, 0], [0, -11.046], [0, 0], [11.046, 0], [0, 0], [0, 0], [1.182, 1.241], [0, 0], [0, 0], [0, 11.046]], "o": [[0, -11.046], [0, 0], [11.046, 0], [0, 0], [0, 11.046], [0, 0], [0, 0], [-1.182, 1.241], [0, 0], [0, 0], [-11.046, 0], [0, 0]], "v": [[-125, -81.825], [-105, -101.825], [105, -101.825], [125, -81.825], [125, 48.175], [105, 68.175], [33.333, 68.175], [2.172, 100.894], [-2.172, 100.894], [-33.333, 68.175], [-105, 68.175], [-125, 48.175]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.294, 0.702, 0.992, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-2803-engagement-alt').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "bubble", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 1, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.922, 0.902, 0.937], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.294, 0.702, 0.992], "ix": 1}}]}, {"ty": 5, "nm": "tertiary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [1, 0.78, 0.22], "ix": 1}}]}, {"ty": 5, "nm": "quaternary", "np": 3, "mn": "ADBE Color Control", "ix": 4, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.227, 0.2, 0.278], "ix": 1}}]}], "ip": 0, "op": 161, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-pinch", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 100, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-pinch", "dr": 90}], "props": {}}