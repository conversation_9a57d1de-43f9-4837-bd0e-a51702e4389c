import type { SVGProps } from "react";

export const CopyPasteIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={18}
    height={18}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <path
        fill="#FFDDA1"
        d="M13.394 2.776a.732.732 0 0 1 .732.732v12.448a.733.733 0 0 1-.732.732H1.678a.733.733 0 0 1-.732-.732V3.508a.732.732 0 0 1 .732-.732h11.716Z"
      />
      <path
        fill="#FFBC44"
        d="M10.83 4.607a.732.732 0 0 1-.732.732H4.973a.731.731 0 0 1-.732-.732v-1.83h6.59v1.83ZM13.393 16.688a.732.732 0 0 0 .732-.732V6.437H8.634a.732.732 0 0 0-.733.732v9.52h5.492Z"
      />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M7.536 16.688H1.678a.734.734 0 0 1-.732-.732V3.508a.732.732 0 0 1 .732-.732h2.929M10.464 2.776h2.93a.732.732 0 0 1 .732.732v2.563"
      />
      <path
        fill="#E4F1F5"
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M9 2.044a1.465 1.465 0 1 0-2.93 0H4.607V4.24h5.858V2.044H9Z"
      />
      <path
        fill="#fff"
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M17.055 16.688a.732.732 0 0 1-.733.733h-6.59A.732.732 0 0 1 9 16.688v-8.42a.732.732 0 0 1 .732-.733h5.189c.194 0 .38.078.518.215l1.401 1.402a.732.732 0 0 1 .215.517v7.02Z"
      />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M11.197 10.83h3.66M11.197 13.027h3.66"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h18v18H0z" />
      </clipPath>
    </defs>
  </svg>
)
