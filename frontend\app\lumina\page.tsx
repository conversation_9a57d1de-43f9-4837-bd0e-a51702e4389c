'use client'

import { Suspense } from 'react';
import { Loader } from '@/common/components/atoms';
import { Footer } from '@/common/components/organisms';
import dynamic from 'next/dynamic';

const DataDrivenInsights = dynamic(() => import('./components/DataDrivenInsights'), {
  ssr: false,
  loading: () => <Loader />,
});

const Teams = dynamic(() => import('./components/Teams'), {
  ssr: false,
  loading: () => <Loader />,
});

const Join = dynamic(() => import('./components/Join'), {
  ssr: false,
  loading: () => <Loader />,
});

const Tokenomics = dynamic(() => import('./components/Tokenomics'), {
  ssr: false,
  loading: () => <Loader />,
});

const AboutLuminaCasa = dynamic(() => import('./components/AboutLuminaCasa'), {
  ssr: false,
  loading: () => <Loader />,
});

const ScientificApproach = dynamic(() => import('./components/ScientificApproach'), {
  ssr: false,
  loading: () => <Loader />,
});

const Roadmap = dynamic(() => import('./components/Roadmap'), {
  ssr: false,
  loading: () => <Loader />,
});

const Hero = dynamic(() => import('./components/Hero'), {
  ssr: false,
  loading: () => <Loader />,
});

const Toaster = dynamic(() => import('@/common/components/molecules').then(m => m.Toaster), {
  ssr: false,
});

const CasaPage = () => {
  return (
    <main className="overflow-hidden">
      <div className="absolute -z-10 inset-0 w-full h-full bg-gradient-to-br from-eerie-black via-space-cadet/30 to-eerie-black opacity-80"></div>
      <div className="absolute -z-10 top-0 right-0 w-2/3 h-2/3 bg-gradient-to-bl from-han-purple/10 via-transparent to-transparent opacity-50"></div>
      <div className="absolute -z-10 bottom-0 left-0 w-2/3 h-2/3 bg-gradient-to-tr from-tulip/10 via-transparent to-transparent opacity-50"></div>
      <Suspense fallback={<Loader />}>
        <Hero />
      </Suspense>
      <Suspense fallback={<Loader />}>
        <AboutLuminaCasa />
      </Suspense>
      <Suspense fallback={<Loader />}>
        <ScientificApproach />
      </Suspense>
      <Suspense fallback={<Loader />}>
        <DataDrivenInsights />
      </Suspense>
      <Suspense fallback={<Loader />}>
        <Tokenomics />
      </Suspense>
      <Suspense fallback={<Loader />}>
        <Roadmap />
      </Suspense>
      <Suspense fallback={<Loader />}>
        <Teams />
      </Suspense>
      <Suspense fallback={<Loader />}>
        <Join />
      </Suspense>
      <Toaster />
      <Footer />
    </main>
  );
};

export default CasaPage
