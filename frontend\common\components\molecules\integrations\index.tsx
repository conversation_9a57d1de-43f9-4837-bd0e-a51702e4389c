"use client";

import { cn } from "@/common/utils/helpers";
import {
  motion, useAnimation, useInView,
} from "framer-motion";
import {
  useEffect, useId, useRef, useState,
} from "react";
import Marquee from "react-fast-marquee";
import {
  FacebookIcon, InstagramIcon, LinkedInColorIcon, PinterestIcon, XIcon, YoutubeIcon,
} from "../../icons";

const tiles = [
  {
    icon: <FacebookIcon />,
    bg: (
      <div className="pointer-events-none absolute left-1/2 top-1/2 size-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-gradient-to-r from-[#66E1FF] to-[#C2F3FF] opacity-70 blur-[20px]"></div>
    ),
  },
  {
    icon: <InstagramIcon />,
    bg: (
      <div className="pointer-events-none absolute left-1/2 top-1/2 size-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-gradient-to-r from-[#FFBFC5] to-[#191919] opacity-70 blur-[20px]"></div>
    ),
  },
  {
    icon: <PinterestIcon />,
    bg: (
      <div className="pointer-events-none absolute left-1/2 top-1/2 size-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-gradient-to-r from-[#FF808C] to-[#191919] opacity-70 blur-[20px]"></div>
    ),
  },
  {
    icon: <YoutubeIcon />,
    bg: (
      <div className="pointer-events-none absolute left-1/2 top-1/2 size-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-gradient-to-r from-[#FF808C] to-[#191919] opacity-70 blur-[20px]"></div>
    ),
  },
  {
    icon: <LinkedInColorIcon />,
    bg: (
      <div className="pointer-events-none absolute left-1/2 top-1/2 size-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-gradient-to-r from-[#66E1FF] to-[#C2F3FF] opacity-70 blur-[20px]"></div>
    ),
  },
  {
    icon: <XIcon />,
    bg: (
      <div className="pointer-events-none absolute left-1/2 top-1/2 size-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-gradient-to-r from-[#B2B2B2] to-[#191919] opacity-70 blur-[20px]"></div>
    ),
  },
];

function shuffleArray (array: any[]) {
  let currentIndex = array.length;
  let randomIndex;
  while (currentIndex !== 0) {
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;
    [array[currentIndex], array[randomIndex]] = [
      array[randomIndex],
      array[currentIndex],
    ];
  }
  return array;
}

function Card (card: { icon: JSX.Element; bg: JSX.Element }) {
  const id = useId();
  const controls = useAnimation();
  const ref = useRef(null);
  const inView = useInView(ref, { once: true });

  useEffect(() => {
    if (inView) {
      controls.start({
        opacity: 1,
        transition: {
          delay: Math.random() * 2,
          ease: "easeOut",
          duration: 1,
        },
      });
    }
  }, [controls, inView]);

  return (
    <motion.div
      key={id}
      ref={ref}
      initial={{ opacity: 0 }}
      animate={controls}
      className={cn(
        "relative cursor-pointer rounded-xl border p-2 mx-2",
        "bg-white [box-shadow:0_0_0_1px_rgba(0,0,0,.03),0_2px_4px_rgba(0,0,0,.05),0_12px_24px_rgba(0,0,0,.05)]",
        "transform-gpu dark:bg-transparent dark:[border:1px_solid_rgba(255,255,255,.1)] dark:[box-shadow:0_-20px_80px_-20px_#ffffff1f_inset]",
      )}
    >
      {card.icon}
      {card.bg}
    </motion.div>
  );
}

export function Integrations () {
  const [randomTiles1, setRandomTiles1] = useState<typeof tiles>([]);
  const [randomTiles2, setRandomTiles2] = useState<typeof tiles>([]);
  const [randomTiles3, setRandomTiles3] = useState<typeof tiles>([]);
  const [randomTiles4, setRandomTiles4] = useState<typeof tiles>([]);
  useEffect(() => {
    if (typeof window !== "undefined") {
      setRandomTiles1(shuffleArray([...tiles]));
      setRandomTiles2(shuffleArray([...tiles]));
      setRandomTiles3(shuffleArray([...tiles]));
      setRandomTiles4(shuffleArray([...tiles]));
    }
  }, []);

  const tileCount = 6;

  return (
    <div className="w-full max-w-full flex flex-col gap-2">
      <div className="w-full">
        <Marquee
          speed={80}
          delay={.2}
        >
          {randomTiles1.slice(0, tileCount).map((tile, idx) => (
            <Card key={idx} {...tile} />
          ))}
        </Marquee>
      </div>
      <div className="w-full">
        <Marquee
          speed={40}
        >
          {randomTiles2.slice(0, tileCount).map((tile, idx) => (
            <Card key={idx} {...tile} />
          ))}
        </Marquee>
      </div>
      <div className="w-full">
        <Marquee
          speed={50}
          delay={.2}
        >
          {randomTiles3.slice(0, tileCount).map((tile, idx) => (
            <Card key={idx} {...tile} />
          ))}
        </Marquee>
      </div>
      <div className="w-full sm:block hidden">
        <Marquee
          speed={30}
        >
          {randomTiles4.slice(0, tileCount).map((tile, idx) => (
            <Card key={idx} {...tile} />
          ))}
        </Marquee>
      </div>
    </div>
  );
}
