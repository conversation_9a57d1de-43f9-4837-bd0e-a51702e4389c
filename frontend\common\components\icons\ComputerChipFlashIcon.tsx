import type { SVGProps } from "react";

export const ComputerChipFlashIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <path
        fill="#FFEF5E"
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m18.768.833-2.92 3.189h3.318l-5.58 4.782 1.376-3.188H12.79L14.783.833h3.985Z"
      />
      <path
        fill="#E3E3E3"
        d="M11.993 7.21H4.022a.797.797 0 0 0-.797.797v7.971c0 .44.356.797.797.797h7.97c.44 0 .798-.356.798-.797v-7.97a.797.797 0 0 0-.797-.798Z"
      />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M12.79 10.399v5.58a.797.797 0 0 1-.797.796H4.022a.798.798 0 0 1-.797-.797v-7.97a.797.797 0 0 1 .797-.798h7.174M8.007 16.775v2.392M11.196 16.775v2.392M8.007 4.819V7.21M4.819 16.775v2.392M4.819 4.819V7.21M12.79 15.181h2.391M3.225 11.993H.833M15.181 11.993H12.79M3.225 8.804H.833M3.225 15.181H.833"
      />
      <path
        fill="gray"
        d="M9.601 8.804H6.413c-.88 0-1.594.714-1.594 1.595v3.188c0 .88.713 1.594 1.594 1.594H9.6c.88 0 1.594-.714 1.594-1.594v-3.188c0-.88-.713-1.595-1.594-1.595Z"
      />
      <path
        fill="#B2B2B2"
        d="M9.601 8.804H6.413A1.6 1.6 0 0 0 4.819 10.4h6.376a1.6 1.6 0 0 0-1.594-1.595Z"
      />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M9.601 8.804H6.413c-.88 0-1.594.714-1.594 1.595v3.188c0 .88.713 1.594 1.594 1.594H9.6c.88 0 1.594-.714 1.594-1.594v-3.188c0-.88-.713-1.595-1.594-1.595Z"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h20v20H0z" />
      </clipPath>
    </defs>
  </svg>
)
