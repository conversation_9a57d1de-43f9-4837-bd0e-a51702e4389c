'use client'
import {
  forwardRef, useMemo,
  useState,
} from 'react';
import {
  Eye, EyeOff,
} from 'lucide-react';
import { InputProps } from './types';

const baseInputClasses = `flex w-full rounded-xl border border-white/5 hover:border-violets-are-blue focus:border-violets-are-blue text-sm py-3 text-white
placeholder:text-gray-600 outline-none leading-tight duration-200 transition-colors ease-in-out
px-4 disabled:cursor-not-allowed bg-white/5 backdrop-blur-sm disabled:bg-white/10 disabled:border-white/10 disabled:text-neutral-200`;
const getErrorClasses = (error: boolean | undefined) => error ? '!border-tulip pr-10' : '';
export const Input = forwardRef<HTMLInputElement | null, InputProps>((props, ref) => {
  const {
    type = 'text',
    name = 'input-name',
    id = 'input-id',
    placeholder = 'input placeholder',
    value = '',
    disabled = false,
    error = false,
    onChange = () => null,
    ...rest
  } = props;

  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  const inputType = type === 'password' && showPassword ? 'text' : type;
  const computedClasses = useMemo(() => {
    return getErrorClasses(error);
  }, [error]);

  return (
    <div className="relative w-full">
      <input
        type={inputType}
        ref={ref}
        name={name}
        id={id}
        value={value}
        className={`${baseInputClasses} ${computedClasses}`}
        placeholder={placeholder}
        disabled={disabled}
        onChange={onChange}
        {...rest}
      />

      {type === 'password' && (
        <button
          type="button"
          onClick={togglePasswordVisibility}
          className={`absolute inset-y-0 right-0 flex items-center ${error ? "pr-10" : "pr-3"} text-violets-are-blue transition-colors duration-200`}
          tabIndex={-1}
          aria-label={showPassword ? "Hide password" : "Show password"}
        >
          {showPassword ? <EyeOff width={18} height={18} /> : <Eye width={18} height={18} />}
        </button>
      )}
    </div>
  )
});
Input.displayName = "Input";
