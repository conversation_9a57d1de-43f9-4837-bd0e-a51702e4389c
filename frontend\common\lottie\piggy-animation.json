{"v": "5.12.1", "fr": 60, "ip": 0, "op": 60, "w": 430, "h": 430, "nm": "wired-flat-453-savings-pig", "ddd": 0, "assets": [{"id": "comp_1", "nm": "Coin-in", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [218, 206, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [218, 67, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 60, "s": [218, 95, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [3, -120, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[7.726, 0.125], [0, -15.947], [-6.319, 0], [0, 15.947]], "o": [[-8.024, -0.13], [0, 15.947], [6.319, 0], [0, -15.947]], "v": [[6.774, -47.375], [3.752, -16.75], [6.774, 10.375], [10.587, -17.25]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [{"i": [[25.681, 0], [0, -31.48], [-21.506, 0], [0, 31.48]], "o": [[-25.681, 0], [0, 31.48], [23.5, 0], [0, -31.48]], "v": [[7, -75.5], [-39.5, -18.5], [5, 33.5], [53.5, -18.5]], "c": true}]}, {"t": 60, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -69.5], [-44, -18.5], [7, 32.5], [58, -18.5]], "c": true}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 2", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-4, -101.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 21, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "coin 3", "tt": 1, "tp": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [229.125, 206, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [229.125, 67, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 60, "s": [229.125, 95, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [3, -120, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[7.726, 0.125], [0, -15.947], [-6.319, 0], [0, 15.947]], "o": [[-8.024, -0.13], [0, 15.947], [6.319, 0], [0, -15.947]], "v": [[6.774, -47.375], [3.752, -16.75], [6.774, 10.375], [10.587, -17.25]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [{"i": [[25.681, 0], [0, -31.48], [-21.506, 0], [0, 31.48]], "o": [[-25.681, 0], [0, 31.48], [23.5, 0], [0, -31.48]], "v": [[7, -75.5], [-39.5, -18.5], [5, 33.5], [53.5, -18.5]], "c": true}]}, {"t": 60, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -69.5], [-44, -18.5], [7, 32.5], [58, -18.5]], "c": true}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [-4, -101.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 21, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "coin 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [218, 206, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [218, 67, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 60, "s": [218, 95, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [3, -120, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[7.726, 0.125], [0, -15.947], [-6.319, 0], [0, 15.947]], "o": [[-8.024, -0.13], [0, 15.947], [6.319, 0], [0, -15.947]], "v": [[6.774, -47.375], [3.752, -16.75], [6.774, 10.375], [10.587, -17.25]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [{"i": [[25.681, 0], [0, -31.48], [-21.506, 0], [0, 31.48]], "o": [[-25.681, 0], [0, 31.48], [23.5, 0], [0, -31.48]], "v": [[7, -75.5], [-39.5, -18.5], [5, 33.5], [53.5, -18.5]], "c": true}]}, {"t": 60, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -69.5], [-44, -18.5], [7, 32.5], [58, -18.5]], "c": true}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [-4, -101.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 21, "op": 1800, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_2", "nm": "hover-pinch", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON><PERSON> 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [210.982, 264.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1.982, 43, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[7.525, 0], [0, -7.525], [-7.525, 0], [0, 7.525]], "o": [[-7.525, 0], [0, 7.525], [7.525, 0], [0, -7.525]], "v": [[-2, -16.125], [-15.625, -2.5], [-2, 11.125], [11.625, -2.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 22.5, "s": [{"i": [[11.312, 0], [0, -2.438], [-11.312, 0], [0, 2.438]], "o": [[-11.312, 0], [0, 2.438], [11.312, 0], [0, -2.438]], "v": [[6, 26.625], [-5, 28.25], [6, 29.875], [17, 28.25]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27.5, "s": [{"i": [[7.525, 0], [0, -7.525], [-7.525, 0], [0, 7.525]], "o": [[-7.525, 0], [0, 7.525], [7.525, 0], [0, -7.525]], "v": [[-7.625, -20.375], [-21.25, -6.75], [-7.625, 6.875], [6, -6.75]], "c": true}]}, {"t": 32.5, "s": [{"i": [[7.525, 0], [0, -7.525], [-7.525, 0], [0, 7.525]], "o": [[-7.525, 0], [0, 7.525], [7.525, 0], [0, -7.525]], "v": [[-1.875, -15.875], [-15.5, -2.25], [-1.875, 11.375], [11.75, -2.25]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.227, 0.2, 0.278, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [84.75, -29.625], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0, 0], [-27.5, -0.006], [0, 0]], "o": [[0, 0], [27.5, 0.006], [0, 0]], "v": [[61.327, 14.494], [95.913, 10.994], [132.946, 14.494]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 22.5, "s": [{"i": [[0, 0], [-29.542, -0.005], [0, 0]], "o": [[0, 0], [29.542, 0.005], [0, 0]], "v": [[60.768, 50.355], [97.845, 53.915], [137.704, 50.355]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27.5, "s": [{"i": [[0, 0], [-25.682, -0.006], [0, 0]], "o": [[0, 0], [25.682, 0.006], [0, 0]], "v": [[62.715, 9.071], [95.015, 5.462], [129.599, 9.071]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 32.5, "s": [{"i": [[0, 0], [-27.5, -0.006], [0, 0]], "o": [[0, 0], [27.5, 0.006], [0, 0]], "v": [[61.327, 14.494], [95.913, 10.994], [132.946, 14.494]], "c": false}]}, {"t": 37.5, "s": [{"i": [[0, 0], [-27.5, -0.006], [0, 0]], "o": [[0, 0], [27.5, 0.006], [0, 0]], "v": [[61.327, 14.494], [95.913, 10.994], [132.946, 14.494]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.227, 0.2, 0.278, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Mask 2", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 216, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[15.776, 0], [13.408, -3.038], [0, -15.769], [-53.088, 0], [0, 22.713], [31.067, 6.815]], "o": [[-16.23, 0], [-30.452, 6.9], [0, 22.713], [53.088, 0], [0, -15.963], [-13.135, -2.881]], "v": [[3.625, -33.324], [-41.303, -28.565], [-92.5, 7.801], [3.625, 48.926], [99.75, 7.801], [47.424, -28.817]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[15.776, 0], [13.408, -3.038], [0, -15.769], [-53.088, 0], [0, 22.713], [31.067, 6.815]], "o": [[-16.23, 0], [-30.452, 6.9], [0, 22.713], [53.088, 0], [0, -15.963], [-13.135, -2.881]], "v": [[3.593, -15.899], [-41.257, -20.139], [-92.453, 16.226], [3.672, 87.258], [99.797, 16.226], [47.47, -20.391]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[11.672, 0], [8.358, 1.626], [0, -17.566], [-53.088, 0], [0, 22.713], [36.724, 5.784]], "o": [[-12.029, 0], [-36.227, 5.893], [0, 22.713], [53.088, 0], [0, -17.719], [-9.504, 1.777]], "v": [[3.593, 0.953], [-31.608, -0.876], [-92.578, 38.578], [3.547, 139.515], [99.672, 38.578], [40.754, -1.027]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 22, "s": [{"i": [[15.776, 0], [8.178, 3.241], [0, -15.769], [-53.088, 0], [0, 22.713], [28.198, -14.712]], "o": [[-16.23, 0], [-29.027, -11.503], [0, 22.713], [53.088, 0], [0, -15.963], [-10.049, 5.243]], "v": [[4.5, 7.75], [-41.428, -0.991], [-92.625, 35.375], [3.5, 150], [99.625, 35.375], [47.299, -1.243]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27.5, "s": [{"i": [[15.776, 0], [13.408, -3.038], [0, -15.769], [-53.088, 0], [0, 22.713], [31.067, 6.815]], "o": [[-16.23, 0], [-30.452, 6.9], [0, 22.713], [53.088, 0], [0, -15.963], [-13.135, -2.881]], "v": [[3.5, -46.75], [-41.428, -41.991], [-92.625, -5.625], [3.5, 168], [99.625, -5.625], [47.299, -42.243]], "c": true}]}, {"t": 30, "s": [{"i": [[15.776, 0], [13.408, -3.038], [0, -15.769], [-53.088, 0], [0, 22.713], [31.067, 6.815]], "o": [[-16.23, 0], [-30.452, 6.9], [0, 22.713], [53.088, 0], [0, -15.963], [-13.135, -2.881]], "v": [[3.5, -7.25], [-41.428, -2.491], [-92.625, 33.875], [3.5, 207.5], [99.625, 33.875], [47.299, -2.743]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-0.5, -27.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 202.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "Coin-hover", "tt": 2, "tp": 2, "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [210.982, 264.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1.982, 43, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[-0.773, 0.042], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-16.419, 18.396], [0.632, 1.19], [3.088, 0.107], [13.842, -28.617], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0.643, 6.652], [-28.331, 2.527]], "o": [[0.133, 8.152], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [1.942, -2.176], [-0.895, -1.685], [-23.639, -0.816], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [29.864, 2.584], [0.75, -0.067]], "v": [[133.867, 212.348], [133.867, 217.483], [133.867, 225.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.709], [191.361, -43.138], [195.207, -48.253], [188.69, -49.958], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.357, 225.703], [59.357, 212.348], [131.581, 212.473]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-5.148, 6.075], [0, 0], [0, 0], [-7.928, 7.928], [-5.004, 18.601], [0, 0], [0, 8.925], [0, 0], [11.413, 0], [0, 0], [25.612, 12.163], [-11.529, 6.882], [0.019, 1.807], [2.831, 1.185], [18.303, -14.438], [0, 0], [22.309, -17.444], [0, -26.638], [-35.087, -23.669], [0, 0], [-5.968, -5.271], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [6.057, 5.133], [0, 0], [0, 0], [8.852, -1.426], [15.443, -15.443], [0, 0], [11.413, 0], [0, 0], [0, -8.924], [0, 0], [-8.923, -22.841], [0, 0], [2.199, -1.313], [-0.02, -1.881], [-22.643, -9.482], [0, 0], [-34.067, 0], [-22.333, 17.463], [0, 35.235], [0, 0], [-5.256, 5.951], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[139.246, 221.596], [150.03, 230.735], [159.271, 234.18], [179.563, 232.488], [193.515, 218.89], [164.648, 194.426], [197.82, 180.129], [240.87, 141.282], [257.818, 141.063], [278.5, 124.891], [278.5, 108.97], [257.818, 92.798], [238.134, 93.016], [184.1, 38.775], [204.727, 22.883], [208.106, 18.755], [203.437, 15.022], [132.408, 24.743], [81.924, 24.839], [-5.289, 53.078], [-41.427, 121.293], [17.837, 191.919], [0.626, 211.406], [1.902, 231.729], [6.697, 237.032], [36.355, 227.073], [50.445, 223.313], [139.077, 221.599]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27.666, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.933, -0.684], [0, 0], [0, 0], [-7.404, 8.176], [-4.35, 22.835], [0, 0], [0, 10.956], [0, 0], [9.922, 0], [0, 0], [22.265, 14.932], [-6.392, 20.693], [0.553, 1.029], [1.89, -0.91], [19.115, -21.447], [0, 0], [19.394, -21.416], [0, -32.703], [-35.968, -15.366], [0, 0], [-7.891, 1.063], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.682, 7.91], [0, 0], [0, 0], [10.402, -4.446], [14.422, -15.926], [0, 0], [9.922, 0], [0, 0], [0, -10.956], [0, 0], [-7.757, -28.041], [0, 0], [1.219, -3.947], [-0.749, -1.394], [-15.116, 7.275], [0, 0], [-29.616, 0], [-19.415, 21.439], [0, 43.257], [0, 0], [1.06, 7.868], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[131.292, 212.622], [131.078, 207.205], [130.121, 221.793], [143.242, 237.366], [165.898, 239.32], [169.15, 201.621], [190.183, 179.881], [219.285, 120.698], [224.021, 120.698], [242, 100.844], [242, 81.298], [224.021, 61.445], [216.906, 61.445], [169.933, -5.145], [178.135, -53.946], [180.499, -61.919], [176.758, -62.168], [124.995, -22.372], [81.107, -22.253], [5.29, 12.414], [-26.126, 96.159], [24.466, 198.91], [27.938, 224.676], [44.141, 237.01], [66.677, 233.973], [64.665, 215.911], [63.382, 209.176], [131.163, 212.616]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 32.334, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-14.037, 16.275], [-0.145, 1.94], [3.025, 0.276], [13.911, -28.759], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [2.677, -3.104], [0.154, -2.061], [-24.2, -2.207], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[134.184, 213.848], [133.684, 212.483], [133.684, 223.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.708], [190.883, -32.724], [196.207, -40.503], [191.458, -42.702], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.428, 211.703], [59.428, 211.348], [134.041, 213.843]], "c": false}]}, {"t": 37, "s": [{"i": [[-30.742, 2.527], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-16.419, 18.396], [0.632, 1.19], [3.088, 0.107], [13.842, -28.617], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0.643, 6.652], [0, 0]], "o": [[0.133, 8.152], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [1.942, -2.176], [-0.895, -1.685], [-23.639, -0.816], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [30.393, 3.152], [0, 0]], "v": [[133.867, 212.348], [133.867, 217.483], [133.867, 225.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.709], [191.361, -43.138], [195.207, -48.253], [188.69, -49.958], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.357, 225.703], [59.357, 212.348], [132, 212.125]], "c": false}]}], "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Shadow", "tt": 1, "tp": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [228.232, 264.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1.982, 43, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[-0.667, 0.036], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-16.419, 18.396], [0.632, 1.19], [3.088, 0.107], [13.842, -28.617], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0.643, 6.652], [-28.991, 1.586]], "o": [[0.133, 8.152], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [1.942, -2.176], [-0.895, -1.685], [-23.639, -0.816], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [29.97, 2.594], [0.651, -0.036]], "v": [[133.867, 212.348], [133.867, 217.483], [133.867, 225.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.709], [191.361, -43.138], [195.207, -48.253], [188.69, -49.958], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.357, 225.703], [59.357, 213.723], [131.889, 212.456]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-5.148, 6.075], [0, 0], [0, 0], [-7.928, 7.928], [-5.004, 18.601], [0, 0], [0, 8.925], [0, 0], [11.413, 0], [0, 0], [25.612, 12.163], [-11.529, 6.882], [0.019, 1.807], [2.831, 1.185], [18.303, -14.438], [0, 0], [22.309, -17.444], [0, -26.638], [-35.087, -23.669], [0, 0], [-5.968, -5.271], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [6.057, 5.133], [0, 0], [0, 0], [8.852, -1.426], [15.443, -15.443], [0, 0], [11.413, 0], [0, 0], [0, -8.924], [0, 0], [-8.923, -22.841], [0, 0], [2.199, -1.313], [-0.02, -1.881], [-22.643, -9.482], [0, 0], [-34.067, 0], [-22.333, 17.463], [0, 35.235], [0, 0], [-5.256, 5.951], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[139.246, 221.596], [150.03, 230.735], [159.271, 234.18], [179.563, 232.488], [193.515, 218.89], [164.648, 194.426], [197.82, 180.129], [240.87, 141.282], [257.818, 141.063], [278.5, 124.891], [278.5, 108.97], [257.818, 92.798], [238.134, 93.016], [184.1, 38.775], [204.727, 22.883], [208.106, 18.755], [203.437, 15.022], [132.408, 24.743], [81.924, 24.839], [-5.289, 53.078], [-41.427, 121.293], [17.837, 191.919], [0.626, 211.406], [1.902, 231.729], [6.697, 237.032], [36.355, 227.073], [50.445, 223.313], [139.12, 221.599]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27.666, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.933, -0.684], [0, 0], [0, 0], [-7.404, 8.176], [-4.35, 22.835], [0, 0], [0, 10.956], [0, 0], [9.922, 0], [0, 0], [22.265, 14.932], [-6.392, 20.693], [0.553, 1.029], [1.89, -0.91], [19.115, -21.447], [0, 0], [19.394, -21.416], [0, -32.703], [-35.968, -15.366], [0, 0], [-7.891, 1.063], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.682, 7.91], [0, 0], [0, 0], [10.402, -4.446], [14.422, -15.926], [0, 0], [9.922, 0], [0, 0], [0, -10.956], [0, 0], [-7.757, -28.041], [0, 0], [1.219, -3.947], [-0.749, -1.394], [-15.116, 7.275], [0, 0], [-29.616, 0], [-19.415, 21.439], [0, 43.257], [0, 0], [1.06, 7.868], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[131.292, 212.622], [131.078, 207.205], [130.121, 221.793], [143.242, 237.366], [165.898, 239.32], [169.15, 201.621], [190.183, 179.881], [219.285, 120.698], [224.021, 120.698], [242, 100.844], [242, 81.298], [224.021, 61.445], [216.906, 61.445], [169.933, -5.145], [178.135, -53.946], [180.499, -61.919], [176.758, -62.168], [124.995, -22.372], [81.107, -22.253], [5.29, 12.414], [-26.126, 96.159], [24.466, 198.91], [27.938, 224.676], [44.141, 237.01], [66.677, 233.973], [64.665, 215.911], [63.382, 209.176], [131.195, 212.617]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 32.334, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-14.037, 16.275], [-0.145, 1.94], [3.025, 0.276], [13.911, -28.759], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [2.677, -3.104], [0.154, -2.061], [-24.2, -2.207], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[134.184, 213.848], [133.684, 212.483], [133.684, 223.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.708], [190.883, -32.724], [196.207, -40.503], [191.458, -42.702], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.428, 211.703], [59.428, 211.348], [134.077, 213.845]], "c": false}]}, {"t": 37, "s": [{"i": [[-30.367, 1.652], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-16.419, 18.396], [0.632, 1.19], [3.088, 0.107], [13.842, -28.617], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0.643, 6.652], [0, 0]], "o": [[0.133, 8.152], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [1.942, -2.176], [-0.895, -1.685], [-23.639, -0.816], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [30.643, 2.652], [0, 0]], "v": [[133.867, 212.348], [133.867, 217.483], [133.867, 225.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.709], [191.361, -43.138], [195.207, -48.253], [188.69, -49.958], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.357, 225.703], [59.338, 213.723], [132.25, 211.75]], "c": false}]}], "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "quaternary"}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Shape fill", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [210.982, 264.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1.982, 43, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[-30.367, 1.652], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-16.419, 18.396], [0.632, 1.19], [3.088, 0.107], [13.842, -28.617], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0.643, 6.652], [0, 0]], "o": [[0.133, 8.152], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [1.942, -2.176], [-0.895, -1.685], [-23.639, -0.816], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [30.643, 2.652], [0, 0]], "v": [[133.867, 212.348], [133.867, 217.483], [133.867, 225.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.709], [191.361, -43.138], [195.207, -48.253], [188.69, -49.958], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.357, 225.703], [59.357, 212.348], [131.875, 212.125]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-5.148, 6.075], [0, 0], [0, 0], [-7.928, 7.928], [-5.004, 18.601], [0, 0], [0, 8.925], [0, 0], [11.413, 0], [0, 0], [25.612, 12.163], [-11.529, 6.882], [0.019, 1.807], [2.831, 1.185], [18.303, -14.438], [0, 0], [22.309, -17.444], [0, -26.638], [-35.087, -23.669], [0, 0], [-5.968, -5.271], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [6.057, 5.133], [0, 0], [0, 0], [8.852, -1.426], [15.443, -15.443], [0, 0], [11.413, 0], [0, 0], [0, -8.924], [0, 0], [-8.923, -22.841], [0, 0], [2.199, -1.313], [-0.02, -1.881], [-22.643, -9.482], [0, 0], [-34.067, 0], [-22.333, 17.463], [0, 35.235], [0, 0], [-5.256, 5.951], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[139.246, 221.596], [150.03, 230.735], [159.271, 234.18], [179.563, 232.488], [193.515, 218.89], [164.648, 194.426], [197.82, 180.129], [240.87, 141.282], [257.818, 141.063], [278.5, 124.891], [278.5, 108.97], [257.818, 92.798], [238.134, 93.016], [184.1, 38.775], [204.727, 22.883], [208.106, 18.755], [203.437, 15.022], [132.408, 24.743], [81.924, 24.839], [-5.289, 53.078], [-41.427, 121.293], [17.837, 191.919], [0.626, 211.406], [1.902, 231.729], [6.697, 237.032], [36.355, 227.073], [50.445, 223.313], [139.054, 221.6]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27.666, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.933, -0.684], [0, 0], [0, 0], [-7.404, 8.176], [-4.35, 22.835], [0, 0], [0, 10.956], [0, 0], [9.922, 0], [0, 0], [22.265, 14.932], [-6.392, 20.693], [0.553, 1.029], [1.89, -0.91], [19.115, -21.447], [0, 0], [19.394, -21.416], [0, -32.703], [-35.968, -15.366], [0, 0], [-7.891, 1.063], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.682, 7.91], [0, 0], [0, 0], [10.402, -4.446], [14.422, -15.926], [0, 0], [9.922, 0], [0, 0], [0, -10.956], [0, 0], [-7.757, -28.041], [0, 0], [1.219, -3.947], [-0.749, -1.394], [-15.116, 7.275], [0, 0], [-29.616, 0], [-19.415, 21.439], [0, 43.257], [0, 0], [1.06, 7.868], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[131.292, 212.622], [131.078, 207.205], [130.121, 221.793], [143.242, 237.366], [165.898, 239.32], [169.15, 201.621], [190.183, 179.881], [219.285, 120.698], [224.021, 120.698], [242, 100.844], [242, 81.298], [224.021, 61.445], [216.906, 61.445], [169.933, -5.145], [178.135, -53.946], [180.499, -61.919], [176.758, -62.168], [124.995, -22.372], [81.107, -22.253], [5.29, 12.414], [-26.126, 96.159], [24.466, 198.91], [27.938, 224.676], [44.141, 237.01], [66.677, 233.973], [64.665, 215.911], [63.382, 209.176], [131.145, 212.615]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 32.334, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-14.037, 16.275], [-0.145, 1.94], [3.025, 0.276], [13.911, -28.759], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [2.677, -3.104], [0.154, -2.061], [-24.2, -2.207], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[134.184, 213.848], [133.684, 212.483], [133.684, 223.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.708], [190.883, -32.724], [196.207, -40.503], [191.458, -42.702], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.428, 211.703], [59.428, 211.348], [134.022, 213.843]], "c": false}]}, {"t": 37, "s": [{"i": [[-0.824, 0.045], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-16.419, 18.396], [0.632, 1.19], [3.088, 0.107], [13.842, -28.617], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0.643, 6.652], [-28.678, 1.57]], "o": [[0.133, 8.152], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [1.942, -2.176], [-0.895, -1.685], [-23.639, -0.816], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [29.812, 2.58], [0.799, -0.044]], "v": [[133.867, 212.348], [133.867, 217.483], [133.867, 225.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.709], [191.361, -43.138], [195.207, -48.253], [188.69, -49.958], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.357, 225.703], [59.357, 212.348], [131.433, 212.481]], "c": false}]}], "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "quaternary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "quaternary"}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Shape stroke", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [210.982, 264.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1.982, 43, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [0, 26.708], [0, -20.873], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -23.961], [0, 17.399], [0, 0], [0, 0]], "v": [[-29.842, 65.426], [-48.773, 65.426], [-74.419, 37.852], [-41.163, 35.617], [-64.025, 52.563], [-88.656, 52.563]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 22.5, "s": [{"i": [[0, 0], [0, 0], [0, -0.266], [0, 0.208], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0.238], [0, -0.173], [0, 0], [0, 0]], "v": [[-37.171, 93.139], [-62.257, 91.737], [-89.807, 92.012], [-54.082, 92.034], [-78.642, 91.865], [-118.101, 91.961]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27.5, "s": [{"i": [[0, 0], [0, 0], [0, 31.663], [0, -24.745], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -28.406], [0, 20.627], [0, 0], [0, 0]], "v": [[-22.426, 61.595], [-40.105, 61.595], [-64.056, 28.906], [-32.998, 26.256], [-54.349, 46.346], [-77.351, 46.346]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 32.5, "s": [{"i": [[0, 0], [0, 0], [0, 20.692], [0, -16.171], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -18.564], [0, 13.48], [0, 0], [0, 0]], "v": [[-29.842, 65.426], [-48.773, 65.426], [-74.419, 44.064], [-41.163, 42.332], [-64.025, 55.461], [-88.656, 55.461]], "c": false}]}, {"t": 37.5, "s": [{"i": [[0, 0], [0, 0], [0, 26.708], [0, -20.873], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -23.961], [0, 17.399], [0, 0], [0, 0]], "v": [[-22.842, 65.426], [-48.773, 65.426], [-74.419, 37.852], [-41.163, 35.617], [-64.025, 52.563], [-88.656, 52.563]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 1, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "quaternary"}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "quaternary"}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_3", "nm": "Coin-hover", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [218, 95, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [3, -120, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[4.418, 0], [0, -4.418], [-4.418, 0], [0, 4.418]], "o": [[-4.418, 0], [0, 4.418], [4.418, 0], [0, -4.418]], "v": [[7, -135], [-1, -127], [7, -119], [15, -127]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 47.5, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -63], [-44, -12], [7, 39], [58, -12]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 55, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -79.5], [-44, -28.5], [7, 22.5], [58, -28.5]], "c": true}]}, {"t": 60, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -69.5], [-44, -18.5], [7, 32.5], [58, -18.5]], "c": true}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0]}, {"t": 42.5, "s": [100]}], "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 2", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-4, -101.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0]}, {"t": 42.5, "s": [100]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -69.5], [-44, -18.5], [7, 32.5], [58, -18.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 7.5, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -114.5], [-44, -63.5], [7, -12.5], [58, -63.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -82], [-44, -31], [7, 20], [58, -31]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[28.166, 0], [-6.216, -30.457], [-28.167, 0], [-6.27, 30.934]], "o": [[-28.167, 0], [10, 49], [28.167, 0], [7.5, -37]], "v": [[7, -69.5], [-40, -17.5], [9.5, 51.5], [54.5, -14.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[21.807, 0], [-4.238, -30.457], [-21.808, 0], [-4.275, 30.934]], "o": [[-21.808, 0], [6.818, 49], [21.807, 0], [5.114, -37]], "v": [[7.201, -15.5], [-23.762, 49], [9.137, 105.5], [40.671, 52]], "c": true}]}, {"t": 22.5, "s": [{"i": [[21.807, 0], [-4.238, -30.457], [-21.808, 0], [-4.275, 30.934]], "o": [[-21.808, 0], [6.818, 49], [21.807, 0], [5.114, -37]], "v": [[7.806, 85.5], [-23.158, 150], [9.741, 206.5], [41.275, 153]], "c": true}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 2", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-4, -101.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 202.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Shadow", "tt": 1, "tp": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [228.75, 95, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [3, -120, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[4.418, 0], [0, -4.418], [-4.418, 0], [0, 4.418]], "o": [[-4.418, 0], [0, 4.418], [4.418, 0], [0, -4.418]], "v": [[7, -135], [-1, -127], [7, -119], [15, -127]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 47.5, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -63], [-44, -12], [7, 39], [58, -12]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 55, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -79.5], [-44, -28.5], [7, 22.5], [58, -28.5]], "c": true}]}, {"t": 60, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -69.5], [-44, -18.5], [7, 32.5], [58, -18.5]], "c": true}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0]}, {"t": 42.5, "s": [100]}], "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [-4, -101.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0]}, {"t": 42.5, "s": [100]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -69.5], [-44, -18.5], [7, 32.5], [58, -18.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 7.5, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -114.5], [-44, -63.5], [7, -12.5], [58, -63.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -82], [-44, -31], [7, 20], [58, -31]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[28.166, 0], [-6.216, -30.457], [-28.167, 0], [-6.27, 30.934]], "o": [[-28.167, 0], [10, 49], [28.167, 0], [7.5, -37]], "v": [[7, -69.5], [-40, -17.5], [9.5, 51.5], [54.5, -14.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[21.807, 0], [-4.238, -30.457], [-21.808, 0], [-4.275, 30.934]], "o": [[-21.808, 0], [6.818, 49], [21.807, 0], [5.114, -37]], "v": [[7.201, -15.5], [-23.762, 49], [9.137, 105.5], [40.671, 52]], "c": true}]}, {"t": 22.5, "s": [{"i": [[21.807, 0], [-4.238, -30.457], [-21.808, 0], [-4.275, 30.934]], "o": [[-21.808, 0], [6.818, 49], [21.807, 0], [5.114, -37]], "v": [[7.806, 85.5], [-23.158, 150], [9.741, 206.5], [41.275, 153]], "c": true}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [-4, -101.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 202.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Coin-fill", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [218, 95, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [3, -120, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[4.418, 0], [0, -4.418], [-4.418, 0], [0, 4.418]], "o": [[-4.418, 0], [0, 4.418], [4.418, 0], [0, -4.418]], "v": [[7, -135], [-1, -127], [7, -119], [15, -127]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 47.5, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -63], [-44, -12], [7, 39], [58, -12]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 55, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -79.5], [-44, -28.5], [7, 22.5], [58, -28.5]], "c": true}]}, {"t": 60, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -69.5], [-44, -18.5], [7, 32.5], [58, -18.5]], "c": true}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0]}, {"t": 42.5, "s": [100]}], "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [-4, -101.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0]}, {"t": 42.5, "s": [100]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -69.5], [-44, -18.5], [7, 32.5], [58, -18.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 0}, "t": 7.5, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -114.5], [-44, -63.5], [7, -12.5], [58, -63.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -82], [-44, -31], [7, 20], [58, -31]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[28.166, 0], [-6.216, -30.457], [-28.167, 0], [-6.27, 30.934]], "o": [[-28.167, 0], [10, 49], [28.167, 0], [7.5, -37]], "v": [[7, -69.5], [-40, -17.5], [9.5, 51.5], [54.5, -14.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[21.807, 0], [-4.238, -30.457], [-21.808, 0], [-4.275, 30.934]], "o": [[-21.808, 0], [6.818, 49], [21.807, 0], [5.114, -37]], "v": [[7.201, -15.5], [-23.762, 49], [9.137, 105.5], [40.671, 52]], "c": true}]}, {"t": 22.5, "s": [{"i": [[21.807, 0], [-4.238, -30.457], [-21.808, 0], [-4.275, 30.934]], "o": [[-21.808, 0], [6.818, 49], [21.807, 0], [5.114, -37]], "v": [[7.806, 85.5], [-23.158, 150], [9.741, 206.5], [41.275, 153]], "c": true}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [-4, -101.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 202.5, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_5", "nm": "Pig", "fr": 60, "layers": [{"ddd": 0, "ind": 2, "ty": 4, "nm": "L-alt", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1.982, 43, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1.982, 43, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [-27.5, -0.006], [0, 0]], "o": [[0, 0], [27.5, 0.006], [0, 0]], "v": [[61.327, 14.494], [95.913, 10.994], [132.946, 14.494]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [-29.542, -0.005], [0, 0]], "o": [[0, 0], [29.542, 0.005], [0, 0]], "v": [[60.768, 50.355], [97.845, 53.915], [137.704, 50.355]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12.5, "s": [{"i": [[0, 0], [-25.682, -0.006], [0, 0]], "o": [[0, 0], [25.682, 0.006], [0, 0]], "v": [[62.715, 9.071], [95.015, 5.462], [129.599, 9.071]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17.5, "s": [{"i": [[0, 0], [-27.5, -0.006], [0, 0]], "o": [[0, 0], [27.5, 0.006], [0, 0]], "v": [[61.327, 14.494], [95.913, 10.994], [132.946, 14.494]], "c": false}]}, {"t": 22.5, "s": [{"i": [[0, 0], [-27.5, -0.006], [0, 0]], "o": [[0, 0], [27.5, 0.006], [0, 0]], "v": [[61.327, 14.494], [95.913, 10.994], [132.946, 14.494]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 50, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.227, 0.2, 0.278, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 10, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "R-alt", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1.982, 43, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1.982, 43, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [27.5, 0.006], [0, 0]], "o": [[0, 0], [-27.5, -0.006], [0, 0]], "v": [[132.946, 14.494], [95.913, 10.994], [61.327, 14.494]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [29.542, 0.005], [0, 0]], "o": [[0, 0], [-29.542, -0.005], [0, 0]], "v": [[137.704, 50.355], [97.845, 53.915], [60.768, 50.355]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12.5, "s": [{"i": [[0, 0], [25.682, 0.006], [0, 0]], "o": [[0, 0], [-25.682, -0.006], [0, 0]], "v": [[129.599, 9.071], [95.015, 5.462], [62.715, 9.071]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17.5, "s": [{"i": [[0, 0], [27.5, 0.006], [0, 0]], "o": [[0, 0], [-27.5, -0.006], [0, 0]], "v": [[132.946, 14.494], [95.913, 10.994], [61.327, 14.494]], "c": false}]}, {"t": 22.5, "s": [{"i": [[0, 0], [27.5, 0.006], [0, 0]], "o": [[0, 0], [-27.5, -0.006], [0, 0]], "v": [[132.946, 14.494], [95.913, 10.994], [61.327, 14.494]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.227, 0.2, 0.278, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "tertiary"}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 50, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[7.525, 0], [0, -7.525], [-7.525, 0], [0, 7.525]], "o": [[-7.525, 0], [0, 7.525], [7.525, 0], [0, -7.525]], "v": [[-2, -16.125], [-15.625, -2.5], [-2, 11.125], [11.625, -2.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[11.312, 0], [0, -2.438], [-11.312, 0], [0, 2.438]], "o": [[-11.312, 0], [0, 2.438], [11.312, 0], [0, -2.438]], "v": [[6, 26.625], [-5, 28.25], [6, 29.875], [17, 28.25]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12.5, "s": [{"i": [[7.525, 0], [0, -7.525], [-7.525, 0], [0, 7.525]], "o": [[-7.525, 0], [0, 7.525], [7.525, 0], [0, -7.525]], "v": [[-7.625, -20.375], [-21.25, -6.75], [-7.625, 6.875], [6, -6.75]], "c": true}]}, {"t": 17.5, "s": [{"i": [[7.525, 0], [0, -7.525], [-7.525, 0], [0, 7.525]], "o": [[-7.525, 0], [0, 7.525], [7.525, 0], [0, -7.525]], "v": [[-1.875, -15.875], [-15.5, -2.25], [-1.875, 11.375], [11.75, -2.25]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.227, 0.2, 0.278, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [84.75, -29.625], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 3", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 10, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 3, "nm": "R", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [57]}, {"t": 34, "s": [139]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [292.982, 380.5, 0], "to": [15.333, -147.5, 0], "ti": [0, 0, 0]}, {"t": 34, "s": [492.982, 197.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [83.982, 159, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 10, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "mask 3", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32, -65], [-169, -67], [-240, 112.5], [-50, 214], [179, 185], [187.203, -124.116], [32, -64], [1.5, -40], [-3, 8.5], [-33.5, 56.5], [-6, 100], [-46.5, 114], [-48, 144.5], [-47, 114.5], [-6.5, 100], [-33, 56.5], [-3.5, 8], [1.5, -40.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4, -90], [-169, -67], [-245.5, 100.5], [-50, 214], [179, 185], [192.203, -122.616], [56.5, -94.5], [23, -56.5], [14.5, -10.5], [-21.5, 41], [2, 89.5], [-13.5, 90.5], [-55, 108], [-54.5, 111], [-16, 89.5], [-43.5, 51], [-25, -5], [-26, -55.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.261, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-31.75, -104.75], [-169, -67], [-268.5, 70], [-50, 214], [179, 185], [205.203, -123.116], [82, -94.75], [47.5, -70.25], [36.25, -23.75], [-7, 26], [13.5, 77.25], [-34.5, 86], [-17.5, 122.25], [-56.75, 111.25], [-24.5, 84.5], [-59.75, 47], [-46.5, -12.5], [-53.5, -63.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.261, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-59.5, -119], [-169, -67], [-287, 103.5], [-50, 214], [179, 185], [206.203, -107.116], [74, -77], [55, -30], [10, 13], [18.5, 70], [-22, 73], [-22.5, 74], [-23.5, 74], [-37, 84.5], [-37, 83], [-76, 43], [-68, -20], [-81, -72]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-127, -101], [-170, -80], [-271.5, 118.5], [-50, 214], [179, 185], [279.703, -137.116], [126.5, -70.5], [94.5, -27.5], [41.5, 8], [39, 59], [-3.5, 53], [-1, 120.5], [-59.5, 114], [-79, 113.5], [-54.5, 76.5], [-100.5, 50.5], [-108.5, -12], [-135, -60]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-183, -76.5], [-217, -66.5], [-290, 124.5], [-50, 214], [264.5, 193], [256.203, -130.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [6, 105.5], [-69, 115], [-85.5, 119], [-72.5, 81], [-121, 64], [-143.5, 5], [-184, -36]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-220.667, -59.833], [-246.333, -68.5], [-290, 124.5], [-50, 214], [253, 210.5], [248.203, -101.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [40, 64.5], [14.5, 73.5], [-64.5, 112], [-96, 124.833], [-91.667, 81.417], [-144.917, 81.417], [-179.25, 25.75], [-229.75, -16.167]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-239.5, -51.5], [-261, -69.5], [-290, 124.5], [-50, 214], [272, 170], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [40.5, 70.5], [14.5, 73.5], [-43.25, 71.75], [-103.5, 111.5], [-102.75, 70.625], [-157.875, 69.125], [-192.125, 18.625], [-240.625, -9.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-258.333, -43.167], [-275.667, -70.5], [-290, 124.5], [-50, 214], [238.5, 186.5], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-50, 98.5], [-70, 83.167], [-87.833, 83.833], [-111.833, 99.833], [-115, 57.5], [-169.5, 59.667]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-275.667, -43.083], [-290.333, -71.5], [-290, 124.5], [-50, 214], [257.5, 179.5], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-57, 93], [-78, 84.833], [-104.417, 75.167], [-125.667, 91.167], [-129.25, 49.25], [-184.25, 55.083]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-293, -43], [-305, -72.5], [-290, 124.5], [-50, 214], [246.5, 181.5], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-69, 115], [-79.5, 70], [-113, 59.5], [-139.5, 82.5], [-143.5, 41], [-199, 50.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-293, -43], [-305, -72.5], [-290, 124.5], [-50, 214], [232, 181.5], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-69, 115], [-89, 62.5], [-131, 49.5], [-156.5, 73], [-163, 35.5], [-227.5, 51.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [102.297, -15.884], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-52.338, 8.127], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-293, -43], [-305, -72.5], [-290, 124.5], [-50, 214], [253, 170], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-69, 115], [-89, 62.5], [-157.5, 33.5], [-182.5, 65], [-195.5, 25], [-253.5, 43.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-293, -43], [-305, -72.5], [-290, 124.5], [-50, 214], [232, 178.5], [243.203, -51.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-69, 115], [-89, 62.5], [-196.5, 17.5], [-212.5, 57.5], [-225.5, 17.5], [-283.5, 36]], "c": true}]}, {"t": 30, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.261, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-323, -50.5], [-335, -80], [-320, 117], [-80, 206.5], [235.5, 176], [202.203, -107.116], [144.5, -57.5], [111, -24], [45.5, -2.5], [32, 49], [-6.5, 34.5], [-15.5, 66], [-99, 107.5], [-119, 55], [-226.5, 10], [-242.5, 50], [-255.5, 10], [-313.5, 28.5]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156881094, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.070588000119, 0.074510000646, 0.192157000303, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 10, "op": 33, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "R 2", "tt": 1, "tp": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [57]}, {"t": 34, "s": [139]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [292.982, 380.5, 0], "to": [15.333, -147.5, 0], "ti": [0, 0, 0]}, {"t": 34, "s": [492.982, 197.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [83.982, 159, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-23.314, 18.197], [15.65, -32.355], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [-26.687, -2.685], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[133.684, 198.348], [133.684, 212.483], [133.684, 223.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.708], [197.957, -49.315], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.428, 211.703], [59.428, 198.348]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-5.148, 6.075], [0, 0], [0, 0], [-7.928, 7.928], [-5.004, 18.601], [0, 0], [0, 8.925], [0, 0], [11.413, 0], [0, 0], [25.612, 12.163], [-14.543, 6.745], [20.592, -16.243], [0, 0], [22.309, -17.444], [0, -26.638], [-35.087, -23.669], [0, 0], [-5.968, -5.271], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [6.057, 5.133], [0, 0], [0, 0], [8.852, -1.426], [15.443, -15.443], [0, 0], [11.413, 0], [0, 0], [0, -8.924], [0, 0], [-8.923, -22.841], [0, 0], [-23.043, -14.755], [0, 0], [-34.067, 0], [-22.333, 17.463], [0, 35.235], [0, 0], [-5.256, 5.951], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[139.246, 221.596], [150.03, 230.735], [159.271, 234.18], [179.563, 232.488], [193.515, 218.89], [164.648, 194.426], [197.82, 180.129], [240.87, 141.282], [257.818, 141.063], [278.5, 124.891], [278.5, 108.97], [257.818, 92.798], [238.134, 93.016], [184.1, 38.775], [211.543, 19.255], [132.408, 24.743], [81.924, 24.839], [-5.289, 53.078], [-41.427, 121.293], [17.837, 191.919], [0.626, 211.406], [1.902, 231.729], [6.697, 237.032], [36.355, 227.073], [50.445, 223.313]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-6.953, 1.695], [0, 0], [0, 0], [-7.588, 8.089], [-4.58, 21.345], [0, 0], [0, 10.241], [0, 0], [10.447, 0], [0, 0], [23.443, 13.957], [-11.477, 15.16], [21.184, -21.353], [0, 0], [20.42, -20.018], [0, -30.568], [-35.658, -18.289], [0, 0], [-7.214, -1.166], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [1.69, 6.933], [0, 0], [0, 0], [9.857, -3.383], [14.781, -15.756], [0, 0], [10.447, 0], [0, 0], [0, -10.241], [0, 0], [-8.168, -26.211], [0, 0], [-18.681, -0.508], [0, 0], [-31.182, 0], [-20.442, 20.04], [0, 40.433], [0, 0], [-1.163, 7.193], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[137.417, 218.196], [136.341, 218.666], [140.382, 226.153], [156.027, 235.649], [175.619, 232.129], [167.565, 199.089], [192.871, 179.968], [226.883, 127.944], [235.917, 127.867], [254.848, 109.309], [254.848, 91.039], [235.917, 72.481], [224.378, 72.558], [174.92, 10.315], [192.601, -35.168], [127.604, -5.787], [81.395, -5.677], [1.566, 26.727], [-31.512, 105.006], [22.132, 196.449], [18.324, 220.005], [29.273, 235.151], [45.564, 235.05], [53.728, 214.656], [53.831, 216.737]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.933, -0.684], [0, 0], [0, 0], [-7.404, 8.176], [-4.35, 22.835], [0, 0], [0, 10.956], [0, 0], [9.922, 0], [0, 0], [22.265, 14.932], [-9.812, 19.731], [21.505, -24.128], [0, 0], [19.394, -21.416], [0, -32.703], [-35.968, -15.366], [0, 0], [-7.891, 1.063], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.682, 7.91], [0, 0], [0, 0], [10.402, -4.446], [14.422, -15.926], [0, 0], [9.922, 0], [0, 0], [0, -10.956], [0, 0], [-7.757, -28.041], [0, 0], [-16.312, 7.231], [0, 0], [-29.616, 0], [-19.415, 21.439], [0, 43.257], [0, 0], [1.06, 7.868], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[130.069, 211.666], [131.078, 210.705], [130.121, 221.793], [143.242, 237.366], [165.898, 239.32], [169.15, 201.621], [190.183, 179.881], [219.285, 120.698], [224.021, 120.698], [242, 100.844], [242, 81.298], [224.021, 61.445], [216.906, 61.445], [169.933, -5.145], [182.312, -64.731], [124.995, -22.372], [81.107, -22.254], [5.29, 12.414], [-26.126, 96.159], [24.466, 198.91], [27.938, 224.676], [44.141, 237.01], [66.677, 233.973], [63.165, 207.911], [63.739, 209.2]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-23.314, 18.197], [15.65, -32.355], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [-26.687, -2.685], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[134.234, 211.79], [133.684, 212.483], [133.684, 223.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.708], [200.457, -41.815], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.428, 211.703], [57.833, 213.887]], "c": false}]}, {"t": 23, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-23.314, 18.197], [15.65, -32.355], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [-26.687, -2.685], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[133.684, 198.348], [133.566, 198.192], [133.684, 223.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.708], [197.957, -49.315], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.582, 197.974], [59.428, 198.348]], "c": false}]}], "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [-7.389, 0], [0, 0], [-5.553, 0.826]], "o": [[6.999, 1.354], [0, 0], [5.806, 0], [0, 0]], "v": [[59.428, 211.703], [81.021, 213.745], [116.634, 213.745], [133.684, 212.483]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [-7.389, 0], [0, 0], [-17.28, -14.485]], "o": [[4.349, 5.648], [0, 0], [5.806, 0], [0, 0]], "v": [[36.355, 227.073], [81.021, 218.995], [116.634, 218.995], [150.03, 230.735]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-7.389, 0], [0, 0], [-13.041, -0.661]], "o": [[6.259, -0.757], [0, 0], [5.798, 0.184], [0, 0]], "v": [[46.241, 220.007], [80.771, 219.684], [121.738, 218.517], [137.291, 219.099]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [{"i": [[0, 0], [-7.389, 0], [0, 0], [-9.174, -0.337]], "o": [[6.143, 2.252], [0, 0], [5.792, 0.323], [0, 0]], "v": [[53.728, 214.656], [81.021, 215.593], [115.937, 218.154], [137.749, 217.756]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[0, 0], [-7.389, 0], [0, 0], [-5.604, 0.346]], "o": [[7.117, 0.407], [0, 0], [5.784, 0.499], [0, 0]], "v": [[63.165, 207.911], [81.021, 213.745], [113.983, 210.497], [131.078, 210.705]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "t": 17, "s": [{"i": [[0, 0], [-7.389, 0], [0, 0], [-5.553, 0.826]], "o": [[6.999, 1.354], [0, 0], [5.806, 0], [0, 0]], "v": [[59.428, 211.703], [81.021, 213.745], [117.181, 212.337], [133.609, 211.333]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [{"i": [[0, 0], [-7.389, 0], [0, 0], [-5.553, 0.826]], "o": [[6.999, 1.354], [0, 0], [5.806, 0], [0, 0]], "v": [[59.102, 207.616], [80.218, 210.392], [115.73, 206.513], [133.681, 206.857]], "c": false}]}, {"t": 23, "s": [{"i": [[0, 0], [-7.389, 0], [0, 0], [-5.553, 0.826]], "o": [[6.999, 1.354], [0, 0], [5.806, 0], [0, 0]], "v": [[59.582, 197.974], [80.045, 199.285], [115.292, 199.85], [133.566, 198.192]], "c": false}]}], "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "quaternary"}], "ip": 7, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 3, "nm": "L", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [-58]}, {"t": 34, "s": [-115]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [148.982, 378.5, 0], "to": [-29.667, -96.5, 0], "ti": [0, 0, 0]}, {"t": 34, "s": [-41.018, 267.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-60.018, 157, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 10, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "mask 2", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32, -65], [-169, -67], [-240, 112.5], [-50, 214], [179, 185], [202.703, -94.616], [32, -64], [1.5, -40], [-3, 8.5], [-33.5, 56.5], [-6, 100], [-46.5, 114], [-48, 144.5], [-47, 114.5], [-6.5, 100], [-33, 56.5], [-3.5, 8], [1.5, -40.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4, -90], [-169, -67], [-245.5, 100.5], [-50, 214], [179, 185], [202.703, -94.616], [61, -79.5], [23, -56.5], [14.5, -10.5], [-21.5, 41], [2, 89.5], [-41.5, 99.5], [-35, 129.5], [-54.5, 111], [-16, 89.5], [-43.5, 51], [-25, -5], [-26, -55.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.261, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-31.75, -104.75], [-169, -67], [-268.5, 70], [-50, 214], [179, 185], [202.703, -94.616], [82, -94.75], [47.5, -70.25], [36.25, -23.75], [-7, 26], [13.5, 77.25], [-34.5, 86], [-17.5, 122.25], [-56.75, 111.25], [-24.5, 84.5], [-59.75, 47], [-46.5, -12.5], [-53.5, -63.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.261, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-59.5, -119], [-169, -67], [-287, 103.5], [-50, 214], [179, 185], [202.703, -94.616], [74, -77], [55, -30], [10, 13], [18.5, 70], [-22, 73], [-15, 97], [0, 115], [-66, 110], [-37, 83], [-76, 43], [-68, -20], [-81, -72]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-127, -101], [-170, -80], [-271.5, 118.5], [-50, 214], [179, 185], [202.703, -94.616], [126.5, -70.5], [94.5, -27.5], [41.5, 8], [39, 59], [-3.5, 53], [5, 94.5], [-59.75, 112], [-79, 113.5], [-54.5, 76.5], [-100.5, 50.5], [-108.5, -12], [-135, -60]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-183, -76.5], [-217, -66.5], [-290, 124.5], [-50, 214], [179, 185], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-63.75, 121], [-85.5, 119], [-72.5, 81], [-121, 64], [-143.5, 5], [-184, -36]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-201.833, -68.167], [-231.667, -67.5], [-290, 124.5], [-50, 214], [179, 185], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-66.75, 113.5], [-90.75, 121.917], [-82.083, 81.208], [-130.958, 67.708], [-161.375, 15.375], [-206.875, -26.083]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-220.667, -59.833], [-246.333, -68.5], [-290, 124.5], [-50, 214], [179, 185], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-63.5, 119.5], [-96, 124.833], [-91.667, 81.417], [-145.917, 75.417], [-179.25, 25.75], [-229.75, -16.167]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-239.5, -51.5], [-261, -69.5], [-290, 124.5], [-50, 214], [179, 185], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-43.25, 71.75], [-103.5, 111.5], [-102.75, 70.625], [-157.875, 69.125], [-192.125, 18.625], [-240.625, -9.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-258.333, -43.167], [-275.667, -70.5], [-290, 124.5], [-50, 214], [179, 185], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-50, 98.5], [-70, 83.167], [-87.833, 83.833], [-111.833, 99.833], [-113, 58], [-169.5, 59.667]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-275.667, -43.083], [-290.333, -71.5], [-290, 124.5], [-50, 214], [179, 185], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-57, 93], [-78, 84.833], [-104.417, 75.167], [-122.167, 91.167], [-126.75, 43.25], [-184.25, 55.083]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-293, -43], [-305, -72.5], [-290, 124.5], [-50, 214], [179, 185], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-69, 115], [-79.5, 70], [-113, 59.5], [-133.5, 80.5], [-143.5, 41], [-199, 50.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-293, -43], [-305, -72.5], [-290, 124.5], [-50, 214], [179, 185], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-69, 115], [-89, 62.5], [-131, 49.5], [-151.5, 73], [-163, 35.5], [-227.5, 51.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-293, -43], [-305, -72.5], [-290, 124.5], [-50, 214], [179, 185], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-69, 115], [-89, 62.5], [-157.5, 33.5], [-182.5, 65], [-195.5, 25], [-253.5, 43.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-293, -43], [-305, -72.5], [-290, 124.5], [-50, 214], [179, 185], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-69, 115], [-89, 62.5], [-196.5, 17.5], [-212.5, 57.5], [-225.5, 17.5], [-283.5, 36]], "c": true}]}, {"t": 30, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-323, -50.5], [-335, -80], [-320, 117], [-80, 206.5], [149, 177.5], [172.703, -102.116], [144.5, -57.5], [111, -24], [45.5, -2.5], [32, 49], [-6.5, 34.5], [-15.5, 66], [-99, 107.5], [-119, 55], [-226.5, 10], [-242.5, 50], [-255.5, 10], [-313.5, 28.5]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156881094, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.070588000119, 0.074510000646, 0.192157000303, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 10, "op": 33, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "L 2", "tt": 1, "tp": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [-58]}, {"t": 34, "s": [-115]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [148.982, 378.5, 0], "to": [-29.667, -96.5, 0], "ti": [0, 0, 0]}, {"t": 34, "s": [-41.018, 267.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-60.018, 157, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 7.94], [0, 0], [0, 41.947], [-20.79, 20.79], [-31.712, 0], [0, 0], [-26.687, -2.685], [0, 0], [-8.307, -27.192], [0, 0], [0, -10.624], [0, 0], [10.624, 0], [0, 0], [15.443, -15.443], [9.982, -5.324], [0, 0], [0, 0], [0, 7.94], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-7.962, 0], [0, 0], [-33.594, -20.032], [0, -31.712], [20.767, -20.767], [0, 0], [15.65, -32.355], [-23.314, 18.197], [23.842, 14.479], [0, 0], [10.624, 0], [0, 0], [0, 10.624], [0, 0], [-4.658, 22.144], [-7.928, 7.928], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0, 0]], "v": [[59.428, 198.348], [59.428, 205.288], [59.428, 211.703], [59.428, 238], [36.688, 238], [22.278, 223.612], [22.278, 197.614], [-33.804, 98.943], [-0.164, 17.735], [81.021, -15.882], [128.016, -15.997], [197.957, -49.315], [176.135, 0.708], [226.434, 65.28], [234.052, 65.28], [253.305, 84.533], [253.305, 103.487], [234.052, 122.739], [228.981, 122.739], [197.82, 180.129], [170.834, 200.161], [170.834, 238], [148.094, 238], [133.684, 223.612], [133.684, 212.483], [133.684, 198.348]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-5.256, 5.951], [0, 0], [0, 35.235], [-22.333, 17.463], [-34.067, 0], [0, 0], [-23.043, -14.755], [0, 0], [-8.923, -22.841], [0, 0], [0, -8.924], [0, 0], [11.413, 0], [0, 0], [15.443, -15.443], [8.852, -1.426], [0, 0], [0, 0], [6.057, 5.133], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-5.968, -5.271], [0, 0], [-35.087, -23.669], [0, -26.638], [22.309, -17.444], [0, 0], [20.592, -16.243], [-14.543, 6.745], [25.612, 12.163], [0, 0], [11.413, 0], [0, 0], [0, 8.925], [0, 0], [-5.004, 18.601], [-7.928, 7.928], [0, 0], [0, 0], [-5.148, 6.075], [0, 0], [0, 0], [0, 0]], "v": [[50.445, 223.313], [43.123, 225.267], [36.355, 227.073], [6.697, 237.032], [1.902, 231.729], [0.626, 211.406], [17.837, 191.919], [-41.427, 121.293], [-5.289, 53.078], [81.924, 24.839], [132.408, 24.743], [211.543, 19.255], [184.1, 38.775], [238.134, 93.016], [257.818, 92.798], [278.5, 108.97], [278.5, 124.891], [257.818, 141.063], [240.87, 141.282], [197.82, 180.129], [164.648, 194.426], [193.515, 218.89], [179.563, 232.488], [159.271, 234.18], [150.03, 230.735], [139.246, 221.596]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.06, 7.868], [0, 0], [0, 43.257], [-19.415, 21.439], [-29.616, 0], [0, 0], [-16.312, 7.231], [0, 0], [-7.757, -28.041], [0, 0], [0, -10.956], [0, 0], [9.922, 0], [0, 0], [14.422, -15.926], [10.402, -4.446], [0, 0], [0, 0], [-0.682, 7.91], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-7.891, 1.063], [0, 0], [-35.968, -15.366], [0, -32.703], [19.394, -21.416], [0, 0], [21.505, -24.128], [-9.812, 19.731], [22.265, 14.932], [0, 0], [9.922, 0], [0, 0], [0, 10.956], [0, 0], [-4.35, 22.835], [-7.404, 8.176], [0, 0], [0, 0], [-7.933, -0.684], [0, 0], [0, 0], [0, 0]], "v": [[84.623, 164.679], [59.021, 175.608], [63.165, 207.911], [66.677, 233.973], [44.141, 237.01], [27.938, 224.676], [24.466, 198.91], [-26.126, 96.159], [5.29, 12.414], [81.107, -22.254], [124.995, -22.372], [182.312, -64.731], [169.933, -5.145], [216.906, 61.445], [224.021, 61.445], [242, 81.298], [242, 100.844], [224.021, 120.698], [219.285, 120.698], [190.183, 179.881], [169.15, 201.621], [165.898, 239.32], [143.242, 237.366], [130.121, 221.793], [131.078, 210.705], [132.292, 196.622]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 7.94], [0, 0], [0, 41.947], [-20.79, 20.79], [-31.712, 0], [0, 0], [-26.687, -2.685], [0, 0], [-8.307, -27.192], [0, 0], [0, -10.624], [0, 0], [10.624, 0], [0, 0], [15.443, -15.443], [9.982, -5.324], [0, 0], [0, 0], [0, 7.94], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-7.962, 0], [0, 0], [-33.594, -20.032], [0, -31.712], [20.767, -20.767], [0, 0], [15.65, -32.355], [-23.314, 18.197], [23.842, 14.479], [0, 0], [10.624, 0], [0, 0], [0, 10.624], [0, 0], [-4.658, 22.144], [-7.928, 7.928], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0, 0]], "v": [[86.707, 169.763], [59.428, 205.288], [59.428, 211.703], [59.428, 238], [36.688, 238], [22.278, 223.612], [22.278, 197.614], [-33.804, 98.943], [-0.164, 17.735], [81.021, -15.882], [128.016, -15.997], [200.457, -41.815], [176.135, 0.708], [226.434, 65.28], [234.052, 65.28], [253.305, 84.533], [253.305, 103.487], [234.052, 122.739], [228.981, 122.739], [197.82, 180.129], [170.834, 200.161], [170.834, 238], [148.094, 238], [133.684, 223.612], [133.684, 212.483], [133.684, 198.348]], "c": false}]}, {"t": 23, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 7.94], [0, 0], [0, 41.947], [-20.79, 20.79], [-31.712, 0], [0, 0], [-26.687, -2.685], [0, 0], [-8.307, -27.192], [0, 0], [0, -10.624], [0, 0], [10.624, 0], [0, 0], [15.443, -15.443], [9.982, -5.324], [0, 0], [0, 0], [0, 7.94], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-7.962, 0], [0, 0], [-33.594, -20.032], [0, -31.712], [20.767, -20.767], [0, 0], [15.65, -32.355], [-23.314, 18.197], [23.842, 14.479], [0, 0], [10.624, 0], [0, 0], [0, 10.624], [0, 0], [-4.658, 22.144], [-7.928, 7.928], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0, 0]], "v": [[59.428, 198.348], [59.428, 205.288], [59.428, 211.703], [59.428, 238], [36.688, 238], [22.278, 223.612], [22.278, 197.614], [-33.804, 98.943], [-0.164, 17.735], [81.021, -15.882], [128.016, -15.997], [197.957, -49.315], [176.135, 0.708], [226.434, 65.28], [234.052, 65.28], [253.305, 84.533], [253.305, 103.487], [234.052, 122.739], [228.981, 122.739], [197.82, 180.129], [170.834, 200.161], [170.834, 238], [148.094, 238], [133.684, 223.612], [133.684, 212.483], [133.684, 198.348]], "c": false}]}], "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 76.8, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "quaternary"}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 2", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 26.708], [0, -20.873], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -23.961], [0, 17.399], [0, 0], [0, 0]], "v": [[-29.842, 65.426], [-48.773, 65.426], [-74.419, 37.852], [-41.163, 35.617], [-64.025, 52.563], [-88.656, 52.563]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0, -0.266], [0, 0.208], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0.238], [0, -0.173], [0, 0], [0, 0]], "v": [[-37.171, 93.139], [-62.257, 91.737], [-89.807, 92.012], [-54.082, 92.034], [-78.642, 91.865], [-118.101, 91.961]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[0, 0], [0, 0], [0, 31.663], [0, -24.745], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -28.406], [0, 20.627], [0, 0], [0, 0]], "v": [[-22.426, 61.595], [-40.105, 61.595], [-64.056, 28.906], [-32.998, 26.256], [-54.349, 46.346], [-77.351, 46.346]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17, "s": [{"i": [[0, 0], [0, 0], [0, 20.692], [0, -16.171], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -18.564], [0, 13.48], [0, 0], [0, 0]], "v": [[-29.842, 65.426], [-48.773, 65.426], [-74.419, 44.064], [-41.163, 42.332], [-64.025, 55.461], [-88.656, 55.461]], "c": false}]}, {"t": 23, "s": [{"i": [[0, 0], [0, 0], [0, 26.708], [0, -20.873], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -23.961], [0, 17.399], [0, 0], [0, 0]], "v": [[-29.842, 65.426], [-48.773, 65.426], [-74.419, 37.852], [-41.163, 35.617], [-64.025, 52.563], [-88.656, 52.563]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "quaternary"}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 10, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "<PERSON><PERSON><PERSON> 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [210.982, 264.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1.982, 43, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[7.525, 0], [0, -7.525], [-7.525, 0], [0, 7.525]], "o": [[-7.525, 0], [0, 7.525], [7.525, 0], [0, -7.525]], "v": [[-2, -16.125], [-15.625, -2.5], [-2, 11.125], [11.625, -2.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[11.312, 0], [0, -2.438], [-11.312, 0], [0, 2.438]], "o": [[-11.312, 0], [0, 2.438], [11.312, 0], [0, -2.438]], "v": [[6, 26.625], [-5, 28.25], [6, 29.875], [17, 28.25]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12.5, "s": [{"i": [[7.525, 0], [0, -7.525], [-7.525, 0], [0, 7.525]], "o": [[-7.525, 0], [0, 7.525], [7.525, 0], [0, -7.525]], "v": [[-7.625, -20.375], [-21.25, -6.75], [-7.625, 6.875], [6, -6.75]], "c": true}]}, {"t": 17.5, "s": [{"i": [[7.525, 0], [0, -7.525], [-7.525, 0], [0, 7.525]], "o": [[-7.525, 0], [0, 7.525], [7.525, 0], [0, -7.525]], "v": [[-1.875, -15.875], [-15.5, -2.25], [-1.875, 11.375], [11.75, -2.25]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.227, 0.2, 0.278, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [84.75, -29.625], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [-27.5, -0.006], [0, 0]], "o": [[0, 0], [27.5, 0.006], [0, 0]], "v": [[61.327, 14.494], [95.913, 10.994], [132.946, 14.494]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [-29.542, -0.005], [0, 0]], "o": [[0, 0], [29.542, 0.005], [0, 0]], "v": [[60.768, 50.355], [97.845, 53.915], [137.704, 50.355]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12.5, "s": [{"i": [[0, 0], [-25.682, -0.006], [0, 0]], "o": [[0, 0], [25.682, 0.006], [0, 0]], "v": [[62.715, 9.071], [95.015, 5.462], [129.599, 9.071]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17.5, "s": [{"i": [[0, 0], [-27.5, -0.006], [0, 0]], "o": [[0, 0], [27.5, 0.006], [0, 0]], "v": [[61.327, 14.494], [95.913, 10.994], [132.946, 14.494]], "c": false}]}, {"t": 22.5, "s": [{"i": [[0, 0], [-27.5, -0.006], [0, 0]], "o": [[0, 0], [27.5, 0.006], [0, 0]], "v": [[61.327, 14.494], [95.913, 10.994], [132.946, 14.494]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.227, 0.2, 0.278, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 10, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 3, "nm": "coin 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [218, 95, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 23, "s": [218, -124, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [3, -120, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Vector 2", "parent": 12, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-19.33, -119.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[8.502, -9.38], [0.001, -12.66], [0, 0], [-8.502, -9.38], [-12.599, -1.241], [1.69, 0], [9.564, 9.564], [0, 13.526], [-9.564, 9.564], [-13.526, 0], [-1.64, -0.16]], "o": [[-8.502, 9.38], [0, 0], [0.001, 12.66], [8.502, 9.38], [-1.65, 0.16], [-13.526, 0], [-9.564, -9.564], [0, -13.526], [9.564, -9.564], [1.7, 0], [-12.599, 1.241]], "v": [[-4.787, -34.246], [-18, 0], [-18, 0], [-4.787, 34.246], [28, 50.75], [23, 51], [-13.062, 36.062], [-28, 0], [-13.062, -36.062], [23, -51], [28, -50.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Vector", "parent": 12, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [3.68, -119.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-8.387, 5.604], [-10.087, 0], [-9.564, -9.564], [0, -13.526], [5.604, -8.387], [9.319, -3.86], [9.893, 1.968], [7.132, 7.132], [1.968, 9.893], [-3.86, 9.319]], "o": [[8.387, -5.604], [13.526, 0], [9.564, 9.564], [0, 10.087], [-5.604, 8.387], [-9.319, 3.86], [-9.893, -1.968], [-7.132, -7.132], [-1.968, -9.893], [3.86, -9.319]], "v": [[-28.334, -42.405], [0, -51], [36.062, -36.062], [51, 0], [42.405, 28.334], [19.517, 47.118], [-9.95, 50.02], [-36.062, 36.062], [-50.02, 9.95], [-47.118, -19.517]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32, -65], [-169, -67], [-189, 110], [-50, 214], [179, 185], [202.703, -94.616], [32, -64], [1.5, -40], [-3, 8.5], [-33.5, 56.5], [-6, 100], [-46.5, 114], [-48, 144.5], [-47, 114.5], [-6.5, 100], [-33, 56.5], [-3.5, 8], [1.5, -40.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.5, -87.5], [-169, -67], [-189, 110], [-50, 214], [179, 185], [202.703, -94.616], [61, -79.5], [23, -56.5], [14.5, -10.5], [-21.5, 41], [2, 89.5], [-40, 101], [-35, 129.5], [-54.5, 112.5], [2, 89], [-43.5, 51], [-25, -5], [-26, -55.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-31.75, -104.75], [-169, -67], [-189, 110], [-50, 214], [179, 185], [202.703, -94.616], [82, -94.75], [47.5, -70.25], [36.25, -23.75], [-7, 26], [13.5, 77.25], [-34.5, 86], [-20, 139.25], [-24.25, 155.25], [-24.5, 84.5], [-59.75, 47], [-46.5, -12.5], [-53.5, -63.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-63, -107], [-169, -67], [-189, 110], [-50, 214], [179, 185], [202.703, -94.616], [74, -77], [55, -30], [10, 13], [18.5, 70], [-22, 73], [-15, 97], [0, 115], [-66, 110], [-37, 83], [-76, 43], [-68, -20], [-81, -72]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-127, -101], [-169, -67], [-189, 110], [-50, 214], [179, 185], [202.703, -94.616], [126.5, -70.5], [94.5, -27.5], [41.5, 8], [39, 59], [-3.5, 53], [10, 138.5], [-53.5, 143.5], [-79, 113.5], [-54.5, 76.5], [-100.5, 50.5], [-108.5, -12], [-135, -60]], "c": true}]}, {"t": 17, "s": [{"i": [[0, 0], [0, 0], [-31, -49], [-125, 3], [-27, 81], [33.554, 58.771], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [31, 49], [125, -3], [15.146, -45.438], [-26.26, -45.996], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-183, -76.5], [-176.5, -77], [-189, 110], [-50, 214], [179, 185], [202.703, -94.616], [174.5, -50], [141, -16.5], [75.5, 5], [62, 56.5], [23.5, 42], [14.5, 73.5], [-69, 115], [-85.5, 119], [-72.5, 81], [-121, 64], [-143.5, 5], [-184, -36]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156881094, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.070588000119, 0.074510000646, 0.192157000303, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 10, "op": 18, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 0, "nm": "coins", "tt": 2, "tp": 15, "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 18, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 0, "nm": "coins", "tt": 2, "tp": 15, "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 10, "op": 18, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "Mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [210.982, 264.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1.982, 43, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[-0.773, 0.042], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-16.419, 18.396], [0.632, 1.19], [3.088, 0.107], [13.842, -28.617], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0.643, 6.652], [-28.331, 2.527]], "o": [[0.133, 8.152], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [1.942, -2.176], [-0.895, -1.685], [-23.639, -0.816], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [29.864, 2.584], [0.75, -0.067]], "v": [[133.867, 212.348], [133.867, 217.483], [133.867, 225.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.709], [191.361, -43.138], [195.207, -48.253], [188.69, -49.958], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.357, 225.703], [59.357, 212.348], [131.581, 212.473]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-5.148, 6.075], [0, 0], [0, 0], [-7.928, 7.928], [-5.004, 18.601], [0, 0], [0, 8.925], [0, 0], [11.413, 0], [0, 0], [25.612, 12.163], [-11.529, 6.882], [0.019, 1.807], [2.831, 1.185], [18.303, -14.438], [0, 0], [22.309, -17.444], [0, -26.638], [-35.087, -23.669], [0, 0], [-5.968, -5.271], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [6.057, 5.133], [0, 0], [0, 0], [8.852, -1.426], [15.443, -15.443], [0, 0], [11.413, 0], [0, 0], [0, -8.924], [0, 0], [-8.923, -22.841], [0, 0], [2.199, -1.313], [-0.02, -1.881], [-22.643, -9.482], [0, 0], [-34.067, 0], [-22.333, 17.463], [0, 35.235], [0, 0], [-5.256, 5.951], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[139.246, 221.596], [150.03, 230.735], [159.271, 234.18], [179.563, 232.488], [193.515, 218.89], [164.648, 194.426], [197.82, 180.129], [240.87, 141.282], [257.818, 141.063], [278.5, 124.891], [278.5, 108.97], [257.818, 92.798], [238.134, 93.016], [184.1, 38.775], [204.727, 22.883], [208.106, 18.755], [203.437, 15.022], [132.408, 24.743], [81.924, 24.839], [-5.289, 53.078], [-41.427, 121.293], [17.837, 191.919], [0.626, 211.406], [1.902, 231.729], [6.697, 237.032], [36.355, 227.073], [50.445, 223.313], [139.077, 221.599]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12.666, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.933, -0.684], [0, 0], [0, 0], [-7.404, 8.176], [-4.35, 22.835], [0, 0], [0, 10.956], [0, 0], [9.922, 0], [0, 0], [22.265, 14.932], [-6.392, 20.693], [0.553, 1.029], [1.89, -0.91], [19.115, -21.447], [0, 0], [19.394, -21.416], [0, -32.703], [-35.968, -15.366], [0, 0], [-7.891, 1.063], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.682, 7.91], [0, 0], [0, 0], [10.402, -4.446], [14.422, -15.926], [0, 0], [9.922, 0], [0, 0], [0, -10.956], [0, 0], [-7.757, -28.041], [0, 0], [1.219, -3.947], [-0.749, -1.394], [-15.116, 7.275], [0, 0], [-29.616, 0], [-19.415, 21.439], [0, 43.257], [0, 0], [1.06, 7.868], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[131.292, 212.622], [131.078, 207.205], [130.121, 221.793], [143.242, 237.366], [165.898, 239.32], [169.15, 201.621], [190.183, 179.881], [219.285, 120.698], [224.021, 120.698], [242, 100.844], [242, 81.298], [224.021, 61.445], [216.906, 61.445], [169.933, -5.145], [178.135, -53.946], [180.499, -61.919], [176.758, -62.168], [124.995, -22.372], [81.107, -22.253], [5.29, 12.414], [-26.126, 96.159], [24.466, 198.91], [27.938, 224.676], [44.141, 237.01], [66.677, 233.973], [64.665, 215.911], [63.382, 209.176], [131.163, 212.616]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17.334, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-14.037, 16.275], [-0.145, 1.94], [3.025, 0.276], [13.911, -28.759], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [2.677, -3.104], [0.154, -2.061], [-24.2, -2.207], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[134.184, 213.848], [133.684, 212.483], [133.684, 223.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.708], [190.883, -32.724], [196.207, -40.503], [191.458, -42.702], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.428, 211.703], [59.428, 211.348], [134.041, 213.843]], "c": false}]}, {"t": 22, "s": [{"i": [[-30.742, 2.527], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-16.419, 18.396], [0.632, 1.19], [3.088, 0.107], [13.842, -28.617], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0.643, 6.652], [0, 0]], "o": [[0.133, 8.152], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [1.942, -2.176], [-0.895, -1.685], [-23.639, -0.816], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [30.393, 3.152], [0, 0]], "v": [[133.867, 212.348], [133.867, 217.483], [133.867, 225.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.709], [191.361, -43.138], [195.207, -48.253], [188.69, -49.958], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.357, 225.703], [59.357, 212.348], [132, 212.125]], "c": false}]}], "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -15, "op": 10, "st": -15, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "Shadow", "tt": 1, "tp": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [228.232, 264.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1.982, 43, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[-0.667, 0.036], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-16.419, 18.396], [0.632, 1.19], [3.088, 0.107], [13.842, -28.617], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0.643, 6.652], [-28.991, 1.586]], "o": [[0.133, 8.152], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [1.942, -2.176], [-0.895, -1.685], [-23.639, -0.816], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [29.97, 2.594], [0.651, -0.036]], "v": [[133.867, 212.348], [133.867, 217.483], [133.867, 225.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.709], [191.361, -43.138], [195.207, -48.253], [188.69, -49.958], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.357, 225.703], [59.357, 213.723], [131.889, 212.456]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-5.148, 6.075], [0, 0], [0, 0], [-7.928, 7.928], [-5.004, 18.601], [0, 0], [0, 8.925], [0, 0], [11.413, 0], [0, 0], [25.612, 12.163], [-11.529, 6.882], [0.019, 1.807], [2.831, 1.185], [18.303, -14.438], [0, 0], [22.309, -17.444], [0, -26.638], [-35.087, -23.669], [0, 0], [-5.968, -5.271], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [6.057, 5.133], [0, 0], [0, 0], [8.852, -1.426], [15.443, -15.443], [0, 0], [11.413, 0], [0, 0], [0, -8.924], [0, 0], [-8.923, -22.841], [0, 0], [2.199, -1.313], [-0.02, -1.881], [-22.643, -9.482], [0, 0], [-34.067, 0], [-22.333, 17.463], [0, 35.235], [0, 0], [-5.256, 5.951], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[139.246, 221.596], [150.03, 230.735], [159.271, 234.18], [179.563, 232.488], [193.515, 218.89], [164.648, 194.426], [197.82, 180.129], [240.87, 141.282], [257.818, 141.063], [278.5, 124.891], [278.5, 108.97], [257.818, 92.798], [238.134, 93.016], [184.1, 38.775], [204.727, 22.883], [208.106, 18.755], [203.437, 15.022], [132.408, 24.743], [81.924, 24.839], [-5.289, 53.078], [-41.427, 121.293], [17.837, 191.919], [0.626, 211.406], [1.902, 231.729], [6.697, 237.032], [36.355, 227.073], [50.445, 223.313], [139.12, 221.599]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12.666, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.933, -0.684], [0, 0], [0, 0], [-7.404, 8.176], [-4.35, 22.835], [0, 0], [0, 10.956], [0, 0], [9.922, 0], [0, 0], [22.265, 14.932], [-6.392, 20.693], [0.553, 1.029], [1.89, -0.91], [19.115, -21.447], [0, 0], [19.394, -21.416], [0, -32.703], [-35.968, -15.366], [0, 0], [-7.891, 1.063], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.682, 7.91], [0, 0], [0, 0], [10.402, -4.446], [14.422, -15.926], [0, 0], [9.922, 0], [0, 0], [0, -10.956], [0, 0], [-7.757, -28.041], [0, 0], [1.219, -3.947], [-0.749, -1.394], [-15.116, 7.275], [0, 0], [-29.616, 0], [-19.415, 21.439], [0, 43.257], [0, 0], [1.06, 7.868], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[131.292, 212.622], [131.078, 207.205], [130.121, 221.793], [143.242, 237.366], [165.898, 239.32], [169.15, 201.621], [190.183, 179.881], [219.285, 120.698], [224.021, 120.698], [242, 100.844], [242, 81.298], [224.021, 61.445], [216.906, 61.445], [169.933, -5.145], [178.135, -53.946], [180.499, -61.919], [176.758, -62.168], [124.995, -22.372], [81.107, -22.253], [5.29, 12.414], [-26.126, 96.159], [24.466, 198.91], [27.938, 224.676], [44.141, 237.01], [66.677, 233.973], [64.665, 215.911], [63.382, 209.176], [131.195, 212.617]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17.334, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-14.037, 16.275], [-0.145, 1.94], [3.025, 0.276], [13.911, -28.759], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [2.677, -3.104], [0.154, -2.061], [-24.2, -2.207], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[134.184, 213.848], [133.684, 212.483], [133.684, 223.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.708], [190.883, -32.724], [196.207, -40.503], [191.458, -42.702], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.428, 211.703], [59.428, 211.348], [134.077, 213.845]], "c": false}]}, {"t": 22, "s": [{"i": [[-30.367, 1.652], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-16.419, 18.396], [0.632, 1.19], [3.088, 0.107], [13.842, -28.617], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0.643, 6.652], [0, 0]], "o": [[0.133, 8.152], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [1.942, -2.176], [-0.895, -1.685], [-23.639, -0.816], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [30.643, 2.652], [0, 0]], "v": [[133.867, 212.348], [133.867, 217.483], [133.867, 225.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.709], [191.361, -43.138], [195.207, -48.253], [188.69, -49.958], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.357, 225.703], [59.338, 213.723], [132.25, 211.75]], "c": false}]}], "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "quaternary"}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -15, "op": 10, "st": -15, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "Shape fill", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [210.982, 264.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1.982, 43, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[-30.367, 1.652], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-16.419, 18.396], [0.632, 1.19], [3.088, 0.107], [13.842, -28.617], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0.643, 6.652], [0, 0]], "o": [[0.133, 8.152], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [1.942, -2.176], [-0.895, -1.685], [-23.639, -0.816], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [30.643, 2.652], [0, 0]], "v": [[133.867, 212.348], [133.867, 217.483], [133.867, 225.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.709], [191.361, -43.138], [195.207, -48.253], [188.69, -49.958], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.357, 225.703], [59.357, 212.348], [131.875, 212.125]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-5.148, 6.075], [0, 0], [0, 0], [-7.928, 7.928], [-5.004, 18.601], [0, 0], [0, 8.925], [0, 0], [11.413, 0], [0, 0], [25.612, 12.163], [-11.529, 6.882], [0.019, 1.807], [2.831, 1.185], [18.303, -14.438], [0, 0], [22.309, -17.444], [0, -26.638], [-35.087, -23.669], [0, 0], [-5.968, -5.271], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [6.057, 5.133], [0, 0], [0, 0], [8.852, -1.426], [15.443, -15.443], [0, 0], [11.413, 0], [0, 0], [0, -8.924], [0, 0], [-8.923, -22.841], [0, 0], [2.199, -1.313], [-0.02, -1.881], [-22.643, -9.482], [0, 0], [-34.067, 0], [-22.333, 17.463], [0, 35.235], [0, 0], [-5.256, 5.951], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[139.246, 221.596], [150.03, 230.735], [159.271, 234.18], [179.563, 232.488], [193.515, 218.89], [164.648, 194.426], [197.82, 180.129], [240.87, 141.282], [257.818, 141.063], [278.5, 124.891], [278.5, 108.97], [257.818, 92.798], [238.134, 93.016], [184.1, 38.775], [204.727, 22.883], [208.106, 18.755], [203.437, 15.022], [132.408, 24.743], [81.924, 24.839], [-5.289, 53.078], [-41.427, 121.293], [17.837, 191.919], [0.626, 211.406], [1.902, 231.729], [6.697, 237.032], [36.355, 227.073], [50.445, 223.313], [139.054, 221.6]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12.666, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.933, -0.684], [0, 0], [0, 0], [-7.404, 8.176], [-4.35, 22.835], [0, 0], [0, 10.956], [0, 0], [9.922, 0], [0, 0], [22.265, 14.932], [-6.392, 20.693], [0.553, 1.029], [1.89, -0.91], [19.115, -21.447], [0, 0], [19.394, -21.416], [0, -32.703], [-35.968, -15.366], [0, 0], [-7.891, 1.063], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.682, 7.91], [0, 0], [0, 0], [10.402, -4.446], [14.422, -15.926], [0, 0], [9.922, 0], [0, 0], [0, -10.956], [0, 0], [-7.757, -28.041], [0, 0], [1.219, -3.947], [-0.749, -1.394], [-15.116, 7.275], [0, 0], [-29.616, 0], [-19.415, 21.439], [0, 43.257], [0, 0], [1.06, 7.868], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[131.292, 212.622], [131.078, 207.205], [130.121, 221.793], [143.242, 237.366], [165.898, 239.32], [169.15, 201.621], [190.183, 179.881], [219.285, 120.698], [224.021, 120.698], [242, 100.844], [242, 81.298], [224.021, 61.445], [216.906, 61.445], [169.933, -5.145], [178.135, -53.946], [180.499, -61.919], [176.758, -62.168], [124.995, -22.372], [81.107, -22.253], [5.29, 12.414], [-26.126, 96.159], [24.466, 198.91], [27.938, 224.676], [44.141, 237.01], [66.677, 233.973], [64.665, 215.911], [63.382, 209.176], [131.145, 212.615]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17.334, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-14.037, 16.275], [-0.145, 1.94], [3.025, 0.276], [13.911, -28.759], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [2.677, -3.104], [0.154, -2.061], [-24.2, -2.207], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[134.184, 213.848], [133.684, 212.483], [133.684, 223.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.708], [190.883, -32.724], [196.207, -40.503], [191.458, -42.702], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.428, 211.703], [59.428, 211.348], [134.022, 213.843]], "c": false}]}, {"t": 22, "s": [{"i": [[-0.824, 0.045], [0, 0], [0, 0], [-7.962, 0], [0, 0], [0, 0], [-7.928, 7.928], [-4.658, 22.144], [0, 0], [0, 10.624], [0, 0], [10.624, 0], [0, 0], [23.842, 14.479], [-16.419, 18.396], [0.632, 1.19], [3.088, 0.107], [13.842, -28.617], [0, 0], [20.767, -20.767], [0, -31.712], [-33.594, -20.032], [0, 0], [-7.962, 0], [0, 0], [0, 0], [0.643, 6.652], [-28.678, 1.57]], "o": [[0.133, 8.152], [0, 0], [0, 7.94], [0, 0], [0, 0], [9.982, -5.324], [15.443, -15.443], [0, 0], [10.624, 0], [0, 0], [0, -10.624], [0, 0], [-8.307, -27.192], [0, 0], [1.942, -2.176], [-0.895, -1.685], [-23.639, -0.816], [0, 0], [-31.712, 0], [-20.79, 20.79], [0, 41.947], [0, 0], [0, 7.94], [0, 0], [0, 0], [0, 0], [29.812, 2.58], [0.799, -0.044]], "v": [[133.867, 212.348], [133.867, 217.483], [133.867, 225.612], [148.094, 238], [170.834, 238], [170.834, 200.161], [197.82, 180.129], [228.981, 122.739], [234.052, 122.739], [253.305, 103.487], [253.305, 84.533], [234.052, 65.28], [226.434, 65.28], [176.135, 0.709], [191.361, -43.138], [195.207, -48.253], [188.69, -49.958], [128.016, -15.997], [81.021, -15.882], [-0.164, 17.735], [-33.804, 98.943], [22.278, 197.614], [22.278, 223.612], [36.688, 238], [59.428, 238], [59.357, 225.703], [59.357, 212.348], [131.433, 212.481]], "c": false}]}], "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "quaternary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "quaternary"}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -15, "op": 10, "st": -15, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "Shape Layer 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [210.982, 264.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1.982, 43, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 1, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 2, "s": [4]}, {"t": 3, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [99]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 1, "s": [94]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 2, "s": [98]}, {"t": 3, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 202, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.227, 0.2, 0.278, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 1, "s": [5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 2, "s": [4]}, {"t": 3, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [99]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 1, "s": [94]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 2, "s": [98]}, {"t": 3, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 202, "ix": 3}, "m": 1, "ix": 1, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 26.708], [0, -20.873], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -23.961], [0, 17.399], [0, 0], [0, 0]], "v": [[-29.842, 65.426], [-48.773, 65.426], [-74.419, 37.852], [-41.163, 35.617], [-64.025, 52.563], [-88.656, 52.563]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0, -0.266], [0, 0.208], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0.238], [0, -0.173], [0, 0], [0, 0]], "v": [[-37.171, 93.139], [-62.257, 91.737], [-89.807, 92.012], [-54.082, 92.034], [-78.642, 91.865], [-118.101, 91.961]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12.5, "s": [{"i": [[0, 0], [0, 0], [0, 31.663], [0, -24.745], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -28.406], [0, 20.627], [0, 0], [0, 0]], "v": [[-22.426, 61.595], [-40.105, 61.595], [-64.056, 28.906], [-32.998, 26.256], [-54.349, 46.346], [-77.351, 46.346]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17.5, "s": [{"i": [[0, 0], [0, 0], [0, 20.692], [0, -16.171], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -18.564], [0, 13.48], [0, 0], [0, 0]], "v": [[-29.842, 65.426], [-48.773, 65.426], [-74.419, 44.064], [-41.163, 42.332], [-64.025, 55.461], [-88.656, 55.461]], "c": false}]}, {"t": 22.5, "s": [{"i": [[0, 0], [0, 0], [0, 26.708], [0, -20.873], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -23.961], [0, 17.399], [0, 0], [0, 0]], "v": [[-29.842, 65.426], [-48.773, 65.426], [-74.419, 37.852], [-41.163, 35.617], [-64.025, 52.563], [-88.656, 52.563]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 1, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "quaternary"}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "quaternary"}, {"ty": "tr", "p": {"a": 0, "k": [-88, -78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 10, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 0, "nm": "mask", "td": 1, "refId": "comp_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "coin-shadow", "parent": 24, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-88.75, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 15.188], [-15.188, 0], [0, 0], [0, -15.188], [-15.188, 0], [0, 0]], "o": [[0, -15.188], [0, 0], [-15.188, 0], [0, 15.188], [0, 0], [-15.188, 0]], "v": [[-26.25, 0], [1.25, -27.5], [26.25, -27.5], [-1.25, 0], [26.25, 27.5], [1.25, 27.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "coin-shadow", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 18, "op": 300, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "coin-3", "tt": 2, "tp": 22, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 19, "s": [-71]}, {"t": 26, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [84, 262.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [84, 271.071, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 32, "s": [84, 312.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-81, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [{"i": [[0, 0], [0, -15.188], [-15.188, 0], [0, 0], [0, 15.188], [15.188, 0]], "o": [[-15.188, 0], [0, 15.188], [0, 0], [15.188, 0], [0, -15.188], [0, 0]], "v": [[-87.5, -27.5], [-115, 0], [-87.5, 27.5], [-85.797, 27.944], [-58.297, 0.444], [-85.797, -27.056]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [0, -15.188], [-15.188, 0], [0, 0], [0, 15.188], [15.188, 0]], "o": [[-15.188, 0], [0, 15.188], [0, 0], [15.188, 0], [0, -15.188], [0, 0]], "v": [[-87.5, -27.5], [-115, 0], [-87.5, 27.5], [87.5, 27.5], [115, 0], [87.5, -27.5]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}], "ip": 18, "op": 1801, "st": 1, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "coin-shadow 4", "parent": 26, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-88.75, -1, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 15.188], [-15.188, 0], [0, 0], [0, -15.188], [-15.188, 0], [0, 0]], "o": [[0, -15.188], [0, 0], [-15.188, 0], [0, 15.188], [0, 0], [-15.188, 0]], "v": [[-26.25, 0], [1.25, -27.5], [26.25, -27.5], [-1.25, 0], [26.25, 27.5], [1.25, 27.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "coin-shadow", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 35, "op": 300, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "coin-4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 35, "s": [46]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [20]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 42, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 46, "s": [-11]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 55, "s": [7]}, {"t": 60, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [161, 3.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [161, 112.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [155, 147.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [151, 133.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [155, 148.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 55, "s": [156, 137.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 60, "s": [155, 148.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -15.188], [-15.188, 0], [0, 0], [0, 15.188], [15.188, 0]], "o": [[-15.188, 0], [0, 15.188], [0, 0], [15.188, 0], [0, -15.188], [0, 0]], "v": [[-87.5, -27.5], [-115, 0], [-87.5, 27.5], [87.5, 27.5], [115, 0], [87.5, -27.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "rd", "nm": "Round Corners 1", "r": {"a": 0, "k": 27.5, "ix": 1}, "ix": 2, "mn": "ADBE Vector Filter - RC", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 30, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}], "ip": 35, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "coin-shadow 3", "parent": 28, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-88.75, -1, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 15.188], [-15.188, 0], [0, 0], [0, -15.188], [-15.188, 0], [0, 0]], "o": [[0, -15.188], [0, 0], [-15.188, 0], [0, 15.188], [0, 0], [-15.188, 0]], "v": [[-26.25, 0], [1.25, -27.5], [26.25, -27.5], [-1.25, 0], [26.25, 27.5], [1.25, 27.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "coin-shadow", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 29, "op": 300, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "coin-5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 29, "s": [-12]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 34, "s": [-29.429]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 36, "s": [-22]}, {"t": 39, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [203, 31.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [194.716, 120.627, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [182, 166.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 39, "s": [190, 203.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -15.188], [-15.188, 0], [0, 0], [0, 15.188], [15.188, 0]], "o": [[-15.188, 0], [0, 15.188], [0, 0], [15.188, 0], [0, -15.188], [0, 0]], "v": [[-87.5, -27.5], [-115, 0], [-87.5, 27.5], [87.5, 27.5], [115, 0], [87.5, -27.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "rd", "nm": "Round Corners 1", "r": {"a": 0, "k": 27.5, "ix": 1}, "ix": 2, "mn": "ADBE Vector Filter - RC", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}], "ip": 29, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 4, "nm": "coin-shadow 2", "parent": 30, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 0, "k": [88.75, -19.714, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 15.188], [-15.188, 0], [0, 0], [0, -15.188], [-15.188, 0], [0, 0]], "o": [[0, -15.188], [0, 0], [-15.188, 0], [0, 15.188], [0, 0], [-15.188, 0]], "v": [[-26.25, 0], [1.25, -27.5], [26.25, -27.5], [-1.25, 0], [26.25, 27.5], [1.25, 27.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "coin-shadow", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 24, "op": 300, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 30, "ty": 4, "nm": "coin-6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24, "s": [-119]}, {"t": 36, "s": [-180]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [245, 152.786, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [245, 252.786, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 36, "s": [245, 263.786, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-90, -26, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -15.188], [-15.188, 0], [0, 0], [0, 15.188], [15.188, 0]], "o": [[-15.188, 0], [0, 15.188], [0, 0], [15.188, 0], [0, -15.188], [0, 0]], "v": [[-87.5, -27.5], [-115, 0], [-87.5, 27.5], [87.5, 27.5], [115, 0], [87.5, -27.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, -20], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 30, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}], "ip": 24, "op": 1805, "st": 5, "ct": 1, "bm": 0}]}, {"id": "comp_6", "nm": "coins", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "coin 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [170, 285, 0], "to": [-14.5, -243.833, 0], "ti": [0, 0, 0]}, {"t": 36, "s": [-91, -97, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [3, -120, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 15, "op": 1808, "st": 8, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Vector 8", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-19.83, -119.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[8.502, -9.38], [0.001, -12.66], [0, 0], [-8.502, -9.38], [-12.599, -1.241], [1.69, 0], [9.564, 9.564], [0, 13.526], [-9.564, 9.564], [-13.526, 0], [-1.64, -0.16]], "o": [[-8.502, 9.38], [0, 0], [0.001, 12.66], [8.502, 9.38], [-1.65, 0.16], [-13.526, 0], [-9.564, -9.564], [0, -13.526], [9.564, -9.564], [1.7, 0], [-12.599, 1.241]], "v": [[-4.787, -34.246], [-18, 0], [-18, 0], [-4.787, 34.246], [28, 50.75], [23, 51], [-13.062, 36.062], [-28, 0], [-13.062, -36.062], [23, -51], [28, -50.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 15, "op": 309, "st": 9, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Vector 7", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [3.18, -119.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-8.387, 5.604], [-10.087, 0], [-9.564, -9.564], [0, -13.526], [5.604, -8.387], [9.319, -3.86], [9.893, 1.968], [7.132, 7.132], [1.968, 9.893], [-3.86, 9.319]], "o": [[8.387, -5.604], [13.526, 0], [9.564, 9.564], [0, 10.087], [-5.604, 8.387], [-9.319, 3.86], [-9.893, -1.968], [-7.132, -7.132], [-1.968, -9.893], [3.86, -9.319]], "v": [[-28.334, -42.405], [0, -51], [36.062, -36.062], [51, 0], [42.405, 28.334], [19.517, 47.118], [-9.95, 50.02], [-36.062, 36.062], [-50.02, 9.95], [-47.118, -19.517]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 15, "op": 309, "st": 9, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 3, "nm": "coin 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [263, 280, 0], "to": [-57, -207.833, 0], "ti": [0, 0, 0]}, {"t": 34, "s": [440, -109, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [3, -120, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 12, "op": 1805, "st": 5, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Vector 6", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-20.33, -119.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[8.502, -9.38], [0.001, -12.66], [0, 0], [-8.502, -9.38], [-12.599, -1.241], [1.69, 0], [9.564, 9.564], [0, 13.526], [-9.564, 9.564], [-13.526, 0], [-1.64, -0.16]], "o": [[-8.502, 9.38], [0, 0], [0.001, 12.66], [8.502, 9.38], [-1.65, 0.16], [-13.526, 0], [-9.564, -9.564], [0, -13.526], [9.564, -9.564], [1.7, 0], [-12.599, 1.241]], "v": [[-4.787, -34.246], [-18, 0], [-18, 0], [-4.787, 34.246], [28, 50.75], [23, 51], [-13.062, 36.062], [-28, 0], [-13.062, -36.062], [23, -51], [28, -50.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 12, "op": 312, "st": 12, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Vector 5", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [2.68, -119.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-8.387, 5.604], [-10.087, 0], [-9.564, -9.564], [0, -13.526], [5.604, -8.387], [9.319, -3.86], [9.893, 1.968], [7.132, 7.132], [1.968, 9.893], [-3.86, 9.319]], "o": [[8.387, -5.604], [13.526, 0], [9.564, 9.564], [0, 10.087], [-5.604, 8.387], [-9.319, 3.86], [-9.893, -1.968], [-7.132, -7.132], [-1.968, -9.893], [3.86, -9.319]], "v": [[-28.334, -42.405], [0, -51], [36.062, -36.062], [51, 0], [42.405, 28.334], [19.517, 47.118], [-9.95, 50.02], [-36.062, 36.062], [-50.02, 9.95], [-47.118, -19.517]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 12, "op": 312, "st": 12, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 3, "nm": "coin 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9, "s": [218, 280, 0], "to": [-16.5, -201.833, 0], "ti": [0, 0, 0]}, {"t": 28, "s": [-91, -97, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [3, -120, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 9, "op": 1802, "st": 2, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Vector 4", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-20.33, -119.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[8.502, -9.38], [0.001, -12.66], [0, 0], [-8.502, -9.38], [-12.599, -1.241], [1.69, 0], [9.564, 9.564], [0, 13.526], [-9.564, 9.564], [-13.526, 0], [-1.64, -0.16]], "o": [[-8.502, 9.38], [0, 0], [0.001, 12.66], [8.502, 9.38], [-1.65, 0.16], [-13.526, 0], [-9.564, -9.564], [0, -13.526], [9.564, -9.564], [1.7, 0], [-12.599, 1.241]], "v": [[-4.787, -34.246], [-18, 0], [-18, 0], [-4.787, 34.246], [28, 50.75], [23, 51], [-13.062, 36.062], [-28, 0], [-13.062, -36.062], [23, -51], [28, -50.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 9, "op": 309, "st": 9, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Vector 3", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [2.68, -119.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-8.387, 5.604], [-10.087, 0], [-9.564, -9.564], [0, -13.526], [5.604, -8.387], [9.319, -3.86], [9.893, 1.968], [7.132, 7.132], [1.968, 9.893], [-3.86, 9.319]], "o": [[8.387, -5.604], [13.526, 0], [9.564, 9.564], [0, 10.087], [-5.604, 8.387], [-9.319, 3.86], [-9.893, -1.968], [-7.132, -7.132], [-1.968, -9.893], [3.86, -9.319]], "v": [[-28.334, -42.405], [0, -51], [36.062, -36.062], [51, 0], [42.405, 28.334], [19.517, 47.118], [-9.95, 50.02], [-36.062, 36.062], [-50.02, 9.95], [-47.118, -19.517]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 9, "op": 309, "st": 9, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 3, "nm": "coin", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [218, 280, 0], "to": [-27, -157.833, 0], "ti": [0, 0, 0]}, {"t": 26, "s": [440, -109, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [3, -120, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 7, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Vector 2", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-20.33, -119.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[8.502, -9.38], [0.001, -12.66], [0, 0], [-8.502, -9.38], [-12.599, -1.241], [1.69, 0], [9.564, 9.564], [0, 13.526], [-9.564, 9.564], [-13.526, 0], [-1.64, -0.16]], "o": [[-8.502, 9.38], [0, 0], [0.001, 12.66], [8.502, 9.38], [-1.65, 0.16], [-13.526, 0], [-9.564, -9.564], [0, -13.526], [9.564, -9.564], [1.7, 0], [-12.599, 1.241]], "v": [[-4.787, -34.246], [-18, 0], [-18, 0], [-4.787, 34.246], [28, 50.75], [23, 51], [-13.062, 36.062], [-28, 0], [-13.062, -36.062], [23, -51], [28, -50.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 7, "op": 300, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Vector", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [2.68, -119.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-8.387, 5.604], [-10.087, 0], [-9.564, -9.564], [0, -13.526], [5.604, -8.387], [9.319, -3.86], [9.893, 1.968], [7.132, 7.132], [1.968, 9.893], [-3.86, 9.319]], "o": [[8.387, -5.604], [13.526, 0], [9.564, 9.564], [0, 10.087], [-5.604, 8.387], [-9.319, 3.86], [-9.893, -1.968], [-7.132, -7.132], [-1.968, -9.893], [3.86, -9.319]], "v": [[-28.334, -42.405], [0, -51], [36.062, -36.062], [51, 0], [42.405, 28.334], [19.517, 47.118], [-9.95, 50.02], [-36.062, 36.062], [-50.02, 9.95], [-47.118, -19.517]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-453-savings-pig').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 7, "op": 300, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_7", "nm": "mask", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "coin 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [170, 285, 0], "to": [-14.5, -243.833, 0], "ti": [0, 0, 0]}, {"t": 36, "s": [-91, -97, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [3, -120, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[28.167, 0], [0, -28.167], [-28.167, 0], [0, 28.167]], "o": [[-28.167, 0], [0, 28.167], [28.167, 0], [0, -28.167]], "v": [[7, -69.5], [-44, -18.5], [7, 32.5], [58, -18.5]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-4, -101.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 15, "op": 1808, "st": 8, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [-9]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [-20]}, {"t": 29, "s": [-45]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [94.064, 305.617, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [85.064, 312.117, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [52.064, 286.617, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [5.992, 273.617, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 29, "s": [-55.436, 269.117, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-120.936, 90.617, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-40, -21.5], [0, 0], [-9, -4], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [40, 21.5], [0, 0], [9, 4], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-160.5, 15], [-186.5, 142.5], [-99.5, 144], [-76, 165], [-63.784, 158.057], [-49.129, 138.296], [-74.5, 117], [-91, 120.5], [-80, 80], [-134.5, 69.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156881094, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.070588000119, 0.074510000646, 0.192157000303, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 1, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [1, 0.78, 0.22], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.698, 0.408, 0.212], "ix": 1}}]}, {"ty": 5, "nm": "tertiary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.227, 0.2, 0.278], "ix": 1}}]}, {"ty": 5, "nm": "quaternary", "np": 3, "mn": "ADBE Color Control", "ix": 4, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [1, 1, 1], "ix": 1}}]}], "ip": 0, "op": 201, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-pinch", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 70, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-pinch", "dr": 60}], "props": {}}