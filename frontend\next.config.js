/** @type {import('next').NextConfig} */

const nextConfig = {
  reactStrictMode: true,
  eslint: {
    dirs: ['app', 'common'],
  },
  images: {
    dangerouslyAllowSVG: true,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'bronze-deep-gazelle-81.mypinata.cloud',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'iad.microlink.io',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'api.dreamstarter.xyz',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'mediapilot.dreamstarter.xyz',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'kajabi-storefronts-production.kajabi-cdn.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        pathname: '/**',
      },
    ],
    minimumCacheTTL: 60,
    deviceSizes: [640, 750, 828, 1080, 1200, 1920],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
}

export default nextConfig
