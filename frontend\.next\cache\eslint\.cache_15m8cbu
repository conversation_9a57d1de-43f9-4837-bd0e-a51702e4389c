[{"C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\about\\client.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\about\\layout.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\about\\page.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\about\\structuredData.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\api\\categories\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\api\\pinata\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\error.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\dreamathonSection.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\enhancedDevelopmentProcess.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\hero.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\hooks\\useGetIdeas.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\layout.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\page.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\statsSection.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\trendingProjects.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\layout.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\loading.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\metaData.ts": "18", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\not-found.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\privacy\\layout.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\privacy\\page.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\robots.ts": "22", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\sitemap.ts": "23", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\terms\\layout.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\terms\\page.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\aiBot\\index.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\animatedText\\index.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\backgroundGradientAnimation\\index.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\blockchainAnimation\\index.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\button\\animatedBorderButton.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\button\\index.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\button\\types.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\circularSpinner\\index.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\clientOnly\\index.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\connectButton\\index.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\ensResolver\\index.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\globe\\index.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\index.ts": "38", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\input\\index.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\input\\types.ts": "40", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\link\\index.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\link\\types.ts": "42", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\linkStyled\\index.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\loader\\index.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\numberInput\\index.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\placeholderAndVanishInput\\index.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\shaderGradient\\index.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\terminal\\index.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\textArea\\index.tsx": "49", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\textArea\\types.ts": "50", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\tooltip\\index.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\typewriterEffect\\index.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\AnalyticsBoardIcon.tsx": "53", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\BlockchainIcon.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\BuildingIcon.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\CheckBadgeIcon.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\CheckSquareIcon.tsx": "57", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\ClaudeAIIcon.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\ColorPaletteIcon.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\ComputerChipFlashIcon.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\CopyPasteIcon.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\DeleteIcon.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\DexScreenerIcon.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\DownloadCloudIcon.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\ExclamationErrorIcon.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\FacebookIcon.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\FlagTrophyIcon.tsx": "67", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\GridIcon.tsx": "68", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\HeartBreakIcon.tsx": "69", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\HRTeamIcon.tsx": "70", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\index.ts": "71", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\InfoIcon.tsx": "72", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\InstagramIcon.tsx": "73", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\LaunchGoIcon.tsx": "74", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\LinkedInColorIcon.tsx": "75", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\LinkedInIcon.tsx": "76", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\ListIcon.tsx": "77", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\LogoDevAgentIcon.tsx": "78", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\LogoIcon.tsx": "79", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\LogoutIcon.tsx": "80", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\MoneyBagIcon.tsx": "81", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\MonitorUploadIcon.tsx": "82", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\MultipleUsersIcon.tsx": "83", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\NavIcon.tsx": "84", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\OfficeEmployeeIcon.tsx": "85", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\PictureLandscapeIcon.tsx": "86", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\PinterestIcon.tsx": "87", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\PolygonIcon.tsx": "88", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\QuestionIcon.tsx": "89", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\SearchIcon.tsx": "90", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\SunIcon.tsx": "91", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\SunriseIcon.tsx": "92", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\SunsetIcon.tsx": "93", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\TelegramIcon.tsx": "94", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\TelegramMainIcon.tsx": "95", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\TokenXIcon.tsx": "96", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\TrophyIcon.tsx": "97", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\TwitterIcon.tsx": "98", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\UniswapIcon.tsx": "99", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\UniswapTokenIcon.tsx": "100", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\UploadIcon.tsx": "101", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\WalletIcon.tsx": "102", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\XIcon.tsx": "103", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\YoutubeIcon.tsx": "104", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\accordion\\index.tsx": "105", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\backgroundBeamsWithCollision\\index.tsx": "106", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\carousel\\CarouselContainer.tsx": "107", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\carousel\\EmblaCarouselArrowButtons.tsx": "108", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\carousel\\index.tsx": "109", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\chart\\index.tsx": "110", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\index.ts": "111", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\input\\index.tsx": "112", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\input\\types.ts": "113", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\integrations\\index.tsx": "114", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\multiStepLoader\\index.tsx": "115", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\progress\\index.tsx": "116", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\reviewProgress\\index.tsx": "117", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\shootingStars\\index.tsx": "118", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\stakeProgress\\index.tsx": "119", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\swiper\\index.tsx": "120", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\switch\\index.tsx": "121", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\tabs\\index.tsx": "122", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\toast\\config.tsx": "123", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\toast\\index.tsx": "124", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\organisms\\footer\\index.tsx": "125", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\organisms\\header\\index.tsx": "126", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\organisms\\index.ts": "127", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\organisms\\modal\\defaultModal.tsx": "128", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\organisms\\modal\\index.tsx": "129", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\constants.ts": "130", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\index.ts": "131", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\types.ts": "132", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useCopyToClipboard.ts": "133", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useDebounce.ts": "134", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useInterval.ts": "135", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useIsClient.ts": "136", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useLocalStorage.ts": "137", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useOutsideClick.ts": "138", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\usePasswordEncryption.ts": "139", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useToggle.ts": "140", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useWindowDimensions.ts": "141", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lang.ts": "142", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\bookmarkLottie.tsx": "143", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\buyCartLottie.tsx": "144", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\deleteLottie.tsx": "145", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\devLottie.tsx": "146", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\editLottie.tsx": "147", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\equityLottie.tsx": "148", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\globeLottie.tsx": "149", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\magicLottie.tsx": "150", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\piggyLottie.tsx": "151", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\searchLottie.tsx": "152", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\socialLottie.tsx": "153", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\starLottie.tsx": "154", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\starsLottie.tsx": "155", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\wizardLottie.tsx": "156", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\providers.tsx": "157", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\routes.ts": "158", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\theme\\themeElements.ts": "159", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\types.ts": "160", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\blurDataUrl.ts": "161", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\helpers.ts": "162", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\localFont.ts": "163", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\mixpanel\\eventTriggers.ts": "164", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\mixpanel\\index.ts": "165", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\mixpanel\\types.ts": "166", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\network\\baseFetcher.ts": "167", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\network\\endpoints.ts": "168", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\pinata\\config.ts": "169", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\supabase\\client.ts": "170", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\tailwindPlugins\\animations.ts": "171", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\luminaCasaSection.tsx": "172", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\Hero.tsx": "173", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\layout.tsx": "174", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\page.tsx": "175", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\DataDrivenInsights.tsx": "176", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\Join.tsx": "177", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\ScientificApproach.tsx": "178", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\Teams.tsx": "179", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\Tokenomics.tsx": "180", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\ChainSelector.tsx": "181", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\DooglyInterface.tsx": "182", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\TokenSaleCard.tsx": "183", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\TokenSelector.tsx": "184", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\doogly\\adapter\\HttpAdapter.ts": "185", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\doogly\\constants.js": "186", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\doogly\\handlers\\solana.ts": "187", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\doogly\\types\\cosmos.ts": "188", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\doogly\\types\\ethers.ts": "189", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\doogly\\types\\http.ts": "190", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\doogly\\types\\index.ts": "191", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\AboutLuminaCasa.tsx": "192", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\Roadmap.tsx": "193", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\hooks\\useEthPrice.ts": "194", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\network\\priceApi.ts": "195", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\RecentTransactionsCard.tsx": "196", "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\hooks\\useAddressTransactions.ts": "197"}, {"size": 12770, "mtime": 1746897543003, "results": "198", "hashOfConfig": "199"}, {"size": 299, "mtime": 1744813444265, "results": "200", "hashOfConfig": "199"}, {"size": 4149, "mtime": 1746897617068, "results": "201", "hashOfConfig": "199"}, {"size": 2770, "mtime": 1746898330935, "results": "202", "hashOfConfig": "199"}, {"size": 1186, "mtime": 1734173509738, "results": "203", "hashOfConfig": "199"}, {"size": 585, "mtime": 1734173509739, "results": "204", "hashOfConfig": "199"}, {"size": 117, "mtime": 1734173509739, "results": "205", "hashOfConfig": "199"}, {"size": 3532, "mtime": 1744984010143, "results": "206", "hashOfConfig": "199"}, {"size": 7411, "mtime": 1744984010144, "results": "207", "hashOfConfig": "199"}, {"size": 4648, "mtime": 1749925420124, "results": "208", "hashOfConfig": "199"}, {"size": 5406, "mtime": 1744957831650, "results": "209", "hashOfConfig": "199"}, {"size": 243, "mtime": 1734173509747, "results": "210", "hashOfConfig": "199"}, {"size": 3789, "mtime": 1749925402204, "results": "211", "hashOfConfig": "199"}, {"size": 7819, "mtime": 1744984010147, "results": "212", "hashOfConfig": "199"}, {"size": 9015, "mtime": 1749925651636, "results": "213", "hashOfConfig": "199"}, {"size": 832, "mtime": 1746902444767, "results": "214", "hashOfConfig": "199"}, {"size": 142, "mtime": 1734173509751, "results": "215", "hashOfConfig": "199"}, {"size": 2068, "mtime": 1746897394532, "results": "216", "hashOfConfig": "199"}, {"size": 1084, "mtime": 1744957831673, "results": "217", "hashOfConfig": "199"}, {"size": 462, "mtime": 1746898485694, "results": "218", "hashOfConfig": "199"}, {"size": 16118, "mtime": 1746897811417, "results": "219", "hashOfConfig": "199"}, {"size": 569, "mtime": 1746897406547, "results": "220", "hashOfConfig": "199"}, {"size": 1841, "mtime": 1746941165781, "results": "221", "hashOfConfig": "199"}, {"size": 452, "mtime": 1746897801011, "results": "222", "hashOfConfig": "199"}, {"size": 11886, "mtime": 1746897784937, "results": "223", "hashOfConfig": "199"}, {"size": 407, "mtime": 1734173509766, "results": "224", "hashOfConfig": "199"}, {"size": 395, "mtime": 1744957831682, "results": "225", "hashOfConfig": "199"}, {"size": 6108, "mtime": 1744957831683, "results": "226", "hashOfConfig": "199"}, {"size": 258805, "mtime": 1744957855564, "results": "227", "hashOfConfig": "199"}, {"size": 4385, "mtime": 1744957831687, "results": "228", "hashOfConfig": "199"}, {"size": 1690, "mtime": 1744957831688, "results": "229", "hashOfConfig": "199"}, {"size": 1330, "mtime": 1744957831689, "results": "230", "hashOfConfig": "199"}, {"size": 176, "mtime": 1747103215081, "results": "231", "hashOfConfig": "199"}, {"size": 332, "mtime": 1744957831690, "results": "232", "hashOfConfig": "199"}, {"size": 8861, "mtime": 1744987608042, "results": "233", "hashOfConfig": "199"}, {"size": 832, "mtime": 1744957831693, "results": "234", "hashOfConfig": "199"}, {"size": 3712, "mtime": 1744959641374, "results": "235", "hashOfConfig": "199"}, {"size": 1026, "mtime": 1749925102294, "results": "236", "hashOfConfig": "199"}, {"size": 2142, "mtime": 1744813444368, "results": "237", "hashOfConfig": "199"}, {"size": 1176, "mtime": 1734173509773, "results": "238", "hashOfConfig": "199"}, {"size": 1596, "mtime": 1744957831697, "results": "239", "hashOfConfig": "199"}, {"size": 1067, "mtime": 1744813444370, "results": "240", "hashOfConfig": "199"}, {"size": 659, "mtime": 1744957831699, "results": "241", "hashOfConfig": "199"}, {"size": 347, "mtime": 1734173509774, "results": "242", "hashOfConfig": "199"}, {"size": 4921, "mtime": 1744813444374, "results": "243", "hashOfConfig": "199"}, {"size": 10521, "mtime": 1744957831703, "results": "244", "hashOfConfig": "199"}, {"size": 1321, "mtime": 1747033125140, "results": "245", "hashOfConfig": "199"}, {"size": 2985, "mtime": 1747024443285, "results": "246", "hashOfConfig": "199"}, {"size": 2009, "mtime": 1744813444377, "results": "247", "hashOfConfig": "199"}, {"size": 1170, "mtime": 1744813444379, "results": "248", "hashOfConfig": "199"}, {"size": 1193, "mtime": 1744957831706, "results": "249", "hashOfConfig": "199"}, {"size": 3168, "mtime": 1747032823758, "results": "250", "hashOfConfig": "199"}, {"size": 1059, "mtime": 1744957831708, "results": "251", "hashOfConfig": "199"}, {"size": 1495, "mtime": 1744957831709, "results": "252", "hashOfConfig": "199"}, {"size": 1418, "mtime": 1744957831709, "results": "253", "hashOfConfig": "199"}, {"size": 2218, "mtime": 1744957831710, "results": "254", "hashOfConfig": "199"}, {"size": 779, "mtime": 1744957831710, "results": "255", "hashOfConfig": "199"}, {"size": 4191, "mtime": 1734173509786, "results": "256", "hashOfConfig": "199"}, {"size": 3487, "mtime": 1744957831711, "results": "257", "hashOfConfig": "199"}, {"size": 1817, "mtime": 1744957831712, "results": "258", "hashOfConfig": "199"}, {"size": 1719, "mtime": 1744957831712, "results": "259", "hashOfConfig": "199"}, {"size": 937, "mtime": 1744957831713, "results": "260", "hashOfConfig": "199"}, {"size": 2099, "mtime": 1744813444387, "results": "261", "hashOfConfig": "199"}, {"size": 1269, "mtime": 1744957831714, "results": "262", "hashOfConfig": "199"}, {"size": 451, "mtime": 1734173509786, "results": "263", "hashOfConfig": "199"}, {"size": 936, "mtime": 1744957831716, "results": "264", "hashOfConfig": "199"}, {"size": 1444, "mtime": 1744957831716, "results": "265", "hashOfConfig": "199"}, {"size": 2343, "mtime": 1744957831717, "results": "266", "hashOfConfig": "199"}, {"size": 996, "mtime": 1744957831719, "results": "267", "hashOfConfig": "199"}, {"size": 2680, "mtime": 1744957831717, "results": "268", "hashOfConfig": "199"}, {"size": 2408, "mtime": 1746979615505, "results": "269", "hashOfConfig": "199"}, {"size": 1538, "mtime": 1744957831719, "results": "270", "hashOfConfig": "199"}, {"size": 1261, "mtime": 1744957831720, "results": "271", "hashOfConfig": "199"}, {"size": 772, "mtime": 1744957831721, "results": "272", "hashOfConfig": "199"}, {"size": 1517, "mtime": 1744957831722, "results": "273", "hashOfConfig": "199"}, {"size": 1193, "mtime": 1744813444422, "results": "274", "hashOfConfig": "199"}, {"size": 1708, "mtime": 1744957831723, "results": "275", "hashOfConfig": "199"}, {"size": 965, "mtime": 1744813444425, "results": "276", "hashOfConfig": "199"}, {"size": 1731, "mtime": 1744957831724, "results": "277", "hashOfConfig": "199"}, {"size": 690, "mtime": 1744957831725, "results": "278", "hashOfConfig": "199"}, {"size": 1746, "mtime": 1744957831726, "results": "279", "hashOfConfig": "199"}, {"size": 2352, "mtime": 1744957831726, "results": "280", "hashOfConfig": "199"}, {"size": 2039, "mtime": 1744957831727, "results": "281", "hashOfConfig": "199"}, {"size": 3145, "mtime": 1744957831727, "results": "282", "hashOfConfig": "199"}, {"size": 1815, "mtime": 1744957831729, "results": "283", "hashOfConfig": "199"}, {"size": 1756, "mtime": 1744957831729, "results": "284", "hashOfConfig": "199"}, {"size": 992, "mtime": 1744957831731, "results": "285", "hashOfConfig": "199"}, {"size": 653, "mtime": 1744813444431, "results": "286", "hashOfConfig": "199"}, {"size": 1391, "mtime": 1744957831732, "results": "287", "hashOfConfig": "199"}, {"size": 1153, "mtime": 1744957831733, "results": "288", "hashOfConfig": "199"}, {"size": 745, "mtime": 1744957831734, "results": "289", "hashOfConfig": "199"}, {"size": 796, "mtime": 1744957831734, "results": "290", "hashOfConfig": "199"}, {"size": 671, "mtime": 1744957831734, "results": "291", "hashOfConfig": "199"}, {"size": 1003, "mtime": 1737256250488, "results": "292", "hashOfConfig": "199"}, {"size": 1527, "mtime": 1737256250488, "results": "293", "hashOfConfig": "199"}, {"size": 492, "mtime": 1734173509789, "results": "294", "hashOfConfig": "199"}, {"size": 1477, "mtime": 1744957831735, "results": "295", "hashOfConfig": "199"}, {"size": 882, "mtime": 1734173509789, "results": "296", "hashOfConfig": "199"}, {"size": 4949, "mtime": 1744813444436, "results": "297", "hashOfConfig": "199"}, {"size": 4800, "mtime": 1744813444436, "results": "298", "hashOfConfig": "199"}, {"size": 958, "mtime": 1744957831736, "results": "299", "hashOfConfig": "199"}, {"size": 1336, "mtime": 1744957831736, "results": "300", "hashOfConfig": "199"}, {"size": 865, "mtime": 1744957831738, "results": "301", "hashOfConfig": "199"}, {"size": 985, "mtime": 1744957831739, "results": "302", "hashOfConfig": "199"}, {"size": 2062, "mtime": 1744957831742, "results": "303", "hashOfConfig": "199"}, {"size": 7233, "mtime": 1744957831743, "results": "304", "hashOfConfig": "199"}, {"size": 4391, "mtime": 1744957831744, "results": "305", "hashOfConfig": "199"}, {"size": 2829, "mtime": 1734173509794, "results": "306", "hashOfConfig": "199"}, {"size": 588, "mtime": 1734173509794, "results": "307", "hashOfConfig": "199"}, {"size": 4931, "mtime": 1744957831745, "results": "308", "hashOfConfig": "199"}, {"size": 621, "mtime": 1749925623839, "results": "309", "hashOfConfig": "199"}, {"size": 2105, "mtime": 1744813444441, "results": "310", "hashOfConfig": "199"}, {"size": 697, "mtime": 1734173509796, "results": "311", "hashOfConfig": "199"}, {"size": 4882, "mtime": 1744984010162, "results": "312", "hashOfConfig": "199"}, {"size": 6734, "mtime": 1744957831751, "results": "313", "hashOfConfig": "199"}, {"size": 1985, "mtime": 1744957831752, "results": "314", "hashOfConfig": "199"}, {"size": 1189, "mtime": 1744957831752, "results": "315", "hashOfConfig": "199"}, {"size": 3947, "mtime": 1744957831753, "results": "316", "hashOfConfig": "199"}, {"size": 1173, "mtime": 1744957831754, "results": "317", "hashOfConfig": "199"}, {"size": 4554, "mtime": 1744957831755, "results": "318", "hashOfConfig": "199"}, {"size": 1176, "mtime": 1744957831756, "results": "319", "hashOfConfig": "199"}, {"size": 5039, "mtime": 1744957831757, "results": "320", "hashOfConfig": "199"}, {"size": 382, "mtime": 1744957831758, "results": "321", "hashOfConfig": "199"}, {"size": 1308, "mtime": 1744957831759, "results": "322", "hashOfConfig": "199"}, {"size": 3783, "mtime": 1744813444464, "results": "323", "hashOfConfig": "199"}, {"size": 8777, "mtime": 1749925290114, "results": "324", "hashOfConfig": "199"}, {"size": 293, "mtime": 1749925199865, "results": "325", "hashOfConfig": "199"}, {"size": 3542, "mtime": 1744957831767, "results": "326", "hashOfConfig": "199"}, {"size": 7154, "mtime": 1744984010170, "results": "327", "hashOfConfig": "199"}, {"size": 7209, "mtime": 1747589027836, "results": "328", "hashOfConfig": "199"}, {"size": 467, "mtime": 1749925312351, "results": "329", "hashOfConfig": "199"}, {"size": 301, "mtime": 1734173509813, "results": "330", "hashOfConfig": "199"}, {"size": 696, "mtime": 1744957831770, "results": "331", "hashOfConfig": "199"}, {"size": 396, "mtime": 1744957831771, "results": "332", "hashOfConfig": "199"}, {"size": 460, "mtime": 1744957831771, "results": "333", "hashOfConfig": "199"}, {"size": 222, "mtime": 1744957831772, "results": "334", "hashOfConfig": "199"}, {"size": 1802, "mtime": 1744957831772, "results": "335", "hashOfConfig": "199"}, {"size": 570, "mtime": 1734173509813, "results": "336", "hashOfConfig": "199"}, {"size": 793, "mtime": 1737256250516, "results": "337", "hashOfConfig": "199"}, {"size": 409, "mtime": 1744957831773, "results": "338", "hashOfConfig": "199"}, {"size": 1821, "mtime": 1734173509813, "results": "339", "hashOfConfig": "199"}, {"size": 18918, "mtime": 1746897020054, "results": "340", "hashOfConfig": "199"}, {"size": 572, "mtime": 1744957831774, "results": "341", "hashOfConfig": "199"}, {"size": 505, "mtime": 1744957831775, "results": "342", "hashOfConfig": "199"}, {"size": 501, "mtime": 1744957831775, "results": "343", "hashOfConfig": "199"}, {"size": 562, "mtime": 1744957831776, "results": "344", "hashOfConfig": "199"}, {"size": 563, "mtime": 1744957831777, "results": "345", "hashOfConfig": "199"}, {"size": 549, "mtime": 1744957831777, "results": "346", "hashOfConfig": "199"}, {"size": 607, "mtime": 1744957831778, "results": "347", "hashOfConfig": "199"}, {"size": 527, "mtime": 1744957831779, "results": "348", "hashOfConfig": "199"}, {"size": 662, "mtime": 1744957831779, "results": "349", "hashOfConfig": "199"}, {"size": 592, "mtime": 1744957831781, "results": "350", "hashOfConfig": "199"}, {"size": 567, "mtime": 1744957831781, "results": "351", "hashOfConfig": "199"}, {"size": 488, "mtime": 1744957831782, "results": "352", "hashOfConfig": "199"}, {"size": 407, "mtime": 1744957831783, "results": "353", "hashOfConfig": "199"}, {"size": 492, "mtime": 1744957831783, "results": "354", "hashOfConfig": "199"}, {"size": 4181, "mtime": 1747077967543, "results": "355", "hashOfConfig": "199"}, {"size": 2127, "mtime": 1746937471859, "results": "356", "hashOfConfig": "199"}, {"size": 4632, "mtime": 1744984010174, "results": "357", "hashOfConfig": "199"}, {"size": 1152, "mtime": 1744813444493, "results": "358", "hashOfConfig": "199"}, {"size": 41321, "mtime": 1734173509828, "results": "359", "hashOfConfig": "199"}, {"size": 11579, "mtime": 1744957831788, "results": "360", "hashOfConfig": "199"}, {"size": 1518, "mtime": 1734173509828, "results": "361", "hashOfConfig": "199"}, {"size": 847, "mtime": 1746896672079, "results": "362", "hashOfConfig": "199"}, {"size": 733, "mtime": 1737256250530, "results": "363", "hashOfConfig": "199"}, {"size": 348, "mtime": 1737256250531, "results": "364", "hashOfConfig": "199"}, {"size": 680, "mtime": 1734173509829, "results": "365", "hashOfConfig": "199"}, {"size": 2008, "mtime": 1744813444493, "results": "366", "hashOfConfig": "199"}, {"size": 197, "mtime": 1734173509830, "results": "367", "hashOfConfig": "199"}, {"size": 206, "mtime": 1734173509831, "results": "368", "hashOfConfig": "199"}, {"size": 453, "mtime": 1734173509832, "results": "369", "hashOfConfig": "199"}, {"size": 4057, "mtime": 1747025767921, "results": "370", "hashOfConfig": "199"}, {"size": 7729, "mtime": 1748080222327, "results": "371", "hashOfConfig": "199"}, {"size": 1680, "mtime": 1747025767930, "results": "372", "hashOfConfig": "199"}, {"size": 2492, "mtime": 1747547598663, "results": "373", "hashOfConfig": "199"}, {"size": 7831, "mtime": 1747478303267, "results": "374", "hashOfConfig": "199"}, {"size": 9452, "mtime": 1747578952586, "results": "375", "hashOfConfig": "199"}, {"size": 5408, "mtime": 1747583636164, "results": "376", "hashOfConfig": "199"}, {"size": 10152, "mtime": 1748025542193, "results": "377", "hashOfConfig": "199"}, {"size": 9589, "mtime": 1748080208305, "results": "378", "hashOfConfig": "199"}, {"size": 5927, "mtime": 1747140431671, "results": "379", "hashOfConfig": "199"}, {"size": 36393, "mtime": 1748964723582, "results": "380", "hashOfConfig": "199"}, {"size": 5430, "mtime": 1749445324693, "results": "381", "hashOfConfig": "199"}, {"size": 6424, "mtime": 1747141003155, "results": "382", "hashOfConfig": "199"}, {"size": 1028, "mtime": 1747067329685, "results": "383", "hashOfConfig": "199"}, {"size": 3433184, "mtime": 1747046157819, "results": "384", "hashOfConfig": "199"}, {"size": 898, "mtime": 1747067578677, "results": "385", "hashOfConfig": "199"}, {"size": 354, "mtime": 1747046157825, "results": "386", "hashOfConfig": "199"}, {"size": 760, "mtime": 1747046157826, "results": "387", "hashOfConfig": "199"}, {"size": 260, "mtime": 1747046157827, "results": "388", "hashOfConfig": "199"}, {"size": 3250, "mtime": 1747046157827, "results": "389", "hashOfConfig": "199"}, {"size": 9561, "mtime": 1747546816027, "results": "390", "hashOfConfig": "199"}, {"size": 8357, "mtime": 1747584718773, "results": "391", "hashOfConfig": "199"}, {"size": 1207, "mtime": 1747185154923, "results": "392", "hashOfConfig": "199"}, {"size": 1845, "mtime": 1747185179948, "results": "393", "hashOfConfig": "199"}, {"size": 3556, "mtime": 1747589041147, "results": "394", "hashOfConfig": "199"}, {"size": 2187, "mtime": 1749445249260, "results": "395", "hashOfConfig": "199"}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wmqge3", {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\about\\client.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\about\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\about\\structuredData.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\api\\categories\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\api\\pinata\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\error.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\dreamathonSection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\enhancedDevelopmentProcess.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\hero.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\hooks\\useGetIdeas.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\statsSection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\trendingProjects.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\loading.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\metaData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\privacy\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\privacy\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\terms\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\terms\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\aiBot\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\animatedText\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\backgroundGradientAnimation\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\blockchainAnimation\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\button\\animatedBorderButton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\button\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\button\\types.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\circularSpinner\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\clientOnly\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\connectButton\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\ensResolver\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\globe\\index.tsx", ["987", "988"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\input\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\input\\types.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\link\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\link\\types.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\linkStyled\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\loader\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\numberInput\\index.tsx", ["989", "990"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\placeholderAndVanishInput\\index.tsx", ["991", "992"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\shaderGradient\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\terminal\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\textArea\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\textArea\\types.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\tooltip\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\atoms\\typewriterEffect\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\AnalyticsBoardIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\BlockchainIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\BuildingIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\CheckBadgeIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\CheckSquareIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\ClaudeAIIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\ColorPaletteIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\ComputerChipFlashIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\CopyPasteIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\DeleteIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\DexScreenerIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\DownloadCloudIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\ExclamationErrorIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\FacebookIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\FlagTrophyIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\GridIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\HeartBreakIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\HRTeamIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\InfoIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\InstagramIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\LaunchGoIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\LinkedInColorIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\LinkedInIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\ListIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\LogoDevAgentIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\LogoIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\LogoutIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\MoneyBagIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\MonitorUploadIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\MultipleUsersIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\NavIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\OfficeEmployeeIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\PictureLandscapeIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\PinterestIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\PolygonIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\QuestionIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\SearchIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\SunIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\SunriseIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\SunsetIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\TelegramIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\TelegramMainIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\TokenXIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\TrophyIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\TwitterIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\UniswapIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\UniswapTokenIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\UploadIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\WalletIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\XIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\icons\\YoutubeIcon.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\accordion\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\backgroundBeamsWithCollision\\index.tsx", ["993"], ["994"], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\carousel\\CarouselContainer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\carousel\\EmblaCarouselArrowButtons.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\carousel\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\chart\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\input\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\input\\types.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\integrations\\index.tsx", ["995"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\multiStepLoader\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\progress\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\reviewProgress\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\shootingStars\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\stakeProgress\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\swiper\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\switch\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\tabs\\index.tsx", ["996", "997"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\toast\\config.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\toast\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\organisms\\footer\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\organisms\\header\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\organisms\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\organisms\\modal\\defaultModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\organisms\\modal\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\constants.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\types.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useCopyToClipboard.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useDebounce.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useInterval.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useIsClient.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useLocalStorage.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useOutsideClick.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\usePasswordEncryption.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useToggle.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\hooks\\useWindowDimensions.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lang.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\bookmarkLottie.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\buyCartLottie.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\deleteLottie.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\devLottie.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\editLottie.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\equityLottie.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\globeLottie.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\magicLottie.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\piggyLottie.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\searchLottie.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\socialLottie.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\starLottie.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\starsLottie.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\lottie\\wizardLottie.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\providers.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\routes.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\theme\\themeElements.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\types.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\blurDataUrl.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\helpers.ts", ["998"], ["999", "1000"], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\localFont.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\mixpanel\\eventTriggers.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\mixpanel\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\mixpanel\\types.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\network\\baseFetcher.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\network\\endpoints.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\pinata\\config.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\tailwindPlugins\\animations.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\home\\luminaCasaSection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\DataDrivenInsights.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\Join.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\ScientificApproach.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\Teams.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\Tokenomics.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\ChainSelector.tsx", [], ["1001", "1002"], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\DooglyInterface.tsx", ["1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\TokenSaleCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\TokenSelector.tsx", [], ["1017", "1018"], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\doogly\\adapter\\HttpAdapter.ts", ["1019", "1020", "1021"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\doogly\\constants.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\doogly\\handlers\\solana.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\doogly\\types\\cosmos.ts", ["1022"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\doogly\\types\\ethers.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\doogly\\types\\http.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\components\\molecules\\doogly\\types\\index.ts", ["1023", "1024", "1025"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\AboutLuminaCasa.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\Roadmap.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\hooks\\useEthPrice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\common\\utils\\network\\priceApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\components\\RecentTransactionsCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\Web Development\\dreamstartr\\frontend\\app\\lumina\\hooks\\useAddressTransactions.ts", [], [], {"ruleId": "1026", "severity": 1, "message": "1027", "line": 110, "column": 17, "nodeType": "1028", "endLine": 110, "endColumn": 46}, {"ruleId": "1026", "severity": 1, "message": "1029", "line": 123, "column": 18, "nodeType": "1030", "endLine": 123, "endColumn": 23}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 28, "column": 42, "nodeType": "1033", "messageId": "1034", "endLine": 28, "endColumn": 45, "suggestions": "1035"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 31, "column": 45, "nodeType": "1033", "messageId": "1034", "endLine": 31, "endColumn": 48, "suggestions": "1036"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 101, "column": 29, "nodeType": "1033", "messageId": "1034", "endLine": 101, "endColumn": 32, "suggestions": "1037"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 130, "column": 20, "nodeType": "1033", "messageId": "1034", "endLine": 130, "endColumn": 23, "suggestions": "1038"}, {"ruleId": "1026", "severity": 1, "message": "1039", "line": 165, "column": 6, "nodeType": "1040", "endLine": 165, "endColumn": 44, "suggestions": "1041"}, {"ruleId": "1042", "severity": 2, "message": "1043", "line": 121, "column": 4, "nodeType": null, "messageId": "1044", "endLine": 121, "endColumn": 7, "suppressions": "1045"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 54, "column": 31, "nodeType": "1033", "messageId": "1034", "endLine": 54, "endColumn": 34, "suggestions": "1046"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 14, "column": 40, "nodeType": "1033", "messageId": "1034", "endLine": 14, "endColumn": 43, "suggestions": "1047"}, {"ruleId": "1026", "severity": 1, "message": "1048", "line": 60, "column": 44, "nodeType": "1049", "endLine": 60, "endColumn": 51}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 95, "column": 13, "nodeType": "1033", "messageId": "1034", "endLine": 95, "endColumn": 16, "suggestions": "1050"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 25, "column": 58, "nodeType": "1033", "messageId": "1034", "endLine": 25, "endColumn": 61, "suggestions": "1051", "suppressions": "1052"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 25, "column": 64, "nodeType": "1033", "messageId": "1034", "endLine": 25, "endColumn": 67, "suggestions": "1053", "suppressions": "1054"}, {"ruleId": "1055", "severity": 1, "message": "1056", "line": 106, "column": 17, "nodeType": "1057", "endLine": 110, "endColumn": 19, "suppressions": "1058"}, {"ruleId": "1055", "severity": 1, "message": "1056", "line": 151, "column": 23, "nodeType": "1057", "endLine": 155, "endColumn": 25, "suppressions": "1059"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 40, "column": 16, "nodeType": "1033", "messageId": "1034", "endLine": 40, "endColumn": 19, "suggestions": "1060"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 262, "column": 46, "nodeType": "1033", "messageId": "1034", "endLine": 262, "endColumn": 49, "suggestions": "1061"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 322, "column": 38, "nodeType": "1033", "messageId": "1034", "endLine": 322, "endColumn": 41, "suggestions": "1062"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 386, "column": 27, "nodeType": "1033", "messageId": "1034", "endLine": 386, "endColumn": 30, "suggestions": "1063"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 480, "column": 47, "nodeType": "1033", "messageId": "1034", "endLine": 480, "endColumn": 50, "suggestions": "1064"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 497, "column": 21, "nodeType": "1033", "messageId": "1034", "endLine": 497, "endColumn": 24, "suggestions": "1065"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 541, "column": 19, "nodeType": "1033", "messageId": "1034", "endLine": 541, "endColumn": 22, "suggestions": "1066"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 612, "column": 56, "nodeType": "1033", "messageId": "1034", "endLine": 612, "endColumn": 59, "suggestions": "1067"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 620, "column": 21, "nodeType": "1033", "messageId": "1034", "endLine": 620, "endColumn": 24, "suggestions": "1068"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 629, "column": 36, "nodeType": "1033", "messageId": "1034", "endLine": 629, "endColumn": 39, "suggestions": "1069"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 641, "column": 21, "nodeType": "1033", "messageId": "1034", "endLine": 641, "endColumn": 24, "suggestions": "1070"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 699, "column": 23, "nodeType": "1033", "messageId": "1034", "endLine": 699, "endColumn": 26, "suggestions": "1071"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 761, "column": 21, "nodeType": "1033", "messageId": "1034", "endLine": 761, "endColumn": 24, "suggestions": "1072"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 1183, "column": 49, "nodeType": "1033", "messageId": "1034", "endLine": 1183, "endColumn": 52, "suggestions": "1073"}, {"ruleId": "1055", "severity": 1, "message": "1056", "line": 116, "column": 17, "nodeType": "1057", "endLine": 123, "endColumn": 19, "suppressions": "1074"}, {"ruleId": "1055", "severity": 1, "message": "1056", "line": 164, "column": 23, "nodeType": "1057", "endLine": 171, "endColumn": 25, "suppressions": "1075"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 35, "column": 31, "nodeType": "1033", "messageId": "1034", "endLine": 35, "endColumn": 34, "suggestions": "1076"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 43, "column": 28, "nodeType": "1033", "messageId": "1034", "endLine": 43, "endColumn": 31, "suggestions": "1077"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 44, "column": 31, "nodeType": "1033", "messageId": "1034", "endLine": 44, "endColumn": 34, "suggestions": "1078"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 3, "column": 10, "nodeType": "1033", "messageId": "1034", "endLine": 3, "endColumn": 13, "suggestions": "1079"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 102, "column": 23, "nodeType": "1033", "messageId": "1034", "endLine": 102, "endColumn": 26, "suggestions": "1080"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 113, "column": 33, "nodeType": "1033", "messageId": "1034", "endLine": 113, "endColumn": 36, "suggestions": "1081"}, {"ruleId": "1031", "severity": 1, "message": "1032", "line": 128, "column": 17, "nodeType": "1033", "messageId": "1034", "endLine": 128, "endColumn": 20, "suggestions": "1082"}, "react-hooks/exhaustive-deps", "Assignments to the 'width' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "MemberExpression", "Assignments to the 'phi' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "Literal", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1083", "1084"], ["1085", "1086"], ["1087", "1088"], ["1089", "1090"], "React Hook useEffect has a missing dependency: 'parentRef'. Either include it or remove the dependency array.", "ArrayExpression", ["1091"], "@typescript-eslint/no-unused-vars", "'ref' is defined but never used.", "unusedVar", ["1092"], ["1093", "1094"], ["1095", "1096"], "The ref value 'parentRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'parentRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", ["1097", "1098"], ["1099", "1100"], ["1101"], ["1102", "1103"], ["1104"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["1105"], ["1106"], ["1107", "1108"], ["1109", "1110"], ["1111", "1112"], ["1113", "1114"], ["1115", "1116"], ["1117", "1118"], ["1119", "1120"], ["1121", "1122"], ["1123", "1124"], ["1125", "1126"], ["1127", "1128"], ["1129", "1130"], ["1131", "1132"], ["1133", "1134"], ["1135"], ["1136"], ["1137", "1138"], ["1139", "1140"], ["1141", "1142"], ["1143", "1144"], ["1145", "1146"], ["1147", "1148"], ["1149", "1150"], {"messageId": "1151", "fix": "1152", "desc": "1153"}, {"messageId": "1154", "fix": "1155", "desc": "1156"}, {"messageId": "1151", "fix": "1157", "desc": "1153"}, {"messageId": "1154", "fix": "1158", "desc": "1156"}, {"messageId": "1151", "fix": "1159", "desc": "1153"}, {"messageId": "1154", "fix": "1160", "desc": "1156"}, {"messageId": "1151", "fix": "1161", "desc": "1153"}, {"messageId": "1154", "fix": "1162", "desc": "1156"}, {"desc": "1163", "fix": "1164"}, {"kind": "1165", "justification": "1166"}, {"messageId": "1151", "fix": "1167", "desc": "1153"}, {"messageId": "1154", "fix": "1168", "desc": "1156"}, {"messageId": "1151", "fix": "1169", "desc": "1153"}, {"messageId": "1154", "fix": "1170", "desc": "1156"}, {"messageId": "1151", "fix": "1171", "desc": "1153"}, {"messageId": "1154", "fix": "1172", "desc": "1156"}, {"messageId": "1151", "fix": "1173", "desc": "1153"}, {"messageId": "1154", "fix": "1174", "desc": "1156"}, {"kind": "1165", "justification": "1166"}, {"messageId": "1151", "fix": "1175", "desc": "1153"}, {"messageId": "1154", "fix": "1176", "desc": "1156"}, {"kind": "1165", "justification": "1166"}, {"kind": "1165", "justification": "1166"}, {"kind": "1165", "justification": "1166"}, {"messageId": "1151", "fix": "1177", "desc": "1153"}, {"messageId": "1154", "fix": "1178", "desc": "1156"}, {"messageId": "1151", "fix": "1179", "desc": "1153"}, {"messageId": "1154", "fix": "1180", "desc": "1156"}, {"messageId": "1151", "fix": "1181", "desc": "1153"}, {"messageId": "1154", "fix": "1182", "desc": "1156"}, {"messageId": "1151", "fix": "1183", "desc": "1153"}, {"messageId": "1154", "fix": "1184", "desc": "1156"}, {"messageId": "1151", "fix": "1185", "desc": "1153"}, {"messageId": "1154", "fix": "1186", "desc": "1156"}, {"messageId": "1151", "fix": "1187", "desc": "1153"}, {"messageId": "1154", "fix": "1188", "desc": "1156"}, {"messageId": "1151", "fix": "1189", "desc": "1153"}, {"messageId": "1154", "fix": "1190", "desc": "1156"}, {"messageId": "1151", "fix": "1191", "desc": "1153"}, {"messageId": "1154", "fix": "1192", "desc": "1156"}, {"messageId": "1151", "fix": "1193", "desc": "1153"}, {"messageId": "1154", "fix": "1194", "desc": "1156"}, {"messageId": "1151", "fix": "1195", "desc": "1153"}, {"messageId": "1154", "fix": "1196", "desc": "1156"}, {"messageId": "1151", "fix": "1197", "desc": "1153"}, {"messageId": "1154", "fix": "1198", "desc": "1156"}, {"messageId": "1151", "fix": "1199", "desc": "1153"}, {"messageId": "1154", "fix": "1200", "desc": "1156"}, {"messageId": "1151", "fix": "1201", "desc": "1153"}, {"messageId": "1154", "fix": "1202", "desc": "1156"}, {"messageId": "1151", "fix": "1203", "desc": "1153"}, {"messageId": "1154", "fix": "1204", "desc": "1156"}, {"kind": "1165", "justification": "1166"}, {"kind": "1165", "justification": "1166"}, {"messageId": "1151", "fix": "1205", "desc": "1153"}, {"messageId": "1154", "fix": "1206", "desc": "1156"}, {"messageId": "1151", "fix": "1207", "desc": "1153"}, {"messageId": "1154", "fix": "1208", "desc": "1156"}, {"messageId": "1151", "fix": "1209", "desc": "1153"}, {"messageId": "1154", "fix": "1210", "desc": "1156"}, {"messageId": "1151", "fix": "1211", "desc": "1153"}, {"messageId": "1154", "fix": "1212", "desc": "1156"}, {"messageId": "1151", "fix": "1213", "desc": "1153"}, {"messageId": "1154", "fix": "1214", "desc": "1156"}, {"messageId": "1151", "fix": "1215", "desc": "1153"}, {"messageId": "1154", "fix": "1216", "desc": "1156"}, {"messageId": "1151", "fix": "1217", "desc": "1153"}, {"messageId": "1154", "fix": "1218", "desc": "1156"}, "suggestUnknown", {"range": "1219", "text": "1220"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1221", "text": "1222"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1223", "text": "1220"}, {"range": "1224", "text": "1222"}, {"range": "1225", "text": "1220"}, {"range": "1226", "text": "1222"}, {"range": "1227", "text": "1220"}, {"range": "1228", "text": "1222"}, "Update the dependencies array to be: [cycleCollisionDetected, containerRef, parentRef]", {"range": "1229", "text": "1230"}, "directive", "", {"range": "1231", "text": "1220"}, {"range": "1232", "text": "1222"}, {"range": "1233", "text": "1220"}, {"range": "1234", "text": "1222"}, {"range": "1235", "text": "1220"}, {"range": "1236", "text": "1222"}, {"range": "1237", "text": "1220"}, {"range": "1238", "text": "1222"}, {"range": "1239", "text": "1220"}, {"range": "1240", "text": "1222"}, {"range": "1241", "text": "1220"}, {"range": "1242", "text": "1222"}, {"range": "1243", "text": "1220"}, {"range": "1244", "text": "1222"}, {"range": "1245", "text": "1220"}, {"range": "1246", "text": "1222"}, {"range": "1247", "text": "1220"}, {"range": "1248", "text": "1222"}, {"range": "1249", "text": "1220"}, {"range": "1250", "text": "1222"}, {"range": "1251", "text": "1220"}, {"range": "1252", "text": "1222"}, {"range": "1253", "text": "1220"}, {"range": "1254", "text": "1222"}, {"range": "1255", "text": "1220"}, {"range": "1256", "text": "1222"}, {"range": "1257", "text": "1220"}, {"range": "1258", "text": "1222"}, {"range": "1259", "text": "1220"}, {"range": "1260", "text": "1222"}, {"range": "1261", "text": "1220"}, {"range": "1262", "text": "1222"}, {"range": "1263", "text": "1220"}, {"range": "1264", "text": "1222"}, {"range": "1265", "text": "1220"}, {"range": "1266", "text": "1222"}, {"range": "1267", "text": "1220"}, {"range": "1268", "text": "1222"}, {"range": "1269", "text": "1220"}, {"range": "1270", "text": "1222"}, {"range": "1271", "text": "1220"}, {"range": "1272", "text": "1222"}, {"range": "1273", "text": "1220"}, {"range": "1274", "text": "1222"}, {"range": "1275", "text": "1220"}, {"range": "1276", "text": "1222"}, {"range": "1277", "text": "1220"}, {"range": "1278", "text": "1222"}, {"range": "1279", "text": "1220"}, {"range": "1280", "text": "1222"}, {"range": "1281", "text": "1220"}, {"range": "1282", "text": "1222"}, [1139, 1142], "unknown", [1139, 1142], "never", [1288, 1291], [1288, 1291], [2397, 2400], [2397, 2400], [3261, 3264], [3261, 3264], [4016, 4054], "[cycleCollisionDetected, containerRef, parentRef]", [1923, 1926], [1923, 1926], [291, 294], [291, 294], [2614, 2617], [2614, 2617], [574, 577], [574, 577], [580, 583], [580, 583], [1076, 1079], [1076, 1079], [6027, 6030], [6027, 6030], [7883, 7886], [7883, 7886], [9864, 9867], [9864, 9867], [13136, 13139], [13136, 13139], [13547, 13550], [13547, 13550], [14752, 14755], [14752, 14755], [17350, 17353], [17350, 17353], [17580, 17583], [17580, 17583], [17834, 17837], [17834, 17837], [18204, 18207], [18204, 18207], [19411, 19414], [19411, 19414], [21188, 21191], [21188, 21191], [36333, 36336], [36333, 36336], [709, 712], [709, 712], [892, 895], [892, 895], [929, 932], [929, 932], [54, 57], [54, 57], [2280, 2283], [2280, 2283], [2469, 2472], [2469, 2472], [2834, 2837], [2834, 2837]]