import { 
  useState, useCallback,
} from 'react';


export const useToggle = (
  initialState: boolean = false,
): [boolean, (value?: boolean) => void] => {
  const [state, setState] = useState<boolean>(initialState);

  const toggle = useCallback((value?: boolean): void => {
    setState((prevState) => 
      typeof value === 'boolean' ? value : !prevState,
    );
  }, []);

  return [state, toggle];
};
