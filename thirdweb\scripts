npx thirdweb deploy -k grZchApf6tOgPqcKNhKV4IT44KTxBzlMW_Sdy-wcEBqm5OnZ0N6AmjH171GtyCCsxzjnz4qnxWT1yjbSQoDV6A
   
npx thirdweb build 
{
"name": "Idea",
"symbol": "IDEA",
"imageUrl": "imageUrl",
"description": "description",
"categories": "categories",
"productScreenshotUrl": "productScreenshotUrl",
"productUrl": "productUrl",
"twitterUrl": "twitterUrl"
}

// const K = 5e15;
// const INITIAL_PRICE = 3e13;

const K = 5e14;
const INITIAL_PRICE = 42700000000;

const DECIMALS = 1e12;

function calculateCost(currentSupply, tokensToBuy) {
    
    const exponent1 = ((K * (currentSupply + tokensToBuy)) / DECIMALS);
    const exponent2 = ((K * currentSupply) / DECIMALS);
    
    const exp1 = exp(exponent1);
    const exp2 = exp(exponent2);
    
    const cost = ((INITIAL_PRICE * DECIMALS * (exp1 - exp2)) / K);
    
    return (cost / 1e18);
}

function exp(x) {
    let sum = DECIMALS;
    let term = DECIMALS;
    let xPower = x;
    
    for (let i = 1; i <= 20; i++) {
        term = ((term * xPower) / (i * DECIMALS));
        sum += term;
        
        if (term < 1) break;
    }
    
    return sum;
}

// Example usage
console.log(calculateCost(0, 8e8)); // Example call