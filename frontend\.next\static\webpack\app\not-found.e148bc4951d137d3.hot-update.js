"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/not-found",{

/***/ "(app-pages-browser)/./common/lang.ts":
/*!************************!*\
  !*** ./common/lang.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   lang: function() { return /* binding */ lang; }\n/* harmony export */ });\nconst header = {\n    connectButton: {\n        connectWallet: \"Login\",\n        wrongNetwork: \"Wrong Network\",\n        myIdeas: \"My Dreams\",\n        logout: \"Logout\",\n        copyAddress: \"Copy Address\",\n        fundAccount: \"Add Funds\",\n        connectedTo: \"Connected to\"\n    },\n    myDreams: \"My Dreams\",\n    generate: \"Generate Dream\",\n    searchDreams: \"Search Dreams\",\n    bigger: \"Bigger\",\n    dream: \"Dream\",\n    faster: \"& Faster\",\n    dreamsSubHeading: \"Discover innovative projects\",\n    pilotSubheading: \"Enhance your social presence\",\n    dreamathonSubheading: \"Join our community events\",\n    searchIdeas: {\n        placeholder: \"Search for Dreams\",\n        noIdeasFound: \"No Dreams found\"\n    }\n};\nconst notFound = {\n    heading: \"Something went wrong\",\n    subHeading1: \"Brace yourself till we get the error fixed\",\n    subHeading2: \"You may also refresh the page or try again later\",\n    buttonTitle: \"Return Home\"\n};\nconst footer = {\n    terms: \"Terms\",\n    aboutUs: \"About Us\",\n    privacyPolicy: \"Privacy\",\n    contactUs: \"Contact us\",\n    contactUsModal: {\n        heading: \"Contact Us\",\n        submitButton: \"Send\",\n        youCanContact: \"You can contact us at\"\n    }\n};\nconst createIdea = {\n    categories: {\n        noTagsFound: \"No Categories found\"\n    },\n    addCategoryError: \"Unable to add new category\",\n    accountWrongError: \"Please connect with your account to edit this dream\",\n    generateError: \"Something went wrong. Please try again later.\",\n    transactionError: \"Transaction reverted. Please try again.\",\n    aiRegenerateError: \"Failed to regenerate content. Please try again.\",\n    insufficientBalance: \"You have insufficient balance to create a dream\",\n    whatToImprove: \"What do you want to improve?\",\n    reservedDomain: \"This domain is reserved. Please choose another ticker\",\n    heading: \"Create Token\",\n    subHeading: \"Launch Your Dream\",\n    subHeadingCreated: \"Design Your Dream\",\n    subHeadingFundingReached: \"Funding Reached\",\n    subHeading2Part1: \"Share your dream in detail to connect with potential believers. We will create an\",\n    subHeading2Part2: \"that you can trade on\",\n    subHeading2FundingReached: \"We would suggest not to update the dream details as the funding target has been met and keep the original idea intact.\",\n    tokenCreationFeeLabel: \"One Time Fee\",\n    maxSupplyLabel: \"Max Supply\",\n    initialMintLabel: \"Initial Supply\",\n    tokensSuffix: \" Tokens\",\n    targetInfoPart1: \"After funding target of \",\n    targetInfoPart2: \"is met, a liquidity pool will be created on Uniswap.\",\n    ideaNotFound: \"Dream not found!\",\n    ideaCreatedMessage: \"Dream successfully created!\",\n    ideaDetailsUpdated: \"Dream details updated!\",\n    errorOccurred: \"Something went wrong. Please try again!\",\n    cantEditError: \"You can't update as you are not the owner of this dream!\",\n    validationErrors: {\n        nameRequired: \"Please enter a name for your dream\",\n        nameMinError: \"Please enter more than 2 characters\",\n        nameMaxError: \"Please enter less than 20 characters\",\n        tickerRequired: \"Please enter a name for your ticker\",\n        tickerMinError: \"Please enter more than 2 characters\",\n        tickerMaxError: \"Please enter less than 20 characters\",\n        logoRequired: \"Please upload logo or image depicting your dream\",\n        descriptionRequired: \"Please provide a description for your dream\",\n        descriptionMinError: \"Please enter more than 10 characters\",\n        descriptionMaxError: \"Please enter less than 1000 characters\",\n        categoriesRequired: \"Please select a category\",\n        websiteRequired: \"Please provide a valid link\",\n        twitterInvalid: \"Please provide a valid X link\",\n        telegramInvalid: \"Please provide a valid telegram link\"\n    },\n    uploadDifferent: \"Upload a different image\",\n    imageUpload: {\n        title: \"Dream Logo\",\n        postImage: \"Post Image\",\n        imageSizeError: \"Please upload image less than 5 mb\",\n        imageType: \"This is a wrong file format. Only image files are allowed.\",\n        uploadError: \"Could not add file. Please try again.\",\n        uploading: \"Uploading...\",\n        uploadLabel: \"Upload here\"\n    },\n    form: {\n        name: \"Dream Title\",\n        address: \"Address\",\n        ticker: \"Ticker Name\",\n        description: \"Core Vision\",\n        category: \"Select Category\",\n        website: \"Website\",\n        websitePreview: \"Dream Preview\",\n        twitter: \"X(Twitter)\",\n        telegram: \"Telegram\",\n        submitLabel: \"Submit\",\n        connectWallet: \"Connect Wallet\",\n        brandingDetails: \"Dream Identity\",\n        dreamDetails: \"Describe your Dream\",\n        generateButtonLabel: \"Restart\",\n        optional: \"Recommended\",\n        communityChannels: \"Community Channels\",\n        createIdea: \"Create Dream\",\n        confirm: \"Confirm\",\n        subdomainExisting: \"Please note you will be assigned a alternate subdomain as this subdomain is already taken\",\n        viewDream: \"View Dream\",\n        socialAssistants: \"Social Agents\",\n        note: \"Note: You can always edit some parts of the dream later\"\n    }\n};\nconst generateIdea = {\n    promptPlaceholders: [\n        \"Create the next Decentralized Social Media Platform\",\n        \"Create an AI-Driven Marketplace for Digital Assets\",\n        \"Create a DAO Management Platform\",\n        \"Create a Blockchain-Based E-Learning Platform\",\n        \"Create a Smart Contract Development Assistant\"\n    ],\n    promptLoadingStates: [\n        {\n            text: \"Exploring Concepts\"\n        },\n        {\n            text: \"Developing Blueprint\"\n        },\n        {\n            text: \"Designing Brand Identity\"\n        },\n        {\n            text: \"Crafting User Experience\"\n        },\n        {\n            text: \"Refining Design Elements\"\n        },\n        {\n            text: \"Finalizing and Deploying\"\n        }\n    ],\n    greeting: {\n        morning: \"Good morning, tell me about your Dream\",\n        afternoon: \"Good afternoon, tell me about your Dream\",\n        evening: \"Good evening, tell me about your Dream\"\n    },\n    whatsYourDream: \"Tell me about your Dream\",\n    generateError: \"Something went wrong. Please try again later.\",\n    poweredBy: \"powered by\",\n    readyTo: \"You are one step away from transforming your Dreams into reality\",\n    continue: \"Continue to Create Token\",\n    proceed: \"Proceed\",\n    orEnhance: \"Or keep enhancing the dream\",\n    h1: \"Generate your Web3 Dream with Claude AI\"\n};\nconst ideas = {\n    ideaCard: {\n        raised: \"Raised\",\n        holders: \"Believers\",\n        trade: \"Trade\",\n        marketcap: \"Market Cap\",\n        uniswapLabel: \"UNI\",\n        openUniswap: \"Open Uniswap\",\n        dexLabel: \"DEX\",\n        openX: \"Open X\",\n        openWebsite: \"View Website\",\n        openDex: \"Open DEX Screener\",\n        openTelegram: \"Open Telegram\"\n    },\n    currentIdeas: \"Dreams Gallery\",\n    currentIdeasSubHeading: \"Discover innovative dreams ready for community support\",\n    noIdeasHeading: \"No dreams yet in this category\",\n    noIdeasSubHeading: \"Be the first to contribute! Share your innovative dreams and help to grow this category.\",\n    registerIdea: \"Register Existing Dream\",\n    launchNew: \"Generate Dream\",\n    detailedView: \"Detailed View\",\n    compactView: \"Compact View\",\n    refreshData: \"Refresh Data\"\n};\nconst homePage = {\n    timeline: {\n        heading: \"How to Launch Your Project?\",\n        subHeading: \"Transform your innovative ideas into sustainable subscription-based projects with our comprehensive ISO Hub\"\n    },\n    subHeading: \"The ultimate platform for builders to launch, grow, and monetize their projects through Initial Subscription Offerings\",\n    h1: \"Build. Launch. Scale.\",\n    subHeading1: \"Every great project starts with a vision.\",\n    subHeading2: \"What if you could turn your next big idea into a thriving subscription business?\",\n    subHeading3: \"From concept to community-driven success. Launch your Initial Subscription Offering and build sustainable revenue streams with engaged subscribers.\",\n    heroWords: [\n        {\n            text: \"Build.\"\n        },\n        {\n            text: \"Launch.\"\n        },\n        {\n            text: \"Scale.\",\n            className: \"gradientText\"\n        }\n    ],\n    answeredByHuman: \"Supported by Expert Advisors\",\n    haveDream: \"Have an existing project?\",\n    register: \"Register Project\",\n    answeredByAi: \"Powered by AI Tools\",\n    fundButtonLabel: \"Register Existing Project\",\n    generateButtonLabel: \"Launch New ISO\",\n    trendingIdeas: \"Featured Projects\",\n    trendingIdeasSubHeading: \"Discover successful subscription-based projects built by our community\",\n    readyToTurn: \"Ready to Launch Your Subscription Business?\",\n    readyToTurnSubheading: \"Join DreamStartr ISO Hub today and transform your ideas into sustainable revenue streams. Launch your Initial Subscription Offering in minutes.\",\n    dreamGallery: \"Explore Projects\",\n    stats: {\n        heading: \"Platform Overview\",\n        description: \"See how builders are creating sustainable subscription businesses on our platform\",\n        dreamsLaunched: \"Projects launched on platform\",\n        agentsRunning: \"AI agents actively running\",\n        aiPowered: \"AI-powered development support\",\n        fundsRaised: \"Total funds raised for dreams\",\n        activeAgents: \"Running\"\n    },\n    developmentProcess: {\n        heading: \"Process\",\n        stepOneTitle: \"Dream Validation\",\n        stepOneInfo: \"From raw concept to refined vision\",\n        stepOnePoints: \"AI-enhanced ideation \\uD83D\\uDCA1 with expert community feedback \\uD83D\\uDCAC and direct market validation from target users \\uD83C\\uDFAF\",\n        stepTwoTitle: \"AI-Powered Development\",\n        stepTwoInfo: \"Where vision meets execution\",\n        stepTwoPoints: \"Rapid development with our AI-powered IDE \\uD83D\\uDCBB and real-time community-driven validation \\uD83D\\uDCAC\",\n        stepThreeTitle: \"Sustainable Launch\",\n        stepThreeInfo: \"Built for lasting impact\",\n        stepThreePoints: \"Customized token economics \\uD83D\\uDCB0, engaged early users \\uD83D\\uDC65 and proven growth strategies \\uD83D\\uDCC8\",\n        stepFourTitle: \"Accelerated Growth\",\n        stepFourInfo: \"Scaling with purpose\",\n        stepFourPoints: \"Access strategic partnerships \\uD83E\\uDD1D, expand market reach \\uD83C\\uDF10 and track measurable impact \\uD83D\\uDCCA\"\n    }\n};\nconst ideaPage = {\n    createdBy: \"Created by\",\n    believers: \"Believers\",\n    tokenAddress: \"Token address\",\n    fundingRaised: \"Raised\",\n    visitWebsite: \"View website\",\n    attachImage: \"Attach image\",\n    categories: \"Categories\",\n    connectWallet: \"Connect Wallet\",\n    tokenDetails: \"Token Details\",\n    transfers: \"Transfers\",\n    trade: \"Trade\",\n    uniswapLabel: \"UNI\",\n    conversations: \"Comments\",\n    post: \"Post\",\n    noComments: \"No comments yet. Be the first to start the conversation!\",\n    preview: \"Preview\",\n    buyFeed: \"Buy Feed\",\n    notActive: \"This idea is no longer active. Redirecting to homepage.\",\n    sellFeed: \"Sell Feed\",\n    owners: \"Believers\",\n    chart: \"Buy Trend\",\n    stakeholders: \"Believers\",\n    stakeholdersDesc: \"Our visionary believers making this dream possible\",\n    checkTransHeading: \"Check out ongoing trades on\",\n    transactionsTable: {\n        columnOne: \"Account\",\n        columnTwo: \"Value\",\n        columnThree: \"Time\",\n        columnFour: \"Transaction\"\n    },\n    ownersTable: {\n        columnOne: \"Account\",\n        columnTwo: \"Percentage\"\n    },\n    limitedTokensError: \"Limited tokens. There is only %amount% tokens left.\",\n    purchaseSuccess: \"Congratulations! You have purchased\",\n    purchaseError: \"Purchase could not be completed. Please try again!\",\n    postError: \"Unable to add new post\",\n    bondingCurveProgress: \"Progress\",\n    buyTokensFor: \"Buy tokens for\",\n    availableTokens: \"Available tokens\",\n    purchaseLabel: \"Get Quote\",\n    fetchQuoteError: \"Unable to fetch quote\",\n    approveTokenError: \"Unable to approve token\",\n    swapTokensSuccess: \"You have successfully swapped your tokens\",\n    transactionSwapError: \"Transaction failed. Please check your balance and try again\",\n    swapGeneralError: \"An error occurred while processing the swap\",\n    youWillReceive: \"You will receive\",\n    maxButton: \"Max\",\n    deleteComment: \"Delete comment\",\n    likeComment: \"Like comment\",\n    areYouSureToDelete: \"Are you sure you want to delete this comment?\",\n    editIdea: \"Edit Dream\",\n    codeAssist: \"Code Assist\",\n    boostIdea: \"Social Agents\",\n    bondingCurve: \"Bonding Curve Progress\",\n    buyPriceFetchError: \"Unable to fetch buying price\",\n    calculateTokensError: \"Unable to calculate tokens\",\n    priceQuoteError: \"Unable to get price quote\",\n    confirm: \"Confirm\",\n    for: \"for\",\n    confirmPurchase: \"Confirm purchase\",\n    buyTokens: \"Support Dream\",\n    buy: \"Buy\",\n    remaining: \" Remaining\",\n    swapTokens: \"Swap Tokens\",\n    swapTokensDesc: \"You can buy/sell tokens from the liquidity pool\",\n    openUniswap: \"Open Uniswap\",\n    dexLabel: \"DEX\",\n    openDex: \"Open DEX Screener\",\n    openTelegram: \"Open Telegram\",\n    openX: \"Open X\",\n    swapping: \"Swapping...\",\n    swap: \"Swap\",\n    buyTokensDesc: \"Join our community and shape the future - get tokens now \\uD83D\\uDE80\",\n    ensure: \"Ensure you have enough funds in your account\",\n    likelyFail: \"Note: This transaction will likely fail as you do not have enough funds\",\n    buyNow: \"Confirm\",\n    bondingCurveInfo: \"When the market cap reaches %goal% %currency%, all the liquidity from the bonding curve will be deposited into Uniswap, and the LP tokens will be burned. Progression increases as the price goes up.\"\n};\nconst profile = {\n    heading: \"Manage Dreams\",\n    startAllAgents: \"Start All Agents\",\n    subheading: \"Track your dream progress and manage your launch strategy\",\n    noIdeasHeading: \"You have not created any Dream\",\n    noIdeasSubHeading: \"Create your first Dream\",\n    registerIdea: \"Register Existing Dream\",\n    launchNew: \"Generate Dream\",\n    table: {\n        ideaName: \"Dream name\",\n        status: \"Status\",\n        ideaAddress: \"Address\",\n        ticker: \"Ticker\",\n        actions: \"Actions\",\n        view: \"View\",\n        dev: \"Code Assist\",\n        review: \"Social Agents\",\n        edit: \"Edit Dream\"\n    }\n};\nconst dreamathon = {\n    title: \"Dreamathon:\",\n    animatedText: \"Innovation at Scale\",\n    description: \"A large-scale hackathon initiative across 50 Indian cities, bringing together innovators, developers, and dreamers to build the future of Web3.\",\n    ongoingEvents: \"Ongoing Events\",\n    cities: \"50 Indian Cities\",\n    prizesTitle: \"Prizes & Recognition\",\n    prizesDescription: \"Win awards and get your projects featured\",\n    communityTitle: \"Community Support\",\n    communityDescription: \"Connect with mentors and fellow builders\",\n    viewMoreDetails: \"View More Details\"\n};\nconst enhancedDevelopmentProcess = {\n    sectionTitle: \"What we do\",\n    sectionDescription: \"Our platform provides everything you need to bring your dream projects to life\",\n    cards: {\n        blockchain: {\n            title: \"Blockchain Integration\",\n            description: \"DreamStartr helps dreams connect to the blockchain community, providing tools and resources for creators to launch their ideas as tokens and build engaged communities.\"\n        },\n        mediaPilot: {\n            title: \"Media Pilot\",\n            description: \"Explore AI agents exclusively on MediaPilot, making advanced AI technology accessible to everyone regardless of technical background.\"\n        },\n        dreamathons: {\n            title: \"Dreamathons\",\n            description: \"An initiative connecting innovators across the globe, fostering collaboration and bringing dreams to life through technology and community support.\"\n        },\n        devAgent: {\n            title: \"Dev Agent\",\n            description: \"A platform that helps developers create sandbox environments for their dreams, with AI-assisted code generation and development tools.\",\n            terminal: {\n                command: \"> create a beautiful project for a pet shop\",\n                step1: \"✔ Understanding requirements and validating idea.\",\n                step2: \"✔ Generating plan and UX.\",\n                step3: \"✔ Generating code.\",\n                success: \"Success! Starting sandbox.\"\n            }\n        }\n    }\n};\nconst manageIdea = {\n    heading: \" Social Assistants\",\n    subHeading: \"Meet your personal team of AI experts, each uniquely trained to help transform different aspects of your dream into reality.\",\n    twitterDescription: \"This agent can create a personalized persona based on your dream which will schedule tweets, handle replies, and more. Additionally, you can attach AI generated images for your posts.\",\n    tweets: \"Tweets Generated\",\n    posts: \"Posts Generated\",\n    postsSingular: \"Post Generated\",\n    tweetsSingular: \"Tweet Generated\",\n    engagement: \"Engagement Metrics\",\n    createSandboxError: \"Failed to create sandbox. Please try again.\",\n    linkedInDescription: \"This agent can create a personalized persona based on your dream which will schedule posts, handle replies, and more. Additionally, you can attach AI generated images for your posts.\",\n    manageAgent: \"Manage Persona\",\n    modifyCharacter: \"Target X Users\",\n    agentSettings: \"Engagement Settings\",\n    upcomingPosts: \"Upcoming Tweets\",\n    upcomingPostsLinkedin: \"Upcoming Posts\",\n    devAgent: \"Dev Agent\",\n    openAgent: \"Open Agent\",\n    createSandbox: \"Create Sandbox\",\n    openSandbox: \"Open Sandbox\",\n    addNewDev: \"Add New\",\n    active: \"Active\",\n    yourDreams: \"Your Dreams\",\n    partneredDreams: \"Partnered Dreams\",\n    owned: \"Owned\",\n    drafts: \"Drafts\",\n    draft: \"Draft\",\n    back: \"Back to Dream\",\n    devAgentDesc: \"The Dev Agent is a powerful tool that helps you build your dream. It provides you with a sandbox environment to develop your code and deploy your dream.\",\n    enableFollowing: \"Enable Following\",\n    enableFollowDescription: \"Allow agent to follow relevant accounts\",\n    enableActions: \"Enable Interactions\",\n    enableActionsDescription: \"Check and engage with tweets\",\n    enablePosts: \"Enable Tweeting\",\n    postInterval: \"Tweet Interval\",\n    postIntervalDesc: \"Duration between each tweet\",\n    interactionInterval: \"Interaction Interval\",\n    interactionIntervalDesc: \"Frequency of interactions(likes, replies)\",\n    followInterval: \"Follow Interval\",\n    followIntervalDesc: \"Frequency of following new accounts\",\n    enablePostsDescription: \"Allow agent to post personalized tweets\",\n    replies: \"Replies to Tweets\",\n    repliesSingular: \"Reply to Tweets\",\n    likes: \"Tweets Liked\",\n    likesSingular: \"Tweet Liked\",\n    retweets: \"Tweets Retweeted\",\n    retweetsSingular: \"Tweet Retweeted\",\n    followers: \"Accounts Followed\",\n    followersSingular: \"Account Followed\",\n    prev: \"Prev\",\n    next: \"Next\",\n    skip: \"Skip\",\n    agentRunning: \"Running since\",\n    twitterForm: {\n        username: \"Username\",\n        password: \"Password\",\n        email: \"Email\",\n        submit: \"Start Agent\",\n        connect: \"Connect Wallet\",\n        stopAgent: \"Pause Agent\",\n        viewCharacter: \"View Settings\",\n        updateCharacter: \"Update Character\",\n        hideCharacter: \"Hide Settings\"\n    },\n    character: {\n        exampleOne: \"Example 1\",\n        exampleTwo: \"Example 2\",\n        exampleUserLabel: \"User\",\n        exampleAgentLabel: \"Agent\",\n        styleAll: \"All\",\n        styleChat: \"Chat\",\n        stylePost: \"Post\",\n        professional: \"Professional\"\n    },\n    promptLoadingStates: [\n        {\n            text: \"Crafting Your Digital Identity\"\n        },\n        {\n            text: \"Preparing Your Agent\"\n        },\n        {\n            text: \"Processing Platform Data\"\n        },\n        {\n            text: \"Designing Engagement Plan\"\n        },\n        {\n            text: \"Optimizing Post Schedule\"\n        }\n    ],\n    devLoadingStates: [\n        {\n            text: \"Creating Sandbox\"\n        },\n        {\n            text: \"Deploying Agent\"\n        },\n        {\n            text: \"Building Dream\"\n        },\n        {\n            text: \"Testing Dream\"\n        },\n        {\n            text: \"Launching Dream\"\n        }\n    ]\n};\nconst lang = {\n    header,\n    profile,\n    createIdea,\n    manageIdea,\n    homePage,\n    ideas,\n    generateIdea,\n    footer,\n    notFound,\n    ideaPage,\n    dreamathon,\n    enhancedDevelopmentProcess\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (lang);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./common/lang.ts\n"));

/***/ })

});