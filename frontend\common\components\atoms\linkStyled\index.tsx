import Link from "next/link"
import { ReactNode } from "react";

export const LinkStyled = ({
  href,
  target = "",
  onClick,
  children,
  className,
} : {
  href: string;
  onClick?: () => void;
  children: ReactNode;
  className?: string;
  target?: string;
}) => {
  return (
    <Link prefetch={true} target={target} href={href} onClick={(e) => {
      e.stopPropagation()
      onClick && onClick()
    }} className={`transition-all duration-200 ease-in-out text-gray-300 bg-gradient-to-tr from-violets-are-blue to-han-purple bg-clip-text hover:text-transparent font-medium px-4 flex justify-center items-center ${className}`}>{children}</Link>
  )
}
