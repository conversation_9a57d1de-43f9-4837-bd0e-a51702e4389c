{"v": "5.8.1", "fr": 60, "ip": 0, "op": 60, "w": 430, "h": 430, "nm": "wired-flat-298-coins", "ddd": 0, "assets": [{"id": "comp_1", "nm": "outlines", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Coin-Main 11", "sr": 1, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [100], "h": 1}, {"t": 30, "s": [0], "h": 1}, {"t": 32, "s": [100], "h": 1}, {"t": 120, "s": [100], "h": 1}, {"t": 135, "s": [0], "h": 1}, {"t": 300, "s": [100], "h": 1}, {"t": 330, "s": [0], "h": 1}, {"t": 332, "s": [100], "h": 1}], "ix": 11}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 300, "s": [90], "h": 1}], "ix": 10}, "p": {"a": 0, "k": [136, 136, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-46.502, 0], [0, 46.502], [46.502, 0], [0, -46.502]], "o": [[46.502, 0], [0, -46.502], [-46.502, 0], [0, 46.502]], "v": [[0, 84.2], [84.2, 0], [0, -84.2], [-84.2, 0]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [{"i": [[-46.502, 0], [0, 1.839], [46.502, 0], [0, -1.839]], "o": [[46.502, 0], [0, -1.839], [-46.502, 0], [0, 1.839]], "v": [[0, -23.787], [84.2, -27.117], [0, -30.447], [-84.2, -27.117]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[-46.502, 0], [0, 0.138], [46.502, 0], [0, -0.138]], "o": [[46.502, 0], [0, -0.138], [-46.502, 0], [0, 0.138]], "v": [[0, -27.9], [84.2, -28.15], [0, -28.4], [-84.2, -28.15]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [{"i": [[-46.502, 0], [0, 1.839], [46.502, 0], [0, -1.839]], "o": [[46.502, 0], [0, -1.839], [-46.502, 0], [0, 1.839]], "v": [[0, 31.213], [84.2, 27.883], [0, 24.553], [-84.2, 27.883]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[-46.502, 0], [0, 46.502], [46.502, 0], [0, -46.502]], "o": [[46.502, 0], [0, -46.502], [-46.502, 0], [0, 46.502]], "v": [[0, 84.2], [84.2, 0], [0, -84.2], [-84.2, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120, "s": [{"i": [[-46.502, 0], [0, 46.502], [46.502, 0], [0, -46.502]], "o": [[46.502, 0], [0, -46.502], [-46.502, 0], [0, 46.502]], "v": [[0, 84.2], [84.2, 0], [0, -84.2], [-84.2, 0]], "c": true}]}, {"t": 135, "s": [{"i": [[-46.502, 0], [0, 0.138], [46.502, 0], [0, -0.138]], "o": [[46.502, 0], [0, -0.138], [-46.502, 0], [0, 0.138]], "v": [[0, -27.9], [84.2, -28.15], [0, -28.4], [-84.2, -28.15]], "c": true}], "h": 1}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 300, "s": [{"i": [[-46.502, 0], [0, 46.502], [46.502, 0], [0, -46.502]], "o": [[46.502, 0], [0, -46.502], [-46.502, 0], [0, 46.502]], "v": [[0, 84.2], [84.2, 0], [0, -84.2], [-84.2, 0]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 329, "s": [{"i": [[-46.502, 0], [0, 1.839], [46.502, 0], [0, -1.839]], "o": [[46.502, 0], [0, -1.839], [-46.502, 0], [0, 1.839]], "v": [[0, -23.787], [84.2, -27.117], [0, -30.447], [-84.2, -27.117]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 330, "s": [{"i": [[-46.502, 0], [0, 0.138], [46.502, 0], [0, -0.138]], "o": [[46.502, 0], [0, -0.138], [-46.502, 0], [0, 0.138]], "v": [[0, -27.9], [84.2, -28.15], [0, -28.4], [-84.2, -28.15]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 331, "s": [{"i": [[-46.502, 0], [0, 1.839], [46.502, 0], [0, -1.839]], "o": [[46.502, 0], [0, -1.839], [-46.502, 0], [0, 1.839]], "v": [[0, 31.213], [84.2, 27.883], [0, 24.553], [-84.2, 27.883]], "c": true}]}, {"t": 360, "s": [{"i": [[-46.502, 0], [0, 46.502], [46.502, 0], [0, -46.502]], "o": [[46.502, 0], [0, -46.502], [-46.502, 0], [0, 46.502]], "v": [[0, 84.2], [84.2, 0], [0, -84.2], [-84.2, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.698, 0.408, 0.212, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [7]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 35, "s": [7]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 129, "s": [7]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 135, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 297, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 300, "s": [7]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 325, "s": [7]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 331, "s": [0]}, {"t": 334, "s": [7]}], "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 555, "st": 0, "bm": 0}]}, {"id": "comp_2", "nm": "Coin-fill", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 0, "k": [136, 136, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [59.25, 36.75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-21.217, 0], [-16.966, -9.278], [0, -45.16], [36.46, -20.4], [21.111, 0], [17.054, 9.406], [0, 45.006], [-36.318, 20.439]], "o": [[20.7, 0], [37.013, 20.241], [0, 44.748], [-17.201, 9.624], [-20.854, 0], [-36.806, -20.301], [0, -44.643], [17.261, -9.714]], "v": [[1.75, -119.5], [58.884, -104.948], [121, -0.25], [59.893, 103.89], [1.75, 119], [-55.761, 104.241], [-117.5, -0.25], [-56.651, -104.245]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [{"i": [[-8.496, 0], [-6.793, -5.426], [0, -70.455], [14.599, -12.057], [8.453, 0], [6.829, 5.523], [0, 70.216], [-14.542, 12.113]], "o": [[8.289, 0], [14.821, 11.837], [0, 69.813], [-6.888, 5.688], [-8.35, 0], [-14.738, -11.919], [0, -69.649], [6.911, -5.757]], "v": [[1.75, -119.5], [24.627, -112.444], [49.5, -0.25], [25.031, 111.616], [1.75, 119], [-21.279, 111.823], [-46, -0.25], [-21.635, -112.031]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[-13.966, 0], [-0.064, -18.759], [0, -49.288], [0.057, -17.398], [13.917, 0], [-0.192, 19.013], [0, 49.003], [0.17, 17.424]], "o": [[13.722, 0], [0.059, 17.294], [0, 48.527], [-0.064, 19.441], [-13.795, 0], [0.175, -17.334], [0, -48.334], [-0.192, -19.616]], "v": [[1.75, -119.5], [29.443, -90.274], [29.5, -0.25], [29.439, 91.714], [1.75, 119], [-26.188, 92.223], [-25.938, -0.25], [-26.174, -89.256]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [{"i": [[-8.496, 0], [-6.793, -5.426], [0, -70.455], [14.599, -12.057], [8.453, 0], [6.829, 5.523], [0, 70.216], [-14.542, 12.113]], "o": [[8.289, 0], [14.821, 11.837], [0, 69.813], [-6.888, 5.688], [-8.35, 0], [-14.738, -11.919], [0, -69.649], [6.911, -5.757]], "v": [[1.75, -119.5], [24.627, -112.444], [49.5, -0.25], [25.031, 111.616], [1.75, 119], [-21.279, 111.823], [-46, -0.25], [-21.635, -112.031]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[-21.217, 0], [-16.966, -9.278], [0, -45.16], [36.46, -20.4], [21.111, 0], [17.054, 9.406], [0, 45.006], [-36.318, 20.439]], "o": [[20.7, 0], [37.013, 20.241], [0, 44.748], [-17.201, 9.624], [-20.854, 0], [-36.806, -20.301], [0, -44.643], [17.261, -9.714]], "v": [[1.75, -119.5], [58.884, -104.948], [121, -0.25], [59.893, 103.89], [1.75, 119], [-55.761, 104.241], [-117.5, -0.25], [-56.651, -104.245]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 120, "s": [{"i": [[-21.217, 0], [-16.966, -9.278], [0, -45.16], [36.46, -20.4], [21.111, 0], [17.054, 9.406], [0, 45.006], [-36.318, 20.439]], "o": [[20.7, 0], [37.013, 20.241], [0, 44.748], [-17.201, 9.624], [-20.854, 0], [-36.806, -20.301], [0, -44.643], [17.261, -9.714]], "v": [[1.75, -119.5], [58.884, -104.948], [121, -0.25], [59.893, 103.89], [1.75, 119], [-55.761, 104.241], [-117.5, -0.25], [-56.651, -104.245]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [{"i": [[-8.496, 0], [-6.793, -5.426], [0, -70.455], [14.599, -12.057], [8.453, 0], [6.829, 5.523], [0, 70.216], [-14.542, 12.113]], "o": [[8.289, 0], [14.821, 11.837], [0, 69.813], [-6.888, 5.688], [-8.35, 0], [-14.738, -11.919], [0, -69.649], [6.911, -5.757]], "v": [[1.75, -119.5], [24.627, -112.444], [49.5, -0.25], [25.031, 111.616], [1.75, 119], [-21.279, 111.823], [-46, -0.25], [-21.635, -112.031]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [{"i": [[-11.513, 0], [-3.082, -12.78], [0, -58.78], [6.579, -15.003], [11.467, 0], [2.957, 12.963], [0, 58.516], [-6.427, 15.042]], "o": [[11.285, 0], [6.679, 14.847], [0, 58.073], [-3.124, 13.273], [-11.353, 0], [-6.513, -14.906], [0, -57.892], [2.994, -13.401]], "v": [[1.75, -119.5], [27.284, -100.216], [38.469, -0.25], [27.462, 100.639], [1.75, 119], [-23.986, 101.012], [-34.934, -0.25], [-24.138, -99.469]], "c": true}]}, {"t": 135, "s": [{"i": [[-13.966, 0], [-0.064, -18.759], [0, -49.288], [0.057, -17.398], [13.917, 0], [-0.192, 19.013], [0, 49.003], [0.17, 17.424]], "o": [[13.722, 0], [0.059, 17.294], [0, 48.527], [-0.064, 19.441], [-13.795, 0], [0.175, -17.334], [0, -48.334], [-0.192, -19.616]], "v": [[1.75, -119.5], [29.443, -90.274], [29.5, -0.25], [29.439, 91.714], [1.75, 119], [-26.188, 92.223], [-25.938, -0.25], [-26.174, -89.256]], "c": true}], "h": 1}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 300, "s": [{"i": [[-21.217, 0], [-16.966, -9.278], [0, -45.16], [36.46, -20.4], [21.111, 0], [17.054, 9.406], [0, 45.006], [-36.318, 20.439]], "o": [[20.7, 0], [37.013, 20.241], [0, 44.748], [-17.201, 9.624], [-20.854, 0], [-36.806, -20.301], [0, -44.643], [17.261, -9.714]], "v": [[1.75, -119.5], [58.884, -104.948], [121, -0.25], [59.893, 103.89], [1.75, 119], [-55.761, 104.241], [-117.5, -0.25], [-56.651, -104.245]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 324, "s": [{"i": [[-8.496, 0], [-6.793, -5.426], [0, -70.455], [14.599, -12.057], [8.453, 0], [6.829, 5.523], [0, 70.216], [-14.542, 12.113]], "o": [[8.289, 0], [14.821, 11.837], [0, 69.813], [-6.888, 5.688], [-8.35, 0], [-14.738, -11.919], [0, -69.649], [6.911, -5.757]], "v": [[1.75, -119.5], [24.627, -112.444], [49.5, -0.25], [25.031, 111.616], [1.75, 119], [-21.279, 111.823], [-46, -0.25], [-21.635, -112.031]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 330, "s": [{"i": [[-13.966, 0], [-0.064, -18.759], [0, -49.288], [0.057, -17.398], [13.917, 0], [-0.192, 19.013], [0, 49.003], [0.17, 17.424]], "o": [[13.722, 0], [0.059, 17.294], [0, 48.527], [-0.064, 19.441], [-13.795, 0], [0.175, -17.334], [0, -48.334], [-0.192, -19.616]], "v": [[1.75, -119.5], [29.443, -90.274], [29.5, -0.25], [29.439, 91.714], [1.75, 119], [-26.188, 92.223], [-25.938, -0.25], [-26.174, -89.256]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 336, "s": [{"i": [[-8.496, 0], [-6.793, -5.426], [0, -70.455], [14.599, -12.057], [8.453, 0], [6.829, 5.523], [0, 70.216], [-14.542, 12.113]], "o": [[8.289, 0], [14.821, 11.837], [0, 69.813], [-6.888, 5.688], [-8.35, 0], [-14.738, -11.919], [0, -69.649], [6.911, -5.757]], "v": [[1.75, -119.5], [24.627, -112.444], [49.5, -0.25], [25.031, 111.616], [1.75, 119], [-21.279, 111.823], [-46, -0.25], [-21.635, -112.031]], "c": true}]}, {"t": 360, "s": [{"i": [[-21.217, 0], [-16.966, -9.278], [0, -45.16], [36.46, -20.4], [21.111, 0], [17.054, 9.406], [0, 45.006], [-36.318, 20.439]], "o": [[20.7, 0], [37.013, 20.241], [0, 44.748], [-17.201, 9.624], [-20.854, 0], [-36.806, -20.301], [0, -44.643], [17.261, -9.714]], "v": [[1.75, -119.5], [58.884, -104.948], [121, -0.25], [59.893, 103.89], [1.75, 119], [-55.761, 104.241], [-117.5, -0.25], [-56.651, -104.245]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.780392169952, 0.219607844949, 1], "ix": 4}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.780392169952, 0.219607844949, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [57.5, 37], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 900, "st": 0, "bm": 0}]}, {"id": "comp_3", "nm": "Source-empty", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "outlines", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [1.187]}, "o": {"x": [0.239], "y": [-0.074]}, "t": 50, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [0.071]}, "t": 60.436, "s": [19.181]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74.783, "s": [-14]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 89, "s": [16]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 101, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 107.285, "s": [-3]}, {"t": 113, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [210.5, 493, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.942}, "o": {"x": 0.333, "y": 0}, "t": 70, "s": [208.5, 48, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [208.5, 110, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 89, "s": [211.5, 99, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [208.5, 110, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 107.285, "s": [206.5, 105, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 113, "s": [208.5, 110, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 50, "op": 114, "st": -70, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Rectangle 6", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [136.5, 136.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [{"i": [[0.508, 15.973], [36.307, -19.166], [50.977, 1.158], [0, 0], [-4.562, 19.079], [-15.13, -6.711], [-51.778, -0.877], [0, 0]], "o": [[0, 15.464], [0, 0], [-62.051, -1.41], [-21.997, -13.538], [-4.323, 19.154], [0, 0], [51.778, 0.877], [32.103, -15.351]], "v": [[118.5, 0], [81.345, 59.877], [-9.713, 75.095], [-91.507, 55.425], [-118.5, 0], [-91.421, 55.933], [-9.324, 74.926], [81.498, 60.074]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.146}, "t": 59, "s": [{"i": [[0.228, -1.366], [24.811, -8.595], [22.86, 0.519], [0, 0], [-2.046, 17.085], [-15.314, -3.009], [-23.22, -0.393], [0, 0]], "o": [[0, 15.464], [0, 0], [-27.827, -0.632], [-18.394, -6.071], [-1.939, 0.06], [0, 0], [23.22, 0.393], [22.926, -6.884]], "v": [[118.5, 0], [85.046, 38.63], [-7.865, 45.455], [-92.3, 36.634], [-118.5, 0], [-90.913, 9.639], [-7.214, 18.157], [86.463, 11.497]], "c": true}]}, {"t": 65, "s": [{"i": [[0, -15.464], [15.464, 0], [0, 0], [0, 0], [0, 15.464], [-15.464, 0], [0, 0], [0, 0]], "o": [[0, 15.464], [0, 0], [0, 0], [-15.464, 0], [0, -15.464], [0, 0], [0, 0], [15.464, 0]], "v": [[118.5, 0], [90.5, 28], [-3.919, 28], [-90.5, 28], [-118.5, 0], [-90.5, -28], [-5.498, -28], [90.5, -28]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 4", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 98, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}], "ip": 57, "op": 877, "st": -23, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "Coin-fill", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [1.187]}, "o": {"x": [0.239], "y": [-0.074]}, "t": 50, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [0.071]}, "t": 60.436, "s": [19.181]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74.783, "s": [-14]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 89, "s": [16]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 101, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 107.285, "s": [-3]}, {"t": 113, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [210.5, 493, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.942}, "o": {"x": 0.333, "y": 0}, "t": 70, "s": [208.5, 48, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [208.5, 110, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 89, "s": [211.5, 99, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [208.5, 110, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 107.285, "s": [206.5, 105, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 113, "s": [208.5, 110, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 50, "op": 114, "st": -70, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "source-3-empty", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 90, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Rectangle 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [180.5, 277, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 4", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 98, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}], "ip": 89, "op": 900, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Rectangle 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [152.5, 165.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 98, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}], "ip": 89, "op": 900, "st": 0, "bm": 0}]}, {"id": "comp_4", "nm": "source-3-empty", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "outlines", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [0.82]}, "o": {"x": [0.239], "y": [0.07]}, "t": 31, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [-0.067]}, "t": 40.082, "s": [-20]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52.568, "s": [15]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 57.107, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 65.055, "s": [-7]}, {"t": 73, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [155.5, 549, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.911}, "o": {"x": 0.333, "y": 0}, "t": 48.027, "s": [153.5, 124, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 57.107, "s": [153.5, 166, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.52}, "o": {"x": 0.333, "y": 0}, "t": 65.055, "s": [151.5, 160, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 73, "s": [153.5, 166.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 31, "op": 90, "st": -89, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Coin-fill", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [0.82]}, "o": {"x": [0.239], "y": [0.07]}, "t": 31, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [-0.067]}, "t": 40.082, "s": [-20]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52.568, "s": [15]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 57.107, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 65.055, "s": [-7]}, {"t": 73, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [155.5, 549, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.911}, "o": {"x": 0.333, "y": 0}, "t": 48.027, "s": [153.5, 124, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 57.107, "s": [153.5, 166, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.52}, "o": {"x": 0.333, "y": 0}, "t": 65.055, "s": [151.5, 160, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 73, "s": [153.5, 166.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 31, "op": 90, "st": -89, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "source-2-empty", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 90, "st": 0, "bm": 0}]}, {"id": "comp_5", "nm": "source-2-empty", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "outlines", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [1.187]}, "o": {"x": [0.239], "y": [-0.073]}, "t": 14, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [0.071]}, "t": 22, "s": [19.181]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33, "s": [-14]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 37, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 43, "s": [-5]}, {"t": 48, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [208.5, 570, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.911}, "o": {"x": 0.333, "y": 0}, "t": 29, "s": [206.5, 180, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [206.5, 222, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 43, "s": [203.5, 213, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 48, "s": [206.5, 222.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 14, "op": 90, "st": -106, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "outlines", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [1.187]}, "o": {"x": [0.239], "y": [-0.073]}, "t": 0, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [0.071]}, "t": 8, "s": [19.181]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [-14]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [9]}, {"t": 33, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [183.5, 570, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.929}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [181.5, 236, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [181.5, 278, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [188.5, 263, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 33, "s": [181.5, 278, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 28, "op": 90, "st": -120, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "outlines", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [1.187]}, "o": {"x": [0.239], "y": [-0.073]}, "t": 0, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [0.071]}, "t": 8, "s": [19.181]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [-14]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [9]}, {"t": 33, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [183.5, 570, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.929}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [181.5, 236, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [181.5, 278, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [188.5, 263, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 33, "s": [181.5, 278, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 17, "op": 28, "st": -120, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "outlines", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [1.187]}, "o": {"x": [0.239], "y": [-0.073]}, "t": 0, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [0.071]}, "t": 8, "s": [19.181]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [-14]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [9]}, {"t": 33, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [183.5, 570, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.929}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [181.5, 236, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [181.5, 278, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [188.5, 263, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 33, "s": [181.5, 278, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 0, "op": 17, "st": -120, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Rectangle 6", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [136.5, 136.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [{"i": [[0.508, 15.973], [39.556, -10.993], [50.977, 1.158], [0, 0], [0, 15.464], [-13.339, -2.286], [-51.778, -0.877], [0, 0]], "o": [[0, 15.464], [0, 0], [-62.051, -1.41], [-20.241, -6.959], [-1.234, 16.494], [0, 0], [51.778, 0.877], [34.636, -8.161]], "v": [[118.5, 0], [75.4, 49.414], [-11.868, 56.077], [-91.069, 43.091], [-118.5, 0], [-90.888, 43.555], [-11.479, 55.908], [75.507, 49.639]], "c": true}]}, {"t": 29, "s": [{"i": [[0, -15.464], [15.464, 0], [0, 0], [0, 0], [0, 15.464], [-15.464, 0], [0, 0], [0, 0]], "o": [[0, 15.464], [0, 0], [0, 0], [-15.464, 0], [0, -15.464], [0, 0], [0, 0], [15.464, 0]], "v": [[118.5, 0], [90.5, 28], [-3.919, 28], [-90.5, 28], [-118.5, 0], [-90.5, -28], [-5.498, -28], [90.5, -28]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 4", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 98, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}], "ip": 23, "op": 841, "st": -59, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "Coin-fill", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [1.187]}, "o": {"x": [0.239], "y": [-0.073]}, "t": 14, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [0.071]}, "t": 22, "s": [19.181]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33, "s": [-14]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 37, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 43, "s": [-5]}, {"t": 48, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [208.5, 570, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.911}, "o": {"x": 0.333, "y": 0}, "t": 29, "s": [206.5, 180, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [206.5, 222, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 43, "s": [203.5, 213, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 48, "s": [206.5, 222.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 14, "op": 90, "st": -106, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 0, "nm": "Coin-fill", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [1.187]}, "o": {"x": [0.239], "y": [-0.073]}, "t": 0, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [0.071]}, "t": 8, "s": [19.181]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [-14]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [9]}, {"t": 33, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [183.5, 570, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.929}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [181.5, 236, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [181.5, 278, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [188.5, 263, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 33, "s": [181.5, 278, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 0, "op": 93, "st": -120, "bm": 0}]}, {"id": "comp_7", "nm": "jump-stack", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Rectangle 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [180.5, 277, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 4", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [25.002, -56.105], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 3", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [-28.047, -111.988], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 2", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [25.969, -167.949], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 88, "op": 138, "st": 91, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Rectangle 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [180.5, 277, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 4", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [25.002, -56.105], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 3", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [-28.047, -111.988], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 2", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [25.969, -167.949], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 2, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Rectangle 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [180.5, 277, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 4", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 89, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Rectangle 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.11], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.89], "y": [0]}, "t": 18.566, "s": [6]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33.412, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 48.242, "s": [-4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 59.621, "s": [0]}, {"t": 86.716796875, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.11, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [205.5, 221, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.89, "y": 0}, "t": 18.566, "s": [205.5, 201, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33.412, "s": [205.5, 221, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 48.242, "s": [205.5, 202, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 59.621, "s": [205.5, 221, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 74.4, "s": [205.5, 218, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 86.716796875, "s": [205.5, 221, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}], "ip": 1, "op": 89, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Rectangle 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.11], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.89], "y": [0]}, "t": 22, "s": [-7]}, {"i": {"x": [0.47], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32.707, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.53], "y": [0]}, "t": 47.535, "s": [4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 62.904, "s": [0]}, {"t": 88.357421875, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.11, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [152.5, 165, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.89, "y": 0}, "t": 15.283, "s": [152.5, 122, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.47, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 32.707, "s": [152.5, 165, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.53, "y": 0}, "t": 47.535, "s": [152.5, 134, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 62.904, "s": [152.5, 165, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 76.043, "s": [152.5, 159, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 88.357421875, "s": [152.5, 165, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}], "ip": 1, "op": 89, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Rectangle 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.11], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.89], "y": [0]}, "t": 18.717, "s": [8]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 41, "s": [-10]}, {"i": {"x": [0.807], "y": [1.059]}, "o": {"x": [0.568], "y": [0]}, "t": 58.514, "s": [14.692]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 71, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 82.609, "s": [4]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.11, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [206.5, 109, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.89, "y": 0}, "t": 12, "s": [206.5, 49, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.249, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [206.5, 109, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 48.947, "s": [206.5, 55, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 67.01, "s": [206.5, 109, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 77.684, "s": [206.5, 95, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [206.5, 109, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}], "ip": 1, "op": 89, "st": 0, "bm": 0}]}, {"id": "comp_9", "nm": "loop-spin", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "outlines", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [274.4, 252.525, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -300, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [5]}, {"t": 60, "s": [6]}], "ix": 2}, "w": 273, "h": 273, "ip": 0, "op": 288, "st": -300, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Coin-fill", "td": 1, "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -90, "ix": 10}, "p": {"a": 0, "k": [274.4, 251.275, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -300, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [5]}, {"t": 60, "s": [6]}], "ix": 2}, "w": 273, "h": 273, "ip": 0, "op": 288, "st": -300, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "shadow 2", "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [271.5, 246.875, 0], "ix": 2, "l": 2}, "a": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [64.25, 28.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 30, "s": [199.25, -74.75, 0], "h": 1}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [-19.75, 135.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 60, "s": [64.25, 28.25, 0]}], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.5, -110.5], [-75, -71], [135, 167], [203.5, 134]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 870, "st": -30, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "Coin-fill", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -90, "ix": 10}, "p": {"a": 0, "k": [274.4, 251.275, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -300, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [5]}, {"t": 60, "s": [6]}], "ix": 2}, "w": 273, "h": 273, "ip": 0, "op": 288, "st": -300, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Rectangle 5", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [180.5, 277, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 4", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [25.002, -56.105], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 3", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [-28.047, -111.988], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 2", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [25.969, -167.949], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 900, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "mask", "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [246.75, 227.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [59.25, 36.75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-21.217, 0], [-16.966, -9.278], [0, -45.16], [36.46, -20.4], [21.111, 0], [17.054, 9.406], [0, 45.006], [-36.318, 20.439]], "o": [[20.7, 0], [37.013, 20.241], [0, 44.748], [-17.201, 9.624], [-20.854, 0], [-36.806, -20.301], [0, -44.643], [17.261, -9.714]], "v": [[1.75, -119.5], [58.884, -104.948], [121, -0.25], [59.893, 103.89], [1.75, 119], [-55.761, 104.241], [-117.5, -0.25], [-56.651, -104.245]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [{"i": [[-8.496, 0], [-6.793, -5.426], [0, -70.455], [14.599, -12.057], [8.453, 0], [6.829, 5.523], [0, 70.216], [-14.542, 12.113]], "o": [[8.289, 0], [14.821, 11.837], [0, 69.813], [-6.888, 5.688], [-8.35, 0], [-14.738, -11.919], [0, -69.649], [6.911, -5.757]], "v": [[1.75, -119.5], [24.627, -112.444], [49.5, -0.25], [25.031, 111.616], [1.75, 119], [-21.279, 111.823], [-46, -0.25], [-21.635, -112.031]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[-13.966, 0], [-0.064, -18.759], [0, -49.288], [0.057, -17.398], [13.917, 0], [-0.192, 19.013], [0, 49.003], [0.17, 17.424]], "o": [[13.722, 0], [0.059, 17.294], [0, 48.527], [-0.064, 19.441], [-13.795, 0], [0.175, -17.334], [0, -48.334], [-0.192, -19.616]], "v": [[1.75, -119.5], [29.443, -90.274], [29.5, -0.25], [29.439, 91.714], [1.75, 119], [-26.188, 92.223], [-25.938, -0.25], [-26.174, -89.256]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [{"i": [[-8.496, 0], [-6.793, -5.426], [0, -70.455], [14.599, -12.057], [8.453, 0], [6.829, 5.523], [0, 70.216], [-14.542, 12.113]], "o": [[8.289, 0], [14.821, 11.837], [0, 69.813], [-6.888, 5.688], [-8.35, 0], [-14.738, -11.919], [0, -69.649], [6.911, -5.757]], "v": [[1.75, -119.5], [24.627, -112.444], [49.5, -0.25], [25.031, 111.616], [1.75, 119], [-21.279, 111.823], [-46, -0.25], [-21.635, -112.031]], "c": true}]}, {"t": 60, "s": [{"i": [[-21.217, 0], [-16.966, -9.278], [0, -45.16], [36.46, -20.4], [21.111, 0], [17.054, 9.406], [0, 45.006], [-36.318, 20.439]], "o": [[20.7, 0], [37.013, 20.241], [0, 44.748], [-17.201, 9.624], [-20.854, 0], [-36.806, -20.301], [0, -44.643], [17.261, -9.714]], "v": [[1.75, -119.5], [58.884, -104.948], [121, -0.25], [59.893, 103.89], [1.75, 119], [-55.761, 104.241], [-117.5, -0.25], [-56.651, -104.245]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.780392169952, 0.219607844949, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [57.5, 37], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 900, "st": 0, "bm": 1}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Rectangle 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [180.5, 277, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 4", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [25.002, -56.105], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 3", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [-28.047, -111.988], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 2", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [25.969, -167.949], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 900, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 1, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [1, 0.78, 0.22], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.698, 0.408, 0.212], "ix": 1}}]}], "ip": 0, "op": 420, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "loop-spin", "refId": "comp_9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 70, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:loop-spin", "dr": 60}]}