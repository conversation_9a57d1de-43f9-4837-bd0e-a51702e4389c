import { 
  isProd, mixpanelToken,
} from '@/common/constants';
import mixpanel, { Dict } from 'mixpanel-browser';

if (mixpanelToken) {
  if (isProd) {
    mixpanel.init(mixpanelToken, {
      record_sessions_percent: 1,
    });
    mixpanel.start_session_recording()
  } else {
    mixpanel.init(mixpanelToken);
  }
  
}
const actions = {
  identify: (id: string) => {
    mixpanel.identify(id);
  },
  alias: (id: string) => {
    mixpanel.alias(id);
  },
  track: (name: string, props: Dict = {}) => {
    mixpanel.track(name, props);
  },
  people: {
    set: (props: Dict) => {
      mixpanel.people.set(props);
    },
  },
  register: (props: Dict) => {
    mixpanel.register(props);
  },
};

export const Mixpanel = actions;
