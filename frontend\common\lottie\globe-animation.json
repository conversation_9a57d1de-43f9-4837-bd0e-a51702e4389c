{"v": "5.12.1", "fr": 60, "ip": 0, "op": 70, "w": 430, "h": 430, "nm": "wired-outline-27-globe", "ddd": 0, "assets": [{"id": "comp_1", "nm": "Globe_net", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "NULL CONTROL", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.917, 215.001, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [60, 60, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [369, 369, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Warstwa 4", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [60.001, 94.484, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.668, 34.485, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-12.33, 0], [-8.72, -4.56]], "o": [[8.71, -4.56], [12.33, 0], [0, 0]], "v": [[94.343, 38.14], [126.663, 30.83], [158.993, 38.14]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Warstwa 5", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [60.001, 25.516, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.668, -34.483, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [12.33, 0], [8.72, 4.56]], "o": [[-8.71, 4.56], [-12.33, 0], [0, 0]], "v": [[158.993, -38.138], [126.673, -30.828], [94.343, -38.138]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Warstwa 14", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[76.667, 0], [176.667, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Warstwa 20", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}, {"t": 10, "s": [{"i": [[0, 0], [0, 34.5], [17.029, 0]], "o": [[19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [176.25, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 10, "st": -30, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Warstwa 19", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}, {"t": 20, "s": [{"i": [[0, 0], [0, 34.5], [17.029, 0]], "o": [[19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [176.25, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 20, "st": -20, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Warstwa 18", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}, {"t": 30, "s": [{"i": [[0, 0], [0, 34.5], [17.029, 0]], "o": [[19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [176.25, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 30, "st": -10, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Warstwa 22", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 34.5], [-17.029, 0]], "o": [[-19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [77.083, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}, {"t": 40, "s": [{"i": [[0, 0], [0, 34.5], [17.029, 0]], "o": [[19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [176.25, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 2.5, "op": 40, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Warstwa 23", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 34.5], [-17.029, 0]], "o": [[-19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [77.083, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}, {"t": 50, "s": [{"i": [[0, 0], [0, 34.5], [17.029, 0]], "o": [[19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [176.25, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 10, "op": 50, "st": 10, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Warstwa 24", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0, 34.5], [-17.029, 0]], "o": [[-19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [77.083, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}, {"t": 60, "s": [{"i": [[0, 0], [0, 34.5], [17.029, 0]], "o": [[19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [176.25, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 20, "op": 60, "st": 20, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Warstwa 17", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[0, 0], [0, 34.5], [-17.029, 0]], "o": [[-19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [77.083, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}, {"t": 70, "s": [{"i": [[0, 0], [0, 34.5], [17.286, 0]], "o": [[20.218, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [177, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 30, "op": 70, "st": 30, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Warstwa 16", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[0, 0], [0, 34.5], [-17.029, 0]], "o": [[-19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [77.083, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}, {"t": 70, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 40, "op": 100, "st": 40, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Warstwa 21", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0, 0], [0, 34.5], [-17.029, 0]], "o": [[-19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [77.083, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}, {"t": 70, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 50, "op": 100, "st": 50, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Warstwa 3", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [{"i": [[0, 0], [0, 34.5], [-17.029, 0]], "o": [[-19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [77.083, 0], [126.667, -50]], "c": false}]}, {"t": 70, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 60, "op": 100, "st": 60, "ct": 1, "bm": 0}]}, {"id": "comp_3", "nm": "hover-rotate-up-to-down", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Warstwa 2", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [369, 369, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -27.614], [27.614, 0], [0, 27.614], [-27.614, 0]], "o": [[0, 27.614], [-27.614, 0], [0, -27.614], [27.614, 0]], "v": [[176.667, 0], [126.667, 50], [76.667, 0], [126.667, -50]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Globe_net 2", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.299, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [215, 215, 0], "to": [0, 3.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [215, 235, 0], "to": [0, 0, 0], "ti": [0, 3.333, 0]}, {"t": 70, "s": [215, 215, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [90, 90, 100], "ix": 6, "l": 2}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.439], "y": [1.022]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 70, "s": [1.167]}], "ix": 2}, "w": 430, "h": 430, "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_4", "nm": "Globe_net 2", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "NULL CONTROL", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.917, 215.001, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [60, 60, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [369, 369, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Warstwa 4", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [60.001, 94.484, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.668, 34.485, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [-12.33, 0], [-8.72, -4.56]], "o": [[8.71, -4.56], [12.33, 0], [0, 0]], "v": [[94.343, 38.14], [126.663, 30.83], [158.993, 38.14]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [-12.33, 0], [-8.72, -0.631]], "o": [[8.71, -0.631], [12.33, 0], [0, 0]], "v": [[94.343, 38.14], [126.663, 37.128], [158.993, 38.14]], "c": false}]}, {"t": 67.5, "s": [{"i": [[0, 0], [-12.33, 0], [-8.72, -4.56]], "o": [[8.71, -4.56], [12.33, 0], [0, 0]], "v": [[94.343, 38.14], [126.663, 30.83], [158.993, 38.14]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Warstwa 5", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [60.001, 25.516, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.668, -34.483, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [12.33, 0], [8.72, 4.56]], "o": [[-8.71, 4.56], [-12.33, 0], [0, 0]], "v": [[158.993, -38.138], [126.673, -30.828], [94.343, -38.138]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [15.193, 0], [5.788, 7.516]], "o": [[-5.745, 6.974], [-16.515, 0], [0, 0]], "v": [[158.977, -34.344], [126.657, -23.306], [94.327, -34.344]], "c": false}]}, {"t": 67.5, "s": [{"i": [[0, 0], [12.33, 0], [8.72, 4.56]], "o": [[-8.71, 4.56], [-12.33, 0], [0, 0]], "v": [[158.993, -38.138], [126.673, -30.828], [94.343, -38.138]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Warstwa 14", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[76.667, 0], [127.108, 0], [176.667, 0]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [-33.604, 0], [0, 0]], "o": [[0, 0], [33.875, 0], [0, 0]], "v": [[76.667, 6.504], [126.961, 17.073], [176.667, 6.504]], "c": false}]}, {"t": 67.5, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[76.667, 0], [127.108, 0], [176.667, 0]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Warstwa 20", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}, {"t": 10, "s": [{"i": [[0, 0], [0, 34.5], [17.029, 0]], "o": [[19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [176.25, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 10, "st": -30, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Warstwa 19", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}, {"t": 20, "s": [{"i": [[0, 0], [0, 34.5], [17.029, 0]], "o": [[19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [176.25, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 20, "st": -20, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Warstwa 18", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}, {"t": 30, "s": [{"i": [[0, 0], [0, 34.5], [17.029, 0]], "o": [[19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [176.25, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 30, "st": -10, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Warstwa 22", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 34.5], [-17.029, 0]], "o": [[-19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [77.083, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}, {"t": 40, "s": [{"i": [[0, 0], [0, 34.5], [17.029, 0]], "o": [[19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [176.25, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 2.5, "op": 40, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Warstwa 23", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 34.5], [-17.029, 0]], "o": [[-19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [77.083, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}, {"t": 50, "s": [{"i": [[0, 0], [0, 34.5], [17.029, 0]], "o": [[19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [176.25, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 10, "op": 50, "st": 10, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Warstwa 24", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0, 34.5], [-17.029, 0]], "o": [[-19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [77.083, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}, {"t": 60, "s": [{"i": [[0, 0], [0, 34.5], [17.029, 0]], "o": [[19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [176.25, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 20, "op": 60, "st": 20, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Warstwa 17", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[0, 0], [0, 34.5], [-17.029, 0]], "o": [[-19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [77.083, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}, {"t": 70, "s": [{"i": [[0, 0], [0, 34.5], [17.286, 0]], "o": [[20.218, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [177, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 30, "op": 70, "st": 30, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Warstwa 16", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[0, 0], [0, 34.5], [-17.029, 0]], "o": [[-19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [77.083, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}, {"t": 70, "s": [{"i": [[0, 0], [0, 27.614], [17.581, 0]], "o": [[17.581, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [158.5, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 40, "op": 100, "st": 40, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Warstwa 21", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0, 0], [0, 34.5], [-17.029, 0]], "o": [[-19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [77.083, 0], [126.667, -50]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}, {"t": 70, "s": [{"i": [[0, 0], [0, 27.614], [0.083, 15.75]], "o": [[0.083, -8.75], [0, -27.614], [0, 0]], "v": [[126.667, 50], [126.833, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 50, "op": 100, "st": 50, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Warstwa 3", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59.999, 59.999, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [126.667, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [{"i": [[0, 0], [0, 34.5], [-17.029, 0]], "o": [[-19.917, 0], [0, -35], [0, 0]], "v": [[126.667, 50], [77.083, 0], [126.667, -50]], "c": false}]}, {"t": 70, "s": [{"i": [[0, 0], [0, 27.614], [-17.029, 0]], "o": [[-17.029, 0], [0, -27.614], [0, 0]], "v": [[126.667, 50], [95.833, 0], [126.667, -50]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-27-globe').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5.25, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-outline-27-globe').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 60, "op": 100, "st": 60, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "stroke", "np": 3, "mn": "Pseudo/@@rqb6+Q91Sz+MqFDpeGKQ6g", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "<PERSON><PERSON>", "mn": "Pseudo/@@rqb6+Q91Sz+MqFDpeGKQ6g-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}, {"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [1, 1, 1], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [1, 1, 1], "ix": 1}}]}], "ip": 0, "op": 331, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "hover-rotate-up-to-down", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 80, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-rotate-up-to-down", "dr": 70}], "props": {}}