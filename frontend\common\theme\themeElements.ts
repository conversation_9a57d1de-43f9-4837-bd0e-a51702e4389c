export const themeElements = {
  buttons: {
    gradient: {
      style: `flex items-center justify-center hover:translate-y-px focus:translate-y-px group text-white whitespace-nowrap gap-2 outline-none disabled:cursor-not-allowed duration-200 ease-in-out transition-all bg-gradient-to-tr from-han-purple to-tulip hover:from-han-purple/90 hover:to-tulip/90 hover:shadow-md hover:shadow-han-purple/20 focus:shadow-md focus:shadow-han-purple/20 disabled:from-han-purple/40 disabled:to-tulip/40 disabled:shadow-none`,
      size: {
        xs: 'px-3 py-1 text-xs font-medium rounded-lg',
        sm: 'px-4 py-2 text-sm font-medium rounded-xl',
        md: 'px-4 py-2 text-sm font-medium rounded-xl',
        lg: 'px-6 py-2 text-sm font-medium rounded-2xl',
      },
    },
    outline: {
      style: `group whitespace-nowrap text-white hover:translate-y-px focus:translate-y-px outline-none transition-all duration-200 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed`,
      container: `relative overflow-visible p-px`,
      inner: `flex items-center relative justify-center w-auto text-white z-10 bg-gray-800 transition-all duration-200 ease-in-out group-hover:bg-gray-800 gap-2`,
      animation: `flex-none inset-0 overflow-hidden absolute z-0`,
      background: `absolute z-1 flex-none`,
      borderRadius: {
        xs: 'rounded-lg',
        sm: 'rounded-xl',
        md: 'rounded-xl',
        lg: 'rounded-2xl',
      },
      size: {
        xs: 'px-3 py-[3px] text-xs font-medium',
        sm: 'px-4 py-[7px] text-sm font-medium',
        md: 'px-4 py-[7px] text-sm font-medium',
        lg: 'px-4 py-[7px] text-sm font-medium',
      },
    },
    'outline-rounded': {
      style: `flex items-center justify-center group gap-2 whitespace-nowrap rounded-full outline-none transition-all duration-200 ease-in-out bg-gradient-to-tr from-tulip to-violets-are-blue border border-violets-are-blue/15 hover:text-white text-transparent bg-clip-text disabled:opacity-50 disabled:cursor-not-allowed`,
      size: {
        xs: 'px-3 py-1 font-semibold text-xs',
        sm: 'px-4 py-2 font-semibold text-sm',
        md: 'px-4 py-2.5 font-semibold text-sm',
        lg: 'px-6 py-2.5 font-semibold text-sm',
      },
    },
  },
  links: {
    gradient: {
      style: `flex items-center justify-center hover:translate-y-px focus:translate-y-px group text-white whitespace-nowrap gap-2 outline-none disabled:cursor-not-allowed duration-200 ease-in-out transition-all bg-gradient-to-tr from-han-purple to-tulip hover:from-han-purple/90 hover:to-tulip/90 hover:shadow-md hover:shadow-han-purple/20 focus:shadow-md focus:shadow-han-purple/20 disabled:from-han-purple/40 disabled:to-tulip/40 disabled:shadow-none`,
      size: {
        xs: 'px-3 py-1 text-xs font-medium rounded-lg',
        sm: 'px-4 py-2 text-sm font-medium rounded-xl',
        md: 'px-4 py-2 text-sm font-medium rounded-xl',
        lg: 'px-6 py-2 text-sm font-medium rounded-2xl',
      },
    },
    outline: {
      style: `group whitespace-nowrap text-white hover:translate-y-px focus:translate-y-px outline-none transition-all duration-200 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed`,
      container: `relative overflow-visible p-px`,
      inner: `flex items-center relative justify-center w-auto text-white z-10 bg-gray-800 gap-2`,
      animation: `flex-none inset-0 overflow-hidden absolute z-0`,
      background: `absolute z-1 flex-none`,
      borderRadius: {
        xs: 'rounded-lg',
        sm: 'rounded-xl',
        md: 'rounded-xl',
        lg: 'rounded-2xl',
      },
      size: {
        xs: 'px-3 py-[3px] text-xs font-medium',
        sm: 'px-4 py-[7px] text-sm font-medium',
        md: 'px-4 py-[7px] text-sm font-medium',
        lg: 'px-6 py-[7px] text-sm font-medium',
      },
    },
    'text-violet': {
      style: `text-violets-are-blue hover:text-violets-are-blue/80 group font-medium transition-colors duration-200 ease-in-out`,
      size: {
        xs: '',
        sm: '',
        md: '',
        lg: '',
      },
    },
    'outline-rounded': {
      style: `flex items-center justify-center group gap-2 whitespace-nowrap rounded-full outline-none transition-all duration-200 ease-in-out bg-gradient-to-tr from-tulip to-violets-are-blue border border-violets-are-blue/15 hover:text-white text-transparent bg-clip-text disabled:opacity-50 disabled:cursor-not-allowed`,
      size: {
        xs: 'px-3 py-1 font-semibold text-xs',
        sm: 'px-4 py-2 font-semibold text-sm',
        md: 'px-4 py-2.5 font-semibold text-sm',
        lg: 'px-6 py-2.5 font-semibold text-sm',
      },
    },
  },
}
