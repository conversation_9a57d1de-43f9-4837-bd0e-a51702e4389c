import { ReactNode } from "react";
import { Metadata } from "next";
import { Header } from "@/common/components/organisms";

export const metadata: Metadata = {
  title: 'Lumina Casa | Transformative Community Experiences',
  description: 'Merging Capital with CommUnity & Consciousness. Healing, Education, Awakening, Purpose, Prosperity, Joy for All.',
  alternates: {
    canonical: `https://casa.dreamstartr.com`,
  },
  openGraph: {
    title: 'Lumina Casa | Transformative Community Experiences',
    description: 'Merging Capital with CommUnity & Consciousness. Healing, Education, Awakening, Purpose, Prosperity, Joy for All.',
    url: `https://casa.dreamstartr.com`,
    siteName: 'Lumina Casa',
    images: [
      {
        url: 'https://kajabi-storefronts-production.kajabi-cdn.com/kajabi-storefronts-production/file-uploads/themes/2160502787/settings_images/1261d2d-e26-0c6d-be2c-5670714e4f82_Lumina_Casa.png',
        width: 1200,
        height: 630,
        alt: 'Lumina Casa',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Lumina Casa | Transformative Community Experiences',
    description: 'Merging Capital with CommUnity & Consciousness. Healing, Education, Awakening, Purpose, Prosperity, Joy for All.',
    images: ['https://kajabi-storefronts-production.kajabi-cdn.com/kajabi-storefronts-production/file-uploads/themes/2160502787/settings_images/1261d2d-e26-0c6d-be2c-5670714e4f82_Lumina_Casa.png'],
  },
}

export default function Layout ({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <div className="relative">
      <Header />
      {children}
    </div>
  )
}
