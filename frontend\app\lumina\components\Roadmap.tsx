'use client'

import {
  motion, useInView, useScroll, useTransform,
} from 'framer-motion';
import {
  useRef, useState, useEffect,
} from 'react';
import {
  Globe,
  Home,
  Coins,
  Users,
  HeartHandshake,
} from 'lucide-react';
import { useWindowDimensions } from "@/common/hooks/useWindowDimensions";

interface TimelineEntry {
  title: string;
  content: React.ReactNode;
}

const CustomTimeline = ({ data }: { data: TimelineEntry[] }) => {
  const ref = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState(0);

  useEffect(() => {
    if (ref.current) {
      const rect = ref.current.getBoundingClientRect();
      setHeight(rect.height);
    }
  }, [ref]);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start 70%", "end 100%"],
  });

  const heightTransform = useTransform(scrollYProgress, [0, 1], [0, height]);
  const opacityTransform = useTransform(scrollYProgress, [0, 0.1], [0, 1]);
  const { windowSize } = useWindowDimensions();

  return (
    <div
      className=""
      ref={containerRef}
    >
      <div className="mt-4 md:mt-8 relative">
        <div ref={ref} className="mx-auto">
          {data.map((item, index) => (
            <div
              key={index}
              className="flex justify-start mb-4 md:gap-10"
            >
              <div className="flex flex-col md:flex-row z-40 items-center top-40 self-start md:w-full lg:max-w-sm">
                <div className="h-6 absolute left-0 w-6 rounded-full bg-gradient-to-br from-light-yellow to-yellow-200 shadow-md shadow-black flex items-center justify-center">
                  <div className="h-2 w-2 rounded-full bg-eerie-black" />
                </div>
                <h3 className="hidden md:block text-lg md:pl-16 font-semibold text-white">
                  {item.title}
                </h3>
              </div>

              <div className="relative pl-16 pr-4 md:pl-4 w-full flex flex-col justify-center">
                <h3 className="md:hidden block md:pl-16 font-semibold text-white">
                  {item.title}
                </h3>
                {item.content}{" "}
              </div>
            </div>
          ))}
          <div
            style={{
              height: (windowSize === "tablet" || windowSize === "mobile") ? height - 80 + "px" : height - 40 + "px",
            }}
            className="absolute left-3 top-2 overflow-hidden w-[2px] bg-[linear-gradient(to_bottom,var(--tw-gradient-stops))] from-transparent from-[0%] via-neutral-200 dark:via-neutral-700 to-transparent to-[99%]  [mask-image:linear-gradient(to_bottom,transparent_0%,black_10%,black_90%,transparent_100%)] "
          >
            <motion.div
              style={{
                height: heightTransform,
                opacity: opacityTransform,
              }}
              className="absolute top-0 left-3 w-[2px] bg-gradient-to-t from-light-yellow via-yellow-200 to-transparent from-[0%] via-[10%] rounded-full"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const Roadmap = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, {
    once: true,
    amount: 0.2,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  const timelineData = [
    {
      title: "Establish 55 Real-World Excellence Centers",
      content: (
        <div className="">
          <div className="flex items-start gap-3 mb-2">
            <Home className="w-5 h-5 text-light-yellow mt-1 flex-shrink-0 md:block hidden" />
            <p className="text-neutral-300 text-sm">
              Sanctuaries dedicated to quantum wellness, spiritual innovation, sacred economics, and the co-creation of joyful sovereignty.
            </p>
          </div>
        </div>
      ),
    },
    {
      title: "Launch 144 Digital Energetic Nodes",
      content: (
        <div className="">
          <div className="flex items-start gap-3 mb-2">
            <Globe className="w-5 h-5 text-light-yellow mt-1 flex-shrink-0 md:block hidden" />
            <p className="text-neutral-300 text-sm">
              Activating a planetary golden grid to ensure instant access to the Lumina ecosystem — a quantum field of support, wisdom, and opportunity.
            </p>
          </div>
        </div>
      ),
    },
  ];

  return (
    <section
      id="roadmap"
      ref={sectionRef}
      className="py-4 lg:py-8 relative w-full container px-2 md:px-8 mx-auto !max-w-[1200px]"
    >
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="mb-8 lg:mb-12"
      >
        <motion.h2
          variants={itemVariants}
          className="text-xl mb-2 md:text-2xl lg:text-3xl font-semibold text-white"
        >
          Global Vision: 5-Year Expansion Plan
        </motion.h2>
        <motion.div
          variants={itemVariants}
          className="rounded-full h-1 w-20 bg-gradient-to-tr from-light-yellow to-yellow-600 mb-4"
        ></motion.div>
        <motion.p
          variants={itemVariants}
          className="text-neutral-300 mb-4 text-sm md:text-base max-w-3xl"
        >
          Lumina CASA is architecting a planetary system of coherence and vitality
        </motion.p>

        <motion.div variants={itemVariants}>
          <CustomTimeline data={timelineData} />
        </motion.div>

        <motion.div
          variants={itemVariants}
          className="bg-eerie-black border border-light-yellow/15 rounded-3xl p-6 mt-12 w-full relative overflow-hidden"
        >
          <div className="absolute pointer-events-none bottom-10 z-0 left-1/2 -translate-x-1/2 bg-light-yellow opacity-30 w-1/3 h-20 blur-[100px]" />
          <p className="text-white text-center text-shadow-glow text-base md:text-lg font-medium mb-4 max-w-3xl mx-auto">
            "We harmonise wealth, wellness, and wisdom into a living field of radiant action. Every CASA Token is a golden key — unlocking the door to a world guided by love, trust, and evolution."
          </p>

          <p className="text-neutral-300 text-sm md:text-base mb-6 text-center">
            This is not just a token. It is:
          </p>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-sm sm:max-w-3xl mx-auto">
            <div className="flex items-center gap-3">
              <Coins className="text-light-yellow flex-shrink-0 mt-0.5 w-5 h-5" />
              <p className="text-neutral-300 text-sm">
                <div className="text-white font-medium">Wealth with Soul</div>
                <div className=''>prosperity that honors all beings and the planet</div> 
              </p>
            </div>

            <div className="flex items-center gap-3">
              <HeartHandshake className="text-light-yellow flex-shrink-0 mt-0.5 w-5 h-5" />
              <p className="text-neutral-300 text-sm">
                <div className="text-white font-medium">Technology with Heart</div>
                <div>innovation guided by compassion and wisdom</div>
              </p>
            </div>

            <div className="flex items-center gap-3">
              <Users className="text-light-yellow flex-shrink-0 mt-0.5 w-5 h-5" />
              <p className="text-neutral-300 text-sm">
                <div className="text-white font-medium">Governance with Spirit</div>
                <div>decision-making aligned with higher purpose</div>
              </p>
            </div>

            <div className="flex items-center gap-3">
              <Globe className="text-light-yellow flex-shrink-0 mt-0.5 w-5 h-5" />
              <p className="text-neutral-300 text-sm">
                <div className="text-white font-medium">Community with Purpose</div>
                <div>collective action for planetary evolution</div>
              </p>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Roadmap;
