"use client"

import * as React from "react"
import <PERSON><PERSON> from "react-lottie";
import * as ProgressPrimitive from "@radix-ui/react-progress"
import { cn } from "@/common/utils/helpers"
import { 
  FlagTrophyIcon, LaunchGoIcon,
} from "../../icons";
import * as piggyData from '@/common/lottie/piggy-animation.json'

const piggyOptions = {
  loop: true,
  autoplay: false,
  animationData: piggyData,
  rendererSettings: {
    preserveAspectRatio: 'xMidYMid slice',
  },
};
const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>
>(({
  className, value, ...props
}, ref) => (
  <div className="">
    <ProgressPrimitive.Root
      ref={ref}
      className={cn(
        "relative rounded-md w-full p-0.5 border border-violets-are-blue/15 my-6 md:my-8",
        className,
      )}
      {...props}
    >
      <span className="absolute top-[calc(100%+2px)] -left-0.5"><LaunchGoIcon /></span>
      <span className="absolute top-[calc(100%+2px)] right-0"><FlagTrophyIcon /></span>
      <ProgressPrimitive.Indicator
        className="h-1 w-full flex-1 shadow-sm transition-all bg-gradient-to-tr from-violets-are-blue to-han-purple duration-500 ease-in-out absolute top-1/2 left-0 -translate-y-1/2 flex justify-end rounded-[10px]"
        style={{
          width: `${value}%`,
        }}
      >
        {value ? (
          <span style={{
            right: '0',
            transform: `translateY(-32px) translateX(${value > 96 ? 15 : 50}%)`,
          }} className="text-white absolute right-0 z-[9999] text-xs font-bold pointer-events-none">
            <Lottie options={piggyOptions}
              style={{ margin: '0' }}
              height={24}
              width={24}
            />
          </span>
        ) : null}
      </ProgressPrimitive.Indicator>
    </ProgressPrimitive.Root>
  </div>
  
))
Progress.displayName = ProgressPrimitive.Root.displayName

export { Progress }
