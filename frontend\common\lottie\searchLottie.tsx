import Lottie from "react-lottie";
import * as searchAnimData from '@/common/lottie/search-animation.json'

export const SearchLottie = ({ 
  enableSearchAnim,
  width = 32,
  height = 32,
} : { 
  enableSearchAnim: boolean;
  width: number;
  height: number;
}) => {
  const defaultOptions = {
    loop: true,
    autoplay: false,
    animationData: searchAnimData,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  };
  return (
    <Lottie
      options={defaultOptions}
      isStopped={!enableSearchAnim}
      height={width}
      width={height}
    />
  )
}