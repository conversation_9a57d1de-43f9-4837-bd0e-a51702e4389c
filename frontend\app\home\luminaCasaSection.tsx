'use client'

import { 
  motion, useInView,
} from 'framer-motion';
import { useRef } from 'react';
import Image from 'next/image';
import { secondaryFont } from '@/common/utils/localFont';
import { 
  AnimatedText, Link,
} from '@/common/components/atoms';
import { routes } from '@/common/routes';
import { CheckBadgeIcon } from '@/common/components/icons';

export const LuminaCasaSection = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, {
    once: true,
    amount: 0.2,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <section
      ref={sectionRef}
      className="py-16 relative w-full"
    >
      <div className="container px-2 md:px-8 mx-auto !max-w-[1200px]">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-6xl mx-auto"
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:text-left text-center">
            <motion.div
              variants={itemVariants}
            >
              <h2 className={`text-xl md:text-2xl lg:text-3xl font-semibold text-white mb-4`}>
                Introducing Lumina Casa
              </h2>
              <div className="rounded-full h-1 w-20 bg-gradient-to-tr from-han-purple to-tulip my-2 mx-auto lg:mx-0"></div>
              <p className="text-neutral-300 mb-4 text-sm md:text-base">
                Merging Capital with CommUnity & Consciousness. Healing, Education, Awakening, Purpose, Prosperity, Joy for All.
              </p>
              <div className="bg-violets-are-blue/5 border border-violets-are-blue/15 rounded-2xl p-6 mb-8">
                <h3 className={`font-semibold text-white mb-4`}>
                  <span className={`${secondaryFont.className}`}><AnimatedText text="$CASA"></AnimatedText></span> Token Benefits:
                </h3>
                <ul className="space-y-3">
                  <BenefitItem>Access to global community of visionaries</BenefitItem>
                  <BenefitItem>Invitations to exclusive Lumina Casa gatherings</BenefitItem>
                  <BenefitItem>Early access to token presale opportunities</BenefitItem>
                  <BenefitItem>Life-changing teachings and transformative experiences</BenefitItem>
                </ul>
              </div>
              <div className='flex justify-center lg:justify-start'>
                <Link
                  variant='gradient'
                  href={routes.luminaCasaPath}
                  size='sm'
                  width='w-min'
                >
                  Explore Lumina Casa
                </Link>
              </div>
            </motion.div>
            <motion.div
              variants={itemVariants}
              className="lg:block hidden"
            >
              <div className="relative w- rounded-2xl overflow-hidden shadow-2xl">
                <Image
                  src="https://kajabi-storefronts-production.kajabi-cdn.com/kajabi-storefronts-production/file-uploads/themes/2160502787/settings_images/545b047-fc3-3718-5061-7858dd180cff_Lumina_Casa_Systems_Overview.png"
                  alt="LUCA Token"
                  width={1200}
                  height={800}
                  className="w-full h-auto max-h-[420px] object-cover rounded-2xl"
                />
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

const BenefitItem = ({ children }: { children: React.ReactNode }) => {
  return (
    <li className="flex items-center">
      <CheckBadgeIcon />
      <span className="ml-3 text-gray-300 text-xs lg:text-sm text-left">{children}</span>
    </li>
  );
};
