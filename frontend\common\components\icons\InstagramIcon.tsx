import type { SVGProps } from "react";

export const InstagramIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <path
        fill="#FFBFC5"
        d="M17.26 1H6.74A5.74 5.74 0 0 0 1 6.74v10.52A5.739 5.739 0 0 0 6.74 23h10.52A5.739 5.739 0 0 0 23 17.26V6.74A5.74 5.74 0 0 0 17.26 1Z"
      />
      <path
        fill="#FF808C"
        d="M21.26 2.626 2.627 21.262A5.72 5.72 0 0 0 6.739 23h10.522A5.739 5.739 0 0 0 23 17.26V6.74a5.719 5.719 0 0 0-1.74-4.114Z"
      />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M17.26 1H6.74A5.74 5.74 0 0 0 1 6.74v10.52A5.739 5.739 0 0 0 6.74 23h10.52A5.739 5.739 0 0 0 23 17.26V6.74A5.74 5.74 0 0 0 17.26 1Z"
      />
      <path
        fill="#fff"
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M12 17.74a5.739 5.739 0 1 0 0-11.478 5.739 5.739 0 0 0 0 11.477ZM18.695 6.74a1.435 1.435 0 1 0 0-2.871 1.435 1.435 0 0 0 0 2.87Z"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h24v24H0z" />
      </clipPath>
    </defs>
  </svg>
)
