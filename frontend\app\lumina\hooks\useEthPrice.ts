import { 
  useState, useEffect,
} from 'react';
import { fetchEthPrice } from '@/common/utils/network/priceApi';

export const useEthPrice = (refreshInterval = 60000) => {
  const [price, setPrice] = useState<number>(3000); // Default fallback price
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isMounted = true;
    
    const getPrice = async () => {
      try {
        setIsLoading(true);
        const ethPrice = await fetchEthPrice();
        
        if (isMounted) {
          setPrice(ethPrice);
          setError(null);
        }
      } catch (err) {
        if (isMounted) {
          console.error('Error fetching ETH price:', err);
          setError(err instanceof Error ? err : new Error('Unknown error fetching ETH price'));
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    getPrice();

    const intervalId = setInterval(getPrice, refreshInterval);

    return () => {
      isMounted = false;
      clearInterval(intervalId);
    };
  }, [refreshInterval]);

  return { 
    price, 
    isLoading, 
    error,
  };
};
