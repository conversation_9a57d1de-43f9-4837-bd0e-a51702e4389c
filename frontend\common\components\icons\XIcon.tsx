import type { SVGProps } from "react";

export const XIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <path
        fill="#EEE"
        d="M23 23h-4.69L12 14.014 5.69 23H1l7.232-10.669L1 1h4.69L12 10.593 18.307 1H23l-7.232 11.201L23 23Z"
      />
      <path
        fill="#B2B2B2"
        d="M15.768 12.201 23 23h-4.69L12 14.014 5.69 23H1l7.232-10.669 7.536-.13Z"
      />
      <path
        stroke="#191919"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M23 23h-4.69L12 14.014 5.69 23H1l7.232-10.669L1 1h4.69L12 10.593 18.307 1H23l-7.232 11.201L23 23Z"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h24v24H0z" />
      </clipPath>
    </defs>
  </svg>
)
