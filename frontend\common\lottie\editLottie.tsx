'use client'
import <PERSON><PERSON> from "react-lottie";
import * as animData from '@/common/lottie/edit-animation.json'

export const EditLottie = ({
  enableAnim,
} : {
  enableAnim: boolean;
}) => {
  const animOptions = {
    loop: true,
    autoplay: false,
    animationData: animData,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  };
  return (
    <Lottie options={animOptions}
      isStopped={!enableAnim}
      style={{
        margin: '0',
        pointerEvents: 'none',
      }}
      height={20}
      width={20}
    />
  )
}
