import {
  NextRequest, NextResponse,
} from "next/server";
import { createClient } from '@/common/utils/supabase/client';
import { SupabaseTables } from "./common/constants";
import { basePath } from "./common/routes";

export const config = {
  matcher: [
    "/((?!api/|_next/|_static/|images|_vercel|[\\w-]+\\.\\w+).*)",
    "/sitemap.xml",
    "/robots.txt",
  ],
};

export type SubdomainType = {
  address: string;
  subdomain: string;
}

export default async function middleware (req: NextRequest) {
  const url = req.nextUrl;
  const pathname = url.pathname;
  const hostname = req.headers.get("host");

  if (pathname === '/sitemap.xml' || pathname === '/robots.txt') {
    const baseDomain = basePath;

    if (hostname !== baseDomain && hostname !== `www.${baseDomain}`) {
      return NextResponse.redirect(
        `https://${baseDomain}${pathname}`,
        { status: 301 },
      );
    }
  }
  if (
    pathname.startsWith('/_next') ||
    pathname.includes('.') ||
    pathname.startsWith('/api')
  ) {
    return NextResponse.next();
  }

  let response: NextResponse;

  if (hostname?.startsWith('www.')) {
    response = NextResponse.redirect(
      `https://${hostname.replace('www.', '')}${pathname}`,
      { status: 301 },
    );
    response.headers.set('X-Robots-Tag', 'index,follow');
    return response;
  }

  let currentHost;
  const baseDomain = process.env.NEXT_PUBLIC_ROOT_DOMAIN;
  if (process.env.NODE_ENV === "production") {
    currentHost = hostname?.replace(`.${baseDomain}`, "");
  } else {
    currentHost = hostname?.split(":")[0].replace(".localhost", "");
  }

  if (!currentHost || currentHost === baseDomain) {
    if (pathname === "/" && (currentHost === baseDomain)) {
      response = NextResponse.rewrite(new URL(`/home`, req.url));
      response.headers.set('X-Robots-Tag', 'index,follow');

      response.headers.set('Link', `<https://${hostname}${pathname}>; rel="canonical"`);
      return response;
    }

    response = NextResponse.next();
    response.headers.set('X-Robots-Tag', 'index,follow');

    response.headers.set('Link', `<https://${hostname}${pathname}>; rel="canonical"`);
    return response;
  }

  const supabase = createClient();
  const { data: subdomains } = await supabase.from(SupabaseTables.Subdomains).select('*')

  if (currentHost === "lumina") {
    response = NextResponse.rewrite(new URL(`/lumina${pathname}`, req.url));
    response.headers.set('X-Robots-Tag', 'index,follow');
    response.headers.set('Link', `<https://${currentHost}.${baseDomain}${pathname}>; rel="canonical"`);
    return response;
  }

  if (subdomains?.length) {
    const subdomainData = subdomains.find((d: SubdomainType) => d.subdomain.toLowerCase() === currentHost);
    if (subdomainData) {
      response = NextResponse.rewrite(new URL(`/${subdomainData.subdomain}${pathname}`, req.url));
      response.headers.set('X-Robots-Tag', 'index,follow');

      response.headers.set('Link', `<https://${currentHost}.${baseDomain}${pathname}>; rel="canonical"`);
      return response;
    }
  }

  response = NextResponse.next();
  response.headers.set('X-Robots-Tag', 'index,follow');
  return response;
};