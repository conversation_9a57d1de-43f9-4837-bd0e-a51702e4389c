import {
  ButtonHTMLAttributes, ReactNode, SVGProps, ReactElement, MouseEventHandler,
} from "react";

export type ButtonType = 'button' | 'submit' | string;
export type Variants =
  | 'gradient'
  | 'outline-rounded'
  | 'outline'

export type Sizes = 'xs' | 'sm' | 'md' | 'lg';

export const TVariants = {
  gradient: 'gradient',
  outline: 'outline',
  'outline-rounded': 'outline-rounded',
}

export const TSizes = {
  xs: 'xs',
  sm: 'sm',
  md: 'md',
  lg: 'lg',
}

export type Direction = "TOP" | "LEFT" | "BOTTOM" | "RIGHT";

export type IconComponent = (props: SVGProps<SVGSVGElement>) => ReactElement;
export type ButtonProps = Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'type'> & {
  variant: Variants;
  size: Sizes;
  width?: string;
  type?: ButtonType;
  disabled?: boolean;
  icon?: IconComponent;
  children?: ReactNode;
  className?: string;
  "aria-label"?: string;
}

export type AIButtonProps = {
  children?: ReactNode;
  containerClassName?: string;
  borderRadiusClass?: string;
  className?: string;
  duration?: number;
  clockwise?: boolean;
  type?: ButtonType;
  disabled?: boolean;
  width?: string;
  href?: string;
  target?: string;
  rel?: string;
  onClick?: MouseEventHandler<HTMLElement>;
  onMouseEnter?: MouseEventHandler<HTMLElement>;
  onMouseLeave?: MouseEventHandler<HTMLElement>;
}
