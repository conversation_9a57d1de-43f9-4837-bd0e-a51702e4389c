import type { SVGProps } from "react";

export const ClaudeAIIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={77}
    height={16}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a_claude)">
      <path
        fill="#D97757"
        d="m3.242 10.64 3.252-1.765.055-.154-.055-.085h-.159l-.543-.032-1.858-.049-1.612-.065-1.56-.08-.394-.082L0 7.858l.038-.234.33-.215.473.04 1.046.07 1.57.104 1.137.065 1.687.17h.268l.037-.105-.091-.065-.071-.065L4.8 6.56 3.042 5.433l-.92-.647-.498-.329-.251-.307-.109-.672.452-.482.607.04.156.04.614.458 1.314.984L6.122 5.74l.251.202.1-.07.013-.048-.113-.182L5.44 4.01l-.995-1.66-.443-.687-.117-.413a1.946 1.946 0 0 1-.072-.486L4.328.09 4.612 0 5.3.089l.289.242.426.944.69 1.486 1.072 2.02.314.6.167.554.063.17h.108v-.097l.088-1.138.163-1.397.158-1.798.055-.507.259-.607.514-.328.402.186.33.458-.045.296-.197 1.235-.385 1.935-.25 1.296h.145l.168-.162.677-.87L11.65 3.23l.502-.547.586-.603.376-.288h.71l.524.753-.234.777-.732.899-.608.761-.87 1.134-.543.907.05.072.13-.012 1.966-.405 1.062-.185 1.268-.21.573.258.063.264-.226.538-1.356.324-1.59.308-2.368.542-.************* 1.066.098.457.024h1.117l2.08.15.544.347.325.426-.054.324-.837.412-1.13-.259-2.636-.607-.903-.218h-.125v.072l.753.713 1.38 1.206 1.729 1.555.088.384-.222.304-.234-.032-1.52-1.106-.585-.498-1.327-1.08h-.088v.113l.306.433 1.614 2.348.084.72-.117.235-.418.141-.46-.081-.945-1.284-.976-1.446-.787-1.295-.096.053-.464 4.838-.217.247-.503.186-.418-.308-.222-.498.222-.983.268-1.284.217-1.02.197-1.268.117-.421-.008-.028-.096.012-.987 1.312-1.503 1.964-1.188 1.23-.285.11-.493-.248.046-.441.275-.393 1.645-2.025.992-1.255.641-.724-.004-.105h-.038l-4.37 2.745-.777.097-.335-.303.041-.498.159-.162 1.313-.875-.004.005v.004Z"
      />
      <path
        fill="#fff"
        d="M26.981 13.584c-2.1 0-3.536-1.134-4.214-2.88a7.535 7.535 0 0 1-.514-2.845c0-2.928 1.356-4.96 4.352-4.96 2.014 0 3.255.849 3.963 2.878h.862l-.118-2.798c-1.205-.752-2.711-1.134-4.544-1.134-2.582 0-4.778 1.118-6 3.134a6.611 6.611 0 0 0-.929 3.503c0 2.24 1.093 4.223 3.143 5.324a7.524 7.524 0 0 0 3.653.834c2 0 3.587-.369 4.993-1.012l.364-3.085h-.878c-.527 1.409-1.155 2.256-2.198 2.705-.51.222-1.155.336-1.932.336h-.003Zm9.06-10.686.084-1.377h-.594l-2.645.77v.416l1.172.526v9.628c0 .656-.347.801-1.256.912v.704h4.499v-.704c-.912-.11-1.256-.255-1.256-.912v-9.96l-.004-.004v.001Zm17.89 11.743h.347l3.042-.559v-.72l-.426-.032c-.711-.065-.895-.206-.895-.769V7.427l.084-1.648h-.481l-2.875.4v.705l.28.048c.778.11 1.009.32 1.009.847v4.575c-.744.558-1.456.911-2.301.911-.938 0-1.52-.461-1.52-1.539V7.43l.084-1.648h-.493l-2.879.4v.705l.297.048c.778.11 1.009.32 1.009.847v4.223c0 1.79 1.045 2.64 2.712 2.64 1.272 0 2.314-.655 3.096-1.567l-.083 1.567-.005-.004h-.002Zm-8.36-5.697c0-2.288-1.256-3.166-3.524-3.166-2 0-3.453.801-3.453 2.13 0 .396.146.7.443.91l1.523-.194c-.067-.445-.1-.717-.1-.83 0-.753.414-1.134 1.255-1.134 1.244 0 1.87.847 1.87 2.207v.445l-3.137.911c-1.046.276-1.64.514-2.038 1.074a1.97 1.97 0 0 0-.292 1.133c0 1.296.92 2.21 2.494 2.21 1.138 0 2.147-.497 3.025-1.44.314.943.795 1.44 1.653 1.44.694 0 1.322-.27 1.883-.8l-.168-.559a2.753 2.753 0 0 1-.724.097c-.481 0-.71-.368-.71-1.09V8.945Zm-4.018 4.4c-.859 0-1.39-.48-1.39-1.327 0-.575.28-.912.879-1.106l2.544-.782v2.365c-.811.595-1.289.85-2.033.85Zm26.488.737v-.72l-.43-.032c-.711-.065-.892-.207-.892-.769V2.9l.084-1.377h-.599l-2.644.768v.417l1.171.526V6.4a3.785 3.785 0 0 0-2.247-.623c-2.628 0-4.678 1.935-4.678 4.83 0 2.386 1.473 4.034 3.9 4.034 1.255 0 2.348-.591 3.025-1.507l-.083 1.507h.351l3.042-.559Zm-5.507-7.344c1.255 0 2.197.704 2.197 2v3.644a3.068 3.068 0 0 1-2.18.85c-1.8 0-2.712-1.377-2.712-3.215 0-2.064 1.042-3.28 2.695-3.28Zm11.94 1.822c-.234-1.069-.912-1.675-1.854-1.675-1.406 0-2.381 1.024-2.381 2.494 0 2.175 1.188 3.584 3.109 3.584 1.281-.013 2.46-.684 3.092-1.761l.56.145c-.25 1.887-2.016 3.296-4.184 3.296-2.545 0-4.298-1.822-4.298-4.413 0-2.592 1.904-4.45 4.448-4.45 1.9 0 3.239 1.106 3.67 3.024l-6.628 1.968v-.866l4.465-1.34V8.56Z"
      />
    </g>
    <defs>
      <clipPath id="a_claude">
        <path fill="#fff" d="M0 0h77v16H0z" />
      </clipPath>
    </defs>
  </svg>
)
