"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./common/lang.ts":
/*!************************!*\
  !*** ./common/lang.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   lang: function() { return /* binding */ lang; }\n/* harmony export */ });\nconst header = {\n    connectButton: {\n        connectWallet: \"Login\",\n        wrongNetwork: \"Wrong Network\",\n        myIdeas: \"My Projects\",\n        logout: \"Logout\",\n        copyAddress: \"Copy Address\",\n        fundAccount: \"Add Funds\",\n        connectedTo: \"Connected to\"\n    },\n    myDreams: \"My Projects\",\n    generate: \"Launch ISO\",\n    searchDreams: \"Search Projects\",\n    bigger: \"Build\",\n    dream: \"Launch\",\n    faster: \"& Scale\",\n    dreamsSubHeading: \"Discover subscription projects\",\n    pilotSubheading: \"Build your community\",\n    dreamathonSubheading: \"Join builder events\",\n    searchIdeas: {\n        placeholder: \"Search for Projects\",\n        noIdeasFound: \"No Projects found\"\n    }\n};\nconst notFound = {\n    heading: \"Something went wrong\",\n    subHeading1: \"Brace yourself till we get the error fixed\",\n    subHeading2: \"You may also refresh the page or try again later\",\n    buttonTitle: \"Return Home\"\n};\nconst footer = {\n    terms: \"Terms\",\n    aboutUs: \"About Us\",\n    privacyPolicy: \"Privacy\",\n    contactUs: \"Contact us\",\n    contactUsModal: {\n        heading: \"Contact Us\",\n        submitButton: \"Send\",\n        youCanContact: \"You can contact us at\"\n    }\n};\nconst createIdea = {\n    categories: {\n        noTagsFound: \"No Categories found\"\n    },\n    addCategoryError: \"Unable to add new category\",\n    accountWrongError: \"Please connect with your account to edit this dream\",\n    generateError: \"Something went wrong. Please try again later.\",\n    transactionError: \"Transaction reverted. Please try again.\",\n    aiRegenerateError: \"Failed to regenerate content. Please try again.\",\n    insufficientBalance: \"You have insufficient balance to create a dream\",\n    whatToImprove: \"What do you want to improve?\",\n    reservedDomain: \"This domain is reserved. Please choose another ticker\",\n    heading: \"Create Token\",\n    subHeading: \"Launch Your Dream\",\n    subHeadingCreated: \"Design Your Dream\",\n    subHeadingFundingReached: \"Funding Reached\",\n    subHeading2Part1: \"Share your dream in detail to connect with potential believers. We will create an\",\n    subHeading2Part2: \"that you can trade on\",\n    subHeading2FundingReached: \"We would suggest not to update the dream details as the funding target has been met and keep the original idea intact.\",\n    tokenCreationFeeLabel: \"One Time Fee\",\n    maxSupplyLabel: \"Max Supply\",\n    initialMintLabel: \"Initial Supply\",\n    tokensSuffix: \" Tokens\",\n    targetInfoPart1: \"After funding target of \",\n    targetInfoPart2: \"is met, a liquidity pool will be created on Uniswap.\",\n    ideaNotFound: \"Dream not found!\",\n    ideaCreatedMessage: \"Dream successfully created!\",\n    ideaDetailsUpdated: \"Dream details updated!\",\n    errorOccurred: \"Something went wrong. Please try again!\",\n    cantEditError: \"You can't update as you are not the owner of this dream!\",\n    validationErrors: {\n        nameRequired: \"Please enter a name for your dream\",\n        nameMinError: \"Please enter more than 2 characters\",\n        nameMaxError: \"Please enter less than 20 characters\",\n        tickerRequired: \"Please enter a name for your ticker\",\n        tickerMinError: \"Please enter more than 2 characters\",\n        tickerMaxError: \"Please enter less than 20 characters\",\n        logoRequired: \"Please upload logo or image depicting your dream\",\n        descriptionRequired: \"Please provide a description for your dream\",\n        descriptionMinError: \"Please enter more than 10 characters\",\n        descriptionMaxError: \"Please enter less than 1000 characters\",\n        categoriesRequired: \"Please select a category\",\n        websiteRequired: \"Please provide a valid link\",\n        twitterInvalid: \"Please provide a valid X link\",\n        telegramInvalid: \"Please provide a valid telegram link\"\n    },\n    uploadDifferent: \"Upload a different image\",\n    imageUpload: {\n        title: \"Dream Logo\",\n        postImage: \"Post Image\",\n        imageSizeError: \"Please upload image less than 5 mb\",\n        imageType: \"This is a wrong file format. Only image files are allowed.\",\n        uploadError: \"Could not add file. Please try again.\",\n        uploading: \"Uploading...\",\n        uploadLabel: \"Upload here\"\n    },\n    form: {\n        name: \"Project Title\",\n        address: \"Address\",\n        ticker: \"Subscription Token\",\n        description: \"Project Description\",\n        category: \"Select Category\",\n        website: \"Website\",\n        websitePreview: \"Project Preview\",\n        twitter: \"X(Twitter)\",\n        telegram: \"Telegram\",\n        submitLabel: \"Submit\",\n        connectWallet: \"Connect Wallet\",\n        brandingDetails: \"Project Identity\",\n        dreamDetails: \"Describe your Project\",\n        generateButtonLabel: \"Restart\",\n        optional: \"Recommended\",\n        communityChannels: \"Community Channels\",\n        createIdea: \"Create Project\",\n        confirm: \"Confirm\",\n        subdomainExisting: \"Please note you will be assigned a alternate subdomain as this subdomain is already taken\",\n        viewDream: \"View Project\",\n        socialAssistants: \"Community Tools\",\n        note: \"Note: You can always edit some parts of the project later\"\n    }\n};\nconst generateIdea = {\n    promptPlaceholders: [\n        \"Create a subscription-based fitness coaching platform\",\n        \"Launch a premium newsletter for tech professionals\",\n        \"Build a membership site for creative professionals\",\n        \"Create an exclusive learning community for developers\",\n        \"Launch a subscription box service for artisan products\"\n    ],\n    promptLoadingStates: [\n        {\n            text: \"Analyzing Market Opportunity\"\n        },\n        {\n            text: \"Designing Subscription Model\"\n        },\n        {\n            text: \"Creating Brand Identity\"\n        },\n        {\n            text: \"Building Member Experience\"\n        },\n        {\n            text: \"Setting Up Payment Systems\"\n        },\n        {\n            text: \"Preparing ISO Launch\"\n        }\n    ],\n    greeting: {\n        morning: \"Good morning, tell me about your project idea\",\n        afternoon: \"Good afternoon, tell me about your project idea\",\n        evening: \"Good evening, tell me about your project idea\"\n    },\n    whatsYourDream: \"Tell me about your project idea\",\n    generateError: \"Something went wrong. Please try again later.\",\n    poweredBy: \"powered by\",\n    readyTo: \"You are one step away from launching your subscription business\",\n    continue: \"Continue to Launch ISO\",\n    proceed: \"Proceed\",\n    orEnhance: \"Or keep refining the project\",\n    h1: \"Generate your Subscription Business with AI\"\n};\nconst ideas = {\n    ideaCard: {\n        raised: \"Revenue\",\n        holders: \"Subscribers\",\n        trade: \"Subscribe\",\n        marketcap: \"Total Value\",\n        uniswapLabel: \"SUB\",\n        openUniswap: \"View Subscription\",\n        dexLabel: \"ISO\",\n        openX: \"Open X\",\n        openWebsite: \"View Website\",\n        openDex: \"View ISO Details\",\n        openTelegram: \"Open Telegram\"\n    },\n    currentIdeas: \"Project Gallery\",\n    currentIdeasSubHeading: \"Discover successful subscription projects ready for community support\",\n    noIdeasHeading: \"No projects yet in this category\",\n    noIdeasSubHeading: \"Be the first to contribute! Share your innovative subscription projects and help grow this category.\",\n    registerIdea: \"Register Existing Project\",\n    launchNew: \"Launch New ISO\",\n    detailedView: \"Detailed View\",\n    compactView: \"Compact View\",\n    refreshData: \"Refresh Data\"\n};\nconst homePage = {\n    timeline: {\n        heading: \"How to Launch Your Project?\",\n        subHeading: \"Transform your innovative ideas into sustainable subscription-based projects with our comprehensive ISO Hub\"\n    },\n    subHeading: \"The ultimate platform for builders to launch, grow, and monetize their projects through Initial Subscription Offerings\",\n    h1: \"Build. Launch. Scale.\",\n    subHeading1: \"Every great project starts with a vision.\",\n    subHeading2: \"What if you could turn your next big idea into a thriving subscription business?\",\n    subHeading3: \"From concept to community-driven success. Launch your Initial Subscription Offering and build sustainable revenue streams with engaged subscribers.\",\n    heroWords: [\n        {\n            text: \"Build.\"\n        },\n        {\n            text: \"Launch.\"\n        },\n        {\n            text: \"Scale.\",\n            className: \"gradientText\"\n        }\n    ],\n    answeredByHuman: \"Supported by Expert Advisors\",\n    haveDream: \"Have an existing project?\",\n    register: \"Register Project\",\n    answeredByAi: \"Powered by AI Tools\",\n    fundButtonLabel: \"Register Existing Project\",\n    generateButtonLabel: \"Launch New ISO\",\n    trendingIdeas: \"Featured Projects\",\n    trendingIdeasSubHeading: \"Discover successful subscription-based projects built by our community\",\n    readyToTurn: \"Ready to Launch Your Subscription Business?\",\n    readyToTurnSubheading: \"Join DreamStartr ISO Hub today and transform your ideas into sustainable revenue streams. Launch your Initial Subscription Offering in minutes.\",\n    dreamGallery: \"Explore Projects\",\n    stats: {\n        heading: \"Platform Overview\",\n        description: \"See how builders are creating sustainable subscription businesses on our platform\",\n        dreamsLaunched: \"Projects launched on platform\",\n        agentsRunning: \"AI agents actively running\",\n        aiPowered: \"AI-powered development support\",\n        fundsRaised: \"Total funds raised for dreams\",\n        activeAgents: \"Running\"\n    },\n    developmentProcess: {\n        heading: \"Process\",\n        stepOneTitle: \"Dream Validation\",\n        stepOneInfo: \"From raw concept to refined vision\",\n        stepOnePoints: \"AI-enhanced ideation \\uD83D\\uDCA1 with expert community feedback \\uD83D\\uDCAC and direct market validation from target users \\uD83C\\uDFAF\",\n        stepTwoTitle: \"AI-Powered Development\",\n        stepTwoInfo: \"Where vision meets execution\",\n        stepTwoPoints: \"Rapid development with our AI-powered IDE \\uD83D\\uDCBB and real-time community-driven validation \\uD83D\\uDCAC\",\n        stepThreeTitle: \"Sustainable Launch\",\n        stepThreeInfo: \"Built for lasting impact\",\n        stepThreePoints: \"Customized token economics \\uD83D\\uDCB0, engaged early users \\uD83D\\uDC65 and proven growth strategies \\uD83D\\uDCC8\",\n        stepFourTitle: \"Accelerated Growth\",\n        stepFourInfo: \"Scaling with purpose\",\n        stepFourPoints: \"Access strategic partnerships \\uD83E\\uDD1D, expand market reach \\uD83C\\uDF10 and track measurable impact \\uD83D\\uDCCA\"\n    }\n};\nconst ideaPage = {\n    createdBy: \"Created by\",\n    believers: \"Believers\",\n    tokenAddress: \"Token address\",\n    fundingRaised: \"Raised\",\n    visitWebsite: \"View website\",\n    attachImage: \"Attach image\",\n    categories: \"Categories\",\n    connectWallet: \"Connect Wallet\",\n    tokenDetails: \"Token Details\",\n    transfers: \"Transfers\",\n    trade: \"Trade\",\n    uniswapLabel: \"UNI\",\n    conversations: \"Comments\",\n    post: \"Post\",\n    noComments: \"No comments yet. Be the first to start the conversation!\",\n    preview: \"Preview\",\n    buyFeed: \"Buy Feed\",\n    notActive: \"This idea is no longer active. Redirecting to homepage.\",\n    sellFeed: \"Sell Feed\",\n    owners: \"Believers\",\n    chart: \"Buy Trend\",\n    stakeholders: \"Believers\",\n    stakeholdersDesc: \"Our visionary believers making this dream possible\",\n    checkTransHeading: \"Check out ongoing trades on\",\n    transactionsTable: {\n        columnOne: \"Account\",\n        columnTwo: \"Value\",\n        columnThree: \"Time\",\n        columnFour: \"Transaction\"\n    },\n    ownersTable: {\n        columnOne: \"Account\",\n        columnTwo: \"Percentage\"\n    },\n    limitedTokensError: \"Limited tokens. There is only %amount% tokens left.\",\n    purchaseSuccess: \"Congratulations! You have purchased\",\n    purchaseError: \"Purchase could not be completed. Please try again!\",\n    postError: \"Unable to add new post\",\n    bondingCurveProgress: \"Progress\",\n    buyTokensFor: \"Buy tokens for\",\n    availableTokens: \"Available tokens\",\n    purchaseLabel: \"Get Quote\",\n    fetchQuoteError: \"Unable to fetch quote\",\n    approveTokenError: \"Unable to approve token\",\n    swapTokensSuccess: \"You have successfully swapped your tokens\",\n    transactionSwapError: \"Transaction failed. Please check your balance and try again\",\n    swapGeneralError: \"An error occurred while processing the swap\",\n    youWillReceive: \"You will receive\",\n    maxButton: \"Max\",\n    deleteComment: \"Delete comment\",\n    likeComment: \"Like comment\",\n    areYouSureToDelete: \"Are you sure you want to delete this comment?\",\n    editIdea: \"Edit Dream\",\n    codeAssist: \"Code Assist\",\n    boostIdea: \"Social Agents\",\n    bondingCurve: \"Bonding Curve Progress\",\n    buyPriceFetchError: \"Unable to fetch buying price\",\n    calculateTokensError: \"Unable to calculate tokens\",\n    priceQuoteError: \"Unable to get price quote\",\n    confirm: \"Confirm\",\n    for: \"for\",\n    confirmPurchase: \"Confirm purchase\",\n    buyTokens: \"Support Dream\",\n    buy: \"Buy\",\n    remaining: \" Remaining\",\n    swapTokens: \"Swap Tokens\",\n    swapTokensDesc: \"You can buy/sell tokens from the liquidity pool\",\n    openUniswap: \"Open Uniswap\",\n    dexLabel: \"DEX\",\n    openDex: \"Open DEX Screener\",\n    openTelegram: \"Open Telegram\",\n    openX: \"Open X\",\n    swapping: \"Swapping...\",\n    swap: \"Swap\",\n    buyTokensDesc: \"Join our community and shape the future - get tokens now \\uD83D\\uDE80\",\n    ensure: \"Ensure you have enough funds in your account\",\n    likelyFail: \"Note: This transaction will likely fail as you do not have enough funds\",\n    buyNow: \"Confirm\",\n    bondingCurveInfo: \"When the market cap reaches %goal% %currency%, all the liquidity from the bonding curve will be deposited into Uniswap, and the LP tokens will be burned. Progression increases as the price goes up.\"\n};\nconst profile = {\n    heading: \"Manage Projects\",\n    startAllAgents: \"Start All Tools\",\n    subheading: \"Track your project progress and manage your subscription business\",\n    noIdeasHeading: \"You have not created any Project\",\n    noIdeasSubHeading: \"Create your first Project\",\n    registerIdea: \"Register Existing Project\",\n    launchNew: \"Launch New ISO\",\n    table: {\n        ideaName: \"Project name\",\n        status: \"Status\",\n        ideaAddress: \"Address\",\n        ticker: \"Token\",\n        actions: \"Actions\",\n        view: \"View\",\n        dev: \"Dev Tools\",\n        review: \"Community Tools\",\n        edit: \"Edit Project\"\n    }\n};\nconst dreamathon = {\n    title: \"Builder Events:\",\n    animatedText: \"Connect & Create\",\n    description: \"Join our global community of builders creating the next generation of subscription businesses. Network, learn, and launch together.\",\n    ongoingEvents: \"Live Events\",\n    cities: \"Global Community\",\n    prizesTitle: \"Funding & Support\",\n    prizesDescription: \"Get funding and mentorship for your ISO\",\n    communityTitle: \"Builder Network\",\n    communityDescription: \"Connect with fellow builders and industry experts\",\n    viewMoreDetails: \"Join Builder Network\"\n};\nconst enhancedDevelopmentProcess = {\n    sectionTitle: \"How ISO Hub Works\",\n    sectionDescription: \"Everything you need to launch and scale your subscription-based project\",\n    cards: {\n        blockchain: {\n            title: \"Subscription Management\",\n            description: \"Launch Initial Subscription Offerings with smart contract automation. Manage recurring payments, subscriber tiers, and revenue distribution seamlessly.\"\n        },\n        mediaPilot: {\n            title: \"Community Building\",\n            description: \"Build engaged subscriber communities with integrated social tools, exclusive content delivery, and member engagement analytics.\"\n        },\n        dreamathons: {\n            title: \"Builder Network\",\n            description: \"Connect with fellow builders, share resources, and collaborate on projects. Access mentorship and partnership opportunities within our ecosystem.\"\n        },\n        devAgent: {\n            title: \"Development Tools\",\n            description: \"Comprehensive development environment with AI-assisted coding, project templates, and deployment tools specifically designed for subscription businesses.\",\n            terminal: {\n                command: \"> create subscription service for fitness coaching\",\n                step1: \"✔ Setting up subscription tiers and pricing.\",\n                step2: \"✔ Generating member portal and content delivery.\",\n                step3: \"✔ Configuring payment processing and analytics.\",\n                success: \"Success! Your ISO is ready to launch.\"\n            }\n        }\n    }\n};\nconst manageIdea = {\n    heading: \" Community Tools\",\n    subHeading: \"Meet your personal team of AI experts, each uniquely trained to help grow and manage your subscription business.\",\n    twitterDescription: \"This tool creates personalized content for your subscription business, schedules posts, engages with potential subscribers, and grows your community.\",\n    tweets: \"Posts Generated\",\n    posts: \"Content Created\",\n    postsSingular: \"Content Created\",\n    tweetsSingular: \"Post Generated\",\n    engagement: \"Subscriber Metrics\",\n    createSandboxError: \"Failed to create development environment. Please try again.\",\n    linkedInDescription: \"This tool creates professional content for your subscription business, schedules posts, engages with potential subscribers, and builds your professional network.\",\n    manageAgent: \"Manage Persona\",\n    modifyCharacter: \"Target X Users\",\n    agentSettings: \"Engagement Settings\",\n    upcomingPosts: \"Upcoming Tweets\",\n    upcomingPostsLinkedin: \"Upcoming Posts\",\n    devAgent: \"Dev Tools\",\n    openAgent: \"Open Tools\",\n    createSandbox: \"Create Environment\",\n    openSandbox: \"Open Environment\",\n    addNewDev: \"Add New\",\n    active: \"Active\",\n    yourDreams: \"Your Projects\",\n    partneredDreams: \"Partnered Projects\",\n    owned: \"Owned\",\n    drafts: \"Drafts\",\n    draft: \"Draft\",\n    back: \"Back to Project\",\n    devAgentDesc: \"The Development Tools provide a comprehensive environment to build your subscription business. Create member portals, payment systems, and content delivery platforms.\",\n    enableFollowing: \"Enable Following\",\n    enableFollowDescription: \"Allow agent to follow relevant accounts\",\n    enableActions: \"Enable Interactions\",\n    enableActionsDescription: \"Check and engage with tweets\",\n    enablePosts: \"Enable Tweeting\",\n    postInterval: \"Tweet Interval\",\n    postIntervalDesc: \"Duration between each tweet\",\n    interactionInterval: \"Interaction Interval\",\n    interactionIntervalDesc: \"Frequency of interactions(likes, replies)\",\n    followInterval: \"Follow Interval\",\n    followIntervalDesc: \"Frequency of following new accounts\",\n    enablePostsDescription: \"Allow agent to post personalized tweets\",\n    replies: \"Replies to Tweets\",\n    repliesSingular: \"Reply to Tweets\",\n    likes: \"Tweets Liked\",\n    likesSingular: \"Tweet Liked\",\n    retweets: \"Tweets Retweeted\",\n    retweetsSingular: \"Tweet Retweeted\",\n    followers: \"Accounts Followed\",\n    followersSingular: \"Account Followed\",\n    prev: \"Prev\",\n    next: \"Next\",\n    skip: \"Skip\",\n    agentRunning: \"Running since\",\n    twitterForm: {\n        username: \"Username\",\n        password: \"Password\",\n        email: \"Email\",\n        submit: \"Start Agent\",\n        connect: \"Connect Wallet\",\n        stopAgent: \"Pause Agent\",\n        viewCharacter: \"View Settings\",\n        updateCharacter: \"Update Character\",\n        hideCharacter: \"Hide Settings\"\n    },\n    character: {\n        exampleOne: \"Example 1\",\n        exampleTwo: \"Example 2\",\n        exampleUserLabel: \"User\",\n        exampleAgentLabel: \"Agent\",\n        styleAll: \"All\",\n        styleChat: \"Chat\",\n        stylePost: \"Post\",\n        professional: \"Professional\"\n    },\n    promptLoadingStates: [\n        {\n            text: \"Crafting Your Digital Identity\"\n        },\n        {\n            text: \"Preparing Your Agent\"\n        },\n        {\n            text: \"Processing Platform Data\"\n        },\n        {\n            text: \"Designing Engagement Plan\"\n        },\n        {\n            text: \"Optimizing Post Schedule\"\n        }\n    ],\n    devLoadingStates: [\n        {\n            text: \"Creating Sandbox\"\n        },\n        {\n            text: \"Deploying Agent\"\n        },\n        {\n            text: \"Building Dream\"\n        },\n        {\n            text: \"Testing Dream\"\n        },\n        {\n            text: \"Launching Dream\"\n        }\n    ]\n};\nconst lang = {\n    header,\n    profile,\n    createIdea,\n    manageIdea,\n    homePage,\n    ideas,\n    generateIdea,\n    footer,\n    notFound,\n    ideaPage,\n    dreamathon,\n    enhancedDevelopmentProcess\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (lang);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./common/lang.ts\n"));

/***/ })

});