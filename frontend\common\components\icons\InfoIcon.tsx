import type { SVGProps } from "react";

export const InfoIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={16}
    height={16}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <mask
        id="b"
        width={16}
        height={16}
        x={0}
        y={0}
        maskUnits="userSpaceOnUse"
        style={{
          maskType: "luminance",
        }}
      >
        <path fill="#fff" d="M.667.667h14.666v14.666H.667V.667Z" />
      </mask>
      <g mask="url(#b)">
        <path
          fill="#66E1FF"
          d="M8 14.722A6.722 6.722 0 1 0 8 1.278a6.722 6.722 0 0 0 0 13.444Z"
        />
        <path
          fill="#C2F3FF"
          d="M8 3.417a6.719 6.719 0 0 1 6.629 5.652A6.718 6.718 0 1 0 1.277 8c.003.358.034.716.093 1.07A6.719 6.719 0 0 1 8 3.416Z"
        />
        <path
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M8 14.722A6.722 6.722 0 1 0 8 1.278a6.722 6.722 0 0 0 0 13.444Z"
        />
        <path
          stroke="#1F1F3A"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M9.527 11.056h-.916A.611.611 0 0 1 8 10.444V6.471a.306.306 0 0 0-.306-.305h-.917"
        />
        <path
          stroke="#1F1F3A"
          d="M7.847 4.944a.153.153 0 1 1 0-.305M7.847 4.944a.153.153 0 1 0 0-.305"
        />
      </g>
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h16v16H0z" />
      </clipPath>
    </defs>
  </svg>
)
