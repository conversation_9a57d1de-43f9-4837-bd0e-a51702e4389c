import { Metadata } from "next";

export const metaObject: Metadata = {
  title: 'DreamStartr | Initial Subscription Offering (ISO) Hub for Builders',
  description: 'Launch and scale subscription-based projects with AI assistance. Create ISOs, manage subscribers, and build sustainable revenue streams.',
  metadataBase: new URL("https://dreamstartr.com"),
  alternates: {
    canonical: 'https://dreamstartr.com',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
  keywords: 'DreamStartr, Web3, blockchain projects, AI development, bonding curves, community funding, token creation, project development, Dreams, go to market, GTM, AI-powered crowdfunding, dream funding platform, bonding curve investment, community-validated projects, decentralized fundraising, startup idea generation, AI project development, transparent token pricing, early supporter rewards, web3 project funding, innovation platform, creator funding, automated market maker, project governance, startup community building, AI content generation, decentralized finance platform, idea incubator, dream accelerator, creative project funding, community-driven development, project milestone tracking, tokenized investments, accessible entrepreneurship, AI assistant for startups',
  openGraph: {
    title: 'DreamStartr | Initial Subscription Offering (ISO) Hub for Builders',
    description: 'Launch and scale subscription-based projects with AI assistance. Create ISOs, manage subscribers, and build sustainable revenue streams.',
    url: 'https://dreamstartr.com',
    siteName: 'DreamStartr ISO Hub',
    images: [
      {
        url: 'https://dreamstartr.com/preview.png',
        width: 1200,
        height: 630,
        alt: 'DreamStartr ISO Hub',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'DreamStartr | ISO Hub for Subscription Businesses',
    description: 'Launch and scale subscription-based projects with AI assistance.',
    images: ['https://dreamstartr.com/preview.png'],
  },
}