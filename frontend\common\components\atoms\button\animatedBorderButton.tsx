"use client";
import React, {
  useState, useEffect, useCallback,
} from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { cn } from "@/common/utils/helpers";
import { themeElements } from "@/common/theme/themeElements";
import {
  AIButtonProps, Direction,
} from "./types";

export const AnimatedBorderButton = ({
  children,
  containerClassName,
  borderRadiusClass,
  className,
  duration = 1,
  clockwise = true,
  type = "button",
  disabled = false,
  width = "w-auto",
  href,
  ...props
}: AIButtonProps) => {
  const [hovered, setHovered] = useState<boolean>(false);
  const [direction, setDirection] = useState<Direction>("TOP");

  const buttonStyle = themeElements.buttons.outline.style;
  const containerStyle = themeElements.buttons.outline.container;
  const innerStyle = themeElements.buttons.outline.inner;
  const animationStyle = themeElements.buttons.outline.animation;
  const backgroundStyle = themeElements.buttons.outline.background;

  const rotateDirection = useCallback((currentDirection: Direction): Direction => {
    const directions: Direction[] = ["TOP", "LEFT", "BOTTOM", "RIGHT"];
    const currentIndex = directions.indexOf(currentDirection);
    const nextIndex = clockwise
      ? (currentIndex - 1 + directions.length) % directions.length
      : (currentIndex + 1) % directions.length;
    return directions[nextIndex];
  }, [clockwise]);

  const movingMap: Record<Direction, string> = {
    TOP: "radial-gradient(20.7% 50% at 50% 0%, #FE8989 0%, rgba(255, 255, 255, 0) 100%)",
    LEFT: "radial-gradient(16.6% 43.1% at 0% 50%, #FE8989 0%, rgba(255, 255, 255, 0) 100%)",
    BOTTOM:
      "radial-gradient(20.7% 50% at 50% 100%, #FE8989 0%, rgba(255, 255, 255, 0) 100%)",
    RIGHT:
      "radial-gradient(16.2% 41.199999999999996% at 100% 50%, #FE8989 0%, rgba(255, 255, 255, 0) 100%)",
  };

  const highlight =
    "radial-gradient(75% 181.15942028985506% at 50% 50%, #FE8989 0%, rgba(255, 255, 255, 0) 100%)";

  useEffect(() => {
    if (!hovered) {
      const interval = setInterval(() => {
        setDirection((prevState) => rotateDirection(prevState));
      }, duration * 1000);
      return () => clearInterval(interval);
    }
  }, [hovered, duration, rotateDirection]);

  const isLink = !!href;

  const commonClassNames = cn(
    buttonStyle,
    containerStyle,
    borderRadiusClass,
    `bg-gradient-to-tr from-light-cobalt-blue to-violets-are-blue`,
    width,
  );

  const handleMouseEnter = (e: React.MouseEvent<HTMLElement>) => {
    if (!disabled) {
      setHovered(true);
    }
    if (props.onMouseEnter) {
      props.onMouseEnter(e);
    }
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLElement>) => {
    setHovered(false);
    if (props.onMouseLeave) {
      props.onMouseLeave(e);
    }
  };

  const ButtonContent = (
    <>
      <div
        className={cn(
          innerStyle,
          `rounded-[inherit]`,
          className,
          containerClassName,
        )}
      >
        {children}
      </div>
      <motion.div
        className={cn(
          animationStyle,
          "rounded-[inherit]",
        )}
        style={{
          filter: "blur(2px)",
          position: "absolute",
          width: "100%",
          height: "100%",
        }}
        initial={{ background: movingMap[direction] }}
        animate={{
          background: hovered
            ? [movingMap[direction], highlight]
            : movingMap[direction],
        }}
        transition={{
          ease: "linear",
          duration: duration ?? 1,
        }}
      />
      <div className={cn(
        backgroundStyle,
        "bg-gray-800 group-hover:bg-gray-900 rounded-[inherit] inset-0.5",
      )} />
    </>
  );

  const restProps = { ...props };
  delete restProps.onMouseEnter;
  delete restProps.onMouseLeave;

  return isLink ? (
    <Link
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      href={href}
      className={cn(commonClassNames, "cursor-pointer")}
      {...restProps}
    >
      {ButtonContent}
    </Link>
  ) : (
    <button
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={commonClassNames}
      type={type === 'button' || type === 'submit' ? type : 'button'}
      disabled={disabled}
      {...restProps}
    >
      {ButtonContent}
    </button>
  );
}
