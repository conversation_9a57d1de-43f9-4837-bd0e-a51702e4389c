import { Suspense } from 'react';
import { <PERSON>ada<PERSON> } from "next";
import { Loader } from '@/common/components/atoms';
import AboutClient from './client';
import AboutStructuredData from './structuredData';

export const metadata: Metadata = {
  title: 'About DreamStartr | AI-Powered Web3 Project Development Platform',
  description: 'DreamStartr merges AI, community validation & bonding curves to help creators turn innovative ideas into successful blockchain & AI projects.',
  keywords: 'DreamStartr, Web3 platform, AI project development, bonding curves, community validation, blockchain projects, decentralized finance, project creation, crowdfunding',
  alternates: {
    canonical: `https://dreamstartr.com/about`,
  },
  openGraph: {
    title: 'About DreamStartr | AI-Powered Web3 Project Development Platform',
    description: 'Learn about DreamStartr, the AI-powered platform that helps you turn innovative ideas into successful Web3 projects.',
    url: `https://dreamstartr.com/about`,
    images: [
      {
        url: 'https://dreamstartr.com/about-preview.png',
        width: 1200,
        height: 630,
        alt: 'About DreamStartr',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About DreamStartr | AI-Powered Web3 Project Development Platform',
    description: 'Learn about DreamStartr, the AI-powered platform that helps you turn innovative ideas into successful Web3 projects.',
    images: ['https://dreamstartr.com/about-preview.png'],
  },
  robots: {
    index: true,
    follow: true,
  },
}

const AboutPage = () => {
  return (
    <>
      <div className="hidden">
        <h1>About DreamStartr - AI-Powered Web3 Project Development Platform</h1>
        <p>DreamStartr is revolutionizing how ideas become reality by combining the power of AI, community validation, and decentralized finance. Our platform empowers you to transform your dreams into successful projects with the support of an engaged community.</p>
        
        <h2>Our Core Features</h2>
        <ul>
          <li><strong>AI-Powered Creation:</strong> Our AI assistant helps refine concepts, generate content, and create professional project assets to bring your dreams to life.</li>
          <li><strong>Bonding Curves:</strong> Fair and transparent token pricing through automated market makers, rewarding early supporters while maintaining sustainable growth.</li>
          <li><strong>AI Community Growth:</strong> Dedicated AI agents help build and engage your community through automated updates, responses, and content sharing.</li>
          <li><strong>Community Validation:</strong> Projects grow through transparent community feedback, governance, and milestone tracking.</li>
        </ul>
        
        <h2>How DreamStartr Helps Creators</h2>
        <p>Transform your ideas into reality with AI assistance, get instant access to a supportive community, secure funding through fair token distribution, maintain creative control of your project, and access powerful tools for community building.</p>
        
        <h2>Benefits for Supporters</h2>
        <p>Discover innovative projects early, support ideas you believe in, earn potential returns through bonding curves, participate in project governance, and help shape the future of exciting projects.</p>
        
        <h2>Getting Started with DreamStartr</h2>
        <ol>
          <li>Connect Your Wallet or Create a Wallet using your email</li>
          <li>Choose to either launch your own dream or browse existing projects to support</li>
          <li>Join the community and engage with creators, supporters, and AI agents</li>
        </ol>
        
        <h2>Our Vision</h2>
        <p>DreamStartr believes that great ideas can come from anywhere. By combining AI with community-driven development and decentralized finance, we're creating an ecosystem where innovation thrives and dreams become reality.</p>
      </div>
      <Suspense fallback={<Loader />}>
        <AboutClient />
      </Suspense>
      <AboutStructuredData />
    </>
  );
};

export default AboutPage;
