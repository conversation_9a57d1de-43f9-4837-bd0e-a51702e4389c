import {
  motion,
  SVGMotionProps,
} from "framer-motion";

interface NavIconProps extends SVGMotionProps<SVGSVGElement> {
  active?: boolean;
}

export const NavIcon = ({
  active = false,
  ...props
}: NavIconProps) => {
  const transition = {
    duration: 0.3,
    ease: "easeInOut",
  };

  const topLineVariants = {
    closed: {
      rotate: 0,
      y: 0,
    },
    open: {
      rotate: 45,
      y: 5.5,
    },
  };

  const middleLineVariants = {
    closed: {
      opacity: 1,
    },
    open: {
      opacity: 0,
    },
  };

  const bottomLineVariants = {
    closed: {
      rotate: 0,
      y: 0,
    },
    open: {
      rotate: -45,
      y: -5.5,
    },
  };

  return (
    <motion.svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={18}
      fill="none"
      viewBox="0 0 24 18"
      {...props}
    >
      <motion.g
        initial="closed"
        animate={active ? "open" : "closed"}
        transition={transition}
      >
        <motion.g
          variants={topLineVariants}
          style={{
            originX: "center",
            originY: "center",
            transformBox: "fill-box",
            position: "relative",
          }}
        >
          <motion.path
            fill="#6673FF"
            d="M23.21 3.165c0 .233-.14.456-.387.621a1.708 1.708 0 0 1-.932.258H2.109c-.35 0-.685-.093-.932-.258S.79 3.398.79 3.165v-.88c0-.233.139-.456.386-.621.247-.165.583-.258.932-.258h19.782c.35 0 .685.093.932.258s.386.388.386.621v.88Z"
          />
          <motion.path
            fill="#C4C2FF"
            d="M23.21 2.725v-.44c0-.233-.14-.456-.387-.621a1.709 1.709 0 0 0-.932-.258H2.109c-.35 0-.685.093-.932.258s-.386.388-.386.621v.44h22.418Z"
          />
        </motion.g>

        <motion.g variants={middleLineVariants}>
          <motion.path
            fill="#6673FF"
            d="M23.21 8.44c0 .233-.14.456-.387.621a1.708 1.708 0 0 1-.932.258H2.109c-.35 0-.685-.093-.932-.258S.79 8.673.79 8.44v-.88c0-.233.139-.456.386-.621.247-.165.583-.258.932-.258h19.782c.35 0 .685.093.932.258s.386.388.386.621v.88Z"
          />
          <motion.path
            fill="#C4C2FF"
            d="M23.21 8v-.44c0-.233-.14-.456-.387-.621a1.708 1.708 0 0 0-.932-.258H2.109c-.35 0-.685.093-.932.258S.79 7.327.79 7.56V8h22.418Z"
          />
        </motion.g>

        <motion.g
          variants={bottomLineVariants}
          style={{
            originX: "center",
            originY: "center",
            transformBox: "fill-box",
            position: "relative",
          }}
        >
          <motion.path
            fill="#6673FF"
            d="M23.21 13.715c0 .233-.14.457-.387.621a1.708 1.708 0 0 1-.932.258H2.109c-.35 0-.685-.093-.932-.258s-.386-.388-.386-.621v-.88c0-.233.139-.456.386-.621.247-.165.583-.258.932-.258h19.782c.35 0 .685.093.932.258s.386.388.386.621v.88Z"
          />
          <motion.path
            fill="#C4C2FF"
            d="M23.21 13.275v-.44c0-.233-.14-.456-.387-.621a1.708 1.708 0 0 0-.932-.258H2.109c-.35 0-.685.093-.932.258s-.386.388-.386.621v.44h22.418Z"
          />
        </motion.g>
      </motion.g>
    </motion.svg>
  );
}
