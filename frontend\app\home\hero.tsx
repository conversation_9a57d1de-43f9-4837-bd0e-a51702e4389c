'use client'
import {
  useEffect,
} from "react";
import { motion } from "framer-motion";
import {
  ShaderGradient,
  TooltipProvider,
  TypewriterEffect,
} from "@/common/components/atoms";
import {
  heroWords,
} from "@/common/constants";
import lang from "@/common/lang";
import { secondaryFont } from "@/common/utils/localFont";
import {
  MixpanelEventName,
  useMixpanelEvent,
} from "@/common/utils/mixpanel/eventTriggers";


const {
  homePage: homePageCopy,
} = lang

export const Hero = () => {

  const {
    mixpanelEvent,
  } = useMixpanelEvent()

  useEffect(() => {
    mixpanelEvent({
      eventName: MixpanelEventName.pageView,
      mixpanelProps: {
        page: 'Home',
      },
    });
  }, [mixpanelEvent])

  return (
    <TooltipProvider>
      <div className="pt-24 max-w-[100vw] relative min-h-screen">
        <ShaderGradient />
        <div className='w-full container mx-auto px-2 md:px-8'>
          <div className="pt-44 sm:pt-48 flex flex-col items-center">
            <motion.div
              initial={{
                opacity: 0,
              }}
              animate={{
                opacity: 1,
              }}
              transition={{
                delay: 1,
                type: "spring",
                stiffness: 260,
                damping: 20,
              }}
            >
              <TypewriterEffect words={heroWords} className={`font-semibold ${secondaryFont.className} text-center text-4xl sm:text-5xl mt-4`} />
            </motion.div>
            <motion.div
              className="flex flex-col items-center"
              initial={{
                opacity: 0,
                transform: 'translateY(20px)',
              }}
              animate={{
                opacity: 1,
                transform: 'translateY(0)',
              }}
              transition={{
                delay: 1.5,
                type: "spring",
                stiffness: 260,
                damping: 20,
              }}
            >
              <h2 className='text-white/70 mt-4 w-full text-center'>
                {homePageCopy.subHeading}
              </h2>
            </motion.div>
            
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};
