'use client'

import {
  useState, useEffect,
} from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { secondaryFont } from '@/common/utils/localFont';
import {
  ShaderGradient, TypewriterEffect,
} from '@/common/components/atoms';
import TokenSaleCard from './TokenSaleCard';
import Image from 'next/image';

const luminaHeroWords = [
  {
    text: "The",
  },
  {
    text: "World's",
  },
  {
    text: "First",
  },
  {
    text: "Quantum",
    className: "text-light-yellow",
  },
  {
    text: "Longevity",
    className: "text-light-yellow",
  },
  {
    text: "Protocol",
  },
];

const Hero = () => {
  const [isTypewriterComplete, setIsTypewriterComplete] = useState(false);
  const totalCharCount = luminaHeroWords.reduce((acc, word) => acc + word.text.length, 0);
  const animationDuration = totalCharCount * 0.05;

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsTypewriterComplete(true);
    }, animationDuration * 1000);

    return () => clearTimeout(timer);
  }, [animationDuration]);
  return (
    <div className='relative'>
      <ShaderGradient
        firstColor="249, 213, 110"
        secondColor="14, 17, 17"
        thirdColor="255, 204, 0"
        fourthColor="255, 180, 0"
        fifthColor="230, 214, 155"
      />
      <section className="py-4 relative lg:py-16 w-full container px-2 md:px-8 mx-auto !max-w-[1200px]">
        <div className="pt-20">
          <motion.div
            initial={{
              opacity: 0,
              y: -10,
            }}
            animate={{
              opacity: 1,
              y: 0,
            }}
            transition={{ duration: 0.5 }}
            className="flex flex-col items-center md:inline-block mb-2 md:mb-8"
          >
            <div className="flex w-min whitespace-nowrap gap-1 items-center bg-violets-are-blue/20 rounded-full px-4 py-1 mb-2 lg:mb-4">
              <span className="h-2 w-2 bg-light-yellow rounded-full animate-pulse"></span>
              <span className="text-xs lg:text-sm text-light-yellow font-medium">Fundraising Live</span>
            </div>

            <div className='mt-2 md:mt-10 flex items-center gap-4'>
              <Image src="/casa_logo.png" alt="hero" width={1200} height={800} className="w-auto h-full max-h-10 md:max-h-12 lg:max-h-16" />
              <div className={`text-light-yellow text-3xl md:text-5xl lg:text-6xl font-semibold text-shadow-glow ${secondaryFont.className}`}>Lumina Casa</div>
            </div>
          </motion.div>

          <div className="flex flex-col lg:flex-row gap-8 items-start">
            <motion.div
              initial={{
                opacity: 0,
                y: 20,
              }}
              animate={{
                opacity: 1,
                y: 0,
              }}
              transition={{ duration: 0.8 }}
              className="lg:w-7/12 pt-4"
            >
              <div className="mb-12">
                <TypewriterEffect
                  words={luminaHeroWords}
                  className={`md:text-left text-center mx-auto md:mx-0 text-xl md:text-2xl lg:text-3xl font-semibold text-white max-w-[420px] ${secondaryFont.className}`}
                />
                <motion.div
                  initial={{
                    opacity: 0,
                    y: 20,
                  }}
                  animate={{
                    opacity: isTypewriterComplete ? 1 : 0,
                    y: isTypewriterComplete ? 0 : 20,
                  }}
                  transition={{
                    duration: 0.6,
                    delay: 0.2,
                  }}
                  className='text-sm mt-2 text-gray-300 text-center md:text-left'>Backed by Real World Assets</motion.div>
              </div>

              <motion.p
                className="text-gray-300 mb-4 max-w-3xl md:text-left text-center"
                initial={{
                  opacity: 0,
                  y: 20,
                }}
                animate={{
                  opacity: isTypewriterComplete ? 1 : 0,
                  y: isTypewriterComplete ? 0 : 20,
                }}
                transition={{
                  duration: 0.6,
                  delay: 0.2,
                }}
              >
                You are invited to join the next evolutionary wave of conscious capital: <b className='text-white'>Lumina CASA</b> — a system where wealth is sacred, enterprise is conscious, and innovation is aligned with the soul of Earth. Integrating bio-resonance, sacred geometry, and quantum harmonics to extend the healthy human lifespan.
              </motion.p>

              <motion.div
                className="flex md:justify-start justify-center gap-4 mb-12"
                initial={{
                  opacity: 0,
                  y: 20,
                }}
                animate={{
                  opacity: isTypewriterComplete ? 1 : 0,
                  y: isTypewriterComplete ? 0 : 20,
                }}
                transition={{
                  duration: 0.6,
                  delay: 0.4,
                }}
              >
                <Link
                  href="https://dreamstartr.com/LUMINA_CASA_WHITE_PAPER.pdf"
                  rel="noopener noreferrer"
                  className={`w-full sm:w-min whitespace-nowrap block text-center py-2 px-6 bg-violets-are-blue/5 border border-white/20 hover:bg-yellow-200/80 hover:text-black text-white font-medium rounded-xl transition-all duration-300`}
                >
                  Read Whitepaper
                </Link>
              </motion.div>

              <motion.div
                className="grid grid-cols-4 gap-4 mt-8"
                initial={{
                  opacity: 0,
                  y: 20,
                }}
                animate={{
                  opacity: isTypewriterComplete ? 1 : 0,
                  y: isTypewriterComplete ? 0 : 20,
                }}
                transition={{
                  duration: 0.6,
                  delay: 0.6,
                }}>
                <div className="md:text-left text-center">
                  <div className={`text-light-yellow text-xl font-semibold text-shadow-glow ${secondaryFont.className}`}>$CASA</div>
                  <div className="text-gray-400 text-xs">Token Symbol</div>
                </div>
                <div className="md:text-left text-center">
                  <div className={`text-white text-xl font-semibold`}>1B</div>
                  <div className="text-gray-400 text-xs">Total Supply</div>
                </div>
                <div className="md:text-left text-center">
                  <div className={`text-white text-xl font-semibold`}>55</div>
                  <div className="text-gray-400 text-xs">Real World Sanctuaries</div>
                </div>
                <div className="md:text-left text-center">
                  <div className={`text-white text-xl font-semibold`}>40%</div>
                  <div className="text-gray-400 text-xs">Rental Yield</div>
                </div>
              </motion.div>
            </motion.div>

            <motion.div
              initial={{
                opacity: 0,
                y: 40,
              }}
              animate={{
                opacity: isTypewriterComplete ? 1 : 0,
                y: isTypewriterComplete ? 0 : 40,
              }}
              transition={{
                duration: 0.8,
                delay: 0.8,
              }}
              className="lg:w-5/12 w-full mt-8 lg:mt-0"
            >
              <TokenSaleCard />
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Hero;
