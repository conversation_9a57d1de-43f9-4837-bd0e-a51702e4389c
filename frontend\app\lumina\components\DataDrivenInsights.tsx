'use client'

import {
  motion, useInView,
} from 'framer-motion';
import { useRef } from 'react';

const DataDrivenInsights = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, {
    once: true,
    amount: 0.2,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  const statVariants = {
    hidden: {
      opacity: 0,
      scale: 0.8,
    },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const stats = [
    {
      value: "78%",
      description: "Aging acceleration from current societal inhibitors",
    },
    {
      value: "4.8 Trillion",
      description: "Longevity Market",
    },
    {
      value: "40%",
      description: "Global Rental Yield",
    },
  ];

  const problemSolutions = [
    {
      type: "problem",
      items: [
        {
          text: "$4.8 trillion longevity market with accelerating population decline",
          highlight: '$4.8 trillion',
        },
        {
          text: "Modern society causing rapid ageing in human populations",
          highlight: "rapid ageing",
        },
        {
          text: "Most health interventions fail to address root quantum causes",
          highlight: '',
        },
      ],
    },
    {
      type: "solution",
      items: [
        {
          text: "Real World Sanctuaries with a global enlightened and conscious community of builders",
          highlight: 'Real World Sanctuaries',
        },
        {
          text: "Longevity programs backed by sacred geometries for collective longevity advancement",
          highlight: 'collective longevity',
        },
        {
          text: "Fractionalized and democratized community ownership model",
          highlight: 'democratized community',
        },
      ],
    },
  ];

  return (
    <section
      id="market"
      ref={sectionRef}
      className="py-4 lg:py-16 relative w-full container px-2 md:px-8 mx-auto !max-w-[1200px]"
    >
      <div className="">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Header */}
          <div className="mb-8">
            <div className="w-min whitespace-nowrap bg-violets-are-blue/20 rounded-full px-4 py-1 mb-2 lg:mb-4 text-xs lg:text-sm text-light-yellow font-medium">
              Data-Driven Insights
            </div>
            <h2 className="text-xl md:text-2xl lg:text-3xl font-semibold text-white my-2">
              Market Hypothesis
            </h2>
            <div className="rounded-full h-1 w-20 bg-gradient-to-tr from-light-yellow to-yellow-600 mb-4"></div>
            <p className="text-neutral-300 mb-4 text-sm md:text-base max-w-3xl">
              Our hypothesis is that our quantum longevity protocol will radically transmute the current effects of degenerative modern society.
            </p>
          </div>

          {/* Stats Section */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 md:grid-cols-3 gap-4 lg:gap-6 mb-10 lg:mb-12"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={statVariants}
                className="bg-violets-are-blue/5 border border-violets-are-blue/15 rounded-3xl p-4 lg:p-6 text-center transition-all duration-300"
              >
                <h3 className="text-2xl md:text-4xl font-bold text-light-yellow mb-2">
                  {stat.value}
                </h3>
                <p className="text-neutral-300 text-sm">
                  {stat.description}
                </p>
              </motion.div>
            ))}
          </motion.div>

          {/* Problem & Solution Section */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 relative overflow-hidden md:grid-cols-2 gap-6 bg-eerie-black border border-light-yellow/5 rounded-3xl p-6 lg:p-8"
          >
            <div className="absolute pointer-events-none bottom-10 z-0 left-1/2 -translate-x-1/2 bg-light-yellow opacity-50 w-1/4 lg:w-1/2 h-20 blur-[120px]" />

            {/* Problem Column */}
            <motion.div variants={itemVariants} className='relative z-10'>
              <h3 className="text-xl font-semibold text-white mb-4">
                The Problem
              </h3>
              <div className="space-y-4">
                {problemSolutions[0].items.map((item, idx) => (
                  <div key={idx} className="flex items-center gap-3">
                    <div className="mt-1 min-w-6 w-6 h-6 rounded-full flex items-center justify-center bg-light-yellow/20 border border-light-yellow/30">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-light-yellow">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                    </div>
                    <p className="text-neutral-300 text-sm">
                      {item.highlight ? (
                        <>
                          {item.text.split(item.highlight as string)[0]}
                          <span className="text-white font-medium">{item.highlight}</span>
                          {item.text.split(item.highlight as string)[1]}
                        </>
                      ) : (
                        item.text
                      )}
                    </p>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Solution Column */}
            <motion.div variants={itemVariants} className='relative z-10'>
              <h3 className="text-xl font-semibold text-white mb-4">
                Our Solution
              </h3>
              <div className="space-y-4">
                {problemSolutions[1].items.map((item, idx) => (
                  <div key={idx} className="flex items-center gap-3">
                    <div className="mt-1 min-w-6 w-6 h-6 rounded-full flex items-center justify-center bg-light-yellow/20 border border-light-yellow/30">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-light-yellow">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                    </div>
                    <p className="text-neutral-300 text-sm">
                      {item.highlight ? (
                        <>
                          {item.text.split(item.highlight as string)[0]}
                          <span className="text-white font-medium">{item.highlight}</span>
                          {item.text.split(item.highlight as string)[1]}
                        </>
                      ) : (
                        item.text
                      )}
                    </p>
                  </div>
                ))}
              </div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default DataDrivenInsights;
