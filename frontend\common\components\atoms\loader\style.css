
.loader {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(#E3A47F, #CA428C, #6741E8);
    animation: animate 0.5s linear infinite;
  }
  
  @keyframes animate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  .loader span {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(#E3A47F, #CA428C, #6741E8);
  }
  
  .loader span:nth-child(1){
    filter: blur(5px);
  }
  
  .loader span:nth-child(2){
    filter: blur(10px);
  }
  
  .loader span:nth-child(3){
    filter: blur(25px);
  }
  
  .loader span:nth-child(4){
    filter: blur(50px);
  }
  
  .loader:after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    bottom: 10px;
    right: 10px;
    background: #240229;
    border-radius: 50%;
  }