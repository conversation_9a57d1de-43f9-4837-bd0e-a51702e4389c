{"v": "5.12.1", "fr": 60, "ip": 0, "op": 90, "w": 430, "h": 430, "nm": "wired-flat-1865-shooting-stars", "ddd": 0, "assets": [{"id": "comp_0", "nm": "in-reveal", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "outline 15", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [198.09, 196.838, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": -30, "s": [182.09, 180.838, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": -6, "s": [518.09, 517.839, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": -5, "s": [-225.91, -230.162, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [198.09, 196.838, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": -30, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-142.502, -143.044], [61.273, 61.273]], "c": false}]}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": -6, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-258.106, -258.44], [61.273, 61.273]], "c": false}]}, {"t": -5, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-142.502, -143.044], [61.273, 61.273]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 339, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 30, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": -94, "s": [254]}, {"t": 90, "s": [3944]}], "ix": 7}}], "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -94, "op": 750, "st": -94, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "outline 17", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [347.66, 187.44, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": -24, "s": [339.66, 180.44, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": -4, "s": [567.66, 413.44, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": -3, "s": [-133.34, -290.56, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [347.66, 187.44, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.111, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-131.095, -131.99]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": -24, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-168.288, -169.297]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": -5, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-187.471, -188.114]], "c": false}]}, {"i": {"x": 0.09, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": -3, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-329.538, -329.547]], "c": false}]}, {"t": 90, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-131.095, -131.99]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 178, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 26, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": -94, "s": [-55]}, {"t": 90, "s": [-3524]}], "ix": 7}}], "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -94, "op": 750, "st": -94, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "outline 16", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [347.66, 187.44, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": -24, "s": [339.66, 180.44, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": -4, "s": [567.66, 413.44, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": -3, "s": [-133.34, -290.56, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [347.66, 187.44, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[76.368, 77.217], [-19.737, -20.348]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 54, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 100, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": -94, "s": [-81]}, {"t": 90, "s": [-3624]}], "ix": 7}}], "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -94, "op": 750, "st": -94, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "outline 19", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [187.66, 345.44, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": -21, "s": [179.66, 338.44, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": -3, "s": [407.66, 571.44, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": -2, "s": [-293.34, -132.56, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [187.66, 345.44, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.111, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-131.095, -131.99]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": -21, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-168.288, -169.297]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": -4, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-187.471, -188.114]], "c": false}]}, {"i": {"x": 0.09, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": -2, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-329.538, -329.547]], "c": false}]}, {"t": 90, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-131.095, -131.99]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 178, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 26, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": -94, "s": [-97]}, {"t": 90, "s": [-3566]}], "ix": 7}}], "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -94, "op": 750, "st": -94, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "outline 18", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [187.66, 345.44, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": -21, "s": [179.66, 338.44, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": -3, "s": [407.66, 571.44, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": -2, "s": [-293.34, -132.56, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [187.66, 345.44, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[76.368, 77.217], [-19.737, -20.348]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 54, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 100, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": -94, "s": [-81]}, {"t": 90, "s": [-3624]}], "ix": 7}}], "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -94, "op": 750, "st": -94, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "outline 21", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [171.09, 90.838, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": -14, "s": [155.09, 74.838, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 0, "s": [491.09, 411.838, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 1, "s": [-252.91, -336.162, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [171.09, 90.838, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -21, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": -5, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 1, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"t": 12, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 100, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 160, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": -94, "s": [54]}, {"t": 90, "s": [-3586]}], "ix": 7}}], "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -21, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": -5, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 1, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"t": 12, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 100, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 160, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": -94, "s": [184]}, {"t": 90, "s": [-3456]}], "ix": 7}}], "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": -94, "op": 750, "st": -94, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "outline 20", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [81.09, 158.838, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": -18, "s": [65.09, 142.838, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": -1, "s": [401.09, 479.838, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-342.91, -268.162, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [81.09, 158.838, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -25, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": -9, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"t": 11, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 100, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 160, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": -94, "s": [10]}, {"t": 90, "s": [-3630]}], "ix": 7}}], "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -25, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": -9, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"t": 11, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 100, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 160, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": -94, "s": [140]}, {"t": 90, "s": [-3500]}], "ix": 7}}], "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": -94, "op": 750, "st": -94, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 3, "nm": "Star-1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -94, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -92, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -90, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -88, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -86, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -84, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -82, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -80, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -78, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -76, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -74, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -72, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -70, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -68, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -66, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -64, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -62, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -60, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -58, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -56, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -54, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -52, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -50, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -48, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -46, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -44, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -42, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -40, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -38, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -36, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -34, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -32, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -30, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -28, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -26, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -24, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -22, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -20, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -18, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -16, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -14, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -12, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -10, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -8, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -6, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -4, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -2, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 4, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 6, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 34, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 36, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 38, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 40, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 46, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 48, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 50, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 52, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 54, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 56, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 58, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 62, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 64, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 69, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 72, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 75, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 78, "s": [1]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [135.282, 292.116, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": -21, "s": [127.282, 285.116, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": -3, "s": [355.282, 518.116, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": -2, "s": [-345.718, -185.884, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [135.282, 292.116, 0]}], "ix": 2, "l": 2}, "a": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [0, 0, 0], "to": [0.167, 0.667, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -92, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -90, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -88, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -86, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -84, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -82, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -80, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -78, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -76, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -74, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -72, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -70, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -68, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -66, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -64, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -62, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -60, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -58, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -56, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -54, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -52, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -50, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -48, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -46, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -44, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -42, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -40, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -38, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -36, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -34, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -32, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -30, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -28, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -26, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -24, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -22, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -20, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -18, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -16, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -14, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -12, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -10, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -8, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -6, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -4, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -2, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 2, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 6, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 14, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 22, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 32, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 34, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 36, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 48, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 54, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 58, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 69, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 72, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 75, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 78, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0.167, 0.667, 0]}, {"t": 90, "s": [0, 0, 0]}], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": -94, "op": 750, "st": -94, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Vector 5", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-9.447, 12.361, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.136, 2.08], [0, 0], [0, 0], [0, 0], [0, 0], [-2.087, -1.136], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.086, 1.137], [0, 0], [0, 0], [1.141, 2.078]], "v": [[13.59, 18.664], [20.465, 6.073], [11.665, -10.027], [-7.035, -20.227], [-18.9, -13.762], [-18.899, -8.493], [-1.335, 1.073], [8.327, 18.671]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Vector 3", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1.467, 1.234, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.087, 1.137], [0, 0], [0, 0], [1.134, -2.083], [0, 0], [0, 0], [-2.087, -1.137], [0, 0], [0, 0], [-1.141, 2.079], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-1.141, -2.079], [0, 0], [0, 0], [-2.087, 1.137], [0, 0], [0, 0], [1.134, 2.083], [0, 0], [0, 0], [2.087, -1.137]], "v": [[29.813, -2.635], [12.25, -12.2], [2.59, -29.792], [-2.674, -29.782], [-12.25, -12.2], [-29.813, -2.635], [-29.813, 2.635], [-12.25, 12.2], [-2.674, 29.782], [2.59, 29.792], [12.25, 12.2], [29.813, 2.635]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 3, "nm": "Star-2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -94, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -92, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -90, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -88, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -86, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -84, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -82, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -80, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -78, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -76, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -74, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -72, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -70, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -68, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -66, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -64, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -62, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -60, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -58, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -56, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -54, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -52, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -50, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -48, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -46, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -44, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -42, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -40, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -38, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -36, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -34, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -32, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -30, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -28, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -26, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -24, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -22, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -20, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -18, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -16, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -14, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -12, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -10, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -8, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -6, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -4, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -2, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 4, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 6, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 34, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 36, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 38, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 40, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 46, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 48, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 50, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 52, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 54, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 56, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 58, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 62, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 64, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 69, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 72, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 75, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 78, "s": [-1]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [294.66, 132.94, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": -24, "s": [286.66, 125.94, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": -4, "s": [514.66, 358.94, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": -3, "s": [-186.34, -345.06, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [294.66, 132.94, 0]}], "ix": 2, "l": 2}, "a": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [0, 0, 0], "to": [-0.333, -0.5, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -92, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -90, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -88, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -86, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -84, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -82, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -80, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -78, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -76, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -74, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -72, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -70, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -68, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -66, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -64, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -62, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -60, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -58, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -56, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -54, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -52, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -50, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -48, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -46, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -44, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -42, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -40, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -38, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -36, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -34, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -32, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -30, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -28, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -26, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -24, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -22, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -20, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -18, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -16, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -14, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -12, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -10, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -8, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -6, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -4, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -2, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 2, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 6, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 14, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 22, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 32, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 34, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 36, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 48, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 54, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 58, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 69, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 72, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 75, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 78, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [-0.333, -0.5, 0]}, {"t": 90, "s": [0, 0, 0]}], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": -94, "op": 750, "st": -94, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Vector 6", "parent": 11, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-9.435, 12.383, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.141, 2.08], [0, 0], [0, 0], [0, 0], [0, 0], [-2.079, -1.141], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.082, 1.135], [0, 0], [0, 0], [1.135, 2.083]], "v": [[13.514, 18.715], [20.474, 6.027], [11.674, -10.073], [-7.026, -20.273], [-18.91, -13.797], [-18.918, -8.533], [-1.326, 1.127], [8.249, 18.708]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Vector 2", "parent": 11, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1.45, 1.26, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.076, 1.136], [0, 0], [0, 0], [1.134, -2.083], [0, 0], [0, 0], [-2.079, -1.141], [0, 0], [0, 0], [-1.141, 2.079], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-1.141, -2.079], [0, 0], [0, 0], [-2.083, 1.134], [0, 0], [0, 0], [1.134, 2.083], [0, 0], [0, 0], [2.072, -1.143]], "v": [[29.798, -2.671], [12.289, -12.25], [2.63, -29.842], [-2.635, -29.832], [-12.211, -12.25], [-29.793, -2.674], [-29.802, 2.59], [-12.211, 12.25], [-2.635, 29.832], [2.63, 29.842], [12.289, 12.25], [29.807, 2.588]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 3, "nm": "Star-3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -94, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -92, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -90, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -88, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -86, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -84, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -82, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -80, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -78, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -76, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -74, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -72, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -70, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -68, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -66, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -64, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -62, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -60, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -58, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -56, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -54, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -52, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -50, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -48, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -46, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -44, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -42, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -40, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -38, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -36, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -34, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -32, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -30, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -28, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -26, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -24, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -22, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -20, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -18, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -16, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -14, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -12, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -10, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -8, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -6, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -4, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -2, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 4, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 6, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 34, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 36, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 38, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 40, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 46, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 48, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 50, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 52, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 54, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 56, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 58, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 62, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 64, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 69, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 72, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 75, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 78, "s": [1]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [305.09, 307.838, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": -30, "s": [289.09, 291.838, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": -6, "s": [625.09, 628.839, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": -5, "s": [-118.91, -119.162, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [305.09, 307.838, 0]}], "ix": 2, "l": 2}, "a": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -94, "s": [0, 0, 0], "to": [0.333, -0.5, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -92, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -90, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -88, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -86, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -84, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -82, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -80, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -78, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -76, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -74, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -72, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -70, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -68, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -66, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -64, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -62, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -60, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -58, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -56, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -54, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -52, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -50, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -48, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -46, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -44, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -42, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -40, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -38, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -36, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -34, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -32, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -30, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -28, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -26, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -24, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -22, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -20, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -18, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -16, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -14, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -12, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -10, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -8, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -6, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -4, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": -2, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 2, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 6, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 14, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 22, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 32, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 34, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 36, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 48, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 54, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 58, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 69, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 72, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 75, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 78, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0.333, -0.5, 0]}, {"t": 90, "s": [0, 0, 0]}], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": -94, "op": 750, "st": -94, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Vector 4", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-36.714, 23.227, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-1.783, -1.737], [0, 0], [0, 0], [-2.199, 1.155]], "o": [[0, 0], [0, 0], [0, 0], [-2.464, 0.354], [0, 0], [0, 0], [-0.417, 2.449], [0, 0]], "v": [[20.823, 30.984], [28.423, -13.216], [-1.277, -42.116], [-25.847, -38.583], [-27.513, -33.464], [3.123, -3.616], [-4.06, 38.606], [0.293, 41.765]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Vector", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1.46, 1.871, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.098, -2.232], [0, 0], [0, 0], [-1.782, -1.736], [0, 0], [0, 0], [-2.199, 1.156], [0, 0], [0, 0], [0.416, 2.448], [0, 0], [0, 0], [2.462, 0.356], [0, 0]], "o": [[-1.102, -2.23], [0, 0], [0, 0], [-2.462, 0.356], [0, 0], [0, 0], [-0.417, 2.449], [0, 0], [0, 0], [2.199, 1.153], [0, 0], [0, 0], [1.782, -1.736], [0, 0], [0, 0]], "v": [[2.646, -61.806], [-2.736, -61.801], [-21.65, -23.359], [-64.027, -17.23], [-65.691, -12.113], [-35.05, 17.741], [-42.233, 59.96], [-37.88, 63.119], [-0.05, 43.241], [37.883, 63.127], [42.234, 59.967], [35.05, 17.741], [65.691, -12.113], [64.027, -17.23], [21.65, -23.359]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.78, 0.22, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-flat-1865-shooting-stars').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 1, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [1, 0.78, 0.22], "ix": 1}}]}], "ip": 0, "op": 281, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "in-reveal", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 100, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:in-reveal", "dr": 90}], "props": {}}