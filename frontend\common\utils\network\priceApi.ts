/**
 * Utility functions for fetching cryptocurrency price data
 */

/**
 * Fetches the current ETH price in USD from CoinGecko API
 * @returns Promise with the ETH price in USD
 */
export const fetchEthPrice = async (): Promise<number> => {
  try {
    // CoinGecko API endpoint for ETH price in USD
    const response = await fetch(
      'https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd',
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      },
    );

    if (!response.ok) {
      throw new Error(`CoinGecko API error: ${response.status}`);
    }

    const data = await response.json();
    return data.ethereum.usd;
  } catch (error) {
    console.error('Error fetching ETH price:', error);
    // Return a fallback price if the API call fails
    return 3000; // Fallback to $3000 as in the original code
  }
};

/**
 * Fetches the current price of a cryptocurrency by its ID from CoinGecko API
 * @param coinId The CoinGecko ID of the cryptocurrency (e.g., 'ethereum', 'bitcoin')
 * @param currency The currency to get the price in (default: 'usd')
 * @returns Promise with the price in the specified currency
 */
export const fetchCryptoPrice = async (
  coinId: string,
  currency: string = 'usd',
): Promise<number> => {
  try {
    const response = await fetch(
      `https://api.coingecko.com/api/v3/simple/price?ids=${coinId}&vs_currencies=${currency}`,
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      },
    );

    if (!response.ok) {
      throw new Error(`CoinGecko API error: ${response.status}`);
    }

    const data = await response.json();
    return data[coinId][currency];
  } catch (error) {
    console.error(`Error fetching ${coinId} price:`, error);
    return 0;
  }
};
