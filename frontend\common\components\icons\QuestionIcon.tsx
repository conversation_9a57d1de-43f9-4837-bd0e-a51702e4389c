import type { SVGProps } from "react";

export const QuestionIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={16}
    height={16}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <path
        fill="#66E1FF"
        d="M14.696 12.858H4.174L.922 15.226a.16.16 0 0 1-.231-.044.16.16 0 0 1-.024-.084V1.38a.638.638 0 0 1 .637-.637h13.392a.638.638 0 0 1 .637.637v10.84a.638.638 0 0 1-.637.638Z"
      />
      <path
        fill="#C2F3FF"
        d="m.922 15.226 3.252-2.368h.08l11.08-11.08V1.38a.637.637 0 0 0-.638-.637H1.304a.638.638 0 0 0-.637.637v13.717a.16.16 0 0 0 .255.129Z"
      />
      <path
        stroke="#0E1111"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M5.768 5.366a2.393 2.393 0 1 1 2.909 2.336.642.642 0 0 0-.518.621v.71"
      />
      <path
        stroke="#0E1111"
        d="M8.16 10.945a.16.16 0 1 1 0-.318M8.16 10.945a.16.16 0 1 0 0-.318"
      />
      <path
        stroke="#0E1111"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M14.696 12.858H4.174L.922 15.226a.16.16 0 0 1-.231-.044.16.16 0 0 1-.024-.084V1.38a.638.638 0 0 1 .637-.637h13.392a.638.638 0 0 1 .637.637v10.84a.638.638 0 0 1-.637.638Z"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h16v16H0z" />
      </clipPath>
    </defs>
  </svg>
)
