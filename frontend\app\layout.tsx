import {
  ReactNode,
} from "react";
import { Providers } from "@/common/providers";
import {
  primaryFont,
} from "@/common/utils/localFont";
import { metaObject } from "./metaData";
import './globals.css'

export const metadata = metaObject

export default async function RootLayout ({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <html lang="en" className={`${primaryFont.className}`}>
      <body className={`bg-eerie-black`}>
        <div className="pointer-events-none fixed -z-10 inset-0">
          <div
            style={{
              backgroundSize: '109px',
              backgroundImage: 'url(/noise.png)',
            }}
            className="w-full h-full opacity-[.03] bg-repeat"></div>
        </div>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}
