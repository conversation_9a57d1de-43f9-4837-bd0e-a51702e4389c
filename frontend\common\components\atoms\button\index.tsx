import { useMemo } from 'react';
import { themeElements } from '@/common/theme/themeElements';
import { AnimatedBorderButton } from './animatedBorderButton';
import {
  ButtonProps, TVariants, TSizes,
} from './types';

const getVariantClasses = (variant: keyof typeof TVariants) => themeElements.buttons[variant].style;
const getSizeClasses = (variant: keyof typeof TVariants, size: keyof typeof TSizes) => themeElements.buttons[variant].size[size];

export const Button = ({
  variant = 'gradient',
  size = 'md',
  width = 'w-auto',
  type = 'button',
  disabled = false,
  icon: Icon,
  className,
  children,
  ...props
}: ButtonProps) => {

  const computedClasses = useMemo(() => {
    const variantClasses = getVariantClasses(variant);
    const sizeClasses = getSizeClasses(variant, size);
    const extraClasses = className || '';
    return [variantClasses, sizeClasses, extraClasses].join(' ');
  }, [variant, size, className]);

  if (variant === 'outline') {
    const getBorderRadiusClass = themeElements.buttons["outline"].borderRadius[size];

    const sizeClasses = getSizeClasses(variant, size);
    return (
      <AnimatedBorderButton
        width={width}
        type={type}
        disabled={disabled}
        className={className}
        borderRadiusClass={getBorderRadiusClass}
        containerClassName={sizeClasses}
        {...props}
      >
        {children}
      </AnimatedBorderButton>
    );
  }

  return (
    <button
      className={`${width} ${computedClasses}`}
      type={type === 'button' || type === 'submit' ? type : 'button'}
      disabled={disabled}
      {...props}
    >
      {!!Icon && <Icon />}
      {children}
    </button>
  );
};
