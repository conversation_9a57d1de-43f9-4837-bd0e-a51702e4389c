import {
  useState, useEffect,
} from 'react';
import { fetcher } from '@/common/utils/network/baseFetcher';

export type AddressTransaction = {
  hash: string;
  block_timestamp: string;
  block_number: string;
  transaction_hash?: string;
  from_address: string;
  to_address: string;
  value: string;
  gas: string;
  gas_price: string;
  summary: string;
  native_transfers?: {
    value: string;
    value_formatted: string;
    token_symbol: string;
  }[];
  category?: string;
};

export type AddressTransactionsResponse = {
  result: AddressTransaction[];
  total?: number;
  page: number;
  page_size: number;
  limit?: string;
};

export const useAddressTransactions = (limit: number = 3) => {
  const [transactions, setTransactions] = useState<AddressTransaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchTransactions = async () => {
      const destinationAddress = process.env.NEXT_PUBLIC_LUMINACASA_DESTINATION_ADDRESS ??
        "******************************************";

      setIsLoading(true);
      try {
        const url = `https://deep-index.moralis.io/api/v2.2/wallets/${destinationAddress}/history?chain=base&limit=${limit}&order=DESC`;

        const response = await fetcher(url, {
          arg: {
            method: 'GET',
            headers: {
              'X-API-Key': process.env.NEXT_PUBLIC_X_API_KEY || '',
            },
          },
        });

        if (response && response.result) {
          const mappedTransactions = response.result.map((tx: AddressTransaction) => ({
            ...tx,
            transaction_hash: tx.hash,
          }));
          setTransactions(mappedTransactions);
        } else {
          setTransactions([]);
        }
      } catch (err) {
        console.error('Error fetching address transactions:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch transactions'));
        setTransactions([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTransactions();
  }, [limit]);

  return {
    transactions,
    isLoading,
    error,
  };
};
