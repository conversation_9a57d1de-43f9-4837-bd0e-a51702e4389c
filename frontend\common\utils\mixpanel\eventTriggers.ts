import { Dict } from 'mixpanel-browser';
import { useEffect } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { useAccount } from 'wagmi';
import { Mixpanel } from '.';
import {
  MixpanelEventName, CustomEventProps,
} from './types';

export type { Dict };
export { MixpanelEventName };

export const useMixpanelEvent = () => {
  const {
    address,
  } = useAccount()
  const {
    authenticated,
  } = usePrivy()

  useEffect(() => {
    if (authenticated) {
      Mixpanel.identify(address as string);
    }
  }, [authenticated, address])

  const mixpanelEvent = ({
    mixpanelProps = {}, eventName,
  }: CustomEventProps) => {
    try {
      Mixpanel.track(eventName, mixpanelProps);
    } catch (err) {
      console.error('error', `Mixpanel tracking error: ${err}`);
    }
  };
  return {
    mixpanelEvent,
  }
}
