"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-speech-recognition";
exports.ids = ["vendor-chunks/react-speech-recognition"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-speech-recognition/lib/NativeSpeechRecognition.js":
/*!******************************************************************************!*\
  !*** ./node_modules/react-speech-recognition/lib/NativeSpeechRecognition.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = exports.isNative = void 0;\nvar NativeSpeechRecognition = typeof window !== 'undefined' && (window.SpeechRecognition || window.webkitSpeechRecognition || window.mozSpeechRecognition || window.msSpeechRecognition || window.oSpeechRecognition);\n\nvar isNative = function isNative(SpeechRecognition) {\n  return SpeechRecognition === NativeSpeechRecognition;\n};\n\nexports.isNative = isNative;\nvar _default = NativeSpeechRecognition;\nexports[\"default\"] = _default;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtc3BlZWNoLXJlY29nbml0aW9uL2xpYi9OYXRpdmVTcGVlY2hSZWNvZ25pdGlvbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixrQkFBa0IsR0FBRyxnQkFBZ0I7QUFDckM7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGdCQUFnQjtBQUNoQjtBQUNBLGtCQUFrQiIsInNvdXJjZXMiOlsid2VicGFjazovL2RyZWFtc3RhcnRyLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXNwZWVjaC1yZWNvZ25pdGlvbi9saWIvTmF0aXZlU3BlZWNoUmVjb2duaXRpb24uanM/YzU5MCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHNbXCJkZWZhdWx0XCJdID0gZXhwb3J0cy5pc05hdGl2ZSA9IHZvaWQgMDtcbnZhciBOYXRpdmVTcGVlY2hSZWNvZ25pdGlvbiA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmICh3aW5kb3cuU3BlZWNoUmVjb2duaXRpb24gfHwgd2luZG93LndlYmtpdFNwZWVjaFJlY29nbml0aW9uIHx8IHdpbmRvdy5tb3pTcGVlY2hSZWNvZ25pdGlvbiB8fCB3aW5kb3cubXNTcGVlY2hSZWNvZ25pdGlvbiB8fCB3aW5kb3cub1NwZWVjaFJlY29nbml0aW9uKTtcblxudmFyIGlzTmF0aXZlID0gZnVuY3Rpb24gaXNOYXRpdmUoU3BlZWNoUmVjb2duaXRpb24pIHtcbiAgcmV0dXJuIFNwZWVjaFJlY29nbml0aW9uID09PSBOYXRpdmVTcGVlY2hSZWNvZ25pdGlvbjtcbn07XG5cbmV4cG9ydHMuaXNOYXRpdmUgPSBpc05hdGl2ZTtcbnZhciBfZGVmYXVsdCA9IE5hdGl2ZVNwZWVjaFJlY29nbml0aW9uO1xuZXhwb3J0c1tcImRlZmF1bHRcIl0gPSBfZGVmYXVsdDsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-speech-recognition/lib/NativeSpeechRecognition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-speech-recognition/lib/RecognitionManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/react-speech-recognition/lib/RecognitionManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\n\nvar _isAndroid = _interopRequireDefault(__webpack_require__(/*! ./isAndroid */ \"(ssr)/./node_modules/react-speech-recognition/lib/isAndroid.js\"));\n\nvar _utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-speech-recognition/lib/utils.js\");\n\nvar _NativeSpeechRecognition = __webpack_require__(/*! ./NativeSpeechRecognition */ \"(ssr)/./node_modules/react-speech-recognition/lib/NativeSpeechRecognition.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nvar RecognitionManager = /*#__PURE__*/function () {\n  function RecognitionManager(SpeechRecognition) {\n    _classCallCheck(this, RecognitionManager);\n\n    this.recognition = null;\n    this.pauseAfterDisconnect = false;\n    this.interimTranscript = '';\n    this.finalTranscript = '';\n    this.listening = false;\n    this.isMicrophoneAvailable = true;\n    this.subscribers = {};\n\n    this.onStopListening = function () {};\n\n    this.previousResultWasFinalOnly = false;\n    this.resetTranscript = this.resetTranscript.bind(this);\n    this.startListening = this.startListening.bind(this);\n    this.stopListening = this.stopListening.bind(this);\n    this.abortListening = this.abortListening.bind(this);\n    this.setSpeechRecognition = this.setSpeechRecognition.bind(this);\n    this.disableRecognition = this.disableRecognition.bind(this);\n    this.setSpeechRecognition(SpeechRecognition);\n\n    if ((0, _isAndroid[\"default\"])()) {\n      this.updateFinalTranscript = (0, _utils.debounce)(this.updateFinalTranscript, 250, true);\n    }\n  }\n\n  _createClass(RecognitionManager, [{\n    key: \"setSpeechRecognition\",\n    value: function setSpeechRecognition(SpeechRecognition) {\n      var browserSupportsRecogniser = !!SpeechRecognition && ((0, _NativeSpeechRecognition.isNative)(SpeechRecognition) || (0, _utils.browserSupportsPolyfills)());\n\n      if (browserSupportsRecogniser) {\n        this.disableRecognition();\n        this.recognition = new SpeechRecognition();\n        this.recognition.continuous = false;\n        this.recognition.interimResults = true;\n        this.recognition.onresult = this.updateTranscript.bind(this);\n        this.recognition.onend = this.onRecognitionDisconnect.bind(this);\n        this.recognition.onerror = this.onError.bind(this);\n      }\n\n      this.emitBrowserSupportsSpeechRecognitionChange(browserSupportsRecogniser);\n    }\n  }, {\n    key: \"subscribe\",\n    value: function subscribe(id, callbacks) {\n      this.subscribers[id] = callbacks;\n    }\n  }, {\n    key: \"unsubscribe\",\n    value: function unsubscribe(id) {\n      delete this.subscribers[id];\n    }\n  }, {\n    key: \"emitListeningChange\",\n    value: function emitListeningChange(listening) {\n      var _this = this;\n\n      this.listening = listening;\n      Object.keys(this.subscribers).forEach(function (id) {\n        var onListeningChange = _this.subscribers[id].onListeningChange;\n        onListeningChange(listening);\n      });\n    }\n  }, {\n    key: \"emitMicrophoneAvailabilityChange\",\n    value: function emitMicrophoneAvailabilityChange(isMicrophoneAvailable) {\n      var _this2 = this;\n\n      this.isMicrophoneAvailable = isMicrophoneAvailable;\n      Object.keys(this.subscribers).forEach(function (id) {\n        var onMicrophoneAvailabilityChange = _this2.subscribers[id].onMicrophoneAvailabilityChange;\n        onMicrophoneAvailabilityChange(isMicrophoneAvailable);\n      });\n    }\n  }, {\n    key: \"emitTranscriptChange\",\n    value: function emitTranscriptChange(interimTranscript, finalTranscript) {\n      var _this3 = this;\n\n      Object.keys(this.subscribers).forEach(function (id) {\n        var onTranscriptChange = _this3.subscribers[id].onTranscriptChange;\n        onTranscriptChange(interimTranscript, finalTranscript);\n      });\n    }\n  }, {\n    key: \"emitClearTranscript\",\n    value: function emitClearTranscript() {\n      var _this4 = this;\n\n      Object.keys(this.subscribers).forEach(function (id) {\n        var onClearTranscript = _this4.subscribers[id].onClearTranscript;\n        onClearTranscript();\n      });\n    }\n  }, {\n    key: \"emitBrowserSupportsSpeechRecognitionChange\",\n    value: function emitBrowserSupportsSpeechRecognitionChange(browserSupportsSpeechRecognitionChange) {\n      var _this5 = this;\n\n      Object.keys(this.subscribers).forEach(function (id) {\n        var _this5$subscribers$id = _this5.subscribers[id],\n            onBrowserSupportsSpeechRecognitionChange = _this5$subscribers$id.onBrowserSupportsSpeechRecognitionChange,\n            onBrowserSupportsContinuousListeningChange = _this5$subscribers$id.onBrowserSupportsContinuousListeningChange;\n        onBrowserSupportsSpeechRecognitionChange(browserSupportsSpeechRecognitionChange);\n        onBrowserSupportsContinuousListeningChange(browserSupportsSpeechRecognitionChange);\n      });\n    }\n  }, {\n    key: \"disconnect\",\n    value: function disconnect(disconnectType) {\n      if (this.recognition && this.listening) {\n        switch (disconnectType) {\n          case 'ABORT':\n            this.pauseAfterDisconnect = true;\n            this.abort();\n            break;\n\n          case 'RESET':\n            this.pauseAfterDisconnect = false;\n            this.abort();\n            break;\n\n          case 'STOP':\n          default:\n            this.pauseAfterDisconnect = true;\n            this.stop();\n        }\n      }\n    }\n  }, {\n    key: \"disableRecognition\",\n    value: function disableRecognition() {\n      if (this.recognition) {\n        this.recognition.onresult = function () {};\n\n        this.recognition.onend = function () {};\n\n        this.recognition.onerror = function () {};\n\n        if (this.listening) {\n          this.stopListening();\n        }\n      }\n    }\n  }, {\n    key: \"onError\",\n    value: function onError(event) {\n      if (event && event.error && event.error === 'not-allowed') {\n        this.emitMicrophoneAvailabilityChange(false);\n        this.disableRecognition();\n      }\n    }\n  }, {\n    key: \"onRecognitionDisconnect\",\n    value: function onRecognitionDisconnect() {\n      this.onStopListening();\n      this.listening = false;\n\n      if (this.pauseAfterDisconnect) {\n        this.emitListeningChange(false);\n      } else if (this.recognition) {\n        if (this.recognition.continuous) {\n          this.startListening({\n            continuous: this.recognition.continuous\n          });\n        } else {\n          this.emitListeningChange(false);\n        }\n      }\n\n      this.pauseAfterDisconnect = false;\n    }\n  }, {\n    key: \"updateTranscript\",\n    value: function updateTranscript(_ref) {\n      var results = _ref.results,\n          resultIndex = _ref.resultIndex;\n      var currentIndex = resultIndex === undefined ? results.length - 1 : resultIndex;\n      this.interimTranscript = '';\n      this.finalTranscript = '';\n\n      for (var i = currentIndex; i < results.length; ++i) {\n        if (results[i].isFinal && (!(0, _isAndroid[\"default\"])() || results[i][0].confidence > 0)) {\n          this.updateFinalTranscript(results[i][0].transcript);\n        } else {\n          this.interimTranscript = (0, _utils.concatTranscripts)(this.interimTranscript, results[i][0].transcript);\n        }\n      }\n\n      var isDuplicateResult = false;\n\n      if (this.interimTranscript === '' && this.finalTranscript !== '') {\n        if (this.previousResultWasFinalOnly) {\n          isDuplicateResult = true;\n        }\n\n        this.previousResultWasFinalOnly = true;\n      } else {\n        this.previousResultWasFinalOnly = false;\n      }\n\n      if (!isDuplicateResult) {\n        this.emitTranscriptChange(this.interimTranscript, this.finalTranscript);\n      }\n    }\n  }, {\n    key: \"updateFinalTranscript\",\n    value: function updateFinalTranscript(newFinalTranscript) {\n      this.finalTranscript = (0, _utils.concatTranscripts)(this.finalTranscript, newFinalTranscript);\n    }\n  }, {\n    key: \"resetTranscript\",\n    value: function resetTranscript() {\n      this.disconnect('RESET');\n    }\n  }, {\n    key: \"startListening\",\n    value: function () {\n      var _startListening = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee() {\n        var _ref2,\n            _ref2$continuous,\n            continuous,\n            language,\n            isContinuousChanged,\n            isLanguageChanged,\n            _args = arguments;\n\n        return regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                _ref2 = _args.length > 0 && _args[0] !== undefined ? _args[0] : {}, _ref2$continuous = _ref2.continuous, continuous = _ref2$continuous === void 0 ? false : _ref2$continuous, language = _ref2.language;\n\n                if (this.recognition) {\n                  _context.next = 3;\n                  break;\n                }\n\n                return _context.abrupt(\"return\");\n\n              case 3:\n                isContinuousChanged = continuous !== this.recognition.continuous;\n                isLanguageChanged = language && language !== this.recognition.lang;\n\n                if (!(isContinuousChanged || isLanguageChanged)) {\n                  _context.next = 11;\n                  break;\n                }\n\n                if (!this.listening) {\n                  _context.next = 9;\n                  break;\n                }\n\n                _context.next = 9;\n                return this.stopListening();\n\n              case 9:\n                this.recognition.continuous = isContinuousChanged ? continuous : this.recognition.continuous;\n                this.recognition.lang = isLanguageChanged ? language : this.recognition.lang;\n\n              case 11:\n                if (this.listening) {\n                  _context.next = 22;\n                  break;\n                }\n\n                if (!this.recognition.continuous) {\n                  this.resetTranscript();\n                  this.emitClearTranscript();\n                }\n\n                _context.prev = 13;\n                _context.next = 16;\n                return this.start();\n\n              case 16:\n                this.emitListeningChange(true);\n                _context.next = 22;\n                break;\n\n              case 19:\n                _context.prev = 19;\n                _context.t0 = _context[\"catch\"](13);\n\n                // DOMExceptions indicate a redundant microphone start - safe to swallow\n                if (!(_context.t0 instanceof DOMException)) {\n                  this.emitMicrophoneAvailabilityChange(false);\n                }\n\n              case 22:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this, [[13, 19]]);\n      }));\n\n      function startListening() {\n        return _startListening.apply(this, arguments);\n      }\n\n      return startListening;\n    }()\n  }, {\n    key: \"abortListening\",\n    value: function () {\n      var _abortListening = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee2() {\n        var _this6 = this;\n\n        return regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                this.disconnect('ABORT');\n                this.emitListeningChange(false);\n                _context2.next = 4;\n                return new Promise(function (resolve) {\n                  _this6.onStopListening = resolve;\n                });\n\n              case 4:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this);\n      }));\n\n      function abortListening() {\n        return _abortListening.apply(this, arguments);\n      }\n\n      return abortListening;\n    }()\n  }, {\n    key: \"stopListening\",\n    value: function () {\n      var _stopListening = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee3() {\n        var _this7 = this;\n\n        return regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                this.disconnect('STOP');\n                this.emitListeningChange(false);\n                _context3.next = 4;\n                return new Promise(function (resolve) {\n                  _this7.onStopListening = resolve;\n                });\n\n              case 4:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this);\n      }));\n\n      function stopListening() {\n        return _stopListening.apply(this, arguments);\n      }\n\n      return stopListening;\n    }()\n  }, {\n    key: \"getRecognition\",\n    value: function getRecognition() {\n      return this.recognition;\n    }\n  }, {\n    key: \"start\",\n    value: function () {\n      var _start = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee4() {\n        return regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                if (!(this.recognition && !this.listening)) {\n                  _context4.next = 4;\n                  break;\n                }\n\n                _context4.next = 3;\n                return this.recognition.start();\n\n              case 3:\n                this.listening = true;\n\n              case 4:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this);\n      }));\n\n      function start() {\n        return _start.apply(this, arguments);\n      }\n\n      return start;\n    }()\n  }, {\n    key: \"stop\",\n    value: function stop() {\n      if (this.recognition && this.listening) {\n        this.recognition.stop();\n        this.listening = false;\n      }\n    }\n  }, {\n    key: \"abort\",\n    value: function abort() {\n      if (this.recognition && this.listening) {\n        this.recognition.abort();\n        this.listening = false;\n      }\n    }\n  }]);\n\n  return RecognitionManager;\n}();\n\nexports[\"default\"] = RecognitionManager;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-speech-recognition/lib/RecognitionManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-speech-recognition/lib/SpeechRecognition.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-speech-recognition/lib/SpeechRecognition.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = exports.useSpeechRecognition = void 0;\n\nvar _react = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nvar _utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-speech-recognition/lib/utils.js\");\n\nvar _actions = __webpack_require__(/*! ./actions */ \"(ssr)/./node_modules/react-speech-recognition/lib/actions.js\");\n\nvar _reducers = __webpack_require__(/*! ./reducers */ \"(ssr)/./node_modules/react-speech-recognition/lib/reducers.js\");\n\nvar _RecognitionManager = _interopRequireDefault(__webpack_require__(/*! ./RecognitionManager */ \"(ssr)/./node_modules/react-speech-recognition/lib/RecognitionManager.js\"));\n\nvar _isAndroid = _interopRequireDefault(__webpack_require__(/*! ./isAndroid */ \"(ssr)/./node_modules/react-speech-recognition/lib/isAndroid.js\"));\n\nvar _NativeSpeechRecognition = _interopRequireDefault(__webpack_require__(/*! ./NativeSpeechRecognition */ \"(ssr)/./node_modules/react-speech-recognition/lib/NativeSpeechRecognition.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return; var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nvar _browserSupportsSpeechRecognition = !!_NativeSpeechRecognition[\"default\"];\n\nvar _browserSupportsContinuousListening = _browserSupportsSpeechRecognition && !(0, _isAndroid[\"default\"])();\n\nvar recognitionManager;\n\nvar useSpeechRecognition = function useSpeechRecognition() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n      _ref$transcribing = _ref.transcribing,\n      transcribing = _ref$transcribing === void 0 ? true : _ref$transcribing,\n      _ref$clearTranscriptO = _ref.clearTranscriptOnListen,\n      clearTranscriptOnListen = _ref$clearTranscriptO === void 0 ? true : _ref$clearTranscriptO,\n      _ref$commands = _ref.commands,\n      commands = _ref$commands === void 0 ? [] : _ref$commands;\n\n  var _useState = (0, _react.useState)(SpeechRecognition.getRecognitionManager()),\n      _useState2 = _slicedToArray(_useState, 1),\n      recognitionManager = _useState2[0];\n\n  var _useState3 = (0, _react.useState)(_browserSupportsSpeechRecognition),\n      _useState4 = _slicedToArray(_useState3, 2),\n      browserSupportsSpeechRecognition = _useState4[0],\n      setBrowserSupportsSpeechRecognition = _useState4[1];\n\n  var _useState5 = (0, _react.useState)(_browserSupportsContinuousListening),\n      _useState6 = _slicedToArray(_useState5, 2),\n      browserSupportsContinuousListening = _useState6[0],\n      setBrowserSupportsContinuousListening = _useState6[1];\n\n  var _useReducer = (0, _react.useReducer)(_reducers.transcriptReducer, {\n    interimTranscript: recognitionManager.interimTranscript,\n    finalTranscript: ''\n  }),\n      _useReducer2 = _slicedToArray(_useReducer, 2),\n      _useReducer2$ = _useReducer2[0],\n      interimTranscript = _useReducer2$.interimTranscript,\n      finalTranscript = _useReducer2$.finalTranscript,\n      dispatch = _useReducer2[1];\n\n  var _useState7 = (0, _react.useState)(recognitionManager.listening),\n      _useState8 = _slicedToArray(_useState7, 2),\n      listening = _useState8[0],\n      setListening = _useState8[1];\n\n  var _useState9 = (0, _react.useState)(recognitionManager.isMicrophoneAvailable),\n      _useState10 = _slicedToArray(_useState9, 2),\n      isMicrophoneAvailable = _useState10[0],\n      setMicrophoneAvailable = _useState10[1];\n\n  var commandsRef = (0, _react.useRef)(commands);\n  commandsRef.current = commands;\n\n  var dispatchClearTranscript = function dispatchClearTranscript() {\n    dispatch((0, _actions.clearTranscript)());\n  };\n\n  var resetTranscript = (0, _react.useCallback)(function () {\n    recognitionManager.resetTranscript();\n    dispatchClearTranscript();\n  }, [recognitionManager]);\n\n  var testFuzzyMatch = function testFuzzyMatch(command, input, fuzzyMatchingThreshold) {\n    var commandToString = _typeof(command) === 'object' ? command.toString() : command;\n    var commandWithoutSpecials = commandToString.replace(/[&/\\\\#,+()!$~%.'\":*?<>{}]/g, '').replace(/  +/g, ' ').trim();\n    var howSimilar = (0, _utils.compareTwoStringsUsingDiceCoefficient)(commandWithoutSpecials, input);\n\n    if (howSimilar >= fuzzyMatchingThreshold) {\n      return {\n        command: command,\n        commandWithoutSpecials: commandWithoutSpecials,\n        howSimilar: howSimilar,\n        isFuzzyMatch: true\n      };\n    }\n\n    return null;\n  };\n\n  var testMatch = function testMatch(command, input) {\n    var pattern = (0, _utils.commandToRegExp)(command);\n    var result = pattern.exec(input);\n\n    if (result) {\n      return {\n        command: command,\n        parameters: result.slice(1)\n      };\n    }\n\n    return null;\n  };\n\n  var matchCommands = (0, _react.useCallback)(function (newInterimTranscript, newFinalTranscript) {\n    commandsRef.current.forEach(function (_ref2) {\n      var command = _ref2.command,\n          callback = _ref2.callback,\n          _ref2$matchInterim = _ref2.matchInterim,\n          matchInterim = _ref2$matchInterim === void 0 ? false : _ref2$matchInterim,\n          _ref2$isFuzzyMatch = _ref2.isFuzzyMatch,\n          isFuzzyMatch = _ref2$isFuzzyMatch === void 0 ? false : _ref2$isFuzzyMatch,\n          _ref2$fuzzyMatchingTh = _ref2.fuzzyMatchingThreshold,\n          fuzzyMatchingThreshold = _ref2$fuzzyMatchingTh === void 0 ? 0.8 : _ref2$fuzzyMatchingTh,\n          _ref2$bestMatchOnly = _ref2.bestMatchOnly,\n          bestMatchOnly = _ref2$bestMatchOnly === void 0 ? false : _ref2$bestMatchOnly;\n      var input = !newFinalTranscript && matchInterim ? newInterimTranscript.trim() : newFinalTranscript.trim();\n      var subcommands = Array.isArray(command) ? command : [command];\n      var results = subcommands.map(function (subcommand) {\n        if (isFuzzyMatch) {\n          return testFuzzyMatch(subcommand, input, fuzzyMatchingThreshold);\n        }\n\n        return testMatch(subcommand, input);\n      }).filter(function (x) {\n        return x;\n      });\n\n      if (isFuzzyMatch && bestMatchOnly && results.length >= 2) {\n        results.sort(function (a, b) {\n          return b.howSimilar - a.howSimilar;\n        });\n        var _results$ = results[0],\n            _command = _results$.command,\n            commandWithoutSpecials = _results$.commandWithoutSpecials,\n            howSimilar = _results$.howSimilar;\n        callback(commandWithoutSpecials, input, howSimilar, {\n          command: _command,\n          resetTranscript: resetTranscript\n        });\n      } else {\n        results.forEach(function (result) {\n          if (result.isFuzzyMatch) {\n            var _command2 = result.command,\n                _commandWithoutSpecials = result.commandWithoutSpecials,\n                _howSimilar = result.howSimilar;\n            callback(_commandWithoutSpecials, input, _howSimilar, {\n              command: _command2,\n              resetTranscript: resetTranscript\n            });\n          } else {\n            var _command3 = result.command,\n                parameters = result.parameters;\n            callback.apply(void 0, _toConsumableArray(parameters).concat([{\n              command: _command3,\n              resetTranscript: resetTranscript\n            }]));\n          }\n        });\n      }\n    });\n  }, [resetTranscript]);\n  var handleTranscriptChange = (0, _react.useCallback)(function (newInterimTranscript, newFinalTranscript) {\n    if (transcribing) {\n      dispatch((0, _actions.appendTranscript)(newInterimTranscript, newFinalTranscript));\n    }\n\n    matchCommands(newInterimTranscript, newFinalTranscript);\n  }, [matchCommands, transcribing]);\n  var handleClearTranscript = (0, _react.useCallback)(function () {\n    if (clearTranscriptOnListen) {\n      dispatchClearTranscript();\n    }\n  }, [clearTranscriptOnListen]);\n  (0, _react.useEffect)(function () {\n    var id = SpeechRecognition.counter;\n    SpeechRecognition.counter += 1;\n    var callbacks = {\n      onListeningChange: setListening,\n      onMicrophoneAvailabilityChange: setMicrophoneAvailable,\n      onTranscriptChange: handleTranscriptChange,\n      onClearTranscript: handleClearTranscript,\n      onBrowserSupportsSpeechRecognitionChange: setBrowserSupportsSpeechRecognition,\n      onBrowserSupportsContinuousListeningChange: setBrowserSupportsContinuousListening\n    };\n    recognitionManager.subscribe(id, callbacks);\n    return function () {\n      recognitionManager.unsubscribe(id);\n    };\n  }, [transcribing, clearTranscriptOnListen, recognitionManager, handleTranscriptChange, handleClearTranscript]);\n  var transcript = (0, _utils.concatTranscripts)(finalTranscript, interimTranscript);\n  return {\n    transcript: transcript,\n    interimTranscript: interimTranscript,\n    finalTranscript: finalTranscript,\n    listening: listening,\n    isMicrophoneAvailable: isMicrophoneAvailable,\n    resetTranscript: resetTranscript,\n    browserSupportsSpeechRecognition: browserSupportsSpeechRecognition,\n    browserSupportsContinuousListening: browserSupportsContinuousListening\n  };\n};\n\nexports.useSpeechRecognition = useSpeechRecognition;\nvar SpeechRecognition = {\n  counter: 0,\n  applyPolyfill: function applyPolyfill(PolyfillSpeechRecognition) {\n    if (recognitionManager) {\n      recognitionManager.setSpeechRecognition(PolyfillSpeechRecognition);\n    } else {\n      recognitionManager = new _RecognitionManager[\"default\"](PolyfillSpeechRecognition);\n    }\n\n    var browserSupportsPolyfill = !!PolyfillSpeechRecognition && (0, _utils.browserSupportsPolyfills)();\n    _browserSupportsSpeechRecognition = browserSupportsPolyfill;\n    _browserSupportsContinuousListening = browserSupportsPolyfill;\n  },\n  removePolyfill: function removePolyfill() {\n    if (recognitionManager) {\n      recognitionManager.setSpeechRecognition(_NativeSpeechRecognition[\"default\"]);\n    } else {\n      recognitionManager = new _RecognitionManager[\"default\"](_NativeSpeechRecognition[\"default\"]);\n    }\n\n    _browserSupportsSpeechRecognition = !!_NativeSpeechRecognition[\"default\"];\n    _browserSupportsContinuousListening = _browserSupportsSpeechRecognition && !(0, _isAndroid[\"default\"])();\n  },\n  getRecognitionManager: function getRecognitionManager() {\n    if (!recognitionManager) {\n      recognitionManager = new _RecognitionManager[\"default\"](_NativeSpeechRecognition[\"default\"]);\n    }\n\n    return recognitionManager;\n  },\n  getRecognition: function getRecognition() {\n    var recognitionManager = SpeechRecognition.getRecognitionManager();\n    return recognitionManager.getRecognition();\n  },\n  startListening: function () {\n    var _startListening = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee() {\n      var _ref3,\n          continuous,\n          language,\n          recognitionManager,\n          _args = arguments;\n\n      return regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _ref3 = _args.length > 0 && _args[0] !== undefined ? _args[0] : {}, continuous = _ref3.continuous, language = _ref3.language;\n              recognitionManager = SpeechRecognition.getRecognitionManager();\n              _context.next = 4;\n              return recognitionManager.startListening({\n                continuous: continuous,\n                language: language\n              });\n\n            case 4:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee);\n    }));\n\n    function startListening() {\n      return _startListening.apply(this, arguments);\n    }\n\n    return startListening;\n  }(),\n  stopListening: function () {\n    var _stopListening = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee2() {\n      var recognitionManager;\n      return regeneratorRuntime.wrap(function _callee2$(_context2) {\n        while (1) {\n          switch (_context2.prev = _context2.next) {\n            case 0:\n              recognitionManager = SpeechRecognition.getRecognitionManager();\n              _context2.next = 3;\n              return recognitionManager.stopListening();\n\n            case 3:\n            case \"end\":\n              return _context2.stop();\n          }\n        }\n      }, _callee2);\n    }));\n\n    function stopListening() {\n      return _stopListening.apply(this, arguments);\n    }\n\n    return stopListening;\n  }(),\n  abortListening: function () {\n    var _abortListening = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee3() {\n      var recognitionManager;\n      return regeneratorRuntime.wrap(function _callee3$(_context3) {\n        while (1) {\n          switch (_context3.prev = _context3.next) {\n            case 0:\n              recognitionManager = SpeechRecognition.getRecognitionManager();\n              _context3.next = 3;\n              return recognitionManager.abortListening();\n\n            case 3:\n            case \"end\":\n              return _context3.stop();\n          }\n        }\n      }, _callee3);\n    }));\n\n    function abortListening() {\n      return _abortListening.apply(this, arguments);\n    }\n\n    return abortListening;\n  }(),\n  browserSupportsSpeechRecognition: function browserSupportsSpeechRecognition() {\n    return _browserSupportsSpeechRecognition;\n  },\n  browserSupportsContinuousListening: function browserSupportsContinuousListening() {\n    return _browserSupportsContinuousListening;\n  }\n};\nvar _default = SpeechRecognition;\nexports[\"default\"] = _default;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-speech-recognition/lib/SpeechRecognition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-speech-recognition/lib/actions.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-speech-recognition/lib/actions.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.appendTranscript = exports.clearTranscript = void 0;\n\nvar _constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/react-speech-recognition/lib/constants.js\");\n\nvar clearTranscript = function clearTranscript() {\n  return {\n    type: _constants.CLEAR_TRANSCRIPT\n  };\n};\n\nexports.clearTranscript = clearTranscript;\n\nvar appendTranscript = function appendTranscript(interimTranscript, finalTranscript) {\n  return {\n    type: _constants.APPEND_TRANSCRIPT,\n    payload: {\n      interimTranscript: interimTranscript,\n      finalTranscript: finalTranscript\n    }\n  };\n};\n\nexports.appendTranscript = appendTranscript;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtc3BlZWNoLXJlY29nbml0aW9uL2xpYi9hY3Rpb25zLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLHdCQUF3QixHQUFHLHVCQUF1Qjs7QUFFbEQsaUJBQWlCLG1CQUFPLENBQUMsbUZBQWE7O0FBRXRDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsdUJBQXVCOztBQUV2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsd0JBQXdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJlYW1zdGFydHIvLi9ub2RlX21vZHVsZXMvcmVhY3Qtc3BlZWNoLXJlY29nbml0aW9uL2xpYi9hY3Rpb25zLmpzPzRjMjAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLmFwcGVuZFRyYW5zY3JpcHQgPSBleHBvcnRzLmNsZWFyVHJhbnNjcmlwdCA9IHZvaWQgMDtcblxudmFyIF9jb25zdGFudHMgPSByZXF1aXJlKFwiLi9jb25zdGFudHNcIik7XG5cbnZhciBjbGVhclRyYW5zY3JpcHQgPSBmdW5jdGlvbiBjbGVhclRyYW5zY3JpcHQoKSB7XG4gIHJldHVybiB7XG4gICAgdHlwZTogX2NvbnN0YW50cy5DTEVBUl9UUkFOU0NSSVBUXG4gIH07XG59O1xuXG5leHBvcnRzLmNsZWFyVHJhbnNjcmlwdCA9IGNsZWFyVHJhbnNjcmlwdDtcblxudmFyIGFwcGVuZFRyYW5zY3JpcHQgPSBmdW5jdGlvbiBhcHBlbmRUcmFuc2NyaXB0KGludGVyaW1UcmFuc2NyaXB0LCBmaW5hbFRyYW5zY3JpcHQpIHtcbiAgcmV0dXJuIHtcbiAgICB0eXBlOiBfY29uc3RhbnRzLkFQUEVORF9UUkFOU0NSSVBULFxuICAgIHBheWxvYWQ6IHtcbiAgICAgIGludGVyaW1UcmFuc2NyaXB0OiBpbnRlcmltVHJhbnNjcmlwdCxcbiAgICAgIGZpbmFsVHJhbnNjcmlwdDogZmluYWxUcmFuc2NyaXB0XG4gICAgfVxuICB9O1xufTtcblxuZXhwb3J0cy5hcHBlbmRUcmFuc2NyaXB0ID0gYXBwZW5kVHJhbnNjcmlwdDsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-speech-recognition/lib/actions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-speech-recognition/lib/constants.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-speech-recognition/lib/constants.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.APPEND_TRANSCRIPT = exports.CLEAR_TRANSCRIPT = void 0;\nvar CLEAR_TRANSCRIPT = 'CLEAR_TRANSCRIPT';\nexports.CLEAR_TRANSCRIPT = CLEAR_TRANSCRIPT;\nvar APPEND_TRANSCRIPT = 'APPEND_TRANSCRIPT';\nexports.APPEND_TRANSCRIPT = APPEND_TRANSCRIPT;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtc3BlZWNoLXJlY29nbml0aW9uL2xpYi9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YseUJBQXlCLEdBQUcsd0JBQXdCO0FBQ3BEO0FBQ0Esd0JBQXdCO0FBQ3hCO0FBQ0EseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJlYW1zdGFydHIvLi9ub2RlX21vZHVsZXMvcmVhY3Qtc3BlZWNoLXJlY29nbml0aW9uL2xpYi9jb25zdGFudHMuanM/NWVmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuQVBQRU5EX1RSQU5TQ1JJUFQgPSBleHBvcnRzLkNMRUFSX1RSQU5TQ1JJUFQgPSB2b2lkIDA7XG52YXIgQ0xFQVJfVFJBTlNDUklQVCA9ICdDTEVBUl9UUkFOU0NSSVBUJztcbmV4cG9ydHMuQ0xFQVJfVFJBTlNDUklQVCA9IENMRUFSX1RSQU5TQ1JJUFQ7XG52YXIgQVBQRU5EX1RSQU5TQ1JJUFQgPSAnQVBQRU5EX1RSQU5TQ1JJUFQnO1xuZXhwb3J0cy5BUFBFTkRfVFJBTlNDUklQVCA9IEFQUEVORF9UUkFOU0NSSVBUOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-speech-recognition/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-speech-recognition/lib/index.js":
/*!************************************************************!*\
  !*** ./node_modules/react-speech-recognition/lib/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"useSpeechRecognition\", ({\n  enumerable: true,\n  get: function get() {\n    return _SpeechRecognition.useSpeechRecognition;\n  }\n}));\nexports[\"default\"] = void 0;\n\nvar _SpeechRecognition = _interopRequireWildcard(__webpack_require__(/*! ./SpeechRecognition */ \"(ssr)/./node_modules/react-speech-recognition/lib/SpeechRecognition.js\"));\n\nfunction _getRequireWildcardCache() { if (typeof WeakMap !== \"function\") return null; var cache = new WeakMap(); _getRequireWildcardCache = function _getRequireWildcardCache() { return cache; }; return cache; }\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nvar _default = _SpeechRecognition[\"default\"];\nexports[\"default\"] = _default;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-speech-recognition/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-speech-recognition/lib/isAndroid.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-speech-recognition/lib/isAndroid.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\n\nvar _default = function _default() {\n  return /(android)/i.test(typeof navigator !== 'undefined' ? navigator.userAgent : '');\n};\n\nexports[\"default\"] = _default;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtc3BlZWNoLXJlY29nbml0aW9uL2xpYi9pc0FuZHJvaWQuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysa0JBQWtCOztBQUVsQjtBQUNBO0FBQ0E7O0FBRUEsa0JBQWtCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJlYW1zdGFydHIvLi9ub2RlX21vZHVsZXMvcmVhY3Qtc3BlZWNoLXJlY29nbml0aW9uL2xpYi9pc0FuZHJvaWQuanM/ZGIzYSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHNbXCJkZWZhdWx0XCJdID0gdm9pZCAwO1xuXG52YXIgX2RlZmF1bHQgPSBmdW5jdGlvbiBfZGVmYXVsdCgpIHtcbiAgcmV0dXJuIC8oYW5kcm9pZCkvaS50ZXN0KHR5cGVvZiBuYXZpZ2F0b3IgIT09ICd1bmRlZmluZWQnID8gbmF2aWdhdG9yLnVzZXJBZ2VudCA6ICcnKTtcbn07XG5cbmV4cG9ydHNbXCJkZWZhdWx0XCJdID0gX2RlZmF1bHQ7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-speech-recognition/lib/isAndroid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-speech-recognition/lib/reducers.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-speech-recognition/lib/reducers.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.transcriptReducer = void 0;\n\nvar _constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/react-speech-recognition/lib/constants.js\");\n\nvar _utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/react-speech-recognition/lib/utils.js\");\n\nvar transcriptReducer = function transcriptReducer(state, action) {\n  switch (action.type) {\n    case _constants.CLEAR_TRANSCRIPT:\n      return {\n        interimTranscript: '',\n        finalTranscript: ''\n      };\n\n    case _constants.APPEND_TRANSCRIPT:\n      return {\n        interimTranscript: action.payload.interimTranscript,\n        finalTranscript: (0, _utils.concatTranscripts)(state.finalTranscript, action.payload.finalTranscript)\n      };\n\n    default:\n      throw new Error();\n  }\n};\n\nexports.transcriptReducer = transcriptReducer;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtc3BlZWNoLXJlY29nbml0aW9uL2xpYi9yZWR1Y2Vycy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRix5QkFBeUI7O0FBRXpCLGlCQUFpQixtQkFBTyxDQUFDLG1GQUFhOztBQUV0QyxhQUFhLG1CQUFPLENBQUMsMkVBQVM7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJlYW1zdGFydHIvLi9ub2RlX21vZHVsZXMvcmVhY3Qtc3BlZWNoLXJlY29nbml0aW9uL2xpYi9yZWR1Y2Vycy5qcz9kNGUzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pO1xuZXhwb3J0cy50cmFuc2NyaXB0UmVkdWNlciA9IHZvaWQgMDtcblxudmFyIF9jb25zdGFudHMgPSByZXF1aXJlKFwiLi9jb25zdGFudHNcIik7XG5cbnZhciBfdXRpbHMgPSByZXF1aXJlKFwiLi91dGlsc1wiKTtcblxudmFyIHRyYW5zY3JpcHRSZWR1Y2VyID0gZnVuY3Rpb24gdHJhbnNjcmlwdFJlZHVjZXIoc3RhdGUsIGFjdGlvbikge1xuICBzd2l0Y2ggKGFjdGlvbi50eXBlKSB7XG4gICAgY2FzZSBfY29uc3RhbnRzLkNMRUFSX1RSQU5TQ1JJUFQ6XG4gICAgICByZXR1cm4ge1xuICAgICAgICBpbnRlcmltVHJhbnNjcmlwdDogJycsXG4gICAgICAgIGZpbmFsVHJhbnNjcmlwdDogJydcbiAgICAgIH07XG5cbiAgICBjYXNlIF9jb25zdGFudHMuQVBQRU5EX1RSQU5TQ1JJUFQ6XG4gICAgICByZXR1cm4ge1xuICAgICAgICBpbnRlcmltVHJhbnNjcmlwdDogYWN0aW9uLnBheWxvYWQuaW50ZXJpbVRyYW5zY3JpcHQsXG4gICAgICAgIGZpbmFsVHJhbnNjcmlwdDogKDAsIF91dGlscy5jb25jYXRUcmFuc2NyaXB0cykoc3RhdGUuZmluYWxUcmFuc2NyaXB0LCBhY3Rpb24ucGF5bG9hZC5maW5hbFRyYW5zY3JpcHQpXG4gICAgICB9O1xuXG4gICAgZGVmYXVsdDpcbiAgICAgIHRocm93IG5ldyBFcnJvcigpO1xuICB9XG59O1xuXG5leHBvcnRzLnRyYW5zY3JpcHRSZWR1Y2VyID0gdHJhbnNjcmlwdFJlZHVjZXI7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-speech-recognition/lib/reducers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-speech-recognition/lib/utils.js":
/*!************************************************************!*\
  !*** ./node_modules/react-speech-recognition/lib/utils.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.browserSupportsPolyfills = exports.compareTwoStringsUsingDiceCoefficient = exports.commandToRegExp = exports.concatTranscripts = exports.debounce = void 0;\n\nvar debounce = function debounce(func, wait, immediate) {\n  var timeout;\n  return function () {\n    var context = this;\n    var args = arguments;\n\n    var later = function later() {\n      timeout = null;\n      if (!immediate) func.apply(context, args);\n    };\n\n    var callNow = immediate && !timeout;\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n    if (callNow) func.apply(context, args);\n  };\n};\n\nexports.debounce = debounce;\n\nvar concatTranscripts = function concatTranscripts() {\n  for (var _len = arguments.length, transcriptParts = new Array(_len), _key = 0; _key < _len; _key++) {\n    transcriptParts[_key] = arguments[_key];\n  }\n\n  return transcriptParts.map(function (t) {\n    return t.trim();\n  }).join(' ').trim();\n}; // The command matching code is a modified version of Backbone.Router by Jeremy Ashkenas, under the MIT license.\n\n\nexports.concatTranscripts = concatTranscripts;\nvar optionalParam = /\\s*\\((.*?)\\)\\s*/g;\nvar optionalRegex = /(\\(\\?:[^)]+\\))\\?/g;\nvar namedParam = /(\\(\\?)?:\\w+/g;\nvar splatParam = /\\*/g;\nvar escapeRegExp = /[-{}[\\]+?.,\\\\^$|#]/g;\n\nvar commandToRegExp = function commandToRegExp(command) {\n  if (command instanceof RegExp) {\n    return new RegExp(command.source, 'i');\n  }\n\n  command = command.replace(escapeRegExp, '\\\\$&').replace(optionalParam, '(?:$1)?').replace(namedParam, function (match, optional) {\n    return optional ? match : '([^\\\\s]+)';\n  }).replace(splatParam, '(.*?)').replace(optionalRegex, '\\\\s*$1?\\\\s*');\n  return new RegExp('^' + command + '$', 'i');\n}; // this is from https://github.com/aceakash/string-similarity\n\n\nexports.commandToRegExp = commandToRegExp;\n\nvar compareTwoStringsUsingDiceCoefficient = function compareTwoStringsUsingDiceCoefficient(first, second) {\n  first = first.replace(/\\s+/g, '').toLowerCase();\n  second = second.replace(/\\s+/g, '').toLowerCase();\n  if (!first.length && !second.length) return 1; // if both are empty strings\n\n  if (!first.length || !second.length) return 0; // if only one is empty string\n\n  if (first === second) return 1; // identical\n\n  if (first.length === 1 && second.length === 1) return 0; // both are 1-letter strings\n\n  if (first.length < 2 || second.length < 2) return 0; // if either is a 1-letter string\n\n  var firstBigrams = new Map();\n\n  for (var i = 0; i < first.length - 1; i++) {\n    var bigram = first.substring(i, i + 2);\n    var count = firstBigrams.has(bigram) ? firstBigrams.get(bigram) + 1 : 1;\n    firstBigrams.set(bigram, count);\n  }\n\n  var intersectionSize = 0;\n\n  for (var _i = 0; _i < second.length - 1; _i++) {\n    var _bigram = second.substring(_i, _i + 2);\n\n    var _count = firstBigrams.has(_bigram) ? firstBigrams.get(_bigram) : 0;\n\n    if (_count > 0) {\n      firstBigrams.set(_bigram, _count - 1);\n      intersectionSize++;\n    }\n  }\n\n  return 2.0 * intersectionSize / (first.length + second.length - 2);\n};\n\nexports.compareTwoStringsUsingDiceCoefficient = compareTwoStringsUsingDiceCoefficient;\n\nvar browserSupportsPolyfills = function browserSupportsPolyfills() {\n  return typeof window !== 'undefined' && window.navigator !== undefined && window.navigator.mediaDevices !== undefined && window.navigator.mediaDevices.getUserMedia !== undefined && (window.AudioContext !== undefined || window.webkitAudioContext !== undefined);\n};\n\nexports.browserSupportsPolyfills = browserSupportsPolyfills;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-speech-recognition/lib/utils.js\n");

/***/ })

};
;