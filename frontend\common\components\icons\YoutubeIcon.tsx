import type { SVGProps } from "react";

export const YoutubeIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <path
      fill="#FF808C"
      d="M23 7.637a3.29 3.29 0 0 0-3.288-3.29H4.288A3.289 3.289 0 0 0 1 7.638v8.725a3.287 3.287 0 0 0 3.288 3.29h15.424A3.288 3.288 0 0 0 23 16.362V7.637Z"
    />
    <path
      fill="#FFBFC5"
      d="M4.288 4.348A3.289 3.289 0 0 0 1 7.638v8.724a3.27 3.27 0 0 0 1.218 2.537l14.55-14.551H4.288Z"
    />
    <path
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M23 7.637a3.29 3.29 0 0 0-3.288-3.29H4.288A3.289 3.289 0 0 0 1 7.638v8.725a3.287 3.287 0 0 0 3.288 3.29h15.424A3.288 3.288 0 0 0 23 16.362V7.637Z"
    />
    <path
      fill="#fff"
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M9.608 15.77V7.484l6.475 4.143-6.475 4.143Z"
    />
  </svg>
)
