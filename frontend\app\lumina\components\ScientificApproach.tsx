'use client'

import {
  motion, useInView,
} from 'framer-motion';
import { useRef } from 'react';
import {
  Coins,
  Cpu,
  MapPinHouse,
} from 'lucide-react';

const ScientificApproach = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, {
    once: true,
    amount: 0.2,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  const features = [
    {
      icon: <Cpu className="w-5 h-5 lg:w-8 lg:h-8 text-light-yellow" />,
      title: "Dragonfire Computation and Storage",
      description: "The DragonFire quantum computational system applies dimensionless mathematics and sacred geometry for the next generation of computation and data storage.",
    },
    {
      icon: <MapPinHouse className="w-5 h-5 lg:w-8 lg:h-8 text-light-yellow" />,
      title: "Fractionalized Real World Assets",
      description: "Lumina Casa’s vision of 55 interconnected Real World Sanctuaries for rejuvenation, joy and bliss - be part of a global community and 40% revenue generating real world assets.",
    },
    {
      icon: <Coins className="w-5 h-5 lg:w-8 lg:h-8 text-light-yellow" />,
      title: "Built on Base",
      description: "Built on Base (by Coinbase) : for onboarding the next billion conscious souls to Ethereum, and supporting the global conscious soul ecosystem.",
    },
  ];

  const results = [
    {
      percentage: "45%",
      description: "Reduction in Epigenetic Aging Biomarkers",
    },
    {
      percentage: "50,000+",
      description: "Global Conscious Community",
    },
    {
      percentage: "$11.5M+",
      description: "Value Created",
    },
  ];

  return (
    <section
      id="science"
      ref={sectionRef}
      className="py-4 lg:py-16 relative w-full container px-2 md:px-8 mx-auto !max-w-[1200px]"
    >
      <div className="max-w-[1200px] mx-auto">
        <div className="mb-8">
          <div className="w-min whitespace-nowrap bg-violets-are-blue/20 rounded-full px-4 py-1 mb-2 lg:mb-4 text-xs lg:text-sm text-light-yellow font-medium">
            Scientific Approach
          </div>
          <h2 className="text-xl md:text-2xl lg:text-3xl font-semibold text-white my-2">
            Quantum Geometry Meets Blockchain
          </h2>
          <div className="rounded-full h-1 w-20 bg-gradient-to-tr from-light-yellow to-yellow-600 mb-4"></div>
          <p className="text-neutral-300 mb-4 text-sm md:text-base max-w-3xl">
            Our breakthrough approach integrates quantum bio-resonance, sacred geometry and real world sanctuaries to extend the healthy human lifespan.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6 mb-6 lg:mb-12">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              initial="hidden"
              animate={isInView ? "visible" : "hidden"}
              className="bg-violets-are-blue/5 border border-violets-are-blue/15 rounded-3xl p-4 lg:p-6 transition-all duration-300"
            >
              <div className="mb-2 lg:mb-4">
                <div className="p-3 rounded-full bg-yellow-500/5 inline-block">
                  {feature.icon}
                </div>
              </div>
              <h3 className="text-lg lg:text-xl font-semibold text-white mb-1 lg:mb-3">
                {feature.title}
              </h3>
              <p className="text-neutral-300 text-sm">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className=""
        >
          <div className="mb-2 lg:mb-6">
            <h3 className="text-xl font-semibold text-white md:text-left text-center">
              The Story Today
            </h3>
            <div className="rounded-full h-1 w-20 bg-gradient-to-tr from-light-yellow to-yellow-600 mx-auto my-2 md:hidden"></div>
          </div>
          <div className="grid relative grid-cols-3 gap-6 md:bg-eerie-black md:border overflow-hidden border-light-yellow/5 rounded-3xl p-4 md:p-8">
            <div className="absolute md:block hidden pointer-events-none top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-gradient-to-tr from-light-yellow to-yellow-200 rotate-45 opacity-50 ease-in-out transition-all duration-200 w-full h-full blur-[120px]" />
            {results.map((result, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="text-center"
              >
                <p className={`text-xl md:text-3xl font-bold text-light-yellow mb-2`}>
                  {result.percentage}
                </p>
                <p className="text-neutral-300 text-xs md:text-sm">
                  {result.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ScientificApproach;
