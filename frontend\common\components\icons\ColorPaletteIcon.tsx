import type { SVGProps } from "react";

export const ColorPaletteIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <mask
        id="b"
        width={24}
        height={24}
        x={0}
        y={0}
        maskUnits="userSpaceOnUse"
        style={{
          maskType: "luminance",
        }}
      >
        <path fill="#fff" d="M0 0h24v24H0V0Z" />
      </mask>
      <g mask="url(#b)">
        <path
          fill="#66E1FF"
          d="M23 12a10.762 10.762 0 0 1-.823 4.16l-8.848-3.624c.144-.35.14-.743-.01-1.09l8.83-3.703a11.04 11.04 0 0 1 .85 4.257Z"
        />
        <path
          fill="#C2F3FF"
          d="M22.178 16.16a10.917 10.917 0 0 1-5.93 5.979l-3.693-8.82a1.42 1.42 0 0 0 .775-.783l8.848 3.625Z"
        />
        <path
          fill="#FF808C"
          d="M16.247 22.139a10.942 10.942 0 0 1-8.417.038l3.625-8.857c.171.076.357.117.546.*************-.039.554-.115l3.692 8.819Z"
        />
        <path
          fill="#FFBFC5"
          d="m11.456 13.32-3.625 8.857a11.039 11.039 0 0 1-5.978-5.93l8.828-3.692c.146.348.425.624.775.765Z"
        />
        <path
          fill="#78EB7B"
          d="M10.566 12c-.002.191.037.38.115.555l-8.829 3.692A11 11 0 0 1 1 12a10.81 10.81 0 0 1 .823-4.17l8.857 3.625a1.293 1.293 0 0 0-.114.545Z"
        />
        <path
          fill="#C9F7CA"
          d="M11.446 10.68c-.352.14-.63.42-.765.775L1.824 7.83a10.978 10.978 0 0 1 5.93-5.979l3.692 8.829Z"
        />
        <path
          fill="#FFEF5E"
          d="m16.162 1.823-3.616 8.857a1.375 1.375 0 0 0-1.1 0L7.754 1.85A11.033 11.033 0 0 1 12 1.001a10.759 10.759 0 0 1 4.16.822Z"
        />
        <path
          fill="#FFF9BF"
          d="m22.149 7.743-8.829 3.702a1.384 1.384 0 0 0-.775-.765l3.616-8.857h.01a11.022 11.022 0 0 1 5.978 5.92Z"
        />
        <path
          stroke="#191919"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M23 12a10.762 10.762 0 0 1-.823 4.16l-8.848-3.624c.144-.35.14-.743-.01-1.09l8.83-3.703a11.04 11.04 0 0 1 .85 4.257Z"
        />
        <path
          stroke="#191919"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M22.178 16.16a10.917 10.917 0 0 1-5.93 5.979l-3.693-8.82a1.42 1.42 0 0 0 .775-.783l8.848 3.625ZM16.247 22.139a10.942 10.942 0 0 1-8.417.038l3.625-8.857c.171.076.357.117.546.*************-.039.554-.115l3.692 8.819Z"
        />
        <path
          stroke="#191919"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M1.852 16.247a11.039 11.039 0 0 0 5.979 5.93l3.625-8.857a1.411 1.411 0 0 1-.775-.765m-8.829 3.692 8.829-3.692m-8.829 3.692A11 11 0 0 1 1 12a10.81 10.81 0 0 1 .823-4.17m8.858 4.725a1.33 1.33 0 0 1-.115-.555 1.293 1.293 0 0 1 .114-.545M1.823 7.83l8.857 3.625M1.823 7.83A10.98 10.98 0 0 1 7.754 1.85m2.926 9.604c.136-.354.414-.635.766-.775M7.754 1.85l3.692 8.829M7.754 1.85a11.033 11.033 0 0 1 4.247-.85 10.76 10.76 0 0 1 4.16.822l-3.615 8.857a1.376 1.376 0 0 0-1.1 0"
        />
        <path
          stroke="#191919"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="m22.149 7.743-8.829 3.702a1.384 1.384 0 0 0-.775-.765l3.616-8.857h.01a11.022 11.022 0 0 1 5.978 5.92Z"
        />
      </g>
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h24v24H0z" />
      </clipPath>
    </defs>
  </svg>
)
