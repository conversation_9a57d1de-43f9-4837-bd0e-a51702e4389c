"use client";
import {
  useState, useEffect,
} from "react";
import { cn } from "@/common/utils/helpers";
import {
  AnimatePresence, motion,
} from "framer-motion";

const CheckIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      fill="none"
    >
      <path
        stroke="#fff"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M10.814 2.074a1.52 1.52 0 0 1 2.372 0l1.41 1.755a1.519 1.519 0 0 0 1.35.558l2.237-.243a1.52 1.52 0 0 1 1.674 1.676l-.244 2.236a1.52 1.52 0 0 0 .56 1.35l1.753 1.409a1.521 1.521 0 0 1 0 2.372l-1.754 1.41a1.518 1.518 0 0 0-.56 1.349l.245 2.237a1.522 1.522 0 0 1-1.676 1.675l-2.237-.243a1.523 1.523 0 0 0-1.35.559l-1.408 1.752a1.518 1.518 0 0 1-2.372 0l-1.408-1.754a1.523 1.523 0 0 0-1.35-.56l-2.237.244a1.52 1.52 0 0 1-1.675-1.675l.243-2.237a1.518 1.518 0 0 0-.559-1.349l-1.754-1.408a1.52 1.52 0 0 1 0-2.372l1.754-1.409a1.52 1.52 0 0 0 .56-1.35L4.143 5.82a1.521 1.521 0 0 1 1.675-1.676l2.237.243a1.52 1.52 0 0 0 1.35-.558l1.408-1.755Z"
      />
      <path
        stroke="#fff"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m15.65 9.72-3.714 4.95a.7.7 0 0 1-1.054.076l-2.228-2.229"
      />
    </svg>
  );
};

const CheckFilled = ({ isCurrent }: { isCurrent: boolean }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      fill="none"
    >
      <path
        fill={isCurrent ? "#5606FF" : "#00000040"}
        d="M10.814 2.074a1.52 1.52 0 0 1 2.372 0l1.41 1.755a1.519 1.519 0 0 0 1.35.558l2.237-.243a1.52 1.52 0 0 1 1.674 1.676l-.244 2.236a1.52 1.52 0 0 0 .56 1.35l1.753 1.409a1.521 1.521 0 0 1 0 2.372l-1.754 1.41a1.518 1.518 0 0 0-.56 1.349l.245 2.237a1.522 1.522 0 0 1-1.676 1.675l-2.237-.243a1.523 1.523 0 0 0-1.35.559l-1.408 1.752a1.518 1.518 0 0 1-2.372 0l-1.408-1.754a1.523 1.523 0 0 0-1.35-.56l-2.237.244a1.52 1.52 0 0 1-1.675-1.675l.243-2.237a1.518 1.518 0 0 0-.559-1.349l-1.754-1.408a1.52 1.52 0 0 1 0-2.372l1.754-1.409a1.52 1.52 0 0 0 .56-1.35L4.143 5.82a1.521 1.521 0 0 1 1.675-1.676l2.237.243a1.52 1.52 0 0 0 1.35-.558l1.408-1.755Z"
      />
      <path
        fill={isCurrent ? "#7E5EF2" : "#00000040"}
        d="M4.58 19.42a1.521 1.521 0 0 1-.436-1.239l.243-2.237a1.518 1.518 0 0 0-.559-1.349l-1.754-1.408a1.52 1.52 0 0 1 0-2.372l1.754-1.409a1.52 1.52 0 0 0 .56-1.35L4.143 5.82a1.521 1.521 0 0 1 1.675-1.676l2.237.243a1.52 1.52 0 0 0 1.35-.558l1.408-1.755a1.52 1.52 0 0 1 2.372 0l1.41 1.755a1.519 1.519 0 0 0 1.35.558l2.237-.243a1.523 1.523 0 0 1 1.24.437L4.58 19.42Z"
      />
      <path
        stroke="#fff"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M10.814 2.074a1.52 1.52 0 0 1 2.372 0l1.41 1.755a1.519 1.519 0 0 0 1.35.558l2.237-.243a1.52 1.52 0 0 1 1.674 1.676l-.244 2.236a1.52 1.52 0 0 0 .56 1.35l1.753 1.409a1.521 1.521 0 0 1 0 2.372l-1.754 1.41a1.518 1.518 0 0 0-.56 1.349l.245 2.237a1.522 1.522 0 0 1-1.676 1.675l-2.237-.243a1.523 1.523 0 0 0-1.35.559l-1.408 1.752a1.518 1.518 0 0 1-2.372 0l-1.408-1.754a1.523 1.523 0 0 0-1.35-.56l-2.237.244a1.52 1.52 0 0 1-1.675-1.675l.243-2.237a1.518 1.518 0 0 0-.559-1.349l-1.754-1.408a1.52 1.52 0 0 1 0-2.372l1.754-1.409a1.52 1.52 0 0 0 .56-1.35L4.143 5.82a1.521 1.521 0 0 1 1.675-1.676l2.237.243a1.52 1.52 0 0 0 1.35-.558l1.408-1.755Z"
      />
      <path
        stroke="#fff"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m15.65 9.72-3.714 4.95a.7.7 0 0 1-1.054.076l-2.228-2.229"
      />
    </svg>
  );
};


type LoadingState = {
  text: string;
};

const LoaderCore = ({
  loadingStates,
  value = 0,
}: {
  loadingStates: LoadingState[];
  value?: number;
}) => {
  return (
    <div className="flex relative justify-start max-w-xl mx-auto flex-col mt-40 z-[200]">
      {loadingStates.map((loadingState, index) => {
        const distance = Math.abs(index - value);
        const opacity = Math.max(1 - distance * 0.2, 0);

        return (
          <motion.div
            key={index}
            className={cn("text-left flex gap-2 mb-4")}
            initial={{
              opacity: 0,
              y: -(value * 40),
            }}
            animate={{
              opacity: opacity,
              y: -(value * 40),
            }}
            transition={{ duration: 0.5 }}
          >
            <div>
              {index > value && (
                <CheckIcon />
              )}
              {index <= value && (
                <CheckFilled
                  isCurrent={value === index}
                  // className={cn(
                  //   "text-white",
                  //   value === index &&
                  //     "text-violets-are-blue  opacity-100",
                  // )}
                />
              )}
            </div>
            <span
              className={cn(
                "text-white",
                value === index && "text-violets-are-blue font-medium opacity-100",
              )}
            >
              {loadingState.text}
            </span>
          </motion.div>
        );
      })}
    </div>
  );
};

export const MultiStepLoader = ({
  loadingStates,
  loading,
  duration = 2000,
  loop = true,
}: {
  loadingStates: LoadingState[];
  loading?: boolean;
  duration?: number;
  loop?: boolean;
}) => {
  const [currentState, setCurrentState] = useState(0);

  useEffect(() => {
    if (!loading) {
      setCurrentState(0);
      return;
    }
    const timeout = setTimeout(() => {
      setCurrentState((prevState) =>
        loop
          ? prevState === loadingStates.length - 1
            ? 0
            : prevState + 1
          : Math.min(prevState + 1, loadingStates.length - 1),
      );
    }, duration);

    return () => clearTimeout(timeout);
  }, [currentState, loading, loop, loadingStates.length, duration]);
  return (
    <AnimatePresence mode="wait">
      {loading && (
        <motion.div
          initial={{
            opacity: 0,
          }}
          animate={{
            opacity: 1,
          }}
          exit={{
            opacity: 0,
          }}
          className="w-full h-full fixed inset-0 z-[100] flex items-center justify-center backdrop-blur-2xl"
        >
          <div className="h-96  relative">
            <LoaderCore value={currentState} loadingStates={loadingStates} />
          </div>
          <div className="bg-black z-30 w-[600px] h-[600px] rounded-full blur-[120px] opacity-25 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
          <div className="bg-gradient-to-t inset-x-0 z-20 bottom-0 bg-black h-full absolute [mask-image:radial-gradient(900px_at_center,transparent_30%,white)]" />
        </motion.div>
      )}
    </AnimatePresence>
  );
};
