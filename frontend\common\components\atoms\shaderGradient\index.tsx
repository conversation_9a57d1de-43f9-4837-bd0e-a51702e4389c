'use client'

import { motion } from 'framer-motion'
import { BackgroundGradientAnimation } from '../backgroundGradientAnimation';

interface ShaderGradientProps {
  firstColor?: string;
  secondColor?: string;
  thirdColor?: string;
  fourthColor?: string;
  fifthColor?: string;
  pointerColor?: string;
  size?: string;
  blendingValue?: string;
  interactive?: boolean;
  containerClassName?: string;
  opacity?: string;
}

export const ShaderGradient = ({
  firstColor,
  secondColor,
  thirdColor,
  fourthColor,
  fifthColor,
  pointerColor,
  size,
  blendingValue,
  interactive,
  containerClassName,
  opacity = "opacity-60",
}: ShaderGradientProps = {}) => {

  return (
    <motion.div
      initial={{
        opacity: 0,
      }}
      animate={{
        opacity: 1,
      }}>
      <div className={`absolute top-0 left-1/2 w-1/2 h-screen -z-10 ${opacity}`}>
        <BackgroundGradientAnimation
          firstColor={firstColor}
          secondColor={secondColor}
          thirdColor={thirdColor}
          fourthColor={fourthColor}
          fifthColor={fifthColor}
          pointerColor={pointerColor}
          size={size}
          blendingValue={blendingValue}
          interactive={interactive}
          containerClassName={containerClassName}
        />
      </div>
    </motion.div>
  );
};
